<?php
require_once __DIR__ . '/config.php';

/**
 * Gestionnaire principal des messages
 * Gère toutes les opérations CRUD avec sécurité et confidentialité
 */
class MessageManager {
    private $pdo;
    private $currentUser;
    
    public function __construct($user) {
        $this->pdo = DatabaseConfig::getConnection();
        $this->currentUser = $user;
    }
    
    /**
     * Récupérer les conversations de l'utilisateur connecté - SÉCURISATION ABSOLUE
     */
    public function getConversations() {
        try {
            $userId = (int)$this->currentUser['id'];

            // REQUÊTE BLINDÉE - Confidentialité absolue des conversations
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT
                    CASE
                        WHEN m.expediteur_id = :user_id_case1 THEN m.destinataire_id
                        ELSE m.expediteur_id
                    END as contact_id,
                    CASE
                        WHEN m.expediteur_id = :user_id_case2 THEN u2.nom
                        ELSE u1.nom
                    <PERSON><PERSON> as contact_nom,
                    CASE
                        WHEN m.expediteur_id = :user_id_case3 THEN u2.email
                        ELSE u1.email
                    END as contact_email,
                    CASE
                        WHEN m.expediteur_id = :user_id_case4 THEN r2.nom
                        ELSE r1.nom
                    END as contact_role,
                    MAX(m.date_envoi) as derniere_activite,
                    COUNT(CASE
                        WHEN m.destinataire_id = :user_id_count
                        AND m.lu = 0
                        AND m.supprime_par_destinataire = 0
                        AND m.supprime_destinataire = 0
                        THEN 1
                    END) as messages_non_lus,
                    (SELECT m2.message
                     FROM messages m2
                     WHERE ((m2.expediteur_id = :user_id_sub1 AND m2.destinataire_id = contact_id) OR
                            (m2.expediteur_id = contact_id AND m2.destinataire_id = :user_id_sub2))
                     AND ((m2.expediteur_id = :user_id_sub3 AND m2.supprime_par_expediteur = 0 AND m2.supprime_expediteur = 0) OR
                          (m2.destinataire_id = :user_id_sub4 AND m2.supprime_par_destinataire = 0 AND m2.supprime_destinataire = 0))
                     ORDER BY m2.date_envoi DESC
                     LIMIT 1
                    ) as dernier_message
                FROM messages m
                JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                JOIN roles r1 ON u1.role_id = r1.id
                JOIN roles r2 ON u2.role_id = r2.id
                WHERE (
                    -- SÉCURITÉ ABSOLUE: L'utilisateur doit être impliqué dans la conversation
                    m.expediteur_id = :user_id_where1 OR m.destinataire_id = :user_id_where2
                )
                AND (
                    -- CONDITION DE SUPPRESSION: Messages non supprimés pour l'utilisateur
                    (m.expediteur_id = :user_id_del1 AND m.supprime_par_expediteur = 0 AND m.supprime_expediteur = 0) OR
                    (m.destinataire_id = :user_id_del2 AND m.supprime_par_destinataire = 0 AND m.supprime_destinataire = 0)
                )
                -- VÉRIFICATION SUPPLÉMENTAIRE: Aucun autre utilisateur ne peut voir ces conversations
                AND (m.expediteur_id = :user_id_final1 OR m.destinataire_id = :user_id_final2)
                GROUP BY contact_id, contact_nom, contact_email, contact_role
                ORDER BY derniere_activite DESC
            ");

            // EXÉCUTION SÉCURISÉE avec paramètres nommés pour éviter toute confusion
            $stmt->execute([
                ':user_id_case1' => $userId,
                ':user_id_case2' => $userId,
                ':user_id_case3' => $userId,
                ':user_id_case4' => $userId,
                ':user_id_count' => $userId,
                ':user_id_sub1' => $userId,
                ':user_id_sub2' => $userId,
                ':user_id_sub3' => $userId,
                ':user_id_sub4' => $userId,
                ':user_id_where1' => $userId,
                ':user_id_where2' => $userId,
                ':user_id_del1' => $userId,
                ':user_id_del2' => $userId,
                ':user_id_final1' => $userId,
                ':user_id_final2' => $userId
            ]);

            $conversations = $stmt->fetchAll();

            // LOG DE SÉCURITÉ - Tracer l'accès aux conversations
            logError('Accès conversations autorisé', [
                'user_id' => $userId,
                'conversations_count' => count($conversations),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            return $conversations;

        } catch (Exception $e) {
            // LOG DE SÉCURITÉ - Tracer les erreurs d'accès
            logError('ERREUR ACCÈS CONVERSATIONS', [
                'user_id' => $this->currentUser['id'],
                'error' => $e->getMessage(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            throw new Exception('Accès refusé - Confidentialité des conversations protégée');
        }
    }
    
    /**
     * Récupérer les messages d'une conversation spécifique - SÉCURISATION ABSOLUE
     */
    public function getMessages($contactId) {
        try {
            $contactId = (int)$contactId;
            $userId = (int)$this->currentUser['id'];

            // VÉRIFICATION STRICTE - Contact valide et autorisé
            if (!$this->isValidContact($contactId)) {
                throw new Exception('Contact invalide ou non autorisé');
            }

            // SÉCURITÉ ABSOLUE - Triple vérification de l'accès
            if ($contactId === $userId) {
                throw new Exception('Impossible de récupérer une conversation avec soi-même');
            }

            // REQUÊTE BLINDÉE - Confidentialité absolue garantie
            $stmt = $this->pdo->prepare("
                SELECT m.*,
                       u1.nom as expediteur_nom, u1.email as expediteur_email,
                       u2.nom as destinataire_nom, u2.email as destinataire_email,
                       CASE
                           WHEN m.expediteur_id = :current_user_id THEN 'sent'
                           ELSE 'received'
                       END as message_type,
                       CASE
                           WHEN m.expediteur_id = :current_user_id THEN 1
                           ELSE 0
                       END as can_modify
                FROM messages m
                JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                WHERE (
                    -- CONDITION STRICTE: L'utilisateur doit être soit expéditeur soit destinataire
                    (m.expediteur_id = :user_id_1 AND m.destinataire_id = :contact_id_1) OR
                    (m.expediteur_id = :contact_id_2 AND m.destinataire_id = :user_id_2)
                )
                AND (
                    -- CONDITION DE SUPPRESSION: Message non supprimé pour l'utilisateur
                    (m.expediteur_id = :user_id_3 AND m.supprime_par_expediteur = 0 AND m.supprime_expediteur = 0) OR
                    (m.destinataire_id = :user_id_4 AND m.supprime_par_destinataire = 0 AND m.supprime_destinataire = 0)
                )
                -- SÉCURITÉ SUPPLÉMENTAIRE: Aucun autre utilisateur ne peut voir ces messages
                AND (m.expediteur_id = :user_id_5 OR m.destinataire_id = :user_id_6)
                ORDER BY m.date_envoi ASC
            ");

            // EXÉCUTION SÉCURISÉE avec paramètres nommés
            $stmt->execute([
                ':current_user_id' => $userId,
                ':user_id_1' => $userId,
                ':contact_id_1' => $contactId,
                ':contact_id_2' => $contactId,
                ':user_id_2' => $userId,
                ':user_id_3' => $userId,
                ':user_id_4' => $userId,
                ':user_id_5' => $userId,
                ':user_id_6' => $userId
            ]);

            $messages = $stmt->fetchAll();

            // LOG DE SÉCURITÉ - Tracer l'accès aux messages
            logError('Accès messages autorisé', [
                'user_id' => $userId,
                'contact_id' => $contactId,
                'messages_count' => count($messages),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

            // Marquer les messages comme lus
            $this->markMessagesAsRead($contactId);

            return $messages;

        } catch (Exception $e) {
            // LOG DE SÉCURITÉ - Tracer les tentatives d'accès non autorisées
            logError('TENTATIVE ACCÈS NON AUTORISÉ', [
                'user_id' => $this->currentUser['id'],
                'contact_id' => $contactId,
                'error' => $e->getMessage(),
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
            throw new Exception('Accès refusé - Confidentialité des messages protégée');
        }
    }
    
    /**
     * Envoyer un nouveau message
     */
    public function sendMessage($data) {
        try {
            $errors = Validator::validateMessage($data);
            if (!empty($errors)) {
                throw new Exception(implode(', ', $errors));
            }
            
            $destinataireId = (int)$data['destinataire_id'];
            $message = trim($data['message']);
            $expediteurId = $this->currentUser['id'];
            
            // Vérifier que le destinataire est valide
            if (!$this->isValidContact($destinataireId)) {
                throw new Exception('Destinataire invalide ou non autorisé');
            }
            
            // Insérer le message
            $stmt = $this->pdo->prepare("
                INSERT INTO messages (expediteur_id, destinataire_id, message, date_envoi, lu) 
                VALUES (?, ?, ?, NOW(), 0)
            ");
            
            $result = $stmt->execute([$expediteurId, $destinataireId, $message]);
            
            if (!$result) {
                throw new Exception('Échec de l\'envoi du message');
            }
            
            $messageId = $this->pdo->lastInsertId();
            
            // Récupérer le message créé avec toutes les informations
            return $this->getMessageById($messageId);
            
        } catch (Exception $e) {
            logError('Erreur envoi message', [
                'user_id' => $this->currentUser['id'], 
                'destinataire_id' => $data['destinataire_id'] ?? null,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Modifier un message existant
     */
    public function updateMessage($data) {
        try {
            $errors = Validator::validateMessageUpdate($data);
            if (!empty($errors)) {
                throw new Exception(implode(', ', $errors));
            }
            
            $messageId = (int)$data['id'];
            $nouveauMessage = trim($data['message']);
            $userId = $this->currentUser['id'];
            
            // Vérifier que l'utilisateur est l'expéditeur du message
            $stmt = $this->pdo->prepare("
                SELECT id, expediteur_id, message, modifie, message_original
                FROM messages 
                WHERE id = ? AND expediteur_id = ?
            ");
            $stmt->execute([$messageId, $userId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                throw new Exception('Message non trouvé ou vous n\'êtes pas autorisé à le modifier');
            }
            
            // Sauvegarder l'original si c'est la première modification
            $messageOriginal = $message['modifie'] ? $message['message_original'] : $message['message'];
            
            // Mettre à jour le message
            $stmt = $this->pdo->prepare("
                UPDATE messages 
                SET message = ?, 
                    modifie = 1, 
                    date_modification = NOW(),
                    message_original = ?
                WHERE id = ? AND expediteur_id = ?
            ");
            
            $result = $stmt->execute([$nouveauMessage, $messageOriginal, $messageId, $userId]);
            
            if (!$result) {
                throw new Exception('Échec de la modification du message');
            }
            
            // Récupérer le message modifié
            return $this->getMessageById($messageId);
            
        } catch (Exception $e) {
            logError('Erreur modification message', [
                'user_id' => $this->currentUser['id'], 
                'message_id' => $data['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Supprimer un message
     */
    public function deleteMessage($data) {
        try {
            $errors = Validator::validateMessageDelete($data);
            if (!empty($errors)) {
                throw new Exception(implode(', ', $errors));
            }
            
            $messageId = (int)$data['id'];
            $deleteType = $data['delete_type'] ?? 'self';
            $userId = $this->currentUser['id'];
            
            // Vérifier que l'utilisateur a accès au message
            $stmt = $this->pdo->prepare("
                SELECT id, expediteur_id, destinataire_id 
                FROM messages 
                WHERE id = ? AND (expediteur_id = ? OR destinataire_id = ?)
            ");
            $stmt->execute([$messageId, $userId, $userId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                throw new Exception('Message non trouvé ou accès refusé');
            }
            
            $isExpeditor = ($message['expediteur_id'] == $userId);
            
            // Seul l'expéditeur peut supprimer pour tous
            if ($deleteType === 'both' && !$isExpeditor) {
                throw new Exception('Seul l\'expéditeur peut supprimer le message pour tous');
            }
            
            // Effectuer la suppression selon le type
            if ($deleteType === 'both') {
                // Suppression pour les deux parties
                $stmt = $this->pdo->prepare("
                    UPDATE messages 
                    SET supprime_expediteur = 1, supprime_destinataire = 1 
                    WHERE id = ?
                ");
                $stmt->execute([$messageId]);
                $successMessage = 'Message supprimé pour tous';
            } else {
                // Suppression côté utilisateur uniquement
                if ($isExpeditor) {
                    $stmt = $this->pdo->prepare("
                        UPDATE messages 
                        SET supprime_par_expediteur = 1 
                        WHERE id = ?
                    ");
                } else {
                    $stmt = $this->pdo->prepare("
                        UPDATE messages 
                        SET supprime_par_destinataire = 1 
                        WHERE id = ?
                    ");
                }
                $stmt->execute([$messageId]);
                $successMessage = 'Message supprimé de votre côté';
            }
            
            return ['message' => $successMessage];
            
        } catch (Exception $e) {
            logError('Erreur suppression message', [
                'user_id' => $this->currentUser['id'], 
                'message_id' => $data['id'] ?? null,
                'delete_type' => $data['delete_type'] ?? 'self',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * Récupérer les utilisateurs autorisés pour la messagerie
     */
    public function getAuthorizedUsers() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role 
                FROM utilisateurs u 
                JOIN roles r ON u.role_id = r.id 
                WHERE r.nom IN ('parent', 'enseignant', 'admin', 'responsable') 
                AND u.id != ? 
                ORDER BY r.nom, u.nom
            ");
            $stmt->execute([$this->currentUser['id']]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            logError('Erreur récupération utilisateurs autorisés', [
                'user_id' => $this->currentUser['id'], 
                'error' => $e->getMessage()
            ]);
            throw new Exception('Erreur lors de la récupération des utilisateurs');
        }
    }
    
    /**
     * Vérifier si un contact est valide et autorisé
     */
    private function isValidContact($contactId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.id 
                FROM utilisateurs u 
                JOIN roles r ON u.role_id = r.id 
                WHERE u.id = ? AND r.nom IN ('parent', 'enseignant', 'admin', 'responsable')
            ");
            $stmt->execute([$contactId]);
            return $stmt->fetch() !== false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Marquer les messages comme lus
     */
    private function markMessagesAsRead($contactId) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE messages 
                SET lu = 1 
                WHERE destinataire_id = ? AND expediteur_id = ? AND lu = 0
            ");
            $stmt->execute([$this->currentUser['id'], $contactId]);
            
        } catch (Exception $e) {
            logError('Erreur marquage messages lus', [
                'user_id' => $this->currentUser['id'], 
                'contact_id' => $contactId,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Récupérer un message par son ID
     */
    private function getMessageById($messageId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT m.*, 
                       u1.nom as expediteur_nom, u1.email as expediteur_email,
                       u2.nom as destinataire_nom, u2.email as destinataire_email,
                       CASE 
                           WHEN m.expediteur_id = ? THEN 'sent'
                           ELSE 'received'
                       END as message_type,
                       CASE 
                           WHEN m.expediteur_id = ? THEN 1
                           ELSE 0
                       END as can_modify
                FROM messages m
                JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                WHERE m.id = ?
            ");
            $stmt->execute([$this->currentUser['id'], $this->currentUser['id'], $messageId]);
            return $stmt->fetch();
            
        } catch (Exception $e) {
            throw new Exception('Erreur lors de la récupération du message');
        }
    }
}
?>
