<?php
/**
 * Configuration de base de données pour l'API de messagerie
 * Système de messagerie sécurisé pour l'application scolaire
 */

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'school_management');
define('DB_USER', 'root');
define('DB_PASS', '');

// Configuration CORS
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration du contenu JSON
header('Content-Type: application/json; charset=utf-8');

/**
 * Connexion à la base de données avec gestion d'erreurs
 */
function getDBConnection() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        return $pdo;
    } catch (PDOException $e) {
        error_log("Erreur de connexion DB: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur de connexion à la base de données'
        ]);
        exit();
    }
}

/**
 * Vérification de l'authentification utilisateur
 */
function verifyAuth() {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (empty($authHeader) || !str_starts_with($authHeader, 'Bearer ')) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Token d\'authentification manquant'
        ]);
        exit();
    }
    
    $token = substr($authHeader, 7);
    
    // Ici vous pouvez ajouter la vérification du token JWT si nécessaire
    // Pour l'instant, on vérifie juste la présence du token
    if (empty($token)) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => 'Token invalide'
        ]);
        exit();
    }
    
    return true;
}

/**
 * Vérification des droits d'accès à la messagerie
 * Seuls les Parents (4), Enseignants (2), et Admin (1) ont accès
 */
function verifyMessagingAccess($userId) {
    try {
        $pdo = getDBConnection();
        
        $stmt = $pdo->prepare("
            SELECT u.role_id, r.nom as role_nom 
            FROM utilisateurs u 
            JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        if (!$user) {
            http_response_code(404);
            echo json_encode([
                'success' => false,
                'error' => 'Utilisateur non trouvé'
            ]);
            exit();
        }
        
        // Vérifier les rôles autorisés (Admin=1, Enseignant=2, Parent=4)
        $allowedRoles = [1, 2, 4];
        if (!in_array((int)$user['role_id'], $allowedRoles)) {
            http_response_code(403);
            echo json_encode([
                'success' => false,
                'error' => 'Accès refusé - Rôle non autorisé pour la messagerie'
            ]);
            exit();
        }
        
        return $user;
    } catch (Exception $e) {
        error_log("Erreur vérification accès: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Erreur de vérification des droits'
        ]);
        exit();
    }
}

/**
 * Fonction utilitaire pour valider les paramètres requis
 */
function validateRequiredParams($params, $required) {
    $missing = [];
    foreach ($required as $param) {
        if (!isset($params[$param]) || empty($params[$param])) {
            $missing[] = $param;
        }
    }
    
    if (!empty($missing)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Paramètres manquants: ' . implode(', ', $missing)
        ]);
        exit();
    }
}

/**
 * Fonction utilitaire pour nettoyer et sécuriser les données
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Fonction pour enregistrer les logs d'activité
 */
function logActivity($userId, $action, $details = '') {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare("
            INSERT INTO activity_logs (user_id, action, details, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$userId, $action, $details]);
    } catch (Exception $e) {
        error_log("Erreur log activité: " . $e->getMessage());
    }
}

/**
 * Réponse JSON standardisée pour les succès
 */
function sendSuccessResponse($data = [], $message = 'Opération réussie') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

/**
 * Réponse JSON standardisée pour les erreurs
 */
function sendErrorResponse($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message
    ]);
    exit();
}
?>
