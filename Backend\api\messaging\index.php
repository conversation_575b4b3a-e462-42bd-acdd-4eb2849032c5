<?php
header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

// Gérer les requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'AuthManager.php';
require_once 'MessageManager.php';

/**
 * 🚀 API MESSAGERIE COMPLÈTE
 * Système de messagerie avec fonctionnalités WhatsApp
 */

try {
    // Récupérer la méthode HTTP et l'endpoint
    $method = $_SERVER['REQUEST_METHOD'];

    // Extraire l'action des paramètres GET/POST en priorité
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    // Si pas d'action dans les paramètres, essayer de l'extraire de l'URL
    if (empty($action)) {
        $requestUri = $_SERVER['REQUEST_URI'];
        $path = parse_url($requestUri, PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));
        $lastPart = isset($pathParts[count($pathParts) - 1]) ? $pathParts[count($pathParts) - 1] : '';

        if (!empty($lastPart) && $lastPart !== 'index.php' && $lastPart !== 'messaging') {
            $action = $lastPart;
        }
    }

    // Debug: Log de l'action extraite
    error_log("API Messagerie - Action extraite: '$action' - URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A'));
    
    // Authentification
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    if (empty($authHeader)) {
        $authHeader = $_GET['token'] ?? $_POST['token'] ?? 'test_user_1';
    }
    
    $token = str_replace('Bearer ', '', $authHeader);
    
    $authManager = new AuthManager();
    $currentUser = $authManager->authenticateUser($token);
    
    if (!$currentUser) {
        throw new Exception('Authentification requise');
    }
    
    $messageManager = new MessageManager($currentUser);
    
    // Router les requêtes selon l'action
    switch ($action) {
        
        // 💬 RÉCUPÉRER LES CONVERSATIONS
        case 'conversations':
        case 'getConversations':
            if ($method !== 'GET') {
                throw new Exception('Méthode non autorisée pour les conversations');
            }
            
            $conversations = $messageManager->getConversations();
            
            echo json_encode([
                'success' => true,
                'data' => $conversations,
                'count' => count($conversations),
                'user' => [
                    'id' => $currentUser['id'],
                    'nom' => $currentUser['nom'],
                    'role' => $currentUser['role']
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 📨 RÉCUPÉRER LES MESSAGES D'UNE CONVERSATION
        case 'messages':
        case 'getMessages':
            if ($method !== 'GET') {
                throw new Exception('Méthode non autorisée pour les messages');
            }
            
            $contactId = $_GET['contact_id'] ?? $_GET['contactId'] ?? null;
            if (!$contactId) {
                throw new Exception('ID du contact requis');
            }
            
            $messages = $messageManager->getMessages($contactId);
            
            echo json_encode([
                'success' => true,
                'data' => $messages,
                'count' => count($messages),
                'contact_id' => (int)$contactId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // ✉️ ENVOYER UN MESSAGE
        case 'send':
        case 'sendMessage':
            if ($method !== 'POST') {
                throw new Exception('Méthode POST requise pour envoyer un message');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $destinataireId = $input['destinataire_id'] ?? $input['destinataireId'] ?? null;
            $message = $input['message'] ?? null;
            
            if (!$destinataireId || !$message) {
                throw new Exception('Destinataire et message requis');
            }
            
            $messageId = $messageManager->sendMessage($destinataireId, $message);
            
            echo json_encode([
                'success' => true,
                'message' => 'Message envoyé avec succès',
                'message_id' => $messageId,
                'data' => [
                    'id' => $messageId,
                    'expediteur_id' => $currentUser['id'],
                    'destinataire_id' => (int)$destinataireId,
                    'message' => $message,
                    'date_envoi' => date('Y-m-d H:i:s')
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // ✏️ MODIFIER UN MESSAGE
        case 'edit':
        case 'editMessage':
            if ($method !== 'PUT' && $method !== 'POST') {
                throw new Exception('Méthode PUT ou POST requise pour modifier un message');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $messageId = $input['message_id'] ?? $input['messageId'] ?? null;
            $newContent = $input['message'] ?? $input['content'] ?? null;
            
            if (!$messageId || !$newContent) {
                throw new Exception('ID du message et nouveau contenu requis');
            }
            
            $result = $messageManager->editMessage($messageId, $newContent);
            
            echo json_encode([
                'success' => true,
                'message' => 'Message modifié avec succès',
                'message_id' => (int)$messageId,
                'modified_at' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 🗑️ SUPPRIMER UN MESSAGE
        case 'delete':
        case 'deleteMessage':
            if ($method !== 'DELETE' && $method !== 'POST') {
                throw new Exception('Méthode DELETE ou POST requise pour supprimer un message');
            }
            
            $input = json_decode(file_get_contents('php://input'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $messageId = $input['message_id'] ?? $input['messageId'] ?? null;
            $deleteType = $input['delete_type'] ?? $input['deleteType'] ?? 'for_me';
            
            if (!$messageId) {
                throw new Exception('ID du message requis');
            }
            
            $result = $messageManager->deleteMessage($messageId, $deleteType);
            
            echo json_encode([
                'success' => true,
                'message' => 'Message supprimé avec succès',
                'message_id' => (int)$messageId,
                'delete_type' => $deleteType,
                'deleted_at' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 👥 RÉCUPÉRER LES UTILISATEURS AUTORISÉS
        case 'users':
        case 'getUsers':
        case 'authorizedUsers':
            if ($method !== 'GET') {
                throw new Exception('Méthode GET requise pour les utilisateurs');
            }
            
            $users = $messageManager->getAuthorizedUsers();
            
            echo json_encode([
                'success' => true,
                'data' => $users,
                'count' => count($users)
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 📊 RÉCUPÉRER LES STATISTIQUES
        case 'stats':
        case 'getStats':
        case 'statistics':
            if ($method !== 'GET') {
                throw new Exception('Méthode GET requise pour les statistiques');
            }
            
            $stats = $messageManager->getStats();
            
            echo json_encode([
                'success' => true,
                'data' => $stats,
                'user' => [
                    'id' => $currentUser['id'],
                    'nom' => $currentUser['nom'],
                    'role' => $currentUser['role']
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 🔍 TESTER L'API
        case 'test':
        case 'ping':
            echo json_encode([
                'success' => true,
                'message' => 'API Messagerie fonctionnelle',
                'timestamp' => date('Y-m-d H:i:s'),
                'user' => [
                    'id' => $currentUser['id'],
                    'nom' => $currentUser['nom'],
                    'role' => $currentUser['role']
                ],
                'debug' => [
                    'method' => $method,
                    'action_received' => $action,
                    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'N/A',
                    'get_params' => $_GET,
                    'post_params' => $_POST
                ],
                'endpoints' => [
                    'conversations' => 'GET /conversations',
                    'messages' => 'GET /messages?contact_id=X',
                    'send' => 'POST /send',
                    'edit' => 'PUT /edit',
                    'delete' => 'DELETE /delete',
                    'users' => 'GET /users',
                    'stats' => 'GET /stats'
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            // Action non reconnue - afficher la documentation
            echo json_encode([
                'success' => false,
                'error' => 'Action non reconnue: ' . $action,
                'available_actions' => [
                    'conversations' => 'Récupérer les conversations',
                    'messages' => 'Récupérer les messages d\'une conversation',
                    'send' => 'Envoyer un message',
                    'edit' => 'Modifier un message',
                    'delete' => 'Supprimer un message',
                    'users' => 'Récupérer les utilisateurs autorisés',
                    'stats' => 'Récupérer les statistiques',
                    'test' => 'Tester l\'API'
                ],
                'usage' => [
                    'method' => $method,
                    'action' => $action,
                    'user' => $currentUser['nom'] ?? 'Inconnu'
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s'),
        'debug' => [
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
            'action' => $action ?? 'NONE',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN'
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>