{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\MessagingSystem.js\";\nimport React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\nconst MessagingSystem = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [authorizedUsers, setAuthorizedUsers] = useState([]);\n  const [showNewConversation, setShowNewConversation] = useState(false);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [showContextMenu, setShowContextMenu] = useState(null);\n  const [stats, setStats] = useState({});\n  const messagesEndRef = useRef(null);\n  const contextMenuRef = useRef(null);\n  const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n\n  // Scroll automatique vers le bas\n  const scrollToBottom = () => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fermer le menu contextuel en cliquant ailleurs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n        setShowContextMenu(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fonction pour faire des requêtes API\n  const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n    try {\n      const token = localStorage.getItem('token') || 'test_user_1';\n      const config = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      };\n      if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n        config.body = JSON.stringify(data);\n      }\n      const response = await fetch(`${API_BASE_URL}?action=${endpoint}`, config);\n      const result = await response.json();\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur API');\n      }\n      return result;\n    } catch (error) {\n      console.error('Erreur API:', error);\n      throw error;\n    }\n  };\n\n  // Charger les conversations\n  const loadConversations = async () => {\n    try {\n      setLoading(true);\n      const result = await makeAPIRequest('conversations');\n      setConversations(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les conversations: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les messages d'une conversation\n  const loadMessages = async contactId => {\n    try {\n      setLoading(true);\n      const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n      setMessages(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les messages: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les utilisateurs autorisés\n  const loadAuthorizedUsers = async () => {\n    try {\n      const result = await makeAPIRequest('users');\n      setAuthorizedUsers(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les utilisateurs: ' + error.message);\n    }\n  };\n\n  // Charger les statistiques\n  const loadStats = async () => {\n    try {\n      const result = await makeAPIRequest('stats');\n      setStats(result.data || {});\n    } catch (error) {\n      console.error('Erreur chargement stats:', error);\n    }\n  };\n\n  // Envoyer un message\n  const sendMessage = async () => {\n    if (!newMessage.trim()) return;\n    try {\n      const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n      if (!destinataireId) {\n        setError('Veuillez sélectionner un destinataire');\n        return;\n      }\n      await makeAPIRequest('send', 'POST', {\n        destinataire_id: destinataireId,\n        message: newMessage.trim()\n      });\n      setNewMessage('');\n      setShowNewConversation(false);\n\n      // Recharger les conversations et messages\n      await loadConversations();\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible d\\'envoyer le message: ' + error.message);\n    }\n  };\n\n  // Modifier un message\n  const editMessage = async (messageId, newContent) => {\n    try {\n      await makeAPIRequest('edit', 'PUT', {\n        message_id: messageId,\n        message: newContent\n      });\n      setEditingMessage(null);\n      setEditContent('');\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de modifier le message: ' + error.message);\n    }\n  };\n\n  // Supprimer un message\n  const deleteMessage = async (messageId, deleteType = 'for_me') => {\n    try {\n      await makeAPIRequest('delete', 'DELETE', {\n        message_id: messageId,\n        delete_type: deleteType\n      });\n      setShowContextMenu(null);\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de supprimer le message: ' + error.message);\n    }\n  };\n\n  // Sélectionner une conversation\n  const selectConversation = async conversation => {\n    setSelectedConversation(conversation);\n    setShowNewConversation(false);\n    await loadMessages(conversation.contact_id);\n  };\n\n  // Démarrer une nouvelle conversation\n  const startNewConversation = () => {\n    setSelectedConversation(null);\n    setMessages([]);\n    setShowNewConversation(true);\n  };\n\n  // Gérer le menu contextuel\n  const handleContextMenu = (e, message) => {\n    e.preventDefault();\n    setShowContextMenu({\n      x: e.clientX,\n      y: e.clientY,\n      message: message\n    });\n  };\n\n  // Démarrer l'édition d'un message\n  const startEditing = message => {\n    setEditingMessage(message.id);\n    setEditContent(message.message);\n    setShowContextMenu(null);\n  };\n\n  // Annuler l'édition\n  const cancelEditing = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  // Confirmer l'édition\n  const confirmEdit = async () => {\n    if (editContent.trim() && editingMessage) {\n      await editMessage(editingMessage, editContent.trim());\n    }\n  };\n\n  // Formater la date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffDays <= 7) {\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n\n  // Charger les données au montage du composant\n  useEffect(() => {\n    // Vérifier que l'utilisateur est connecté avant de charger les données\n    if (user && user.id) {\n      loadConversations();\n      loadAuthorizedUsers();\n      loadStats();\n    } else {\n      console.warn('Utilisateur non connecté, chargement des données de test...');\n      // Charger quand même pour les tests\n      loadConversations();\n      loadAuthorizedUsers();\n      loadStats();\n    }\n  }, [user]);\n\n  // Actualiser périodiquement\n  useEffect(() => {\n    const interval = setInterval(() => {\n      loadConversations();\n      if (selectedConversation) {\n        loadMessages(selectedConversation.contact_id);\n      }\n    }, 30000); // Actualiser toutes les 30 secondes\n\n    return () => clearInterval(interval);\n  }, [selectedConversation]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-system\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-stats\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 25\n    }\n  }, stats.total_messages || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 25\n    }\n  }, \"Messages\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 25\n    }\n  }, stats.messages_non_lus || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 25\n    }\n  }, \"Non lus\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 25\n    }\n  }, conversations.length), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 25\n    }\n  }, \"Conversations\")))), error && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 21\n    }\n  }, \"\\u274C \", error), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setError(''),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 21\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 25\n    }\n  }, \"Conversations\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"new-conversation-btn\",\n    onClick: startNewConversation,\n    title: \"Nouvelle conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-list\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 21\n    }\n  }, loading && conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 29\n    }\n  }, \"Chargement...\") : conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-conversations\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 33\n    }\n  }, \"Aucune conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 33\n    }\n  }, \"D\\xE9marrer une conversation\")) : conversations.map(conversation => /*#__PURE__*/React.createElement(\"div\", {\n    key: conversation.contact_id,\n    className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.contact_id) === conversation.contact_id ? 'active' : ''}`,\n    onClick: () => selectConversation(conversation),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 37\n    }\n  }, conversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 41\n    }\n  }, conversation.contact_nom, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 45\n    }\n  }, conversation.contact_role)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-preview\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 41\n    }\n  }, conversation.dernier_message || 'Aucun message'), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-meta\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 45\n    }\n  }, formatDate(conversation.derniere_activite)), conversation.messages_non_lus > 0 && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"unread-badge\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 49\n    }\n  }, conversation.messages_non_lus))))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 17\n    }\n  }, showNewConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setShowNewConversation(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 33\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: selectedUser,\n    onChange: e => setSelectedUser(e.target.value),\n    className: \"user-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur...\"), authorizedUsers.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 41\n    }\n  }, user.nom, \" (\", user.role, \")\"))))) : selectedConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 33\n    }\n  }, selectedConversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_nom), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_role)))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-selected\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 33\n    }\n  }, \"S\\xE9lectionnez une conversation ou d\\xE9marrez-en une nouvelle\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"))), (selectedConversation || showNewConversation) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messages-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 29\n    }\n  }, messages.map(message => /*#__PURE__*/React.createElement(\"div\", {\n    key: message.id,\n    className: `message ${message.message_type}`,\n    onContextMenu: e => handleContextMenu(e, message),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 41\n    }\n  }, editingMessage === message.id ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-edit\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    value: editContent,\n    onChange: e => setEditContent(e.target.value),\n    className: \"edit-textarea\",\n    autoFocus: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 53\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"edit-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: confirmEdit,\n    className: \"confirm-edit\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 57\n    }\n  }, \"\\u2713\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: cancelEditing,\n    className: \"cancel-edit\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 57\n    }\n  }, \"\\u2715\"))) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 53\n    }\n  }, message.message, message.modifie === '1' && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"message-edited\",\n    title: `Modifié le ${formatDate(message.date_modification)}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 61\n    }\n  }, \"(modifi\\xE9)\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 53\n    }\n  }, formatDate(message.date_envoi), message.message_type === 'sent' && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"message-status\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 61\n    }\n  }, message.lu === '1' ? '✓✓' : '✓')))))), /*#__PURE__*/React.createElement(\"div\", {\n    ref: messagesEndRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    value: newMessage,\n    onChange: e => setNewMessage(e.target.value),\n    placeholder: \"Tapez votre message...\",\n    className: \"message-input\",\n    rows: \"1\",\n    onKeyDown: e => {\n      if (e.key === 'Enter' && !e.shiftKey) {\n        e.preventDefault();\n        sendMessage();\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: sendMessage,\n    className: \"send-button\",\n    disabled: !newMessage.trim() || loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE4\")))))), showContextMenu && /*#__PURE__*/React.createElement(\"div\", {\n    ref: contextMenuRef,\n    className: \"context-menu\",\n    style: {\n      left: showContextMenu.x,\n      top: showContextMenu.y\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 17\n    }\n  }, showContextMenu.message.can_modify === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => startEditing(showContextMenu.message),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_me'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour moi\"), showContextMenu.message.can_delete_for_all === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_everyone'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 25\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour tous\")));\n};\nexport default MessagingSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "AuthContext", "MessagingSystem", "user", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "authorizedUsers", "setAuthorizedUsers", "showNewConversation", "setShowNewConversation", "selected<PERSON>ser", "setSelectedUser", "loading", "setLoading", "error", "setError", "editingMessage", "setEditingMessage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showContextMenu", "setShowContextMenu", "stats", "setStats", "messagesEndRef", "contextMenuRef", "API_BASE_URL", "scrollToBottom", "current", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "makeAPIRequest", "endpoint", "method", "data", "token", "localStorage", "getItem", "config", "headers", "body", "JSON", "stringify", "response", "fetch", "result", "json", "success", "Error", "console", "loadConversations", "message", "loadMessages", "contactId", "loadAuthorizedUsers", "loadStats", "sendMessage", "trim", "destinataireId", "contact_id", "destinataire_id", "editMessage", "messageId", "newContent", "message_id", "deleteMessage", "deleteType", "delete_type", "selectConversation", "conversation", "startNewConversation", "handleContextMenu", "e", "preventDefault", "x", "clientX", "y", "clientY", "startEditing", "id", "cancelEditing", "confirmEdit", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "warn", "interval", "setInterval", "clearInterval", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "total_messages", "messages_non_lus", "length", "onClick", "title", "map", "key", "contact_nom", "char<PERSON>t", "toUpperCase", "contact_role", "dernier_message", "derniere_activite", "value", "onChange", "nom", "role", "Fragment", "message_type", "onContextMenu", "autoFocus", "modifie", "date_modification", "date_envoi", "lu", "ref", "placeholder", "rows", "onKeyDown", "shift<PERSON>ey", "disabled", "style", "left", "top", "can_modify", "can_delete_for_all"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/MessagingSystem.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\n\nconst MessagingSystem = () => {\n    const { user } = useContext(AuthContext);\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [authorizedUsers, setAuthorizedUsers] = useState([]);\n    const [showNewConversation, setShowNewConversation] = useState(false);\n    const [selectedUser, setSelectedUser] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [editingMessage, setEditingMessage] = useState(null);\n    const [editContent, setEditContent] = useState('');\n    const [showContextMenu, setShowContextMenu] = useState(null);\n    const [stats, setStats] = useState({});\n    \n    const messagesEndRef = useRef(null);\n    const contextMenuRef = useRef(null);\n    \n    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n    \n    // Scroll automatique vers le bas\n    const scrollToBottom = () => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n        }\n    };\n    \n    useEffect(() => {\n        scrollToBottom();\n    }, [messages]);\n    \n    // Fermer le menu contextuel en cliquant ailleurs\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n                setShowContextMenu(null);\n            }\n        };\n        \n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    \n    // Fonction pour faire des requêtes API\n    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n        try {\n            const token = localStorage.getItem('token') || 'test_user_1';\n            \n            const config = {\n                method,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                }\n            };\n            \n            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n                config.body = JSON.stringify(data);\n            }\n            \n            const response = await fetch(`${API_BASE_URL}?action=${endpoint}`, config);\n            const result = await response.json();\n            \n            if (!result.success) {\n                throw new Error(result.error || 'Erreur API');\n            }\n            \n            return result;\n        } catch (error) {\n            console.error('Erreur API:', error);\n            throw error;\n        }\n    };\n    \n    // Charger les conversations\n    const loadConversations = async () => {\n        try {\n            setLoading(true);\n            const result = await makeAPIRequest('conversations');\n            setConversations(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les conversations: ' + error.message);\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les messages d'une conversation\n    const loadMessages = async (contactId) => {\n        try {\n            setLoading(true);\n            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n            setMessages(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les messages: ' + error.message);\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les utilisateurs autorisés\n    const loadAuthorizedUsers = async () => {\n        try {\n            const result = await makeAPIRequest('users');\n            setAuthorizedUsers(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les utilisateurs: ' + error.message);\n        }\n    };\n    \n    // Charger les statistiques\n    const loadStats = async () => {\n        try {\n            const result = await makeAPIRequest('stats');\n            setStats(result.data || {});\n        } catch (error) {\n            console.error('Erreur chargement stats:', error);\n        }\n    };\n    \n    // Envoyer un message\n    const sendMessage = async () => {\n        if (!newMessage.trim()) return;\n        \n        try {\n            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n            \n            if (!destinataireId) {\n                setError('Veuillez sélectionner un destinataire');\n                return;\n            }\n            \n            await makeAPIRequest('send', 'POST', {\n                destinataire_id: destinataireId,\n                message: newMessage.trim()\n            });\n            \n            setNewMessage('');\n            setShowNewConversation(false);\n            \n            // Recharger les conversations et messages\n            await loadConversations();\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible d\\'envoyer le message: ' + error.message);\n        }\n    };\n    \n    // Modifier un message\n    const editMessage = async (messageId, newContent) => {\n        try {\n            await makeAPIRequest('edit', 'PUT', {\n                message_id: messageId,\n                message: newContent\n            });\n            \n            setEditingMessage(null);\n            setEditContent('');\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de modifier le message: ' + error.message);\n        }\n    };\n    \n    // Supprimer un message\n    const deleteMessage = async (messageId, deleteType = 'for_me') => {\n        try {\n            await makeAPIRequest('delete', 'DELETE', {\n                message_id: messageId,\n                delete_type: deleteType\n            });\n            \n            setShowContextMenu(null);\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de supprimer le message: ' + error.message);\n        }\n    };\n    \n    // Sélectionner une conversation\n    const selectConversation = async (conversation) => {\n        setSelectedConversation(conversation);\n        setShowNewConversation(false);\n        await loadMessages(conversation.contact_id);\n    };\n    \n    // Démarrer une nouvelle conversation\n    const startNewConversation = () => {\n        setSelectedConversation(null);\n        setMessages([]);\n        setShowNewConversation(true);\n    };\n    \n    // Gérer le menu contextuel\n    const handleContextMenu = (e, message) => {\n        e.preventDefault();\n        setShowContextMenu({\n            x: e.clientX,\n            y: e.clientY,\n            message: message\n        });\n    };\n    \n    // Démarrer l'édition d'un message\n    const startEditing = (message) => {\n        setEditingMessage(message.id);\n        setEditContent(message.message);\n        setShowContextMenu(null);\n    };\n    \n    // Annuler l'édition\n    const cancelEditing = () => {\n        setEditingMessage(null);\n        setEditContent('');\n    };\n    \n    // Confirmer l'édition\n    const confirmEdit = async () => {\n        if (editContent.trim() && editingMessage) {\n            await editMessage(editingMessage, editContent.trim());\n        }\n    };\n    \n    // Formater la date\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffTime = Math.abs(now - date);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        \n        if (diffDays === 1) {\n            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });\n        } else if (diffDays <= 7) {\n            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });\n        } else {\n            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });\n        }\n    };\n    \n    // Charger les données au montage du composant\n    useEffect(() => {\n        // Vérifier que l'utilisateur est connecté avant de charger les données\n        if (user && user.id) {\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n        } else {\n            console.warn('Utilisateur non connecté, chargement des données de test...');\n            // Charger quand même pour les tests\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n        }\n    }, [user]);\n    \n    // Actualiser périodiquement\n    useEffect(() => {\n        const interval = setInterval(() => {\n            loadConversations();\n            if (selectedConversation) {\n                loadMessages(selectedConversation.contact_id);\n            }\n        }, 30000); // Actualiser toutes les 30 secondes\n        \n        return () => clearInterval(interval);\n    }, [selectedConversation]);\n    \n    return (\n        <div className=\"messaging-system\">\n            <div className=\"messaging-header\">\n                <h1>💬 Messagerie</h1>\n                <div className=\"messaging-stats\">\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.total_messages || 0}</span>\n                        <span className=\"stat-label\">Messages</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.messages_non_lus || 0}</span>\n                        <span className=\"stat-label\">Non lus</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{conversations.length}</span>\n                        <span className=\"stat-label\">Conversations</span>\n                    </span>\n                </div>\n            </div>\n            \n            {error && (\n                <div className=\"error-message\">\n                    <span>❌ {error}</span>\n                    <button onClick={() => setError('')}>✕</button>\n                </div>\n            )}\n            \n            <div className=\"messaging-container\">\n                {/* Liste des conversations */}\n                <div className=\"conversations-panel\">\n                    <div className=\"conversations-header\">\n                        <h3>Conversations</h3>\n                        <button \n                            className=\"new-conversation-btn\"\n                            onClick={startNewConversation}\n                            title=\"Nouvelle conversation\"\n                        >\n                            ✏️\n                        </button>\n                    </div>\n                    \n                    <div className=\"conversations-list\">\n                        {loading && conversations.length === 0 ? (\n                            <div className=\"loading\">Chargement...</div>\n                        ) : conversations.length === 0 ? (\n                            <div className=\"no-conversations\">\n                                <p>Aucune conversation</p>\n                                <button onClick={startNewConversation}>\n                                    Démarrer une conversation\n                                </button>\n                            </div>\n                        ) : (\n                            conversations.map(conversation => (\n                                <div\n                                    key={conversation.contact_id}\n                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}\n                                    onClick={() => selectConversation(conversation)}\n                                >\n                                    <div className=\"conversation-avatar\">\n                                        {conversation.contact_nom.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"conversation-info\">\n                                        <div className=\"conversation-name\">\n                                            {conversation.contact_nom}\n                                            <span className=\"conversation-role\">\n                                                {conversation.contact_role}\n                                            </span>\n                                        </div>\n                                        <div className=\"conversation-preview\">\n                                            {conversation.dernier_message || 'Aucun message'}\n                                        </div>\n                                        <div className=\"conversation-meta\">\n                                            <span className=\"conversation-time\">\n                                                {formatDate(conversation.derniere_activite)}\n                                            </span>\n                                            {conversation.messages_non_lus > 0 && (\n                                                <span className=\"unread-badge\">\n                                                    {conversation.messages_non_lus}\n                                                </span>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            ))\n                        )}\n                    </div>\n                </div>\n                \n                {/* Zone de chat */}\n                <div className=\"chat-panel\">\n                    {showNewConversation ? (\n                        <div className=\"new-conversation\">\n                            <div className=\"new-conversation-header\">\n                                <h3>Nouvelle conversation</h3>\n                                <button onClick={() => setShowNewConversation(false)}>✕</button>\n                            </div>\n                            <div className=\"new-conversation-content\">\n                                <select\n                                    value={selectedUser}\n                                    onChange={(e) => setSelectedUser(e.target.value)}\n                                    className=\"user-select\"\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur...</option>\n                                    {authorizedUsers.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} ({user.role})\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n                    ) : selectedConversation ? (\n                        <div className=\"chat-header\">\n                            <div className=\"chat-contact-info\">\n                                <div className=\"chat-avatar\">\n                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}\n                                </div>\n                                <div>\n                                    <div className=\"chat-contact-name\">\n                                        {selectedConversation.contact_nom}\n                                    </div>\n                                    <div className=\"chat-contact-role\">\n                                        {selectedConversation.contact_role}\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    ) : (\n                        <div className=\"no-chat-selected\">\n                            <div className=\"no-chat-content\">\n                                <h3>💬 Messagerie</h3>\n                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>\n                                <button onClick={startNewConversation}>\n                                    Nouvelle conversation\n                                </button>\n                            </div>\n                        </div>\n                    )}\n                    \n                    {/* Messages */}\n                    {(selectedConversation || showNewConversation) && (\n                        <>\n                            <div className=\"messages-container\">\n                                {messages.map(message => (\n                                    <div\n                                        key={message.id}\n                                        className={`message ${message.message_type}`}\n                                        onContextMenu={(e) => handleContextMenu(e, message)}\n                                    >\n                                        <div className=\"message-content\">\n                                            {editingMessage === message.id ? (\n                                                <div className=\"message-edit\">\n                                                    <textarea\n                                                        value={editContent}\n                                                        onChange={(e) => setEditContent(e.target.value)}\n                                                        className=\"edit-textarea\"\n                                                        autoFocus\n                                                    />\n                                                    <div className=\"edit-actions\">\n                                                        <button onClick={confirmEdit} className=\"confirm-edit\">\n                                                            ✓\n                                                        </button>\n                                                        <button onClick={cancelEditing} className=\"cancel-edit\">\n                                                            ✕\n                                                        </button>\n                                                    </div>\n                                                </div>\n                                            ) : (\n                                                <>\n                                                    <div className=\"message-text\">\n                                                        {message.message}\n                                                        {message.modifie === '1' && (\n                                                            <span className=\"message-edited\" title={`Modifié le ${formatDate(message.date_modification)}`}>\n                                                                (modifié)\n                                                            </span>\n                                                        )}\n                                                    </div>\n                                                    <div className=\"message-time\">\n                                                        {formatDate(message.date_envoi)}\n                                                        {message.message_type === 'sent' && (\n                                                            <span className=\"message-status\">\n                                                                {message.lu === '1' ? '✓✓' : '✓'}\n                                                            </span>\n                                                        )}\n                                                    </div>\n                                                </>\n                                            )}\n                                        </div>\n                                    </div>\n                                ))}\n                                <div ref={messagesEndRef} />\n                            </div>\n                            \n                            {/* Zone de saisie */}\n                            <div className=\"message-input-container\">\n                                <div className=\"message-input-wrapper\">\n                                    <textarea\n                                        value={newMessage}\n                                        onChange={(e) => setNewMessage(e.target.value)}\n                                        placeholder=\"Tapez votre message...\"\n                                        className=\"message-input\"\n                                        rows=\"1\"\n                                        onKeyDown={(e) => {\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                sendMessage();\n                                            }\n                                        }}\n                                    />\n                                    <button\n                                        onClick={sendMessage}\n                                        className=\"send-button\"\n                                        disabled={!newMessage.trim() || loading}\n                                    >\n                                        📤\n                                    </button>\n                                </div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            </div>\n            \n            {/* Menu contextuel */}\n            {showContextMenu && (\n                <div\n                    ref={contextMenuRef}\n                    className=\"context-menu\"\n                    style={{\n                        left: showContextMenu.x,\n                        top: showContextMenu.y\n                    }}\n                >\n                    {showContextMenu.message.can_modify === 1 && (\n                        <button onClick={() => startEditing(showContextMenu.message)}>\n                            ✏️ Modifier\n                        </button>\n                    )}\n                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>\n                        🗑️ Supprimer pour moi\n                    </button>\n                    {showContextMenu.message.can_delete_for_all === 1 && (\n                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>\n                            🗑️ Supprimer pour tous\n                        </button>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default MessagingSystem;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,+BAA+B;AAEtC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGH,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACS,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtC,MAAMiC,cAAc,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMgC,cAAc,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMiC,YAAY,GAAG,qDAAqD;;EAE1E;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIH,cAAc,CAACI,OAAO,EAAE;MACxBJ,cAAc,CAACI,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACjE;EACJ,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACZmC,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;;EAEd;EACAV,SAAS,CAAC,MAAM;IACZ,MAAMuC,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIP,cAAc,CAACG,OAAO,IAAI,CAACH,cAAc,CAACG,OAAO,CAACK,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAC1Eb,kBAAkB,CAAC,IAAI,CAAC;MAC5B;IACJ,CAAC;IAEDc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IACpE,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa;MAE5D,MAAMC,MAAM,GAAG;QACXL,MAAM;QACNM,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUJ,KAAK;QACpC;MACJ,CAAC;MAED,IAAID,IAAI,KAAKD,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,QAAQ,CAAC,EAAE;QACxEK,MAAM,CAACE,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;MACtC;MAEA,MAAMS,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGzB,YAAY,WAAWa,QAAQ,EAAE,EAAEM,MAAM,CAAC;MAC1E,MAAMO,MAAM,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAEpC,IAAI,CAACD,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAACtC,KAAK,IAAI,YAAY,CAAC;MACjD;MAEA,OAAOsC,MAAM;IACjB,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACZ0C,OAAO,CAAC1C,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,MAAMA,KAAK;IACf;EACJ,CAAC;;EAED;EACA,MAAM2C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA5C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,MAAM,GAAG,MAAMd,cAAc,CAAC,eAAe,CAAC;MACpDvC,gBAAgB,CAACqD,MAAM,CAACX,IAAI,IAAI,EAAE,CAAC;IACvC,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACZC,QAAQ,CAAC,2CAA2C,GAAGD,KAAK,CAAC4C,OAAO,CAAC;IACzE,CAAC,SAAS;MACN7C,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAM8C,YAAY,GAAG,MAAOC,SAAS,IAAK;IACtC,IAAI;MACA/C,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMuC,MAAM,GAAG,MAAMd,cAAc,CAAC,uBAAuBsB,SAAS,EAAE,CAAC;MACvEzD,WAAW,CAACiD,MAAM,CAACX,IAAI,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAAC4C,OAAO,CAAC;IACpE,CAAC,SAAS;MACN7C,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMgD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,MAAMT,MAAM,GAAG,MAAMd,cAAc,CAAC,OAAO,CAAC;MAC5C/B,kBAAkB,CAAC6C,MAAM,CAACX,IAAI,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACZC,QAAQ,CAAC,0CAA0C,GAAGD,KAAK,CAAC4C,OAAO,CAAC;IACxE;EACJ,CAAC;;EAED;EACA,MAAMI,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACA,MAAMV,MAAM,GAAG,MAAMd,cAAc,CAAC,OAAO,CAAC;MAC5Cf,QAAQ,CAAC6B,MAAM,CAACX,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACZ0C,OAAO,CAAC1C,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;;EAED;EACA,MAAMiD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAC3D,UAAU,CAAC4D,IAAI,CAAC,CAAC,EAAE;IAExB,IAAI;MACA,MAAMC,cAAc,GAAGjE,oBAAoB,GAAGA,oBAAoB,CAACkE,UAAU,GAAGxD,YAAY;MAE5F,IAAI,CAACuD,cAAc,EAAE;QACjBlD,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACJ;MAEA,MAAMuB,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;QACjC6B,eAAe,EAAEF,cAAc;QAC/BP,OAAO,EAAEtD,UAAU,CAAC4D,IAAI,CAAC;MAC7B,CAAC,CAAC;MAEF3D,aAAa,CAAC,EAAE,CAAC;MACjBI,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA,MAAMgD,iBAAiB,CAAC,CAAC;MACzB,IAAIzD,oBAAoB,EAAE;QACtB,MAAM2D,YAAY,CAAC3D,oBAAoB,CAACkE,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACZC,QAAQ,CAAC,oCAAoC,GAAGD,KAAK,CAAC4C,OAAO,CAAC;IAClE;EACJ,CAAC;;EAED;EACA,MAAMU,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,KAAK;IACjD,IAAI;MACA,MAAMhC,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE;QAChCiC,UAAU,EAAEF,SAAS;QACrBX,OAAO,EAAEY;MACb,CAAC,CAAC;MAEFrD,iBAAiB,CAAC,IAAI,CAAC;MACvBE,cAAc,CAAC,EAAE,CAAC;;MAElB;MACA,IAAInB,oBAAoB,EAAE;QACtB,MAAM2D,YAAY,CAAC3D,oBAAoB,CAACkE,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACZC,QAAQ,CAAC,qCAAqC,GAAGD,KAAK,CAAC4C,OAAO,CAAC;IACnE;EACJ,CAAC;;EAED;EACA,MAAMc,aAAa,GAAG,MAAAA,CAAOH,SAAS,EAAEI,UAAU,GAAG,QAAQ,KAAK;IAC9D,IAAI;MACA,MAAMnC,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACrCiC,UAAU,EAAEF,SAAS;QACrBK,WAAW,EAAED;MACjB,CAAC,CAAC;MAEFpD,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIrB,oBAAoB,EAAE;QACtB,MAAM2D,YAAY,CAAC3D,oBAAoB,CAACkE,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAAC4C,OAAO,CAAC;IACpE;EACJ,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAG,MAAOC,YAAY,IAAK;IAC/C3E,uBAAuB,CAAC2E,YAAY,CAAC;IACrCnE,sBAAsB,CAAC,KAAK,CAAC;IAC7B,MAAMkD,YAAY,CAACiB,YAAY,CAACV,UAAU,CAAC;EAC/C,CAAC;;EAED;EACA,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IAC/B5E,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,WAAW,CAAC,EAAE,CAAC;IACfM,sBAAsB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMqE,iBAAiB,GAAGA,CAACC,CAAC,EAAErB,OAAO,KAAK;IACtCqB,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB3D,kBAAkB,CAAC;MACf4D,CAAC,EAAEF,CAAC,CAACG,OAAO;MACZC,CAAC,EAAEJ,CAAC,CAACK,OAAO;MACZ1B,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAI3B,OAAO,IAAK;IAC9BzC,iBAAiB,CAACyC,OAAO,CAAC4B,EAAE,CAAC;IAC7BnE,cAAc,CAACuC,OAAO,CAACA,OAAO,CAAC;IAC/BrC,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkE,aAAa,GAAGA,CAAA,KAAM;IACxBtE,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAMqE,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAItE,WAAW,CAAC8C,IAAI,CAAC,CAAC,IAAIhD,cAAc,EAAE;MACtC,MAAMoD,WAAW,CAACpD,cAAc,EAAEE,WAAW,CAAC8C,IAAI,CAAC,CAAC,CAAC;IACzD;EACJ,CAAC;;EAED;EACA,MAAMyB,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACnF,CAAC,MAAM,IAAIJ,QAAQ,IAAI,CAAC,EAAE;MACtB,OAAON,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEH,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrG,CAAC,MAAM;MACH,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEE,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEL,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrH;EACJ,CAAC;;EAED;EACA7G,SAAS,CAAC,MAAM;IACZ;IACA,IAAIK,IAAI,IAAIA,IAAI,CAACyF,EAAE,EAAE;MACjB7B,iBAAiB,CAAC,CAAC;MACnBI,mBAAmB,CAAC,CAAC;MACrBC,SAAS,CAAC,CAAC;IACf,CAAC,MAAM;MACHN,OAAO,CAACkD,IAAI,CAAC,6DAA6D,CAAC;MAC3E;MACAjD,iBAAiB,CAAC,CAAC;MACnBI,mBAAmB,CAAC,CAAC;MACrBC,SAAS,CAAC,CAAC;IACf;EACJ,CAAC,EAAE,CAACjE,IAAI,CAAC,CAAC;;EAEV;EACAL,SAAS,CAAC,MAAM;IACZ,MAAMmH,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BnD,iBAAiB,CAAC,CAAC;MACnB,IAAIzD,oBAAoB,EAAE;QACtB2D,YAAY,CAAC3D,oBAAoB,CAACkE,UAAU,CAAC;MACjD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM2C,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAAC3G,oBAAoB,CAAC,CAAC;EAE1B,oBACIV,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtB/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE/F,KAAK,CAACgG,cAAc,IAAI,CAAQ,CAAC,eAChEhI,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CACzC,CAAC,eACP/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE/F,KAAK,CAACiG,gBAAgB,IAAI,CAAQ,CAAC,eAClEjI,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAa,CACxC,CAAC,eACP/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvH,aAAa,CAAC0H,MAAa,CAAC,eAC3DlI,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAmB,CAC9C,CACL,CACJ,CAAC,EAELvG,KAAK,iBACFxB,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAE,EAACvG,KAAY,CAAC,eACtBxB,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAM1G,QAAQ,CAAC,EAAE,CAAE;IAAAiG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC7C,CACR,eAED/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhC/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChC/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,eAAiB,CAAC,eACtB/H,KAAA,CAAAwH,aAAA;IACIC,SAAS,EAAC,sBAAsB;IAChCU,OAAO,EAAE5C,oBAAqB;IAC9B6C,KAAK,EAAC,uBAAuB;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CAAC,eAEN/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BzG,OAAO,IAAId,aAAa,CAAC0H,MAAM,KAAK,CAAC,gBAClClI,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAkB,CAAC,GAC5CvH,aAAa,CAAC0H,MAAM,KAAK,CAAC,gBAC1BlI,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qBAAsB,CAAC,eAC1B/H,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAE5C,oBAAqB;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAE/B,CACP,CAAC,GAENvH,aAAa,CAAC6H,GAAG,CAAC/C,YAAY,iBAC1BtF,KAAA,CAAAwH,aAAA;IACIc,GAAG,EAAEhD,YAAY,CAACV,UAAW;IAC7B6C,SAAS,EAAE,qBAAqB,CAAA/G,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEkE,UAAU,MAAKU,YAAY,CAACV,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC/GuD,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACC,YAAY,CAAE;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhD/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BzC,YAAY,CAACiD,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC/C,CAAC,eACNzI,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BzC,YAAY,CAACiD,WAAW,eACzBvI,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BzC,YAAY,CAACoD,YACZ,CACL,CAAC,eACN1I,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCzC,YAAY,CAACqD,eAAe,IAAI,eAChC,CAAC,eACN3I,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B/H,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B5B,UAAU,CAACb,YAAY,CAACsD,iBAAiB,CACxC,CAAC,EACNtD,YAAY,CAAC2C,gBAAgB,GAAG,CAAC,iBAC9BjI,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBzC,YAAY,CAAC2C,gBACZ,CAET,CACJ,CACJ,CACR,CAEJ,CACJ,CAAC,eAGNjI,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB7G,mBAAmB,gBAChBlB,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAyB,CAAC,eAC9B/H,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAMhH,sBAAsB,CAAC,KAAK,CAAE;IAAAuG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC9D,CAAC,eACN/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrC/H,KAAA,CAAAwH,aAAA;IACIqB,KAAK,EAAEzH,YAAa;IACpB0H,QAAQ,EAAGrD,CAAC,IAAKpE,eAAe,CAACoE,CAAC,CAAC7C,MAAM,CAACiG,KAAK,CAAE;IACjDpB,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvB/H,KAAA,CAAAwH,aAAA;IAAQqB,KAAK,EAAC,EAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAsC,CAAC,EACvD/G,eAAe,CAACqH,GAAG,CAAC9H,IAAI,iBACrBP,KAAA,CAAAwH,aAAA;IAAQc,GAAG,EAAE/H,IAAI,CAACyF,EAAG;IAAC6C,KAAK,EAAEtI,IAAI,CAACyF,EAAG;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCxH,IAAI,CAACwI,GAAG,EAAC,IAAE,EAACxI,IAAI,CAACyI,IAAI,EAAC,GACnB,CACX,CACG,CACP,CACJ,CAAC,GACNtI,oBAAoB,gBACpBV,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvBrH,oBAAoB,CAAC6H,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACvD,CAAC,eACNzI,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BrH,oBAAoB,CAAC6H,WACrB,CAAC,eACNvI,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BrH,oBAAoB,CAACgI,YACrB,CACJ,CACJ,CACJ,CAAC,gBAEN1I,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtB/H,KAAA,CAAAwH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,iEAA4D,CAAC,eAChE/H,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAE5C,oBAAqB;IAAAmC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAE/B,CACP,CACJ,CACR,EAGA,CAACrH,oBAAoB,IAAIQ,mBAAmB,kBACzClB,KAAA,CAAAwH,aAAA,CAAAxH,KAAA,CAAAiJ,QAAA,qBACIjJ,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BnH,QAAQ,CAACyH,GAAG,CAACjE,OAAO,iBACjBpE,KAAA,CAAAwH,aAAA;IACIc,GAAG,EAAElE,OAAO,CAAC4B,EAAG;IAChByB,SAAS,EAAE,WAAWrD,OAAO,CAAC8E,YAAY,EAAG;IAC7CC,aAAa,EAAG1D,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAErB,OAAO,CAAE;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpD/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3BrG,cAAc,KAAK0C,OAAO,CAAC4B,EAAE,gBAC1BhG,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/H,KAAA,CAAAwH,aAAA;IACIqB,KAAK,EAAEjH,WAAY;IACnBkH,QAAQ,EAAGrD,CAAC,IAAK5D,cAAc,CAAC4D,CAAC,CAAC7C,MAAM,CAACiG,KAAK,CAAE;IAChDpB,SAAS,EAAC,eAAe;IACzB2B,SAAS;IAAA1B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACZ,CAAC,eACF/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB/H,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAEjC,WAAY;IAACuB,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAE/C,CAAC,eACT/H,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAElC,aAAc;IAACwB,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAEhD,CACP,CACJ,CAAC,gBAEN/H,KAAA,CAAAwH,aAAA,CAAAxH,KAAA,CAAAiJ,QAAA,qBACIjJ,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB3D,OAAO,CAACA,OAAO,EACfA,OAAO,CAACiF,OAAO,KAAK,GAAG,iBACpBrJ,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,gBAAgB;IAACW,KAAK,EAAE,cAAcjC,UAAU,CAAC/B,OAAO,CAACkF,iBAAiB,CAAC,EAAG;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAEzF,CAET,CAAC,eACN/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB5B,UAAU,CAAC/B,OAAO,CAACmF,UAAU,CAAC,EAC9BnF,OAAO,CAAC8E,YAAY,KAAK,MAAM,iBAC5BlJ,KAAA,CAAAwH,aAAA;IAAMC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B3D,OAAO,CAACoF,EAAE,KAAK,GAAG,GAAG,IAAI,GAAG,GAC3B,CAET,CACP,CAEL,CACJ,CACR,CAAC,eACFxJ,KAAA,CAAAwH,aAAA;IAAKiC,GAAG,EAAEvH,cAAe;IAAAwF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC1B,CAAC,eAGN/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC/H,KAAA,CAAAwH,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC/H,KAAA,CAAAwH,aAAA;IACIqB,KAAK,EAAE/H,UAAW;IAClBgI,QAAQ,EAAGrD,CAAC,IAAK1E,aAAa,CAAC0E,CAAC,CAAC7C,MAAM,CAACiG,KAAK,CAAE;IAC/Ca,WAAW,EAAC,wBAAwB;IACpCjC,SAAS,EAAC,eAAe;IACzBkC,IAAI,EAAC,GAAG;IACRC,SAAS,EAAGnE,CAAC,IAAK;MACd,IAAIA,CAAC,CAAC6C,GAAG,KAAK,OAAO,IAAI,CAAC7C,CAAC,CAACoE,QAAQ,EAAE;QAClCpE,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBjB,WAAW,CAAC,CAAC;MACjB;IACJ,CAAE;IAAAiD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACF/H,KAAA,CAAAwH,aAAA;IACIW,OAAO,EAAE1D,WAAY;IACrBgD,SAAS,EAAC,aAAa;IACvBqC,QAAQ,EAAE,CAAChJ,UAAU,CAAC4D,IAAI,CAAC,CAAC,IAAIpD,OAAQ;IAAAoG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3C,cAEO,CACP,CACJ,CACP,CAEL,CACJ,CAAC,EAGLjG,eAAe,iBACZ9B,KAAA,CAAAwH,aAAA;IACIiC,GAAG,EAAEtH,cAAe;IACpBsF,SAAS,EAAC,cAAc;IACxBsC,KAAK,EAAE;MACHC,IAAI,EAAElI,eAAe,CAAC6D,CAAC;MACvBsE,GAAG,EAAEnI,eAAe,CAAC+D;IACzB,CAAE;IAAA6B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDjG,eAAe,CAACsC,OAAO,CAAC8F,UAAU,KAAK,CAAC,iBACrClK,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAMpC,YAAY,CAACjE,eAAe,CAACsC,OAAO,CAAE;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAEtD,CACX,eACD/H,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACpD,eAAe,CAACsC,OAAO,CAAC4B,EAAE,EAAE,QAAQ,CAAE;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uCAEpE,CAAC,EACRjG,eAAe,CAACsC,OAAO,CAAC+F,kBAAkB,KAAK,CAAC,iBAC7CnK,KAAA,CAAAwH,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAMjD,aAAa,CAACpD,eAAe,CAACsC,OAAO,CAAC4B,EAAE,EAAE,cAAc,CAAE;IAAA0B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wCAE1E,CAEX,CAER,CAAC;AAEd,CAAC;AAED,eAAezH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}