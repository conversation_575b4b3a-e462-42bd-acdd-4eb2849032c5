{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\UsersList.js\";\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport Swal from 'sweetalert2';\nimport UserEditModal from '../components/UserEditModal';\nimport { FaUsers, FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaUserTie, FaGraduationCap, FaChalkboardTeacher, FaUserFriends, FaSpinner, FaPlus, FaSortAlphaDown, FaSortAlphaUp, FaChevronLeft, FaChevronRight, FaAngleDoubleLeft, FaAngleDoubleRight } from 'react-icons/fa';\nconst UsersList = () => {\n  const [users, setUsers] = useState([]);\n  const [filteredUsers, setFilteredUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [editingUser, setEditingUser] = useState(null);\n  const [showEditModal, setShowEditModal] = useState(false);\n\n  // États pour la pagination\n  const [currentPage, setCurrentPage] = useState(1);\n  const [usersPerPage, setUsersPerPage] = useState(12);\n  const [paginatedUsers, setPaginatedUsers] = useState([]);\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n  useEffect(() => {\n    filterAndSortUsers();\n  }, [users, searchTerm, roleFilter, sortOrder]);\n  useEffect(() => {\n    paginateUsers();\n  }, [filteredUsers, currentPage, usersPerPage, paginateUsers]);\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('http://localhost//Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true');\n      const data = await response.json();\n      if (data.success) {\n        setUsers(data.users);\n      } else {\n        setError(data.error || 'Erreur lors du chargement des utilisateurs');\n      }\n    } catch (err) {\n      setError('Erreur de connexion au serveur');\n      console.error('Erreur:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const filterAndSortUsers = () => {\n    let filtered = users.filter(user => {\n      const matchesSearch = user.nom.toLowerCase().includes(searchTerm.toLowerCase()) || user.email.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesRole = roleFilter === '' || user.role_nom === roleFilter;\n      return matchesSearch && matchesRole;\n    });\n\n    // Tri\n    filtered.sort((a, b) => {\n      const comparison = a.nom.localeCompare(b.nom);\n      return sortOrder === 'asc' ? comparison : -comparison;\n    });\n    setFilteredUsers(filtered);\n    // Réinitialiser à la première page quand les filtres changent\n    setCurrentPage(1);\n  };\n\n  // Fonction de pagination avec useCallback pour éviter les re-rendus infinis\n  const paginateUsers = useCallback(() => {\n    const startIndex = (currentPage - 1) * usersPerPage;\n    const endIndex = startIndex + usersPerPage;\n    const paginated = filteredUsers.slice(startIndex, endIndex);\n    setPaginatedUsers(paginated);\n  }, [filteredUsers, currentPage, usersPerPage]);\n\n  // Fonctions de navigation de pagination\n  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\n  const goToPage = page => {\n    if (page >= 1 && page <= totalPages) {\n      setCurrentPage(page);\n    }\n  };\n  const goToFirstPage = () => setCurrentPage(1);\n  const goToLastPage = () => setCurrentPage(totalPages);\n  const goToPreviousPage = () => goToPage(currentPage - 1);\n  const goToNextPage = () => goToPage(currentPage + 1);\n  const handleUsersPerPageChange = newUsersPerPage => {\n    setUsersPerPage(newUsersPerPage);\n    setCurrentPage(1); // Réinitialiser à la première page\n  };\n  const getRoleIcon = role => {\n    switch (role === null || role === void 0 ? void 0 : role.toLowerCase()) {\n      case 'enseignant':\n        return /*#__PURE__*/React.createElement(FaChalkboardTeacher, {\n          style: {\n            color: '#4CAF50'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 33\n          }\n        });\n      case 'etudiant':\n        return /*#__PURE__*/React.createElement(FaGraduationCap, {\n          style: {\n            color: '#2196F3'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 31\n          }\n        });\n      case 'parent':\n        return /*#__PURE__*/React.createElement(FaUserFriends, {\n          style: {\n            color: '#FF9800'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 29\n          }\n        });\n      case 'responsable':\n      case 'admin':\n        return /*#__PURE__*/React.createElement(FaUserTie, {\n          style: {\n            color: '#9C27B0'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 28\n          }\n        });\n      default:\n        return /*#__PURE__*/React.createElement(FaUsers, {\n          style: {\n            color: '#757575'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 23\n          }\n        });\n    }\n  };\n  const getRoleColor = role => {\n    switch (role === null || role === void 0 ? void 0 : role.toLowerCase()) {\n      case 'enseignant':\n        return '#4CAF50';\n      case 'etudiant':\n        return '#2196F3';\n      case 'parent':\n        return '#FF9800';\n      case 'responsable':\n      case 'admin':\n        return '#9C27B0';\n      default:\n        return '#757575';\n    }\n  };\n\n  // Fonction pour ouvrir le modal de modification\n  const handleEdit = async user => {\n    try {\n      // Récupérer les détails complets de l'utilisateur\n      const response = await fetch(`http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php?id=${user.id}`);\n      const data = await response.json();\n      if (data.success) {\n        setEditingUser(data.user);\n        setShowEditModal(true);\n      } else {\n        Swal.fire('Erreur', data.error || 'Impossible de charger les détails de l\\'utilisateur', 'error');\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des détails:', error);\n      Swal.fire('Erreur', 'Erreur de connexion au serveur', 'error');\n    }\n  };\n\n  // Fonction pour gérer la sauvegarde après modification\n  const handleSaveUser = result => {\n    Swal.fire('Succès', result.message || 'Utilisateur modifié avec succès', 'success');\n    fetchUsers(); // Recharger la liste\n  };\n\n  // Fonction pour fermer le modal\n  const handleCloseModal = () => {\n    setShowEditModal(false);\n    setEditingUser(null);\n  };\n\n  // Fonction pour supprimer un utilisateur\n  const handleDelete = async user => {\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      html: `\n        <p>Cette action supprimera définitivement l'utilisateur :</p>\n        <strong>${user.nom} (${user.email})</strong>\n        <p style=\"color: #e53935; margin-top: 10px;\">\n          <strong>⚠️ Cette action est irréversible !</strong>\n        </p>\n      `,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer',\n      cancelButtonText: 'Annuler',\n      reverseButtons: true\n    });\n    if (result.isConfirmed) {\n      try {\n        const response = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php', {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            id: user.id\n          })\n        });\n        const data = await response.json();\n        if (data.success) {\n          Swal.fire('Supprimé!', data.message || 'L\\'utilisateur a été supprimé avec succès', 'success');\n          fetchUsers(); // Recharger la liste\n        } else {\n          Swal.fire('Erreur', data.error || 'Impossible de supprimer l\\'utilisateur', 'error');\n        }\n      } catch (error) {\n        console.error('Erreur lors de la suppression:', error);\n        Swal.fire('Erreur', 'Erreur de connexion au serveur', 'error');\n      }\n    }\n  };\n  const styles = {\n    container: {\n      minHeight: '100vh',\n      backgroundColor: 'var(--antiflash-white)',\n      padding: '20px',\n      marginLeft: '70px'\n    },\n    header: {\n      backgroundColor: 'var(--cerulean)',\n      borderRadius: '15px',\n      padding: '25px',\n      marginBottom: '25px',\n      color: 'white',\n      boxShadow: '0 4px 10px rgba(0,0,0,0.1)'\n    },\n    title: {\n      fontSize: '2rem',\n      fontWeight: 'bold',\n      color: 'white',\n      marginBottom: '20px',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '15px'\n    },\n    controls: {\n      display: 'flex',\n      gap: '15px',\n      flexWrap: 'wrap',\n      alignItems: 'center'\n    },\n    searchBox: {\n      position: 'relative',\n      flex: '1',\n      minWidth: '250px'\n    },\n    searchInput: {\n      width: '100%',\n      padding: '12px 15px 12px 45px',\n      borderRadius: '10px',\n      border: '1px solid var(--cerulean-2)',\n      background: 'white',\n      color: 'var(--text-dark)',\n      fontSize: '14px'\n    },\n    searchIcon: {\n      position: 'absolute',\n      left: '15px',\n      top: '50%',\n      transform: 'translateY(-50%)',\n      color: 'var(--cerulean-2)'\n    },\n    select: {\n      padding: '12px 15px',\n      borderRadius: '10px',\n      border: '1px solid var(--cerulean-2)',\n      background: 'white',\n      color: 'var(--text-dark)',\n      fontSize: '14px'\n    },\n    button: {\n      padding: '12px 20px',\n      borderRadius: '10px',\n      border: 'none',\n      background: 'var(--moonstone)',\n      color: 'white',\n      fontSize: '14px',\n      fontWeight: 'bold',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      transition: 'all 0.3s ease'\n    },\n    sortButton: {\n      padding: '12px',\n      borderRadius: '10px',\n      border: 'none',\n      background: 'var(--cerulean-2)',\n      color: 'white',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease'\n    },\n    statsBar: {\n      display: 'flex',\n      gap: '15px',\n      marginTop: '15px'\n    },\n    statItem: {\n      background: 'white',\n      padding: '10px 15px',\n      borderRadius: '8px',\n      color: 'var(--text-dark)',\n      fontSize: '14px',\n      border: '1px solid var(--cerulean-2)'\n    },\n    usersGrid: {\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',\n      gap: '20px'\n    },\n    userCard: {\n      background: 'white',\n      borderRadius: '15px',\n      padding: '20px',\n      border: '1px solid var(--cerulean-2)',\n      transition: 'all 0.3s ease',\n      position: 'relative'\n    },\n    userHeader: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '15px',\n      marginBottom: '15px'\n    },\n    userAvatar: {\n      width: '50px',\n      height: '50px',\n      borderRadius: '50%',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      fontSize: '1.5rem',\n      color: 'white'\n    },\n    userName: {\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      color: 'var(--text-dark)',\n      marginBottom: '5px'\n    },\n    userEmail: {\n      fontSize: '0.9rem',\n      color: 'gray'\n    },\n    userDetails: {\n      fontSize: '0.9rem',\n      color: '#555',\n      marginBottom: '15px'\n    },\n    userRole: {\n      display: 'inline-flex',\n      alignItems: 'center',\n      gap: '5px',\n      padding: '4px 12px',\n      borderRadius: '20px',\n      fontSize: '0.8rem',\n      fontWeight: 'bold',\n      marginBottom: '15px'\n    },\n    viewButton: {\n      color: '#55a630'\n    },\n    editButton: {\n      background: 'var(--moonstone)',\n      color: 'white'\n    },\n    deleteButton: {\n      background: '#ef233c',\n      color: 'white'\n    },\n    loadingContainer: {\n      textAlign: 'center',\n      padding: '50px',\n      color: 'var(--cerulean)'\n    },\n    errorContainer: {\n      textAlign: 'center',\n      padding: '50px',\n      color: '#e53935'\n    },\n    emptyState: {\n      textAlign: 'center',\n      padding: '50px',\n      color: 'gray'\n    },\n    // Styles de pagination\n    paginationInfo: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      marginTop: '15px',\n      padding: '10px 0',\n      borderTop: '1px solid #e0e0e0'\n    },\n    resultsInfo: {\n      display: 'flex',\n      alignItems: 'center'\n    },\n    resultsText: {\n      fontSize: '14px',\n      color: '#666',\n      fontWeight: '500'\n    },\n    usersPerPageSelector: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '10px'\n    },\n    usersPerPageLabel: {\n      fontSize: '14px',\n      color: '#666',\n      fontWeight: '500'\n    },\n    usersPerPageSelect: {\n      padding: '5px 10px',\n      border: '1px solid #ddd',\n      borderRadius: '4px',\n      fontSize: '14px',\n      backgroundColor: 'white',\n      cursor: 'pointer'\n    },\n    paginationContainer: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      gap: '15px',\n      marginTop: '30px',\n      padding: '20px',\n      backgroundColor: 'white',\n      borderRadius: '10px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n    },\n    paginationControls: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '5px'\n    },\n    paginationButton: {\n      padding: '8px 12px',\n      border: '1px solid #ddd',\n      backgroundColor: 'white',\n      color: '#333',\n      borderRadius: '4px',\n      cursor: 'pointer',\n      fontSize: '14px',\n      fontWeight: '500',\n      transition: 'all 0.3s ease',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minWidth: '40px',\n      height: '40px'\n    },\n    paginationButtonActive: {\n      backgroundColor: 'var(--cerulean)',\n      color: 'white',\n      borderColor: 'var(--cerulean)',\n      fontWeight: 'bold'\n    },\n    paginationButtonDisabled: {\n      backgroundColor: '#f5f5f5',\n      color: '#ccc',\n      cursor: 'not-allowed',\n      borderColor: '#e0e0e0'\n    },\n    pageNumbers: {\n      display: 'flex',\n      gap: '2px',\n      margin: '0 10px'\n    },\n    pageInfo: {\n      fontSize: '14px',\n      color: '#666',\n      fontWeight: '500'\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.container,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.loadingContainer,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(FaSpinner, {\n      style: {\n        fontSize: '3rem',\n        animation: 'spin 1s linear infinite'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 11\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        marginTop: '20px'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 11\n      }\n    }, \"Chargement des utilisateurs...\")));\n  }\n  if (error) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.container,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      style: styles.errorContainer,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      style: {\n        fontSize: '1.2rem'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }\n    }, error)));\n  }\n  const uniqueRoles = [...new Set(users.map(user => user.role_nom).filter(Boolean))];\n  const totalUsers = filteredUsers.length;\n  const roleStats = uniqueRoles.map(role => ({\n    role,\n    count: filteredUsers.filter(user => user.role_nom === role).length\n  }));\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.container,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.header,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    style: styles.title,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaUsers, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 11\n    }\n  }), \" Liste des Utilisateurs\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.controls,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.searchBox,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaSearch, {\n    style: styles.searchIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(\"input\", {\n    style: styles.searchInput,\n    type: \"text\",\n    placeholder: \"Rechercher par nom ou email...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 13\n    }\n  })), /*#__PURE__*/React.createElement(\"select\", {\n    style: styles.select,\n    value: roleFilter,\n    onChange: e => setRoleFilter(e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 13\n    }\n  }, \"Tous les r\\xF4les\"), uniqueRoles.map(role => /*#__PURE__*/React.createElement(\"option\", {\n    key: role,\n    value: role,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 15\n    }\n  }, role))), /*#__PURE__*/React.createElement(\"button\", {\n    style: styles.sortButton,\n    onClick: () => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc'),\n    title: `Trier par nom ${sortOrder === 'asc' ? 'décroissant' : 'croissant'}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 11\n    }\n  }, sortOrder === 'asc' ? /*#__PURE__*/React.createElement(FaSortAlphaDown, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 36\n    }\n  }) : /*#__PURE__*/React.createElement(FaSortAlphaUp, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 58\n    }\n  })), /*#__PURE__*/React.createElement(Link, {\n    to: \"/registers\",\n    style: styles.button,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaPlus, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 13\n    }\n  }), \" Nouvel Utilisateur\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statsBar,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.statItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 11\n    }\n  }, \"Total: \", totalUsers, \" utilisateur\", totalUsers > 1 ? 's' : ''), roleStats.map(stat => /*#__PURE__*/React.createElement(\"div\", {\n    key: stat.role,\n    style: styles.statItem,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 13\n    }\n  }, stat.role, \": \", stat.count))), filteredUsers.length > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.paginationInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.resultsInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.resultsText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 15\n    }\n  }, \"Affichage de \", (currentPage - 1) * usersPerPage + 1, \" \\xE0 \", Math.min(currentPage * usersPerPage, filteredUsers.length), \" sur \", filteredUsers.length, \" utilisateurs\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.usersPerPageSelector,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    style: styles.usersPerPageLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 15\n    }\n  }, \"Afficher :\"), /*#__PURE__*/React.createElement(\"select\", {\n    style: styles.usersPerPageSelect,\n    value: usersPerPage,\n    onChange: e => handleUsersPerPageChange(parseInt(e.target.value)),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: 6,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 598,\n      columnNumber: 17\n    }\n  }, \"6 par page\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: 12,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 17\n    }\n  }, \"12 par page\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 17\n    }\n  }, \"24 par page\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: 48,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 17\n    }\n  }, \"48 par page\"))))), filteredUsers.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.emptyState,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaUsers, {\n    style: {\n      fontSize: '3rem',\n      marginBottom: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 11\n    }\n  }, \"Aucun utilisateur trouv\\xE9\")) : paginatedUsers.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.emptyState,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FaUsers, {\n    style: {\n      fontSize: '3rem',\n      marginBottom: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 11\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 11\n    }\n  }, \"Aucun utilisateur sur cette page\")) : /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.usersGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 9\n    }\n  }, paginatedUsers.map(user => /*#__PURE__*/React.createElement(\"div\", {\n    key: user.id,\n    style: styles.userCard,\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-5px)';\n      e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0)';\n      e.currentTarget.style.boxShadow = 'none';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userHeader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 634,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userAvatar,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 635,\n      columnNumber: 17\n    }\n  }, getRoleIcon(user.role_nom)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userName,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 19\n    }\n  }, user.nom), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userEmail,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 640,\n      columnNumber: 19\n    }\n  }, user.email))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      ...styles.userRole,\n      background: `${getRoleColor(user.role_nom)}20`,\n      border: `1px solid ${getRoleColor(user.role_nom)}50`,\n      color: getRoleColor(user.role_nom)\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 15\n    }\n  }, getRoleIcon(user.role_nom), user.role_nom), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userDetails,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 17\n    }\n  }, \"Membre depuis: \", user.created_at_formatted), user.parent_telephone && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 659,\n      columnNumber: 19\n    }\n  }, \"T\\xE9l\\xE9phone: \", user.parent_telephone), user.groupe_nom && /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 19\n    }\n  }, \"Groupe: \", user.groupe_nom)), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.userActions,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 666,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    to: `/profil/${user.id}`,\n    style: {\n      ...styles.actionButton,\n      ...styles.viewButton\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaEye, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 19\n    }\n  }), \" Voir\"), /*#__PURE__*/React.createElement(\"button\", {\n    style: {\n      ...styles.actionButton,\n      ...styles.editButton\n    },\n    onClick: () => handleEdit(user),\n    title: \"Modifier l'utilisateur\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 673,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaEdit, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 19\n    }\n  }), \" Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    style: {\n      ...styles.actionButton,\n      ...styles.deleteButton\n    },\n    onClick: () => handleDelete(user),\n    title: \"Supprimer l'utilisateur\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaTrash, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 19\n    }\n  }), \" Supprimer\"))))), filteredUsers.length > usersPerPage && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.paginationContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.paginationControls,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    style: {\n      ...styles.paginationButton,\n      ...(currentPage === 1 ? styles.paginationButtonDisabled : {})\n    },\n    onClick: goToFirstPage,\n    disabled: currentPage === 1,\n    title: \"Premi\\xE8re page\",\n    onMouseEnter: e => {\n      if (currentPage !== 1) {\n        e.target.style.backgroundColor = 'var(--cerulean)';\n        e.target.style.color = 'white';\n      }\n    },\n    onMouseLeave: e => {\n      if (currentPage !== 1) {\n        e.target.style.backgroundColor = 'white';\n        e.target.style.color = '#333';\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 698,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaAngleDoubleLeft, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 719,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    style: {\n      ...styles.paginationButton,\n      ...(currentPage === 1 ? styles.paginationButtonDisabled : {})\n    },\n    onClick: goToPreviousPage,\n    disabled: currentPage === 1,\n    title: \"Page pr\\xE9c\\xE9dente\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 723,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaChevronLeft, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 732,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.pageNumbers,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 13\n    }\n  }, Array.from({\n    length: Math.min(5, totalPages)\n  }, (_, index) => {\n    let pageNumber;\n    if (totalPages <= 5) {\n      pageNumber = index + 1;\n    } else if (currentPage <= 3) {\n      pageNumber = index + 1;\n    } else if (currentPage >= totalPages - 2) {\n      pageNumber = totalPages - 4 + index;\n    } else {\n      pageNumber = currentPage - 2 + index;\n    }\n    return /*#__PURE__*/React.createElement(\"button\", {\n      key: pageNumber,\n      style: {\n        ...styles.paginationButton,\n        ...(currentPage === pageNumber ? styles.paginationButtonActive : {})\n      },\n      onClick: () => goToPage(pageNumber),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 750,\n        columnNumber: 19\n      }\n    }, pageNumber);\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    style: {\n      ...styles.paginationButton,\n      ...(currentPage === totalPages ? styles.paginationButtonDisabled : {})\n    },\n    onClick: goToNextPage,\n    disabled: currentPage === totalPages,\n    title: \"Page suivante\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 765,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaChevronRight, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 15\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    style: {\n      ...styles.paginationButton,\n      ...(currentPage === totalPages ? styles.paginationButtonDisabled : {})\n    },\n    onClick: goToLastPage,\n    disabled: currentPage === totalPages,\n    title: \"Derni\\xE8re page\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FaAngleDoubleRight, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 787,\n      columnNumber: 15\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.pageInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 792,\n      columnNumber: 11\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages)), /*#__PURE__*/React.createElement(UserEditModal, {\n    user: editingUser,\n    isOpen: showEditModal,\n    onClose: handleCloseModal,\n    onSave: handleSaveUser,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 799,\n      columnNumber: 7\n    }\n  }));\n};\nexport default UsersList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Link", "<PERSON><PERSON>", "UserEditModal", "FaUsers", "FaSearch", "FaFilter", "FaEye", "FaEdit", "FaTrash", "FaUserTie", "FaGraduationCap", "FaChalkboardTeacher", "FaUserFriends", "FaSpinner", "FaPlus", "FaSortAlphaDown", "FaSortAlphaUp", "FaChevronLeft", "FaChevronRight", "FaAngleDoubleLeft", "FaAngleDoubleRight", "UsersList", "users", "setUsers", "filteredUsers", "setFilteredUsers", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "sortOrder", "setSortOrder", "editingUser", "setEditingUser", "showEditModal", "setShowEditModal", "currentPage", "setCurrentPage", "usersPerPage", "setUsersPerPage", "paginatedUsers", "setPaginatedUsers", "fetchUsers", "filterAndSortUsers", "paginateUsers", "response", "fetch", "data", "json", "success", "err", "console", "filtered", "filter", "user", "matchesSearch", "nom", "toLowerCase", "includes", "email", "matchesRole", "role_nom", "sort", "a", "b", "comparison", "localeCompare", "startIndex", "endIndex", "paginated", "slice", "totalPages", "Math", "ceil", "length", "goToPage", "page", "goToFirstPage", "goToLastPage", "goToPreviousPage", "goToNextPage", "handleUsersPerPageChange", "newUsersPerPage", "getRoleIcon", "role", "createElement", "style", "color", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getRoleColor", "handleEdit", "id", "fire", "handleSaveUser", "result", "message", "handleCloseModal", "handleDelete", "title", "html", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "reverseButtons", "isConfirmed", "method", "headers", "body", "JSON", "stringify", "styles", "container", "minHeight", "backgroundColor", "padding", "marginLeft", "header", "borderRadius", "marginBottom", "boxShadow", "fontSize", "fontWeight", "display", "alignItems", "gap", "controls", "flexWrap", "searchBox", "position", "flex", "min<PERSON><PERSON><PERSON>", "searchInput", "width", "border", "background", "searchIcon", "left", "top", "transform", "select", "button", "cursor", "transition", "sortButton", "statsBar", "marginTop", "statItem", "usersGrid", "gridTemplateColumns", "userCard", "userHeader", "userAvatar", "height", "justifyContent", "userName", "userEmail", "userDetails", "userRole", "viewButton", "edit<PERSON><PERSON><PERSON>", "deleteButton", "loadingContainer", "textAlign", "<PERSON><PERSON><PERSON><PERSON>", "emptyState", "paginationInfo", "borderTop", "resultsInfo", "resultsText", "usersPerPageSelector", "usersPerPageLabel", "usersPerPageSelect", "paginationContainer", "flexDirection", "paginationControls", "paginationButton", "paginationButtonActive", "borderColor", "paginationButtonDisabled", "pageNumbers", "margin", "pageInfo", "animation", "uniqueRoles", "Set", "map", "Boolean", "totalUsers", "roleStats", "count", "type", "placeholder", "value", "onChange", "e", "target", "key", "onClick", "to", "stat", "min", "parseInt", "onMouseEnter", "currentTarget", "onMouseLeave", "userInfo", "created_at_formatted", "parent_telephone", "groupe_nom", "userActions", "actionButton", "disabled", "Array", "from", "_", "index", "pageNumber", "isOpen", "onClose", "onSave"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/UsersList.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Link } from 'react-router-dom';\nimport Swal from 'sweetalert2';\nimport UserEditModal from '../components/UserEditModal';\nimport {\n  FaUsers,\n  FaSearch,\n  FaFilter,\n  FaEye,\n  FaEdit,\n  FaTrash,\n  FaUserTie,\n  FaGraduationCap,\n  FaChalkboardTeacher,\n  FaUserFriends,\n  FaSpinner,\n  FaPlus,\n  FaSortAlphaDown,\n  FaSortAlphaUp,\n  FaChevronLeft,\n  FaChevronRight,\n  FaAngleDoubleLeft,\n  FaAngleDoubleRight\n} from 'react-icons/fa';\n\nconst UsersList = () => {\n  const [users, setUsers] = useState([]);\n  const [filteredUsers, setFilteredUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [roleFilter, setRoleFilter] = useState('');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [editingUser, setEditingUser] = useState(null);\n  const [showEditModal, setShowEditModal] = useState(false);\n\n  // États pour la pagination\n  const [currentPage, setCurrentPage] = useState(1);\n  const [usersPerPage, setUsersPerPage] = useState(12);\n  const [paginatedUsers, setPaginatedUsers] = useState([]);\n\n  useEffect(() => {\n    fetchUsers();\n  }, []);\n\n  useEffect(() => {\n    filterAndSortUsers();\n  }, [users, searchTerm, roleFilter, sortOrder]);\n\n  useEffect(() => {\n    paginateUsers();\n  }, [filteredUsers, currentPage, usersPerPage, paginateUsers]);\n\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('http://localhost//Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true');\n      const data = await response.json();\n      \n      if (data.success) {\n        setUsers(data.users);\n      } else {\n        setError(data.error || 'Erreur lors du chargement des utilisateurs');\n      }\n    } catch (err) {\n      setError('Erreur de connexion au serveur');\n      console.error('Erreur:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterAndSortUsers = () => {\n    let filtered = users.filter(user => {\n      const matchesSearch = user.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           user.email.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesRole = roleFilter === '' || user.role_nom === roleFilter;\n      return matchesSearch && matchesRole;\n    });\n\n    // Tri\n    filtered.sort((a, b) => {\n      const comparison = a.nom.localeCompare(b.nom);\n      return sortOrder === 'asc' ? comparison : -comparison;\n    });\n\n    setFilteredUsers(filtered);\n    // Réinitialiser à la première page quand les filtres changent\n    setCurrentPage(1);\n  };\n\n  // Fonction de pagination avec useCallback pour éviter les re-rendus infinis\n  const paginateUsers = useCallback(() => {\n    const startIndex = (currentPage - 1) * usersPerPage;\n    const endIndex = startIndex + usersPerPage;\n    const paginated = filteredUsers.slice(startIndex, endIndex);\n    setPaginatedUsers(paginated);\n  }, [filteredUsers, currentPage, usersPerPage]);\n\n  // Fonctions de navigation de pagination\n  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);\n\n  const goToPage = (page) => {\n    if (page >= 1 && page <= totalPages) {\n      setCurrentPage(page);\n    }\n  };\n\n  const goToFirstPage = () => setCurrentPage(1);\n  const goToLastPage = () => setCurrentPage(totalPages);\n  const goToPreviousPage = () => goToPage(currentPage - 1);\n  const goToNextPage = () => goToPage(currentPage + 1);\n\n  const handleUsersPerPageChange = (newUsersPerPage) => {\n    setUsersPerPage(newUsersPerPage);\n    setCurrentPage(1); // Réinitialiser à la première page\n  };\n\n  const getRoleIcon = (role) => {\n    switch (role?.toLowerCase()) {\n      case 'enseignant': return <FaChalkboardTeacher style={{ color: '#4CAF50' }} />;\n      case 'etudiant': return <FaGraduationCap style={{ color: '#2196F3' }} />;\n      case 'parent': return <FaUserFriends style={{ color: '#FF9800' }} />;\n      case 'responsable':\n      case 'admin': return <FaUserTie style={{ color: '#9C27B0' }} />;\n      default: return <FaUsers style={{ color: '#757575' }} />;\n    }\n  };\n\n  const getRoleColor = (role) => {\n    switch (role?.toLowerCase()) {\n      case 'enseignant': return '#4CAF50';\n      case 'etudiant': return '#2196F3';\n      case 'parent': return '#FF9800';\n      case 'responsable':\n      case 'admin': return '#9C27B0';\n      default: return '#757575';\n    }\n  };\n\n  // Fonction pour ouvrir le modal de modification\n  const handleEdit = async (user) => {\n    try {\n      // Récupérer les détails complets de l'utilisateur\n      const response = await fetch(`http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php?id=${user.id}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setEditingUser(data.user);\n        setShowEditModal(true);\n      } else {\n        Swal.fire('Erreur', data.error || 'Impossible de charger les détails de l\\'utilisateur', 'error');\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des détails:', error);\n      Swal.fire('Erreur', 'Erreur de connexion au serveur', 'error');\n    }\n  };\n\n  // Fonction pour gérer la sauvegarde après modification\n  const handleSaveUser = (result) => {\n    Swal.fire('Succès', result.message || 'Utilisateur modifié avec succès', 'success');\n    fetchUsers(); // Recharger la liste\n  };\n\n  // Fonction pour fermer le modal\n  const handleCloseModal = () => {\n    setShowEditModal(false);\n    setEditingUser(null);\n  };\n\n  // Fonction pour supprimer un utilisateur\n  const handleDelete = async (user) => {\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      html: `\n        <p>Cette action supprimera définitivement l'utilisateur :</p>\n        <strong>${user.nom} (${user.email})</strong>\n        <p style=\"color: #e53935; margin-top: 10px;\">\n          <strong>⚠️ Cette action est irréversible !</strong>\n        </p>\n      `,\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer',\n      cancelButtonText: 'Annuler',\n      reverseButtons: true\n    });\n\n    if (result.isConfirmed) {\n      try {\n        const response = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php', {\n          method: 'DELETE',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({ id: user.id })\n        });\n\n        const data = await response.json();\n\n        if (data.success) {\n          Swal.fire('Supprimé!', data.message || 'L\\'utilisateur a été supprimé avec succès', 'success');\n          fetchUsers(); // Recharger la liste\n        } else {\n          Swal.fire('Erreur', data.error || 'Impossible de supprimer l\\'utilisateur', 'error');\n        }\n      } catch (error) {\n        console.error('Erreur lors de la suppression:', error);\n        Swal.fire('Erreur', 'Erreur de connexion au serveur', 'error');\n      }\n    }\n  };\n\n  const styles = {\n  container: {\n    minHeight: '100vh',\n    backgroundColor: 'var(--antiflash-white)',\n    padding: '20px',\n    marginLeft: '70px'\n  },\n  header: {\n    backgroundColor: 'var(--cerulean)',\n    borderRadius: '15px',\n    padding: '25px',\n    marginBottom: '25px',\n    color: 'white',\n    boxShadow: '0 4px 10px rgba(0,0,0,0.1)'\n  },\n  title: {\n    fontSize: '2rem',\n    fontWeight: 'bold',\n    color: 'white',\n    marginBottom: '20px',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '15px'\n  },\n  controls: {\n    display: 'flex',\n    gap: '15px',\n    flexWrap: 'wrap',\n    alignItems: 'center'\n  },\n  searchBox: {\n    position: 'relative',\n    flex: '1',\n    minWidth: '250px'\n  },\n  searchInput: {\n    width: '100%',\n    padding: '12px 15px 12px 45px',\n    borderRadius: '10px',\n    border: '1px solid var(--cerulean-2)',\n    background: 'white',\n    color: 'var(--text-dark)',\n    fontSize: '14px'\n  },\n  searchIcon: {\n    position: 'absolute',\n    left: '15px',\n    top: '50%',\n    transform: 'translateY(-50%)',\n    color: 'var(--cerulean-2)'\n  },\n  select: {\n    padding: '12px 15px',\n    borderRadius: '10px',\n    border: '1px solid var(--cerulean-2)',\n    background: 'white',\n    color: 'var(--text-dark)',\n    fontSize: '14px'\n  },\n  button: {\n    padding: '12px 20px',\n    borderRadius: '10px',\n    border: 'none',\n    background: 'var(--moonstone)',\n    color: 'white',\n    fontSize: '14px',\n    fontWeight: 'bold',\n    cursor: 'pointer',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    transition: 'all 0.3s ease'\n  },\n  sortButton: {\n    padding: '12px',\n    borderRadius: '10px',\n    border: 'none',\n    background: 'var(--cerulean-2)',\n    color: 'white',\n    cursor: 'pointer',\n    transition: 'all 0.3s ease'\n  },\n  statsBar: {\n    display: 'flex',\n    gap: '15px',\n    marginTop: '15px'\n  },\n  statItem: {\n    background: 'white',\n    padding: '10px 15px',\n    borderRadius: '8px',\n    color: 'var(--text-dark)',\n    fontSize: '14px',\n    border: '1px solid var(--cerulean-2)'\n  },\n  usersGrid: {\n    display: 'grid',\n    gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',\n    gap: '20px'\n  },\n  userCard: {\n    background: 'white',\n    borderRadius: '15px',\n    padding: '20px',\n    border: '1px solid var(--cerulean-2)',\n    transition: 'all 0.3s ease',\n    position: 'relative'\n  },\n  userHeader: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: '15px',\n    marginBottom: '15px'\n  },\n  userAvatar: {\n    width: '50px',\n    height: '50px',\n    borderRadius: '50%',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontSize: '1.5rem',\n    color: 'white'\n  },\n  userName: {\n    fontSize: '1.2rem',\n    fontWeight: 'bold',\n    color: 'var(--text-dark)',\n    marginBottom: '5px'\n  },\n  userEmail: {\n    fontSize: '0.9rem',\n    color: 'gray'\n  },\n  userDetails: {\n    fontSize: '0.9rem',\n    color: '#555',\n    marginBottom: '15px'\n  },\n  userRole: {\n    display: 'inline-flex',\n    alignItems: 'center',\n    gap: '5px',\n    padding: '4px 12px',\n    borderRadius: '20px',\n    fontSize: '0.8rem',\n    fontWeight: 'bold',\n    marginBottom: '15px'\n  },\n  viewButton: {\n   \n    color: '#55a630'\n  },\n  editButton: {\n    background: 'var(--moonstone)',\n    color: 'white'\n  },\n  deleteButton: {\n    background: '#ef233c',\n    color: 'white'\n  },\n  loadingContainer: {\n    textAlign: 'center',\n    padding: '50px',\n    color: 'var(--cerulean)'\n  },\n  errorContainer: {\n    textAlign: 'center',\n    padding: '50px',\n    color: '#e53935'\n  },\n  emptyState: {\n    textAlign: 'center',\n    padding: '50px',\n    color: 'gray'\n  },\n\n  // Styles de pagination\n  paginationInfo: {\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    marginTop: '15px',\n    padding: '10px 0',\n    borderTop: '1px solid #e0e0e0'\n  },\n\n  resultsInfo: {\n    display: 'flex',\n    alignItems: 'center'\n  },\n\n  resultsText: {\n    fontSize: '14px',\n    color: '#666',\n    fontWeight: '500'\n  },\n\n  usersPerPageSelector: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: '10px'\n  },\n\n  usersPerPageLabel: {\n    fontSize: '14px',\n    color: '#666',\n    fontWeight: '500'\n  },\n\n  usersPerPageSelect: {\n    padding: '5px 10px',\n    border: '1px solid #ddd',\n    borderRadius: '4px',\n    fontSize: '14px',\n    backgroundColor: 'white',\n    cursor: 'pointer'\n  },\n\n  paginationContainer: {\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    gap: '15px',\n    marginTop: '30px',\n    padding: '20px',\n    backgroundColor: 'white',\n    borderRadius: '10px',\n    boxShadow: '0 2px 10px rgba(0,0,0,0.1)'\n  },\n\n  paginationControls: {\n    display: 'flex',\n    alignItems: 'center',\n    gap: '5px'\n  },\n\n  paginationButton: {\n    padding: '8px 12px',\n    border: '1px solid #ddd',\n    backgroundColor: 'white',\n    color: '#333',\n    borderRadius: '4px',\n    cursor: 'pointer',\n    fontSize: '14px',\n    fontWeight: '500',\n    transition: 'all 0.3s ease',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    minWidth: '40px',\n    height: '40px'\n  },\n\n  paginationButtonActive: {\n    backgroundColor: 'var(--cerulean)',\n    color: 'white',\n    borderColor: 'var(--cerulean)',\n    fontWeight: 'bold'\n  },\n\n  paginationButtonDisabled: {\n    backgroundColor: '#f5f5f5',\n    color: '#ccc',\n    cursor: 'not-allowed',\n    borderColor: '#e0e0e0'\n  },\n\n  pageNumbers: {\n    display: 'flex',\n    gap: '2px',\n    margin: '0 10px'\n  },\n\n  pageInfo: {\n    fontSize: '14px',\n    color: '#666',\n    fontWeight: '500'\n  }\n}\n\n  if (loading) {\n    return (\n      <div style={styles.container}>\n        <div style={styles.loadingContainer}>\n          <FaSpinner style={{ fontSize: '3rem', animation: 'spin 1s linear infinite' }} />\n          <p style={{ marginTop: '20px' }}>Chargement des utilisateurs...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={styles.container}>\n        <div style={styles.errorContainer}>\n          <p style={{ fontSize: '1.2rem' }}>{error}</p>\n        </div>\n      </div>\n    );\n  }\n\n  const uniqueRoles = [...new Set(users.map(user => user.role_nom).filter(Boolean))];\n  const totalUsers = filteredUsers.length;\n  const roleStats = uniqueRoles.map(role => ({\n    role,\n    count: filteredUsers.filter(user => user.role_nom === role).length\n  }));\n\n  return (\n    <div style={styles.container}>\n      {/* En-tête avec contrôles */}\n      <div style={styles.header}>\n        <h1 style={styles.title}>\n          <FaUsers /> Liste des Utilisateurs\n        </h1>\n        \n        <div style={styles.controls}>\n          <div style={styles.searchBox}>\n            <FaSearch style={styles.searchIcon} />\n            <input\n              style={styles.searchInput}\n              type=\"text\"\n              placeholder=\"Rechercher par nom ou email...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n            />\n          </div>\n          \n          <select\n            style={styles.select}\n            value={roleFilter}\n            onChange={(e) => setRoleFilter(e.target.value)}\n          >\n            <option value=\"\">Tous les rôles</option>\n            {uniqueRoles.map(role => (\n              <option key={role} value={role}>{role}</option>\n            ))}\n          </select>\n          \n          <button\n            style={styles.sortButton}\n            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}\n            title={`Trier par nom ${sortOrder === 'asc' ? 'décroissant' : 'croissant'}`}\n          >\n            {sortOrder === 'asc' ? <FaSortAlphaDown /> : <FaSortAlphaUp />}\n          </button>\n          \n          <Link to=\"/registers\" style={styles.button}>\n            <FaPlus /> Nouvel Utilisateur\n          </Link>\n        </div>\n\n        {/* Statistiques */}\n        <div style={styles.statsBar}>\n          <div style={styles.statItem}>\n            Total: {totalUsers} utilisateur{totalUsers > 1 ? 's' : ''}\n          </div>\n          {roleStats.map(stat => (\n            <div key={stat.role} style={styles.statItem}>\n              {stat.role}: {stat.count}\n            </div>\n          ))}\n        </div>\n\n        {/* Informations de pagination */}\n        {filteredUsers.length > 0 && (\n          <div style={styles.paginationInfo}>\n            <div style={styles.resultsInfo}>\n              <span style={styles.resultsText}>\n                Affichage de {((currentPage - 1) * usersPerPage) + 1} à {Math.min(currentPage * usersPerPage, filteredUsers.length)} sur {filteredUsers.length} utilisateurs\n              </span>\n            </div>\n\n            <div style={styles.usersPerPageSelector}>\n              <label style={styles.usersPerPageLabel}>Afficher :</label>\n              <select\n                style={styles.usersPerPageSelect}\n                value={usersPerPage}\n                onChange={(e) => handleUsersPerPageChange(parseInt(e.target.value))}\n              >\n                <option value={6}>6 par page</option>\n                <option value={12}>12 par page</option>\n                <option value={24}>24 par page</option>\n                <option value={48}>48 par page</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Liste des utilisateurs */}\n      {filteredUsers.length === 0 ? (\n        <div style={styles.emptyState}>\n          <FaUsers style={{ fontSize: '3rem', marginBottom: '20px' }} />\n          <p>Aucun utilisateur trouvé</p>\n        </div>\n      ) : paginatedUsers.length === 0 ? (\n        <div style={styles.emptyState}>\n          <FaUsers style={{ fontSize: '3rem', marginBottom: '20px' }} />\n          <p>Aucun utilisateur sur cette page</p>\n        </div>\n      ) : (\n        <div style={styles.usersGrid}>\n          {paginatedUsers.map(user => (\n            <div \n              key={user.id} \n              style={styles.userCard}\n              onMouseEnter={(e) => {\n                e.currentTarget.style.transform = 'translateY(-5px)';\n                e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';\n              }}\n              onMouseLeave={(e) => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              }}\n            >\n              <div style={styles.userHeader}>\n                <div style={styles.userAvatar}>\n                  {getRoleIcon(user.role_nom)}\n                </div>\n                <div style={styles.userInfo}>\n                  <div style={styles.userName}>{user.nom}</div>\n                  <div style={styles.userEmail}>{user.email}</div>\n                </div>\n              </div>\n              \n              <div \n                style={{\n                  ...styles.userRole,\n                  background: `${getRoleColor(user.role_nom)}20`,\n                  border: `1px solid ${getRoleColor(user.role_nom)}50`,\n                  color: getRoleColor(user.role_nom)\n                }}\n              >\n                {getRoleIcon(user.role_nom)}\n                {user.role_nom}\n              </div>\n              \n              <div style={styles.userDetails}>\n                <div>Membre depuis: {user.created_at_formatted}</div>\n                {user.parent_telephone && (\n                  <div>Téléphone: {user.parent_telephone}</div>\n                )}\n                {user.groupe_nom && (\n                  <div>Groupe: {user.groupe_nom}</div>\n                )}\n              </div>\n              \n              <div style={styles.userActions}>\n                <Link\n                  to={`/profil/${user.id}`}\n                  style={{ ...styles.actionButton, ...styles.viewButton }}\n                >\n                  <FaEye /> Voir\n                </Link>\n                <button\n                  style={{ ...styles.actionButton, ...styles.editButton }}\n                  onClick={() => handleEdit(user)}\n                  title=\"Modifier l'utilisateur\"\n                >\n                  <FaEdit /> Modifier\n                </button>\n                <button\n                  style={{ ...styles.actionButton, ...styles.deleteButton }}\n                  onClick={() => handleDelete(user)}\n                  title=\"Supprimer l'utilisateur\"\n                >\n                  <FaTrash /> Supprimer\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Contrôles de pagination */}\n      {filteredUsers.length > usersPerPage && (\n        <div style={styles.paginationContainer}>\n          <div style={styles.paginationControls}>\n            {/* Bouton première page */}\n            <button\n              style={{\n                ...styles.paginationButton,\n                ...(currentPage === 1 ? styles.paginationButtonDisabled : {})\n              }}\n              onClick={goToFirstPage}\n              disabled={currentPage === 1}\n              title=\"Première page\"\n              onMouseEnter={(e) => {\n                if (currentPage !== 1) {\n                  e.target.style.backgroundColor = 'var(--cerulean)';\n                  e.target.style.color = 'white';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (currentPage !== 1) {\n                  e.target.style.backgroundColor = 'white';\n                  e.target.style.color = '#333';\n                }\n              }}\n            >\n              <FaAngleDoubleLeft />\n            </button>\n\n            {/* Bouton page précédente */}\n            <button\n              style={{\n                ...styles.paginationButton,\n                ...(currentPage === 1 ? styles.paginationButtonDisabled : {})\n              }}\n              onClick={goToPreviousPage}\n              disabled={currentPage === 1}\n              title=\"Page précédente\"\n            >\n              <FaChevronLeft />\n            </button>\n\n            {/* Numéros de pages */}\n            <div style={styles.pageNumbers}>\n              {Array.from({ length: Math.min(5, totalPages) }, (_, index) => {\n                let pageNumber;\n                if (totalPages <= 5) {\n                  pageNumber = index + 1;\n                } else if (currentPage <= 3) {\n                  pageNumber = index + 1;\n                } else if (currentPage >= totalPages - 2) {\n                  pageNumber = totalPages - 4 + index;\n                } else {\n                  pageNumber = currentPage - 2 + index;\n                }\n\n                return (\n                  <button\n                    key={pageNumber}\n                    style={{\n                      ...styles.paginationButton,\n                      ...(currentPage === pageNumber ? styles.paginationButtonActive : {})\n                    }}\n                    onClick={() => goToPage(pageNumber)}\n                  >\n                    {pageNumber}\n                  </button>\n                );\n              })}\n            </div>\n\n            {/* Bouton page suivante */}\n            <button\n              style={{\n                ...styles.paginationButton,\n                ...(currentPage === totalPages ? styles.paginationButtonDisabled : {})\n              }}\n              onClick={goToNextPage}\n              disabled={currentPage === totalPages}\n              title=\"Page suivante\"\n            >\n              <FaChevronRight />\n            </button>\n\n            {/* Bouton dernière page */}\n            <button\n              style={{\n                ...styles.paginationButton,\n                ...(currentPage === totalPages ? styles.paginationButtonDisabled : {})\n              }}\n              onClick={goToLastPage}\n              disabled={currentPage === totalPages}\n              title=\"Dernière page\"\n            >\n              <FaAngleDoubleRight />\n            </button>\n          </div>\n\n          {/* Informations de page */}\n          <div style={styles.pageInfo}>\n            Page {currentPage} sur {totalPages}\n          </div>\n        </div>\n      )}\n\n      {/* Modal de modification */}\n      <UserEditModal\n        user={editingUser}\n        isOpen={showEditModal}\n        onClose={handleCloseModal}\n        onSave={handleSaveUser}\n      />\n    </div>\n  );\n};\n\nexport default UsersList;\n\n\n\n\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,OAAO,EACPC,QAAQ,EACRC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,eAAe,EACfC,mBAAmB,EACnBC,aAAa,EACbC,SAAS,EACTC,MAAM,EACNC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,iBAAiB,EACjBC,kBAAkB,QACb,gBAAgB;AAEvB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiC,UAAU,EAAEC,aAAa,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdgD,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAENhD,SAAS,CAAC,MAAM;IACdiD,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACzB,KAAK,EAAEQ,UAAU,EAAEE,UAAU,EAAEE,SAAS,CAAC,CAAC;EAE9CpC,SAAS,CAAC,MAAM;IACdkD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACxB,aAAa,EAAEgB,WAAW,EAAEE,YAAY,EAAEM,aAAa,CAAC,CAAC;EAE7D,MAAMF,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFnB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMsB,QAAQ,GAAG,MAAMC,KAAK,CAAC,qFAAqF,CAAC;MACnH,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB9B,QAAQ,CAAC4B,IAAI,CAAC7B,KAAK,CAAC;MACtB,CAAC,MAAM;QACLO,QAAQ,CAACsB,IAAI,CAACvB,KAAK,IAAI,4CAA4C,CAAC;MACtE;IACF,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZzB,QAAQ,CAAC,gCAAgC,CAAC;MAC1C0B,OAAO,CAAC3B,KAAK,CAAC,SAAS,EAAE0B,GAAG,CAAC;IAC/B,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIS,QAAQ,GAAGlC,KAAK,CAACmC,MAAM,CAACC,IAAI,IAAI;MAClC,MAAMC,aAAa,GAAGD,IAAI,CAACE,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,IAC1DH,IAAI,CAACK,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC;MAChF,MAAMG,WAAW,GAAGhC,UAAU,KAAK,EAAE,IAAI0B,IAAI,CAACO,QAAQ,KAAKjC,UAAU;MACrE,OAAO2B,aAAa,IAAIK,WAAW;IACrC,CAAC,CAAC;;IAEF;IACAR,QAAQ,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,MAAMC,UAAU,GAAGF,CAAC,CAACP,GAAG,CAACU,aAAa,CAACF,CAAC,CAACR,GAAG,CAAC;MAC7C,OAAO1B,SAAS,KAAK,KAAK,GAAGmC,UAAU,GAAG,CAACA,UAAU;IACvD,CAAC,CAAC;IAEF5C,gBAAgB,CAAC+B,QAAQ,CAAC;IAC1B;IACAf,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;;EAED;EACA,MAAMO,aAAa,GAAGjD,WAAW,CAAC,MAAM;IACtC,MAAMwE,UAAU,GAAG,CAAC/B,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,MAAM8B,QAAQ,GAAGD,UAAU,GAAG7B,YAAY;IAC1C,MAAM+B,SAAS,GAAGjD,aAAa,CAACkD,KAAK,CAACH,UAAU,EAAEC,QAAQ,CAAC;IAC3D3B,iBAAiB,CAAC4B,SAAS,CAAC;EAC9B,CAAC,EAAE,CAACjD,aAAa,EAAEgB,WAAW,EAAEE,YAAY,CAAC,CAAC;;EAE9C;EACA,MAAMiC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACrD,aAAa,CAACsD,MAAM,GAAGpC,YAAY,CAAC;EAEjE,MAAMqC,QAAQ,GAAIC,IAAI,IAAK;IACzB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAIL,UAAU,EAAE;MACnClC,cAAc,CAACuC,IAAI,CAAC;IACtB;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAAA,KAAMxC,cAAc,CAAC,CAAC,CAAC;EAC7C,MAAMyC,YAAY,GAAGA,CAAA,KAAMzC,cAAc,CAACkC,UAAU,CAAC;EACrD,MAAMQ,gBAAgB,GAAGA,CAAA,KAAMJ,QAAQ,CAACvC,WAAW,GAAG,CAAC,CAAC;EACxD,MAAM4C,YAAY,GAAGA,CAAA,KAAML,QAAQ,CAACvC,WAAW,GAAG,CAAC,CAAC;EAEpD,MAAM6C,wBAAwB,GAAIC,eAAe,IAAK;IACpD3C,eAAe,CAAC2C,eAAe,CAAC;IAChC7C,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAM8C,WAAW,GAAIC,IAAI,IAAK;IAC5B,QAAQA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE3B,WAAW,CAAC,CAAC;MACzB,KAAK,YAAY;QAAE,oBAAOjE,KAAA,CAAA6F,aAAA,CAAC9E,mBAAmB;UAAC+E,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;MAC9E,KAAK,UAAU;QAAE,oBAAOrG,KAAA,CAAA6F,aAAA,CAAC/E,eAAe;UAACgF,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;MACxE,KAAK,QAAQ;QAAE,oBAAOrG,KAAA,CAAA6F,aAAA,CAAC7E,aAAa;UAAC8E,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;MACpE,KAAK,aAAa;MAClB,KAAK,OAAO;QAAE,oBAAOrG,KAAA,CAAA6F,aAAA,CAAChF,SAAS;UAACiF,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;MAC/D;QAAS,oBAAOrG,KAAA,CAAA6F,aAAA,CAACtF,OAAO;UAACuF,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAAE,CAAC;IAC1D;EACF,CAAC;EAED,MAAMC,YAAY,GAAIV,IAAI,IAAK;IAC7B,QAAQA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE3B,WAAW,CAAC,CAAC;MACzB,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,aAAa;MAClB,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;;EAED;EACA,MAAMsC,UAAU,GAAG,MAAOzC,IAAI,IAAK;IACjC,IAAI;MACF;MACA,MAAMT,QAAQ,GAAG,MAAMC,KAAK,CAAC,iFAAiFQ,IAAI,CAAC0C,EAAE,EAAE,CAAC;MACxH,MAAMjD,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBhB,cAAc,CAACc,IAAI,CAACO,IAAI,CAAC;QACzBnB,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC,MAAM;QACLtC,IAAI,CAACoG,IAAI,CAAC,QAAQ,EAAElD,IAAI,CAACvB,KAAK,IAAI,qDAAqD,EAAE,OAAO,CAAC;MACnG;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D3B,IAAI,CAACoG,IAAI,CAAC,QAAQ,EAAE,gCAAgC,EAAE,OAAO,CAAC;IAChE;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIC,MAAM,IAAK;IACjCtG,IAAI,CAACoG,IAAI,CAAC,QAAQ,EAAEE,MAAM,CAACC,OAAO,IAAI,iCAAiC,EAAE,SAAS,CAAC;IACnF1D,UAAU,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC;;EAED;EACA,MAAM2D,gBAAgB,GAAGA,CAAA,KAAM;IAC7BlE,gBAAgB,CAAC,KAAK,CAAC;IACvBF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMqE,YAAY,GAAG,MAAOhD,IAAI,IAAK;IACnC,MAAM6C,MAAM,GAAG,MAAMtG,IAAI,CAACoG,IAAI,CAAC;MAC7BM,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE;AACZ;AACA,kBAAkBlD,IAAI,CAACE,GAAG,KAAKF,IAAI,CAACK,KAAK;AACzC;AACA;AACA;AACA,OAAO;MACD8C,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,gBAAgB;MACnCC,gBAAgB,EAAE,SAAS;MAC3BC,cAAc,EAAE;IAClB,CAAC,CAAC;IAEF,IAAIZ,MAAM,CAACa,WAAW,EAAE;MACtB,IAAI;QACF,MAAMnE,QAAQ,GAAG,MAAMC,KAAK,CAAC,4EAA4E,EAAE;UACzGmE,MAAM,EAAE,QAAQ;UAChBC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAErB,EAAE,EAAE1C,IAAI,CAAC0C;UAAG,CAAC;QACtC,CAAC,CAAC;QAEF,MAAMjD,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,EAAE;UAChBpD,IAAI,CAACoG,IAAI,CAAC,WAAW,EAAElD,IAAI,CAACqD,OAAO,IAAI,2CAA2C,EAAE,SAAS,CAAC;UAC9F1D,UAAU,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UACL7C,IAAI,CAACoG,IAAI,CAAC,QAAQ,EAAElD,IAAI,CAACvB,KAAK,IAAI,wCAAwC,EAAE,OAAO,CAAC;QACtF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd2B,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD3B,IAAI,CAACoG,IAAI,CAAC,QAAQ,EAAE,gCAAgC,EAAE,OAAO,CAAC;MAChE;IACF;EACF,CAAC;EAED,MAAMqB,MAAM,GAAG;IACfC,SAAS,EAAE;MACTC,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE,wBAAwB;MACzCC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNH,eAAe,EAAE,iBAAiB;MAClCI,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfI,YAAY,EAAE,MAAM;MACpBvC,KAAK,EAAE,OAAO;MACdwC,SAAS,EAAE;IACb,CAAC;IACDxB,KAAK,EAAE;MACLyB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClB1C,KAAK,EAAE,OAAO;MACduC,YAAY,EAAE,MAAM;MACpBI,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IACDC,QAAQ,EAAE;MACRH,OAAO,EAAE,MAAM;MACfE,GAAG,EAAE,MAAM;MACXE,QAAQ,EAAE,MAAM;MAChBH,UAAU,EAAE;IACd,CAAC;IACDI,SAAS,EAAE;MACTC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE;IACZ,CAAC;IACDC,WAAW,EAAE;MACXC,KAAK,EAAE,MAAM;MACblB,OAAO,EAAE,qBAAqB;MAC9BG,YAAY,EAAE,MAAM;MACpBgB,MAAM,EAAE,6BAA6B;MACrCC,UAAU,EAAE,OAAO;MACnBvD,KAAK,EAAE,kBAAkB;MACzByC,QAAQ,EAAE;IACZ,CAAC;IACDe,UAAU,EAAE;MACVP,QAAQ,EAAE,UAAU;MACpBQ,IAAI,EAAE,MAAM;MACZC,GAAG,EAAE,KAAK;MACVC,SAAS,EAAE,kBAAkB;MAC7B3D,KAAK,EAAE;IACT,CAAC;IACD4D,MAAM,EAAE;MACNzB,OAAO,EAAE,WAAW;MACpBG,YAAY,EAAE,MAAM;MACpBgB,MAAM,EAAE,6BAA6B;MACrCC,UAAU,EAAE,OAAO;MACnBvD,KAAK,EAAE,kBAAkB;MACzByC,QAAQ,EAAE;IACZ,CAAC;IACDoB,MAAM,EAAE;MACN1B,OAAO,EAAE,WAAW;MACpBG,YAAY,EAAE,MAAM;MACpBgB,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,kBAAkB;MAC9BvD,KAAK,EAAE,OAAO;MACdyC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,MAAM;MAClBoB,MAAM,EAAE,SAAS;MACjBnB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,KAAK;MACVkB,UAAU,EAAE;IACd,CAAC;IACDC,UAAU,EAAE;MACV7B,OAAO,EAAE,MAAM;MACfG,YAAY,EAAE,MAAM;MACpBgB,MAAM,EAAE,MAAM;MACdC,UAAU,EAAE,mBAAmB;MAC/BvD,KAAK,EAAE,OAAO;MACd8D,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;IACDE,QAAQ,EAAE;MACRtB,OAAO,EAAE,MAAM;MACfE,GAAG,EAAE,MAAM;MACXqB,SAAS,EAAE;IACb,CAAC;IACDC,QAAQ,EAAE;MACRZ,UAAU,EAAE,OAAO;MACnBpB,OAAO,EAAE,WAAW;MACpBG,YAAY,EAAE,KAAK;MACnBtC,KAAK,EAAE,kBAAkB;MACzByC,QAAQ,EAAE,MAAM;MAChBa,MAAM,EAAE;IACV,CAAC;IACDc,SAAS,EAAE;MACTzB,OAAO,EAAE,MAAM;MACf0B,mBAAmB,EAAE,uCAAuC;MAC5DxB,GAAG,EAAE;IACP,CAAC;IACDyB,QAAQ,EAAE;MACRf,UAAU,EAAE,OAAO;MACnBjB,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfmB,MAAM,EAAE,6BAA6B;MACrCS,UAAU,EAAE,eAAe;MAC3Bd,QAAQ,EAAE;IACZ,CAAC;IACDsB,UAAU,EAAE;MACV5B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,MAAM;MACXN,YAAY,EAAE;IAChB,CAAC;IACDiC,UAAU,EAAE;MACVnB,KAAK,EAAE,MAAM;MACboB,MAAM,EAAE,MAAM;MACdnC,YAAY,EAAE,KAAK;MACnBK,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpB8B,cAAc,EAAE,QAAQ;MACxBjC,QAAQ,EAAE,QAAQ;MAClBzC,KAAK,EAAE;IACT,CAAC;IACD2E,QAAQ,EAAE;MACRlC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClB1C,KAAK,EAAE,kBAAkB;MACzBuC,YAAY,EAAE;IAChB,CAAC;IACDqC,SAAS,EAAE;MACTnC,QAAQ,EAAE,QAAQ;MAClBzC,KAAK,EAAE;IACT,CAAC;IACD6E,WAAW,EAAE;MACXpC,QAAQ,EAAE,QAAQ;MAClBzC,KAAK,EAAE,MAAM;MACbuC,YAAY,EAAE;IAChB,CAAC;IACDuC,QAAQ,EAAE;MACRnC,OAAO,EAAE,aAAa;MACtBC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,KAAK;MACVV,OAAO,EAAE,UAAU;MACnBG,YAAY,EAAE,MAAM;MACpBG,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,MAAM;MAClBH,YAAY,EAAE;IAChB,CAAC;IACDwC,UAAU,EAAE;MAEV/E,KAAK,EAAE;IACT,CAAC;IACDgF,UAAU,EAAE;MACVzB,UAAU,EAAE,kBAAkB;MAC9BvD,KAAK,EAAE;IACT,CAAC;IACDiF,YAAY,EAAE;MACZ1B,UAAU,EAAE,SAAS;MACrBvD,KAAK,EAAE;IACT,CAAC;IACDkF,gBAAgB,EAAE;MAChBC,SAAS,EAAE,QAAQ;MACnBhD,OAAO,EAAE,MAAM;MACfnC,KAAK,EAAE;IACT,CAAC;IACDoF,cAAc,EAAE;MACdD,SAAS,EAAE,QAAQ;MACnBhD,OAAO,EAAE,MAAM;MACfnC,KAAK,EAAE;IACT,CAAC;IACDqF,UAAU,EAAE;MACVF,SAAS,EAAE,QAAQ;MACnBhD,OAAO,EAAE,MAAM;MACfnC,KAAK,EAAE;IACT,CAAC;IAED;IACAsF,cAAc,EAAE;MACd3C,OAAO,EAAE,MAAM;MACf+B,cAAc,EAAE,eAAe;MAC/B9B,UAAU,EAAE,QAAQ;MACpBsB,SAAS,EAAE,MAAM;MACjB/B,OAAO,EAAE,QAAQ;MACjBoD,SAAS,EAAE;IACb,CAAC;IAEDC,WAAW,EAAE;MACX7C,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAC;IAED6C,WAAW,EAAE;MACXhD,QAAQ,EAAE,MAAM;MAChBzC,KAAK,EAAE,MAAM;MACb0C,UAAU,EAAE;IACd,CAAC;IAEDgD,oBAAoB,EAAE;MACpB/C,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IAED8C,iBAAiB,EAAE;MACjBlD,QAAQ,EAAE,MAAM;MAChBzC,KAAK,EAAE,MAAM;MACb0C,UAAU,EAAE;IACd,CAAC;IAEDkD,kBAAkB,EAAE;MAClBzD,OAAO,EAAE,UAAU;MACnBmB,MAAM,EAAE,gBAAgB;MACxBhB,YAAY,EAAE,KAAK;MACnBG,QAAQ,EAAE,MAAM;MAChBP,eAAe,EAAE,OAAO;MACxB4B,MAAM,EAAE;IACV,CAAC;IAED+B,mBAAmB,EAAE;MACnBlD,OAAO,EAAE,MAAM;MACfmD,aAAa,EAAE,QAAQ;MACvBlD,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE,MAAM;MACXqB,SAAS,EAAE,MAAM;MACjB/B,OAAO,EAAE,MAAM;MACfD,eAAe,EAAE,OAAO;MACxBI,YAAY,EAAE,MAAM;MACpBE,SAAS,EAAE;IACb,CAAC;IAEDuD,kBAAkB,EAAE;MAClBpD,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,GAAG,EAAE;IACP,CAAC;IAEDmD,gBAAgB,EAAE;MAChB7D,OAAO,EAAE,UAAU;MACnBmB,MAAM,EAAE,gBAAgB;MACxBpB,eAAe,EAAE,OAAO;MACxBlC,KAAK,EAAE,MAAM;MACbsC,YAAY,EAAE,KAAK;MACnBwB,MAAM,EAAE,SAAS;MACjBrB,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,KAAK;MACjBqB,UAAU,EAAE,eAAe;MAC3BpB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpB8B,cAAc,EAAE,QAAQ;MACxBvB,QAAQ,EAAE,MAAM;MAChBsB,MAAM,EAAE;IACV,CAAC;IAEDwB,sBAAsB,EAAE;MACtB/D,eAAe,EAAE,iBAAiB;MAClClC,KAAK,EAAE,OAAO;MACdkG,WAAW,EAAE,iBAAiB;MAC9BxD,UAAU,EAAE;IACd,CAAC;IAEDyD,wBAAwB,EAAE;MACxBjE,eAAe,EAAE,SAAS;MAC1BlC,KAAK,EAAE,MAAM;MACb8D,MAAM,EAAE,aAAa;MACrBoC,WAAW,EAAE;IACf,CAAC;IAEDE,WAAW,EAAE;MACXzD,OAAO,EAAE,MAAM;MACfE,GAAG,EAAE,KAAK;MACVwD,MAAM,EAAE;IACV,CAAC;IAEDC,QAAQ,EAAE;MACR7D,QAAQ,EAAE,MAAM;MAChBzC,KAAK,EAAE,MAAM;MACb0C,UAAU,EAAE;IACd;EACF,CAAC;EAEC,IAAI3G,OAAO,EAAE;IACX,oBACE9B,KAAA,CAAA6F,aAAA;MAAKC,KAAK,EAAEgC,MAAM,CAACC,SAAU;MAAA/B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3BrG,KAAA,CAAA6F,aAAA;MAAKC,KAAK,EAAEgC,MAAM,CAACmD,gBAAiB;MAAAjF,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAClCrG,KAAA,CAAA6F,aAAA,CAAC5E,SAAS;MAAC6E,KAAK,EAAE;QAAE0C,QAAQ,EAAE,MAAM;QAAE8D,SAAS,EAAE;MAA0B,CAAE;MAAAtG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAE,CAAC,eAChFrG,KAAA,CAAA6F,aAAA;MAAGC,KAAK,EAAE;QAAEmE,SAAS,EAAE;MAAO,CAAE;MAAAjE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,gCAAiC,CAC/D,CACF,CAAC;EAEV;EAEA,IAAIrE,KAAK,EAAE;IACT,oBACEhC,KAAA,CAAA6F,aAAA;MAAKC,KAAK,EAAEgC,MAAM,CAACC,SAAU;MAAA/B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC3BrG,KAAA,CAAA6F,aAAA;MAAKC,KAAK,EAAEgC,MAAM,CAACqD,cAAe;MAAAnF,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAChCrG,KAAA,CAAA6F,aAAA;MAAGC,KAAK,EAAE;QAAE0C,QAAQ,EAAE;MAAS,CAAE;MAAAxC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAErE,KAAS,CACzC,CACF,CAAC;EAEV;EAEA,MAAMuK,WAAW,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC9K,KAAK,CAAC+K,GAAG,CAAC3I,IAAI,IAAIA,IAAI,CAACO,QAAQ,CAAC,CAACR,MAAM,CAAC6I,OAAO,CAAC,CAAC,CAAC;EAClF,MAAMC,UAAU,GAAG/K,aAAa,CAACsD,MAAM;EACvC,MAAM0H,SAAS,GAAGL,WAAW,CAACE,GAAG,CAAC7G,IAAI,KAAK;IACzCA,IAAI;IACJiH,KAAK,EAAEjL,aAAa,CAACiC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACO,QAAQ,KAAKuB,IAAI,CAAC,CAACV;EAC9D,CAAC,CAAC,CAAC;EAEH,oBACElF,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACC,SAAU;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACM,MAAO;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBrG,KAAA,CAAA6F,aAAA;IAAIC,KAAK,EAAEgC,MAAM,CAACf,KAAM;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBrG,KAAA,CAAA6F,aAAA,CAACtF,OAAO;IAAAyF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,2BACT,CAAC,eAELrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACe,QAAS;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACiB,SAAU;IAAA/C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BrG,KAAA,CAAA6F,aAAA,CAACrF,QAAQ;IAACsF,KAAK,EAAEgC,MAAM,CAACyB,UAAW;IAAAvD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACtCrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAEgC,MAAM,CAACqB,WAAY;IAC1B2D,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,gCAAgC;IAC5CC,KAAK,EAAE9K,UAAW;IAClB+K,QAAQ,EAAGC,CAAC,IAAK/K,aAAa,CAAC+K,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAChD,CACE,CAAC,eAENrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAEgC,MAAM,CAAC6B,MAAO;IACrBqD,KAAK,EAAE5K,UAAW;IAClB6K,QAAQ,EAAGC,CAAC,IAAK7K,aAAa,CAAC6K,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/CrG,KAAA,CAAA6F,aAAA;IAAQmH,KAAK,EAAC,EAAE;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mBAAsB,CAAC,EACvCkG,WAAW,CAACE,GAAG,CAAC7G,IAAI,iBACnB5F,KAAA,CAAA6F,aAAA;IAAQuH,GAAG,EAAExH,IAAK;IAACoH,KAAK,EAAEpH,IAAK;IAAAI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAET,IAAa,CAC/C,CACK,CAAC,eAET5F,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAEgC,MAAM,CAACiC,UAAW;IACzBsD,OAAO,EAAEA,CAAA,KAAM9K,YAAY,CAACD,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,CAAE;IAClEyE,KAAK,EAAE,iBAAiBzE,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,WAAW,EAAG;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAE3E/D,SAAS,KAAK,KAAK,gBAAGtC,KAAA,CAAA6F,aAAA,CAAC1E,eAAe;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,gBAAGrG,KAAA,CAAA6F,aAAA,CAACzE,aAAa;IAAA4E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACvD,CAAC,eAETrG,KAAA,CAAA6F,aAAA,CAACzF,IAAI;IAACkN,EAAE,EAAC,YAAY;IAACxH,KAAK,EAAEgC,MAAM,CAAC8B,MAAO;IAAA5D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzCrG,KAAA,CAAA6F,aAAA,CAAC3E,MAAM;IAAA8E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,uBACN,CACH,CAAC,eAGNrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACkC,QAAS;IAAAhE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACoC,QAAS;IAAAlE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SACpB,EAACsG,UAAU,EAAC,cAAY,EAACA,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EACpD,CAAC,EACLC,SAAS,CAACH,GAAG,CAACc,IAAI,iBACjBvN,KAAA,CAAA6F,aAAA;IAAKuH,GAAG,EAAEG,IAAI,CAAC3H,IAAK;IAACE,KAAK,EAAEgC,MAAM,CAACoC,QAAS;IAAAlE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzCkH,IAAI,CAAC3H,IAAI,EAAC,IAAE,EAAC2H,IAAI,CAACV,KAChB,CACN,CACE,CAAC,EAGLjL,aAAa,CAACsD,MAAM,GAAG,CAAC,iBACvBlF,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACuD,cAAe;IAAArF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACyD,WAAY;IAAAvF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BrG,KAAA,CAAA6F,aAAA;IAAMC,KAAK,EAAEgC,MAAM,CAAC0D,WAAY;IAAAxF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAClB,EAAE,CAACzD,WAAW,GAAG,CAAC,IAAIE,YAAY,GAAI,CAAC,EAAC,QAAG,EAACkC,IAAI,CAACwI,GAAG,CAAC5K,WAAW,GAAGE,YAAY,EAAElB,aAAa,CAACsD,MAAM,CAAC,EAAC,OAAK,EAACtD,aAAa,CAACsD,MAAM,EAAC,eAC3I,CACH,CAAC,eAENlF,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAAC2D,oBAAqB;IAAAzF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCrG,KAAA,CAAA6F,aAAA;IAAOC,KAAK,EAAEgC,MAAM,CAAC4D,iBAAkB;IAAA1F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAAiB,CAAC,eAC1DrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAEgC,MAAM,CAAC6D,kBAAmB;IACjCqB,KAAK,EAAElK,YAAa;IACpBmK,QAAQ,EAAGC,CAAC,IAAKzH,wBAAwB,CAACgI,QAAQ,CAACP,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC,CAAE;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpErG,KAAA,CAAA6F,aAAA;IAAQmH,KAAK,EAAE,CAAE;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAAkB,CAAC,eACrCrG,KAAA,CAAA6F,aAAA;IAAQmH,KAAK,EAAE,EAAG;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAmB,CAAC,eACvCrG,KAAA,CAAA6F,aAAA;IAAQmH,KAAK,EAAE,EAAG;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAmB,CAAC,eACvCrG,KAAA,CAAA6F,aAAA;IAAQmH,KAAK,EAAE,EAAG;IAAAhH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAmB,CAChC,CACL,CACF,CAEJ,CAAC,EAGLzE,aAAa,CAACsD,MAAM,KAAK,CAAC,gBACzBlF,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACsD,UAAW;IAAApF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BrG,KAAA,CAAA6F,aAAA,CAACtF,OAAO;IAACuF,KAAK,EAAE;MAAE0C,QAAQ,EAAE,MAAM;MAAEF,YAAY,EAAE;IAAO,CAAE;IAAAtC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC9DrG,KAAA,CAAA6F,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,6BAA2B,CAC3B,CAAC,GACJrD,cAAc,CAACkC,MAAM,KAAK,CAAC,gBAC7BlF,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACsD,UAAW;IAAApF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BrG,KAAA,CAAA6F,aAAA,CAACtF,OAAO;IAACuF,KAAK,EAAE;MAAE0C,QAAQ,EAAE,MAAM;MAAEF,YAAY,EAAE;IAAO,CAAE;IAAAtC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC9DrG,KAAA,CAAA6F,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,kCAAmC,CACnC,CAAC,gBAENrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACqC,SAAU;IAAAnE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BrD,cAAc,CAACyJ,GAAG,CAAC3I,IAAI,iBACtB9D,KAAA,CAAA6F,aAAA;IACEuH,GAAG,EAAEtJ,IAAI,CAAC0C,EAAG;IACbV,KAAK,EAAEgC,MAAM,CAACuC,QAAS;IACvBqD,YAAY,EAAGR,CAAC,IAAK;MACnBA,CAAC,CAACS,aAAa,CAAC7H,KAAK,CAAC4D,SAAS,GAAG,kBAAkB;MACpDwD,CAAC,CAACS,aAAa,CAAC7H,KAAK,CAACyC,SAAS,GAAG,6BAA6B;IACjE,CAAE;IACFqF,YAAY,EAAGV,CAAC,IAAK;MACnBA,CAAC,CAACS,aAAa,CAAC7H,KAAK,CAAC4D,SAAS,GAAG,eAAe;MACjDwD,CAAC,CAACS,aAAa,CAAC7H,KAAK,CAACyC,SAAS,GAAG,MAAM;IAC1C,CAAE;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACwC,UAAW;IAAAtE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACyC,UAAW;IAAAvE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3BV,WAAW,CAAC7B,IAAI,CAACO,QAAQ,CACvB,CAAC,eACNrE,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAAC+F,QAAS;IAAA7H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAAC4C,QAAS;IAAA1E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvC,IAAI,CAACE,GAAS,CAAC,eAC7ChE,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAAC6C,SAAU;IAAA3E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvC,IAAI,CAACK,KAAW,CAC5C,CACF,CAAC,eAENnE,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MACL,GAAGgC,MAAM,CAAC+C,QAAQ;MAClBvB,UAAU,EAAE,GAAGhD,YAAY,CAACxC,IAAI,CAACO,QAAQ,CAAC,IAAI;MAC9CgF,MAAM,EAAE,aAAa/C,YAAY,CAACxC,IAAI,CAACO,QAAQ,CAAC,IAAI;MACpD0B,KAAK,EAAEO,YAAY,CAACxC,IAAI,CAACO,QAAQ;IACnC,CAAE;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDV,WAAW,CAAC7B,IAAI,CAACO,QAAQ,CAAC,EAC1BP,IAAI,CAACO,QACH,CAAC,eAENrE,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAAC8C,WAAY;IAAA5E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BrG,KAAA,CAAA6F,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,iBAAe,EAACvC,IAAI,CAACgK,oBAA0B,CAAC,EACpDhK,IAAI,CAACiK,gBAAgB,iBACpB/N,KAAA,CAAA6F,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,mBAAW,EAACvC,IAAI,CAACiK,gBAAsB,CAC7C,EACAjK,IAAI,CAACkK,UAAU,iBACdhO,KAAA,CAAA6F,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK,UAAQ,EAACvC,IAAI,CAACkK,UAAgB,CAElC,CAAC,eAENhO,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACmG,WAAY;IAAAjI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BrG,KAAA,CAAA6F,aAAA,CAACzF,IAAI;IACHkN,EAAE,EAAE,WAAWxJ,IAAI,CAAC0C,EAAE,EAAG;IACzBV,KAAK,EAAE;MAAE,GAAGgC,MAAM,CAACoG,YAAY;MAAE,GAAGpG,MAAM,CAACgD;IAAW,CAAE;IAAA9E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExDrG,KAAA,CAAA6F,aAAA,CAACnF,KAAK;IAAAsF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,SACL,CAAC,eACPrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MAAE,GAAGgC,MAAM,CAACoG,YAAY;MAAE,GAAGpG,MAAM,CAACiD;IAAW,CAAE;IACxDsC,OAAO,EAAEA,CAAA,KAAM9G,UAAU,CAACzC,IAAI,CAAE;IAChCiD,KAAK,EAAC,wBAAwB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE9BrG,KAAA,CAAA6F,aAAA,CAAClF,MAAM;IAAAqF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,aACJ,CAAC,eACTrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MAAE,GAAGgC,MAAM,CAACoG,YAAY;MAAE,GAAGpG,MAAM,CAACkD;IAAa,CAAE;IAC1DqC,OAAO,EAAEA,CAAA,KAAMvG,YAAY,CAAChD,IAAI,CAAE;IAClCiD,KAAK,EAAC,yBAAyB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE/BrG,KAAA,CAAA6F,aAAA,CAACjF,OAAO;IAAAoF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,cACL,CACL,CACF,CACN,CACE,CACN,EAGAzE,aAAa,CAACsD,MAAM,GAAGpC,YAAY,iBAClC9C,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAAC8D,mBAAoB;IAAA5F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrCrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACgE,kBAAmB;IAAA9F,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEpCrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MACL,GAAGgC,MAAM,CAACiE,gBAAgB;MAC1B,IAAInJ,WAAW,KAAK,CAAC,GAAGkF,MAAM,CAACoE,wBAAwB,GAAG,CAAC,CAAC;IAC9D,CAAE;IACFmB,OAAO,EAAEhI,aAAc;IACvB8I,QAAQ,EAAEvL,WAAW,KAAK,CAAE;IAC5BmE,KAAK,EAAC,kBAAe;IACrB2G,YAAY,EAAGR,CAAC,IAAK;MACnB,IAAItK,WAAW,KAAK,CAAC,EAAE;QACrBsK,CAAC,CAACC,MAAM,CAACrH,KAAK,CAACmC,eAAe,GAAG,iBAAiB;QAClDiF,CAAC,CAACC,MAAM,CAACrH,KAAK,CAACC,KAAK,GAAG,OAAO;MAChC;IACF,CAAE;IACF6H,YAAY,EAAGV,CAAC,IAAK;MACnB,IAAItK,WAAW,KAAK,CAAC,EAAE;QACrBsK,CAAC,CAACC,MAAM,CAACrH,KAAK,CAACmC,eAAe,GAAG,OAAO;QACxCiF,CAAC,CAACC,MAAM,CAACrH,KAAK,CAACC,KAAK,GAAG,MAAM;MAC/B;IACF,CAAE;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFrG,KAAA,CAAA6F,aAAA,CAACtE,iBAAiB;IAAAyE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACd,CAAC,eAGTrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MACL,GAAGgC,MAAM,CAACiE,gBAAgB;MAC1B,IAAInJ,WAAW,KAAK,CAAC,GAAGkF,MAAM,CAACoE,wBAAwB,GAAG,CAAC,CAAC;IAC9D,CAAE;IACFmB,OAAO,EAAE9H,gBAAiB;IAC1B4I,QAAQ,EAAEvL,WAAW,KAAK,CAAE;IAC5BmE,KAAK,EAAC,uBAAiB;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBrG,KAAA,CAAA6F,aAAA,CAACxE,aAAa;IAAA2E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACV,CAAC,eAGTrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACqE,WAAY;IAAAnG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5B+H,KAAK,CAACC,IAAI,CAAC;IAAEnJ,MAAM,EAAEF,IAAI,CAACwI,GAAG,CAAC,CAAC,EAAEzI,UAAU;EAAE,CAAC,EAAE,CAACuJ,CAAC,EAAEC,KAAK,KAAK;IAC7D,IAAIC,UAAU;IACd,IAAIzJ,UAAU,IAAI,CAAC,EAAE;MACnByJ,UAAU,GAAGD,KAAK,GAAG,CAAC;IACxB,CAAC,MAAM,IAAI3L,WAAW,IAAI,CAAC,EAAE;MAC3B4L,UAAU,GAAGD,KAAK,GAAG,CAAC;IACxB,CAAC,MAAM,IAAI3L,WAAW,IAAImC,UAAU,GAAG,CAAC,EAAE;MACxCyJ,UAAU,GAAGzJ,UAAU,GAAG,CAAC,GAAGwJ,KAAK;IACrC,CAAC,MAAM;MACLC,UAAU,GAAG5L,WAAW,GAAG,CAAC,GAAG2L,KAAK;IACtC;IAEA,oBACEvO,KAAA,CAAA6F,aAAA;MACEuH,GAAG,EAAEoB,UAAW;MAChB1I,KAAK,EAAE;QACL,GAAGgC,MAAM,CAACiE,gBAAgB;QAC1B,IAAInJ,WAAW,KAAK4L,UAAU,GAAG1G,MAAM,CAACkE,sBAAsB,GAAG,CAAC,CAAC;MACrE,CAAE;MACFqB,OAAO,EAAEA,CAAA,KAAMlI,QAAQ,CAACqJ,UAAU,CAAE;MAAAxI,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEnCmI,UACK,CAAC;EAEb,CAAC,CACE,CAAC,eAGNxO,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MACL,GAAGgC,MAAM,CAACiE,gBAAgB;MAC1B,IAAInJ,WAAW,KAAKmC,UAAU,GAAG+C,MAAM,CAACoE,wBAAwB,GAAG,CAAC,CAAC;IACvE,CAAE;IACFmB,OAAO,EAAE7H,YAAa;IACtB2I,QAAQ,EAAEvL,WAAW,KAAKmC,UAAW;IACrCgC,KAAK,EAAC,eAAe;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAErBrG,KAAA,CAAA6F,aAAA,CAACvE,cAAc;IAAA0E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACX,CAAC,eAGTrG,KAAA,CAAA6F,aAAA;IACEC,KAAK,EAAE;MACL,GAAGgC,MAAM,CAACiE,gBAAgB;MAC1B,IAAInJ,WAAW,KAAKmC,UAAU,GAAG+C,MAAM,CAACoE,wBAAwB,GAAG,CAAC,CAAC;IACvE,CAAE;IACFmB,OAAO,EAAE/H,YAAa;IACtB6I,QAAQ,EAAEvL,WAAW,KAAKmC,UAAW;IACrCgC,KAAK,EAAC,kBAAe;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAErBrG,KAAA,CAAA6F,aAAA,CAACrE,kBAAkB;IAAAwE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACf,CACL,CAAC,eAGNrG,KAAA,CAAA6F,aAAA;IAAKC,KAAK,EAAEgC,MAAM,CAACuE,QAAS;IAAArG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OACtB,EAACzD,WAAW,EAAC,OAAK,EAACmC,UACrB,CACF,CACN,eAGD/E,KAAA,CAAA6F,aAAA,CAACvF,aAAa;IACZwD,IAAI,EAAEtB,WAAY;IAClBiM,MAAM,EAAE/L,aAAc;IACtBgM,OAAO,EAAE7H,gBAAiB;IAC1B8H,MAAM,EAAEjI,cAAe;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACxB,CACE,CAAC;AAEV,CAAC;AAED,eAAe5E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}