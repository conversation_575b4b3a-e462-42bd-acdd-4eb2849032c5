import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const CoursCRUD = () => {
    const { user } = useContext(AuthContext);
    const [cours, setCours] = useState([]);
    const [matieres, setMatieres] = useState([]);
    const [classes, setClasses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingCours, setEditingCours] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [matiereFilter, setMatiereFilter] = useState('all');
    const [classeFilter, setClasseFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        titre: '',
        description: '',
        fichier_pdf: null,
        date_publication: '',
        matiere_id: '',
        classe_id: ''
    });

    // Vérifier si l'utilisateur est Admin ou Enseignant
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';
    const isTeacher = user?.role === 'Enseignant' || user?.role === 'enseignant' || user?.role === 'teacher';
    const canManage = isAdmin || isTeacher;

    useEffect(() => {
        fetchCours();
        fetchMatieres();
        fetchClasses();
    }, []);

    const fetchMatieres = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setMatieres(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des matières:', error);
            setMatieres([
                { id: 1, nom: 'Mathématiques' },
                { id: 2, nom: 'Physique' },
                { id: 3, nom: 'Français' },
                { id: 4, nom: 'Histoire' }
            ]);
        }
    };

    const fetchClasses = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/classes/classe.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            setClasses(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('Erreur lors du chargement des classes:', error);
            setClasses([
                { id: 1, nom: 'Classe A' },
                { id: 2, nom: 'Classe B' },
                { id: 3, nom: 'Classe C' },
                { id: 4, nom: 'Classe D' }
            ]);
        }
    };

    const fetchCours = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des cours...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/cours/cours.php', {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            let coursData = response.data;
            if (!Array.isArray(coursData)) {
                coursData = [];
            }

            setCours(coursData);
            console.log('✅ Cours chargés:', coursData.length, 'éléments');
        } catch (error) {
            console.error('❌ Erreur lors du chargement des cours:', error);

            // Données de test avec fichiers PDF (compatible avec structure BDD réelle)
            const testCours = [
                {
                    id: 1,
                    titre: 'Introduction aux Mathématiques',
                    description: 'Cours de base en mathématiques',
                    fichier_pdf: 'math_intro.pdf',
                    date_ajout: '2024-01-15',
                    date_publication: '2024-01-15', // Pour compatibilité
                    matiere_id: 1,
                    matiere_nom: 'Mathématiques',
                    classe_id: 1, // Valeur par défaut
                    classe_nom: 'Toutes classes', // Valeur par défaut
                    taille_fichier: '2.5 MB'
                },
                {
                    id: 2,
                    titre: 'Les Forces en Physique',
                    description: 'Étude des forces et du mouvement',
                    fichier_pdf: 'physique_forces.pdf',
                    date_ajout: '2024-01-20',
                    date_publication: '2024-01-20',
                    matiere_id: 2,
                    matiere_nom: 'Physique',
                    classe_id: 1,
                    classe_nom: 'Toutes classes',
                    taille_fichier: '3.2 MB'
                },
                {
                    id: 3,
                    titre: 'Grammaire Française',
                    description: 'Les règles de grammaire essentielles',
                    fichier_pdf: 'francais_grammaire.pdf',
                    date_ajout: '2024-01-25',
                    date_publication: '2024-01-25',
                    matiere_id: 3,
                    matiere_nom: 'Français',
                    classe_id: 1,
                    classe_nom: 'Toutes classes',
                    taille_fichier: '1.8 MB'
                },
                {
                    id: 4,
                    titre: 'Histoire Moderne',
                    description: 'Les événements du 20ème siècle',
                    fichier_pdf: 'histoire_moderne.pdf',
                    date_ajout: '2024-02-01',
                    date_publication: '2024-02-01',
                    matiere_id: 4,
                    matiere_nom: 'Histoire',
                    classe_id: 1,
                    classe_nom: 'Toutes classes',
                    taille_fichier: '4.1 MB'
                },
                {
                    id: 5,
                    titre: 'Algèbre Avancée',
                    description: 'Concepts avancés en algèbre',
                    fichier_pdf: 'math_algebre.pdf',
                    date_ajout: '2024-02-05',
                    date_publication: '2024-02-05',
                    matiere_id: 1,
                    matiere_nom: 'Mathématiques',
                    classe_id: 1,
                    classe_nom: 'Toutes classes',
                    taille_fichier: '3.7 MB'
                },
                {
                    id: 6,
                    titre: 'Optique et Lumière',
                    description: 'Propriétés de la lumière',
                    fichier_pdf: 'physique_optique.pdf',
                    date_ajout: '2024-02-10',
                    date_publication: '2024-02-10',
                    matiere_id: 2,
                    matiere_nom: 'Physique',
                    classe_id: 1,
                    classe_nom: 'Toutes classes',
                    taille_fichier: '2.9 MB'
                }
            ];

            setCours(testCours);
            Swal.fire({
                title: '🧪 Mode Test',
                text: `Connexion API échouée. Utilisation de ${testCours.length} cours de test avec fichiers PDF.`,
                icon: 'info',
                timer: 3000,
                showConfirmButton: false
            });
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent créer/modifier des cours', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';

            // Validation côté frontend
            if (!formData.titre.trim()) {
                Swal.fire('Erreur', 'Le titre du cours est requis', 'error');
                return;
            }

            if (!formData.matiere_id) {
                Swal.fire('Erreur', 'Veuillez sélectionner une matière', 'error');
                return;
            }

            if (!formData.classe_id) {
                Swal.fire('Erreur', 'Veuillez sélectionner une classe', 'error');
                return;
            }

            if (!formData.date_publication) {
                Swal.fire('Erreur', 'Veuillez sélectionner une date de publication', 'error');
                return;
            }

            // Validation du fichier PDF : requis seulement pour la création
            if (!editingCours && !formData.fichier_pdf) {
                Swal.fire('Erreur', 'Veuillez sélectionner un fichier PDF pour créer un nouveau cours', 'error');
                return;
            }

            // Pour la modification, le fichier PDF est optionnel
            if (editingCours && formData.fichier_pdf) {
                console.log('📁 Nouveau fichier PDF sélectionné pour la modification:', formData.fichier_pdf.name);
            } else if (editingCours && !formData.fichier_pdf) {
                console.log('📁 Aucun nouveau fichier PDF - conservation du fichier existant');
            }

            // Validation de l'ID pour la modification
            if (editingCours && (!editingCours.id || isNaN(editingCours.id))) {
                Swal.fire('Erreur', 'ID du cours invalide pour la modification', 'error');
                return;
            }

            // Créer FormData pour gérer l'upload de fichier
            const formDataToSend = new FormData();
            formDataToSend.append('titre', formData.titre.trim());
            formDataToSend.append('description', formData.description.trim());
            formDataToSend.append('matiere_id', formData.matiere_id);
            formDataToSend.append('classe_id', formData.classe_id);
            formDataToSend.append('date_publication', formData.date_publication);

            if (editingCours) {
                formDataToSend.append('id', editingCours.id);
                formDataToSend.append('_method', 'PUT'); // Champ caché pour identifier PUT
            }

            if (formData.fichier_pdf) {
                formDataToSend.append('fichier_pdf', formData.fichier_pdf);
            }

            // Logs détaillés pour debug
            console.log('🔄 Envoi requête cours:', {
                method: editingCours ? 'PUT (via POST)' : 'POST',
                url,
                titre: formData.titre,
                matiere_id: formData.matiere_id,
                classe_id: formData.classe_id,
                date_publication: formData.date_publication,
                editingId: editingCours?.id,
                hasFile: !!formData.fichier_pdf,
                fileSize: formData.fichier_pdf ? formData.fichier_pdf.size : 0,
                token: token ? 'présent' : 'absent'
            });

            // Afficher le contenu du FormData pour debug
            console.log('📦 FormData contents:');
            for (let [key, value] of formDataToSend.entries()) {
                if (value instanceof File) {
                    console.log(`  ${key}: File(${value.name}, ${value.size} bytes)`);
                } else {
                    console.log(`  ${key}: ${value}`);
                }
            }

            // Pour les modifications, utiliser POST avec _method=PUT car FormData ne fonctionne pas bien avec PUT
            const response = await axios({
                method: 'POST',
                url,
                data: formDataToSend,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data'
                },
                timeout: 30000 // 30 secondes pour l'upload
            });

            console.log('✅ Réponse complète:', {
                status: response.status,
                data: response.data,
                headers: response.headers
            });

            if (response.data && response.data.success === true) {
                Swal.fire('Succès', `Cours ${editingCours ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingCours(null);
                resetForm();
                fetchCours();
            } else {
                const errorMsg = response.data?.error || response.data?.message || 'Erreur inconnue';
                throw new Error(errorMsg);
            }
        } catch (error) {
            console.error('❌ Erreur complète:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                config: {
                    method: error.config?.method,
                    url: error.config?.url,
                    data: error.config?.data
                }
            });

            let errorMessage = 'Une erreur est survenue';

            if (error.response?.data?.error) {
                errorMessage = error.response.data.error;
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.response?.status === 413) {
                errorMessage = 'Le fichier est trop volumineux (max 10MB)';
            } else if (error.response?.status === 415) {
                errorMessage = 'Type de fichier non supporté (PDF uniquement)';
            } else if (error.message) {
                errorMessage = error.message;
            }

            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = (cours) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent modifier des cours', 'error');
            return;
        }

        // Vérifier que le cours est valide
        if (!cours || !cours.id) {
            Swal.fire('Erreur', 'Cours invalide', 'error');
            return;
        }

        console.log('✏️ Édition cours:', {
            id: cours.id,
            titre: cours.titre,
            matiere_id: cours.matiere_id,
            classe_id: cours.classe_id,
            date_publication: cours.date_publication
        });

        setEditingCours(cours);
        setFormData({
            titre: cours.titre || '',
            description: cours.description || '',
            fichier_pdf: null, // Ne pas pré-remplir le fichier
            date_publication: cours.date_publication || '',
            matiere_id: cours.matiere_id || '',
            classe_id: cours.classe_id || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!canManage) {
            Swal.fire('Erreur', 'Seuls les administrateurs et enseignants peuvent supprimer des cours', 'error');
            return;
        }

        // Vérifier que l'ID est valide
        if (!id || isNaN(id)) {
            Swal.fire('Erreur', 'ID du cours invalide', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action supprimera également le fichier PDF associé!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';

                console.log('🔄 Suppression cours:', {
                    id: id,
                    type: typeof id,
                    url: url,
                    token: token ? 'présent' : 'absent'
                });

                const response = await axios({
                    method: 'DELETE',
                    url: url,
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: JSON.stringify({ id: parseInt(id) }),
                    timeout: 10000
                });

                console.log('✅ Réponse suppression:', {
                    status: response.status,
                    data: response.data
                });

                if (response.data && response.data.success === true) {
                    Swal.fire('Supprimé!', 'Le cours et son fichier PDF ont été supprimés.', 'success');
                    fetchCours();
                } else {
                    const errorMsg = response.data?.error || response.data?.message || 'Erreur lors de la suppression';
                    console.error('❌ Erreur dans la réponse:', response.data);
                    throw new Error(errorMsg);
                }
            } catch (error) {
                console.error('❌ Erreur suppression complète:', {
                    message: error.message,
                    response: error.response?.data,
                    status: error.response?.status,
                    id: id
                });

                let errorMessage = 'Impossible de supprimer le cours';

                if (error.response?.status === 404) {
                    errorMessage = 'Cours non trouvé';
                } else if (error.response?.status === 403) {
                    errorMessage = 'Accès non autorisé';
                } else if (error.response?.data?.error) {
                    errorMessage = error.response.data.error;
                } else if (error.response?.data?.message) {
                    errorMessage = error.response.data.message;
                } else if (error.message) {
                    errorMessage = error.message;
                }

                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    // Fonction pour télécharger le PDF
    const handleDownloadPDF = async (cours) => {
        try {
            const token = localStorage.getItem('token');

            // Vérifier que le cours a un fichier PDF
            if (!cours.fichier_pdf) {
                Swal.fire('Erreur', 'Aucun fichier PDF associé à ce cours', 'error');
                return;
            }

            const url = `http://localhost/Project_PFE/Backend/pages/cours/download.php?file=${encodeURIComponent(cours.fichier_pdf)}&cours_id=${cours.id}`;

            console.log('📥 Téléchargement PDF:', {
                titre: cours.titre,
                fichier: cours.fichier_pdf,
                url: url
            });

            const response = await axios({
                method: 'GET',
                url,
                headers: {
                    Authorization: `Bearer ${token}`
                },
                responseType: 'blob',
                timeout: 30000 // 30 secondes de timeout
            });

            // Vérifier que la réponse est bien un PDF
            if (response.data.type !== 'application/pdf' && !response.headers['content-type']?.includes('application/pdf')) {
                console.error('❌ Type de fichier incorrect:', response.data.type);
                Swal.fire('Erreur', 'Le fichier téléchargé n\'est pas un PDF valide', 'error');
                return;
            }

            // Créer un lien de téléchargement
            const blob = new Blob([response.data], { type: 'application/pdf' });
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `${cours.titre.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`;
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(downloadUrl);

            console.log('✅ Téléchargement réussi:', cours.titre);
            Swal.fire({
                title: 'Téléchargement réussi!',
                text: `Le fichier "${cours.titre}.pdf" a été téléchargé.`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });

        } catch (error) {
            console.error('❌ Erreur téléchargement complète:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                cours: cours.titre
            });

            let errorMessage = 'Impossible de télécharger le fichier PDF';

            if (error.response?.status === 404) {
                errorMessage = 'Fichier PDF non trouvé sur le serveur';
            } else if (error.response?.status === 403) {
                errorMessage = 'Accès non autorisé au fichier PDF';
            } else if (error.code === 'ECONNABORTED') {
                errorMessage = 'Timeout - Le téléchargement a pris trop de temps';
            } else if (error.response?.data) {
                try {
                    const errorText = await error.response.data.text();
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.error || errorMessage;
                } catch (e) {
                    // Ignore parsing errors
                }
            }

            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const resetForm = () => {
        setFormData({
            titre: '',
            description: '',
            fichier_pdf: null,
            date_publication: '',
            matiere_id: '',
            classe_id: ''
        });
    };

    // Fonction de test pour vérifier les données PUT
    const testPUT = async () => {
        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/cours/test_put.php';

            // Créer des données de test
            const formDataToSend = new FormData();
            formDataToSend.append('id', '1');
            formDataToSend.append('titre', 'Test PUT');
            formDataToSend.append('description', 'Test description');
            formDataToSend.append('matiere_id', '1');
            formDataToSend.append('classe_id', '1');
            formDataToSend.append('date_publication', '2024-01-15');

            console.log('🧪 Test PUT - Envoi des données...');

            const response = await axios({
                method: 'PUT',
                url,
                data: formDataToSend,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'multipart/form-data'
                },
                timeout: 10000
            });

            console.log('✅ Test PUT réussi:', response.data);
            Swal.fire({
                title: 'Test PUT Réussi',
                html: `<pre>${JSON.stringify(response.data, null, 2)}</pre>`,
                icon: 'success',
                width: '80%'
            });

        } catch (error) {
            console.error('❌ Test PUT échoué:', error);
            Swal.fire('Erreur', 'Test PUT échoué: ' + (error.message || 'Erreur inconnue'), 'error');
        }
    };

    // Fonction de test pour vérifier la connexion API
    const testAPI = async () => {
        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/cours/cours.php';

            console.log('🧪 Test de connexion API...', {
                url: url,
                token: token ? 'présent' : 'absent'
            });

            const response = await axios.get(url, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            });

            console.log('✅ Test API réussi:', {
                status: response.status,
                dataLength: response.data?.length || 0,
                data: response.data
            });

            const coursCount = Array.isArray(response.data) ? response.data.length : 0;
            Swal.fire('Succès', `API connectée ! ${coursCount} cours trouvés`, 'success');

        } catch (error) {
            console.error('❌ Test API échoué:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status,
                code: error.code
            });

            let errorMessage = 'Connexion API échouée';

            if (error.code === 'ECONNREFUSED') {
                errorMessage = 'Serveur non accessible - Vérifiez que le serveur PHP fonctionne';
            } else if (error.code === 'ECONNABORTED') {
                errorMessage = 'Timeout - Le serveur met trop de temps à répondre';
            } else if (error.response?.status === 404) {
                errorMessage = 'API non trouvée - Vérifiez l\'URL du backend';
            } else if (error.response?.status === 500) {
                errorMessage = 'Erreur serveur - Vérifiez les logs PHP';
            } else if (error.message) {
                errorMessage = error.message;
            }

            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    // Filtrage des données
    const filteredCours = cours.filter(c => {
        const matchesSearch = c.titre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             c.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             c.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             c.classe_nom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesMatiere = matiereFilter === 'all' || c.matiere_id?.toString() === matiereFilter;
        const matchesClasse = classeFilter === 'all' || c.classe_id?.toString() === classeFilter;

        return matchesSearch && matchesMatiere && matchesClasse;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentCours = filteredCours.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredCours.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, matiereFilter, classeFilter]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des cours...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>📚 Gestion des Cours </h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredCours.length} cours trouvé(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    <div style={{ display: 'flex', gap: '10px' }}>
                        {canManage && (
                            <button
                                className="btn btn-primary"
                                onClick={() => setShowModal(true)}
                            >
                                <img src="/plus.png" alt="Ajouter" /> Nouveau Cours
                            </button>
                        )}
                        
                    </div>
                </div>
            </div>

            {/* Message d'information pour les non-gestionnaires */}
            {!canManage && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les cours en mode lecture seule.
                        Seuls les administrateurs et enseignants peuvent créer, modifier ou supprimer des cours.
                        Cliquez sur les liens PDF pour télécharger les cours.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher un cours..."
                        value={searchTerm}
                        onChange={(e) => {
                            setSearchTerm(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="matiere-filter">
                    <select
                        value={matiereFilter}
                        onChange={(e) => {
                            setMatiereFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Toutes les matières</option>
                        {matieres.map(matiere => (
                            <option key={matiere.id} value={matiere.id}>
                                {matiere.nom}
                            </option>
                        ))}
                    </select>
                </div>
                <div className="classe-filter">
                    <select
                        value={classeFilter}
                        onChange={(e) => {
                            setClasseFilter(e.target.value);
                            setCurrentPage(1);
                        }}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Toutes les classes</option>
                        {classes.map(classe => (
                            <option key={classe.id} value={classe.id}>
                                {classe.nom}
                            </option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredCours.length === 0 ? (
                    <div className="no-data">
                        <img src="/pdf-icon.png" alt="Aucun cours" />
                        <p>Aucun cours trouvé</p>
                        {(searchTerm || matiereFilter !== 'all' || classeFilter !== 'all') && (
                            <button
                                onClick={() => {
                                    setSearchTerm('');
                                    setMatiereFilter('all');
                                    setClasseFilter('all');
                                }}
                                className="btn btn-secondary"
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>📚 Titre du Cours</th>
                                    <th>📖 Matière</th>
                                    <th>🏫 Classe</th>
                                    <th>📅 Date</th>
                                    <th>📄 Fichier PDF</th>
                                    <th>📊 Taille</th>
                                    {canManage && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentCours.map((c) => (
                                    <tr key={c.id}>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e3f2fd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold'
                                            }}>
                                                #{c.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{c.titre}</strong>
                                                {c.description && (
                                                    <small style={{
                                                        display: 'block',
                                                        color: '#6c757d',
                                                        marginTop: '4px'
                                                    }}>
                                                        {c.description.length > 50
                                                            ? c.description.substring(0, 50) + '...'
                                                            : c.description}
                                                    </small>
                                                )}
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#fff3cd',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#856404'
                                            }}>
                                                {c.matiere_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#d4edda',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                color: '#155724'
                                            }}>
                                                {c.classe_nom || 'Non définie'}
                                            </span>
                                        </td>
                                        <td>
                                            <span style={{ fontSize: '0.9em' }}>
                                                {new Date(c.date_publication).toLocaleDateString('fr-FR')}
                                            </span>
                                        </td>
                                        <td>
                                            <button
                                                className="btn btn-sm btn-success"
                                                onClick={() => handleDownloadPDF(c)}
                                                title="Télécharger le PDF"
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '5px',
                                                    fontSize: '0.8em'
                                                }}
                                            >
                                                📥 PDF
                                            </button>
                                        </td>
                                        <td>
                                            <span style={{
                                                fontSize: '0.8em',
                                                color: '#6c757d'
                                            }}>
                                                {c.taille_fichier || 'N/A'}
                                            </span>
                                        </td>
                                        {canManage && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(c)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(c.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Statistiques */}
            {filteredCours.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {filteredCours.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total des cours</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {matieres.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Matières disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#17a2b8', margin: '0' }}>
                            {classes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Classes disponibles</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {currentCours.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Affichés</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier un cours */}
            {showModal && canManage && (
                <div className="modal-overlay">
                    <div className="modal-content" style={{ maxWidth: '600px' }}>
                        <div className="modal-header">
                            <h3>{editingCours ? 'Modifier le cours' : 'Nouveau cours'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingCours(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            {/* Affichage des informations du cours en modification */}
                            {editingCours && (
                                <div style={{
                                    padding: '15px',
                                    backgroundColor: '#e3f2fd',
                                    borderRadius: '8px',
                                    marginBottom: '20px',
                                    border: '1px solid #bbdefb'
                                }}>
                                    <h4 style={{ margin: '0 0 10px 0', color: '#1976d2' }}>
                                        📝 Modification du cours #{editingCours.id}
                                    </h4>
                                    <p style={{ margin: '5px 0', fontSize: '14px', color: '#1976d2' }}>
                                        📄 Fichier actuel: <strong>{editingCours.fichier_pdf || 'Aucun fichier'}</strong>
                                        {editingCours.taille_fichier && ` (${editingCours.taille_fichier})`}
                                    </p>
                                    <p style={{ margin: '5px 0 0 0', fontSize: '12px', color: '#666' }}>
                                        💡 Laissez le champ fichier vide pour conserver le fichier actuel
                                    </p>
                                </div>
                            )}

                            <div className="form-group">
                                <label>Titre du cours *</label>
                                <input
                                    type="text"
                                    value={formData.titre}
                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}
                                    placeholder="Ex: Introduction aux Mathématiques..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Description</label>
                                <textarea
                                    value={formData.description}
                                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                                    placeholder="Description du cours..."
                                    rows="3"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>
                                    Fichier PDF {editingCours ? '(Optionnel - Laisser vide pour conserver le fichier actuel)' : '*'}
                                </label>
                                <input
                                    type="file"
                                    accept=".pdf"
                                    onChange={(e) => {
                                        const file = e.target.files[0];
                                        if (file) {
                                            // Validation côté client
                                            if (file.type !== 'application/pdf') {
                                                Swal.fire('Erreur', 'Seuls les fichiers PDF sont acceptés', 'error');
                                                e.target.value = '';
                                                return;
                                            }
                                            if (file.size > 10 * 1024 * 1024) {
                                                Swal.fire('Erreur', 'Le fichier ne doit pas dépasser 10MB', 'error');
                                                e.target.value = '';
                                                return;
                                            }
                                            console.log('📁 Fichier sélectionné:', {
                                                name: file.name,
                                                size: (file.size / (1024 * 1024)).toFixed(2) + ' MB',
                                                type: file.type
                                            });
                                        }
                                        setFormData({...formData, fichier_pdf: file});
                                    }}
                                    required={!editingCours}
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Formats acceptés: PDF uniquement. Taille max: 10MB
                                    {editingCours && !formData.fichier_pdf && (
                                        <span style={{ color: '#17a2b8', display: 'block', marginTop: '5px' }}>
                                            📄 Fichier actuel: {editingCours.fichier_pdf || 'Aucun fichier'}
                                            {editingCours.taille_fichier && ` (${editingCours.taille_fichier})`}
                                        </span>
                                    )}
                                    {formData.fichier_pdf && (
                                        <span style={{ color: '#28a745', display: 'block', marginTop: '5px' }}>
                                            ✅ Nouveau fichier sélectionné: {formData.fichier_pdf.name}
                                            ({(formData.fichier_pdf.size / (1024 * 1024)).toFixed(2)} MB)
                                        </span>
                                    )}
                                </small>
                            </div>

                            <div style={{ display: 'flex', gap: '15px' }}>
                                <div className="form-group" style={{ flex: 1 }}>
                                    <label>Matière *</label>
                                    <select
                                        value={formData.matiere_id}
                                        onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '10px',
                                            border: '1px solid #ced4da',
                                            borderRadius: '4px',
                                            fontSize: '14px'
                                        }}
                                    >
                                        <option value="">Sélectionner une matière</option>
                                        {matieres.map((matiere) => (
                                            <option key={matiere.id} value={matiere.id}>
                                                {matiere.nom}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="form-group" style={{ flex: 1 }}>
                                    <label>Classe *</label>
                                    <select
                                        value={formData.classe_id}
                                        onChange={(e) => setFormData({...formData, classe_id: e.target.value})}
                                        required
                                        style={{
                                            width: '100%',
                                            padding: '10px',
                                            border: '1px solid #ced4da',
                                            borderRadius: '4px',
                                            fontSize: '14px'
                                        }}
                                    >
                                        <option value="">Sélectionner une classe</option>
                                        {classes.map((classe) => (
                                            <option key={classe.id} value={classe.id}>
                                                {classe.nom}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            <div className="form-group">
                                <label>Date de publication *</label>
                                <input
                                    type="date"
                                    value={formData.date_publication}
                                    onChange={(e) => setFormData({...formData, date_publication: e.target.value})}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingCours(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingCours ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CoursCRUD;
