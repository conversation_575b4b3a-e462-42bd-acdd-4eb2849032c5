import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Swal from 'sweetalert2';
import UserEditModal from '../components/UserEditModal';
import {
  FaUsers,
  FaSearch,
  FaFilter,
  FaEye,
  FaEdit,
  FaTrash,
  FaUserTie,
  FaGraduationCap,
  FaChalkboardTeacher,
  FaUserFriends,
  FaSpinner,
  FaPlus,
  FaSortAlphaDown,
  FaSortAlphaUp,
  FaChevronLeft,
  FaChevronRight,
  FaAngleDoubleLeft,
  FaAngleDoubleRight
} from 'react-icons/fa';

const UsersList = () => {
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [sortOrder, setSortOrder] = useState('asc');
  const [editingUser, setEditingUser] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);

  // États pour la pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage, setUsersPerPage] = useState(12);
  const [paginatedUsers, setPaginatedUsers] = useState([]);

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    filterAndSortUsers();
  }, [users, searchTerm, roleFilter, sortOrder]);

  useEffect(() => {
    paginateUsers();
  }, [filteredUsers, currentPage, usersPerPage]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost//Project_PFE/Backend/pages/utilisateurs/getUsers.php?detailed=true');
      const data = await response.json();
      
      if (data.success) {
        setUsers(data.users);
      } else {
        setError(data.error || 'Erreur lors du chargement des utilisateurs');
      }
    } catch (err) {
      setError('Erreur de connexion au serveur');
      console.error('Erreur:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortUsers = () => {
    let filtered = users.filter(user => {
      const matchesSearch = user.nom.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.email.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesRole = roleFilter === '' || user.role_nom === roleFilter;
      return matchesSearch && matchesRole;
    });

    // Tri
    filtered.sort((a, b) => {
      const comparison = a.nom.localeCompare(b.nom);
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    setFilteredUsers(filtered);
    // Réinitialiser à la première page quand les filtres changent
    setCurrentPage(1);
  };

  // Fonction de pagination
  const paginateUsers = () => {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const paginated = filteredUsers.slice(startIndex, endIndex);
    setPaginatedUsers(paginated);
  };

  // Fonctions de navigation de pagination
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  const goToPage = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const goToFirstPage = () => setCurrentPage(1);
  const goToLastPage = () => setCurrentPage(totalPages);
  const goToPreviousPage = () => goToPage(currentPage - 1);
  const goToNextPage = () => goToPage(currentPage + 1);

  const handleUsersPerPageChange = (newUsersPerPage) => {
    setUsersPerPage(newUsersPerPage);
    setCurrentPage(1); // Réinitialiser à la première page
  };

  const getRoleIcon = (role) => {
    switch (role?.toLowerCase()) {
      case 'enseignant': return <FaChalkboardTeacher style={{ color: '#4CAF50' }} />;
      case 'etudiant': return <FaGraduationCap style={{ color: '#2196F3' }} />;
      case 'parent': return <FaUserFriends style={{ color: '#FF9800' }} />;
      case 'responsable':
      case 'admin': return <FaUserTie style={{ color: '#9C27B0' }} />;
      default: return <FaUsers style={{ color: '#757575' }} />;
    }
  };

  const getRoleColor = (role) => {
    switch (role?.toLowerCase()) {
      case 'enseignant': return '#4CAF50';
      case 'etudiant': return '#2196F3';
      case 'parent': return '#FF9800';
      case 'responsable':
      case 'admin': return '#9C27B0';
      default: return '#757575';
    }
  };

  // Fonction pour ouvrir le modal de modification
  const handleEdit = async (user) => {
    try {
      // Récupérer les détails complets de l'utilisateur
      const response = await fetch(`http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php?id=${user.id}`);
      const data = await response.json();

      if (data.success) {
        setEditingUser(data.user);
        setShowEditModal(true);
      } else {
        Swal.fire('Erreur', data.error || 'Impossible de charger les détails de l\'utilisateur', 'error');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des détails:', error);
      Swal.fire('Erreur', 'Erreur de connexion au serveur', 'error');
    }
  };

  // Fonction pour gérer la sauvegarde après modification
  const handleSaveUser = (result) => {
    Swal.fire('Succès', result.message || 'Utilisateur modifié avec succès', 'success');
    fetchUsers(); // Recharger la liste
  };

  // Fonction pour fermer le modal
  const handleCloseModal = () => {
    setShowEditModal(false);
    setEditingUser(null);
  };

  // Fonction pour supprimer un utilisateur
  const handleDelete = async (user) => {
    const result = await Swal.fire({
      title: 'Êtes-vous sûr?',
      html: `
        <p>Cette action supprimera définitivement l'utilisateur :</p>
        <strong>${user.nom} (${user.email})</strong>
        <p style="color: #e53935; margin-top: 10px;">
          <strong>⚠️ Cette action est irréversible !</strong>
        </p>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Oui, supprimer',
      cancelButtonText: 'Annuler',
      reverseButtons: true
    });

    if (result.isConfirmed) {
      try {
        const response = await fetch('http://localhost/Project_PFE/Backend/pages/utilisateurs/userManagement.php', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ id: user.id })
        });

        const data = await response.json();

        if (data.success) {
          Swal.fire('Supprimé!', data.message || 'L\'utilisateur a été supprimé avec succès', 'success');
          fetchUsers(); // Recharger la liste
        } else {
          Swal.fire('Erreur', data.error || 'Impossible de supprimer l\'utilisateur', 'error');
        }
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        Swal.fire('Erreur', 'Erreur de connexion au serveur', 'error');
      }
    }
  };

  const styles = {
  container: {
    minHeight: '100vh',
    backgroundColor: 'var(--antiflash-white)',
    padding: '20px',
    marginLeft: '70px'
  },
  header: {
    backgroundColor: 'var(--cerulean)',
    borderRadius: '15px',
    padding: '25px',
    marginBottom: '25px',
    color: 'white',
    boxShadow: '0 4px 10px rgba(0,0,0,0.1)'
  },
  title: {
    fontSize: '2rem',
    fontWeight: 'bold',
    color: 'white',
    marginBottom: '20px',
    display: 'flex',
    alignItems: 'center',
    gap: '15px'
  },
  controls: {
    display: 'flex',
    gap: '15px',
    flexWrap: 'wrap',
    alignItems: 'center'
  },
  searchBox: {
    position: 'relative',
    flex: '1',
    minWidth: '250px'
  },
  searchInput: {
    width: '100%',
    padding: '12px 15px 12px 45px',
    borderRadius: '10px',
    border: '1px solid var(--cerulean-2)',
    background: 'white',
    color: 'var(--text-dark)',
    fontSize: '14px'
  },
  searchIcon: {
    position: 'absolute',
    left: '15px',
    top: '50%',
    transform: 'translateY(-50%)',
    color: 'var(--cerulean-2)'
  },
  select: {
    padding: '12px 15px',
    borderRadius: '10px',
    border: '1px solid var(--cerulean-2)',
    background: 'white',
    color: 'var(--text-dark)',
    fontSize: '14px'
  },
  button: {
    padding: '12px 20px',
    borderRadius: '10px',
    border: 'none',
    background: 'var(--moonstone)',
    color: 'white',
    fontSize: '14px',
    fontWeight: 'bold',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    transition: 'all 0.3s ease'
  },
  sortButton: {
    padding: '12px',
    borderRadius: '10px',
    border: 'none',
    background: 'var(--cerulean-2)',
    color: 'white',
    cursor: 'pointer',
    transition: 'all 0.3s ease'
  },
  statsBar: {
    display: 'flex',
    gap: '15px',
    marginTop: '15px'
  },
  statItem: {
    background: 'white',
    padding: '10px 15px',
    borderRadius: '8px',
    color: 'var(--text-dark)',
    fontSize: '14px',
    border: '1px solid var(--cerulean-2)'
  },
  usersGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
    gap: '20px'
  },
  userCard: {
    background: 'white',
    borderRadius: '15px',
    padding: '20px',
    border: '1px solid var(--cerulean-2)',
    transition: 'all 0.3s ease',
    position: 'relative'
  },
  userHeader: {
    display: 'flex',
    alignItems: 'center',
    gap: '15px',
    marginBottom: '15px'
  },
  userAvatar: {
    width: '50px',
    height: '50px',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '1.5rem',
    color: 'white'
  },
  userName: {
    fontSize: '1.2rem',
    fontWeight: 'bold',
    color: 'var(--text-dark)',
    marginBottom: '5px'
  },
  userEmail: {
    fontSize: '0.9rem',
    color: 'gray'
  },
  userDetails: {
    fontSize: '0.9rem',
    color: '#555',
    marginBottom: '15px'
  },
  userRole: {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '5px',
    padding: '4px 12px',
    borderRadius: '20px',
    fontSize: '0.8rem',
    fontWeight: 'bold',
    marginBottom: '15px'
  },
  viewButton: {
   
    color: '#55a630'
  },
  editButton: {
    background: 'var(--moonstone)',
    color: 'white'
  },
  deleteButton: {
    background: '#ef233c',
    color: 'white'
  },
  loadingContainer: {
    textAlign: 'center',
    padding: '50px',
    color: 'var(--cerulean)'
  },
  errorContainer: {
    textAlign: 'center',
    padding: '50px',
    color: '#e53935'
  },
  emptyState: {
    textAlign: 'center',
    padding: '50px',
    color: 'gray'
  },

  // Styles de pagination
  paginationInfo: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: '15px',
    padding: '10px 0',
    borderTop: '1px solid #e0e0e0'
  },

  resultsInfo: {
    display: 'flex',
    alignItems: 'center'
  },

  resultsText: {
    fontSize: '14px',
    color: '#666',
    fontWeight: '500'
  },

  usersPerPageSelector: {
    display: 'flex',
    alignItems: 'center',
    gap: '10px'
  },

  usersPerPageLabel: {
    fontSize: '14px',
    color: '#666',
    fontWeight: '500'
  },

  usersPerPageSelect: {
    padding: '5px 10px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '14px',
    backgroundColor: 'white',
    cursor: 'pointer'
  },

  paginationContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '15px',
    marginTop: '30px',
    padding: '20px',
    backgroundColor: 'white',
    borderRadius: '10px',
    boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
  },

  paginationControls: {
    display: 'flex',
    alignItems: 'center',
    gap: '5px'
  },

  paginationButton: {
    padding: '8px 12px',
    border: '1px solid #ddd',
    backgroundColor: 'white',
    color: '#333',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: '500',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: '40px',
    height: '40px'
  },

  paginationButtonActive: {
    backgroundColor: 'var(--cerulean)',
    color: 'white',
    borderColor: 'var(--cerulean)',
    fontWeight: 'bold'
  },

  paginationButtonDisabled: {
    backgroundColor: '#f5f5f5',
    color: '#ccc',
    cursor: 'not-allowed',
    borderColor: '#e0e0e0'
  },

  pageNumbers: {
    display: 'flex',
    gap: '2px',
    margin: '0 10px'
  },

  pageInfo: {
    fontSize: '14px',
    color: '#666',
    fontWeight: '500'
  }
}

  if (loading) {
    return (
      <div style={styles.container}>
        <div style={styles.loadingContainer}>
          <FaSpinner style={{ fontSize: '3rem', animation: 'spin 1s linear infinite' }} />
          <p style={{ marginTop: '20px' }}>Chargement des utilisateurs...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={styles.container}>
        <div style={styles.errorContainer}>
          <p style={{ fontSize: '1.2rem' }}>{error}</p>
        </div>
      </div>
    );
  }

  const uniqueRoles = [...new Set(users.map(user => user.role_nom).filter(Boolean))];
  const totalUsers = filteredUsers.length;
  const roleStats = uniqueRoles.map(role => ({
    role,
    count: filteredUsers.filter(user => user.role_nom === role).length
  }));

  return (
    <div style={styles.container}>
      {/* En-tête avec contrôles */}
      <div style={styles.header}>
        <h1 style={styles.title}>
          <FaUsers /> Liste des Utilisateurs
        </h1>
        
        <div style={styles.controls}>
          <div style={styles.searchBox}>
            <FaSearch style={styles.searchIcon} />
            <input
              style={styles.searchInput}
              type="text"
              placeholder="Rechercher par nom ou email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <select
            style={styles.select}
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
          >
            <option value="">Tous les rôles</option>
            {uniqueRoles.map(role => (
              <option key={role} value={role}>{role}</option>
            ))}
          </select>
          
          <button
            style={styles.sortButton}
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            title={`Trier par nom ${sortOrder === 'asc' ? 'décroissant' : 'croissant'}`}
          >
            {sortOrder === 'asc' ? <FaSortAlphaDown /> : <FaSortAlphaUp />}
          </button>
          
          <Link to="/registers" style={styles.button}>
            <FaPlus /> Nouvel Utilisateur
          </Link>
        </div>

        {/* Statistiques */}
        <div style={styles.statsBar}>
          <div style={styles.statItem}>
            Total: {totalUsers} utilisateur{totalUsers > 1 ? 's' : ''}
          </div>
          {roleStats.map(stat => (
            <div key={stat.role} style={styles.statItem}>
              {stat.role}: {stat.count}
            </div>
          ))}
        </div>

        {/* Informations de pagination */}
        {filteredUsers.length > 0 && (
          <div style={styles.paginationInfo}>
            <div style={styles.resultsInfo}>
              <span style={styles.resultsText}>
                Affichage de {((currentPage - 1) * usersPerPage) + 1} à {Math.min(currentPage * usersPerPage, filteredUsers.length)} sur {filteredUsers.length} utilisateurs
              </span>
            </div>

            <div style={styles.usersPerPageSelector}>
              <label style={styles.usersPerPageLabel}>Afficher :</label>
              <select
                style={styles.usersPerPageSelect}
                value={usersPerPage}
                onChange={(e) => handleUsersPerPageChange(parseInt(e.target.value))}
              >
                <option value={6}>6 par page</option>
                <option value={12}>12 par page</option>
                <option value={24}>24 par page</option>
                <option value={48}>48 par page</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Liste des utilisateurs */}
      {filteredUsers.length === 0 ? (
        <div style={styles.emptyState}>
          <FaUsers style={{ fontSize: '3rem', marginBottom: '20px' }} />
          <p>Aucun utilisateur trouvé</p>
        </div>
      ) : paginatedUsers.length === 0 ? (
        <div style={styles.emptyState}>
          <FaUsers style={{ fontSize: '3rem', marginBottom: '20px' }} />
          <p>Aucun utilisateur sur cette page</p>
        </div>
      ) : (
        <div style={styles.usersGrid}>
          {paginatedUsers.map(user => (
            <div 
              key={user.id} 
              style={styles.userCard}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-5px)';
                e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <div style={styles.userHeader}>
                <div style={styles.userAvatar}>
                  {getRoleIcon(user.role_nom)}
                </div>
                <div style={styles.userInfo}>
                  <div style={styles.userName}>{user.nom}</div>
                  <div style={styles.userEmail}>{user.email}</div>
                </div>
              </div>
              
              <div 
                style={{
                  ...styles.userRole,
                  background: `${getRoleColor(user.role_nom)}20`,
                  border: `1px solid ${getRoleColor(user.role_nom)}50`,
                  color: getRoleColor(user.role_nom)
                }}
              >
                {getRoleIcon(user.role_nom)}
                {user.role_nom}
              </div>
              
              <div style={styles.userDetails}>
                <div>Membre depuis: {user.created_at_formatted}</div>
                {user.parent_telephone && (
                  <div>Téléphone: {user.parent_telephone}</div>
                )}
                {user.groupe_nom && (
                  <div>Groupe: {user.groupe_nom}</div>
                )}
              </div>
              
              <div style={styles.userActions}>
                <Link
                  to={`/profil/${user.id}`}
                  style={{ ...styles.actionButton, ...styles.viewButton }}
                >
                  <FaEye /> Voir
                </Link>
                <button
                  style={{ ...styles.actionButton, ...styles.editButton }}
                  onClick={() => handleEdit(user)}
                  title="Modifier l'utilisateur"
                >
                  <FaEdit /> Modifier
                </button>
                <button
                  style={{ ...styles.actionButton, ...styles.deleteButton }}
                  onClick={() => handleDelete(user)}
                  title="Supprimer l'utilisateur"
                >
                  <FaTrash /> Supprimer
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Contrôles de pagination */}
      {filteredUsers.length > usersPerPage && (
        <div style={styles.paginationContainer}>
          <div style={styles.paginationControls}>
            {/* Bouton première page */}
            <button
              style={{
                ...styles.paginationButton,
                ...(currentPage === 1 ? styles.paginationButtonDisabled : {})
              }}
              onClick={goToFirstPage}
              disabled={currentPage === 1}
              title="Première page"
              onMouseEnter={(e) => {
                if (currentPage !== 1) {
                  e.target.style.backgroundColor = 'var(--cerulean)';
                  e.target.style.color = 'white';
                }
              }}
              onMouseLeave={(e) => {
                if (currentPage !== 1) {
                  e.target.style.backgroundColor = 'white';
                  e.target.style.color = '#333';
                }
              }}
            >
              <FaAngleDoubleLeft />
            </button>

            {/* Bouton page précédente */}
            <button
              style={{
                ...styles.paginationButton,
                ...(currentPage === 1 ? styles.paginationButtonDisabled : {})
              }}
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
              title="Page précédente"
            >
              <FaChevronLeft />
            </button>

            {/* Numéros de pages */}
            <div style={styles.pageNumbers}>
              {Array.from({ length: Math.min(5, totalPages) }, (_, index) => {
                let pageNumber;
                if (totalPages <= 5) {
                  pageNumber = index + 1;
                } else if (currentPage <= 3) {
                  pageNumber = index + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNumber = totalPages - 4 + index;
                } else {
                  pageNumber = currentPage - 2 + index;
                }

                return (
                  <button
                    key={pageNumber}
                    style={{
                      ...styles.paginationButton,
                      ...(currentPage === pageNumber ? styles.paginationButtonActive : {})
                    }}
                    onClick={() => goToPage(pageNumber)}
                  >
                    {pageNumber}
                  </button>
                );
              })}
            </div>

            {/* Bouton page suivante */}
            <button
              style={{
                ...styles.paginationButton,
                ...(currentPage === totalPages ? styles.paginationButtonDisabled : {})
              }}
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
              title="Page suivante"
            >
              <FaChevronRight />
            </button>

            {/* Bouton dernière page */}
            <button
              style={{
                ...styles.paginationButton,
                ...(currentPage === totalPages ? styles.paginationButtonDisabled : {})
              }}
              onClick={goToLastPage}
              disabled={currentPage === totalPages}
              title="Dernière page"
            >
              <FaAngleDoubleRight />
            </button>
          </div>

          {/* Informations de page */}
          <div style={styles.pageInfo}>
            Page {currentPage} sur {totalPages}
          </div>
        </div>
      )}

      {/* Modal de modification */}
      <UserEditModal
        user={editingUser}
        isOpen={showEditModal}
        onClose={handleCloseModal}
        onSave={handleSaveUser}
      />
    </div>
  );
};

export default UsersList;




