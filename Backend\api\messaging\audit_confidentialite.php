<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Audit de Confidentialité - Messagerie</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1400px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #667eea; font-size: 3rem; margin-bottom: 10px; }
        .success { background: #d4edda; border-left: 5px solid #28a745; color: #155724; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .error { background: #f8d7da; border-left: 5px solid #dc3545; color: #721c24; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .warning { background: #fff3cd; border-left: 5px solid #ffc107; color: #856404; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .info { background: #d1ecf1; border-left: 5px solid #17a2b8; color: #0c5460; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .security-check { background: #f8f9fa; border: 2px solid #28a745; padding: 20px; border-radius: 10px; margin: 15px 0; }
        .security-fail { background: #f8f9fa; border: 2px solid #dc3545; padding: 20px; border-radius: 10px; margin: 15px 0; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; overflow-x: auto; font-family: 'Courier New', monospace; margin: 15px 0; font-size: 14px; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin: 30px 0; }
        .test-card { background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #667eea; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; }
        .btn:hover { background: #5a67d8; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ AUDIT DE CONFIDENTIALITÉ</h1>
            <p>Vérification de la sécurité stricte des messages</p>
        </div>

<?php
try {
    // Connexion à la base de données
    $host = 'localhost';
    $dbname = 'GestionScolaire';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>";
    echo "<h2>✅ CONNEXION BASE DE DONNÉES</h2>";
    echo "<p>Connexion réussie à la base de données GestionScolaire</p>";
    echo "</div>";
    
    // Test 1: Vérifier la structure de la table messages
    echo "<div class='info'>";
    echo "<h2>🔍 TEST 1: STRUCTURE TABLE MESSAGES</h2>";
    
    $stmt = $pdo->query("DESCRIBE messages");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredColumns = [
        'id', 'expediteur_id', 'destinataire_id', 'message', 'date_envoi', 'lu',
        'modifie', 'date_modification', 'supprime_par_expediteur', 'supprime_par_destinataire',
        'message_original'
    ];
    
    $missingColumns = [];
    $existingColumns = array_column($columns, 'Field');
    
    foreach ($requiredColumns as $col) {
        if (!in_array($col, $existingColumns)) {
            $missingColumns[] = $col;
        }
    }
    
    if (empty($missingColumns)) {
        echo "<div class='security-check'>";
        echo "<h3>✅ Structure de table CONFORME</h3>";
        echo "<p>Toutes les colonnes requises pour la confidentialité sont présentes</p>";
        echo "</div>";
    } else {
        echo "<div class='security-fail'>";
        echo "<h3>❌ Structure de table INCOMPLÈTE</h3>";
        echo "<p>Colonnes manquantes: " . implode(', ', $missingColumns) . "</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Test 2: Créer des utilisateurs de test
    echo "<div class='info'>";
    echo "<h2>🧪 TEST 2: CRÉATION UTILISATEURS DE TEST</h2>";
    
    // Vérifier si les utilisateurs de test existent
    $testUsers = [
        ['id' => 1001, 'nom' => 'Parent Test 1', 'email' => '<EMAIL>', 'role_id' => 4],
        ['id' => 1002, 'nom' => 'Parent Test 2', 'email' => '<EMAIL>', 'role_id' => 4],
        ['id' => 1003, 'nom' => 'Enseignant Test', 'email' => '<EMAIL>', 'role_id' => 2],
        ['id' => 1004, 'nom' => 'Admin Test', 'email' => '<EMAIL>', 'role_id' => 1]
    ];
    
    foreach ($testUsers as $user) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM utilisateurs WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        if ($stmt->fetchColumn() == 0) {
            $stmt = $pdo->prepare("INSERT INTO utilisateurs (id, nom, email, mot_de_passe, role_id) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$user['id'], $user['nom'], $user['email'], 'test123', $user['role_id']]);
            echo "<p>✅ Utilisateur créé: {$user['nom']}</p>";
        } else {
            echo "<p>ℹ️ Utilisateur existe: {$user['nom']}</p>";
        }
    }
    echo "</div>";
    
    // Test 3: Créer des messages de test
    echo "<div class='info'>";
    echo "<h2>🧪 TEST 3: CRÉATION MESSAGES DE TEST</h2>";
    
    $testMessages = [
        [1001, 1002, "Message privé Parent1 → Parent2"],
        [1002, 1001, "Réponse privée Parent2 → Parent1"],
        [1001, 1003, "Message Parent1 → Enseignant"],
        [1003, 1001, "Réponse Enseignant → Parent1"],
        [1002, 1004, "Message Parent2 → Admin"],
        [1004, 1002, "Réponse Admin → Parent2"],
        [1003, 1004, "Message Enseignant → Admin"]
    ];
    
    // Supprimer les anciens messages de test
    $pdo->exec("DELETE FROM messages WHERE expediteur_id >= 1001 AND destinataire_id >= 1001");
    
    foreach ($testMessages as $msg) {
        $stmt = $pdo->prepare("INSERT INTO messages (expediteur_id, destinataire_id, message) VALUES (?, ?, ?)");
        $stmt->execute($msg);
    }
    
    echo "<div class='security-check'>";
    echo "<h3>✅ Messages de test créés</h3>";
    echo "<p>" . count($testMessages) . " messages de test créés pour l'audit</p>";
    echo "</div>";
    echo "</div>";
    
    // Test 4: AUDIT DE CONFIDENTIALITÉ STRICTE
    echo "<div class='warning'>";
    echo "<h2>🔒 TEST 4: AUDIT DE CONFIDENTIALITÉ STRICTE</h2>";
    echo "<p><strong>RÈGLE:</strong> Chaque utilisateur ne doit voir QUE les messages où il est expéditeur OU destinataire</p>";
    
    $auditResults = [];
    
    foreach ($testUsers as $user) {
        $userId = $user['id'];
        $userName = $user['nom'];
        
        // Requête STRICTE: Messages où l'utilisateur est impliqué
        $stmt = $pdo->prepare("
            SELECT m.*, 
                   u1.nom as expediteur_nom, 
                   u2.nom as destinataire_nom
            FROM messages m
            JOIN utilisateurs u1 ON m.expediteur_id = u1.id
            JOIN utilisateurs u2 ON m.destinataire_id = u2.id
            WHERE (m.expediteur_id = ? OR m.destinataire_id = ?)
            AND m.supprime_par_expediteur = 0 
            AND m.supprime_par_destinataire = 0
            ORDER BY m.date_envoi DESC
        ");
        $stmt->execute([$userId, $userId]);
        $userMessages = $stmt->fetchAll();
        
        // Vérifier que TOUS les messages retournés impliquent bien cet utilisateur
        $isSecure = true;
        $violations = [];
        
        foreach ($userMessages as $message) {
            if ($message['expediteur_id'] != $userId && $message['destinataire_id'] != $userId) {
                $isSecure = false;
                $violations[] = "Message ID {$message['id']} ne devrait pas être visible";
            }
        }
        
        $auditResults[] = [
            'user' => $userName,
            'user_id' => $userId,
            'messages_count' => count($userMessages),
            'is_secure' => $isSecure,
            'violations' => $violations
        ];
    }
    
    // Afficher les résultats de l'audit
    echo "<table>";
    echo "<tr><th>Utilisateur</th><th>ID</th><th>Messages Visibles</th><th>Statut Sécurité</th><th>Violations</th></tr>";
    
    $allSecure = true;
    foreach ($auditResults as $result) {
        $statusClass = $result['is_secure'] ? 'status-ok' : 'status-fail';
        $statusText = $result['is_secure'] ? '✅ SÉCURISÉ' : '❌ VIOLATION';
        
        if (!$result['is_secure']) {
            $allSecure = false;
        }
        
        echo "<tr>";
        echo "<td>{$result['user']}</td>";
        echo "<td>{$result['user_id']}</td>";
        echo "<td>{$result['messages_count']}</td>";
        echo "<td class='$statusClass'>$statusText</td>";
        echo "<td>" . (empty($result['violations']) ? 'Aucune' : implode('<br>', $result['violations'])) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if ($allSecure) {
        echo "<div class='security-check'>";
        echo "<h3>🛡️ CONFIDENTIALITÉ STRICTE RESPECTÉE</h3>";
        echo "<p>✅ Tous les utilisateurs ne voient que leurs propres conversations</p>";
        echo "<p>✅ Aucune violation de confidentialité détectée</p>";
        echo "</div>";
    } else {
        echo "<div class='security-fail'>";
        echo "<h3>🚨 VIOLATIONS DE CONFIDENTIALITÉ DÉTECTÉES</h3>";
        echo "<p>❌ Des utilisateurs peuvent voir des messages qui ne les concernent pas</p>";
        echo "</div>";
    }
    echo "</div>";
    
    // Test 5: Test des requêtes API
    echo "<div class='info'>";
    echo "<h2>🔧 TEST 5: SIMULATION REQUÊTES API</h2>";
    
    // Simuler l'appel API pour chaque utilisateur
    foreach ($testUsers as $user) {
        echo "<h4>👤 Test pour: {$user['nom']} (ID: {$user['id']})</h4>";
        
        // Simuler getConversations()
        $stmt = $pdo->prepare("
            SELECT DISTINCT 
                CASE 
                    WHEN m.expediteur_id = ? THEN m.destinataire_id 
                    ELSE m.expediteur_id 
                END as contact_id,
                CASE 
                    WHEN m.expediteur_id = ? THEN u2.nom
                    ELSE u1.nom
                END as contact_nom
            FROM messages m
            LEFT JOIN utilisateurs u1 ON m.expediteur_id = u1.id
            LEFT JOIN utilisateurs u2 ON m.destinataire_id = u2.id
            WHERE (m.expediteur_id = ? OR m.destinataire_id = ?)
            AND ((m.expediteur_id = ? AND m.supprime_par_expediteur = 0) OR 
                 (m.destinataire_id = ? AND m.supprime_par_destinataire = 0))
            GROUP BY contact_id, contact_nom
            HAVING contact_id IS NOT NULL AND contact_id != ?
        ");
        
        $userId = $user['id'];
        $stmt->execute([$userId, $userId, $userId, $userId, $userId, $userId, $userId]);
        $conversations = $stmt->fetchAll();
        
        echo "<div class='code-block'>";
        echo "Conversations visibles: " . count($conversations) . "\n";
        foreach ($conversations as $conv) {
            echo "- Contact: {$conv['contact_nom']} (ID: {$conv['contact_id']})\n";
        }
        echo "</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ ERREUR AUDIT</h2>";
    echo "<p>Erreur: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

        <div class="success">
            <h2>🎯 RECOMMANDATIONS DE SÉCURITÉ</h2>
            <ul>
                <li><strong>✅ Requêtes SQL blindées</strong> - Toutes les requêtes utilisent des paramètres préparés</li>
                <li><strong>✅ Filtrage strict</strong> - Condition (expediteur_id = user_id OR destinataire_id = user_id)</li>
                <li><strong>✅ Double vérification</strong> - Contrôles multiples dans les requêtes</li>
                <li><strong>✅ Logs de sécurité</strong> - Traçabilité des accès</li>
                <li><strong>✅ Gestion des suppressions</strong> - Respect des flags de suppression</li>
            </ul>
        </div>
        
        <div class="info">
            <h2>🔍 POINTS DE CONTRÔLE IMPLÉMENTÉS</h2>
            
            <div class="test-grid">
                <div class="test-card">
                    <h3>🛡️ Niveau Base de Données</h3>
                    <ul>
                        <li>Requêtes avec WHERE strict</li>
                        <li>Paramètres préparés (PDO)</li>
                        <li>Jointures sécurisées</li>
                        <li>Index sur colonnes critiques</li>
                    </ul>
                </div>
                
                <div class="test-card">
                    <h3>🔒 Niveau Application</h3>
                    <ul>
                        <li>Authentification requise</li>
                        <li>Vérification des rôles</li>
                        <li>Validation des IDs utilisateur</li>
                        <li>Contrôle d'accès par endpoint</li>
                    </ul>
                </div>
                
                <div class="test-card">
                    <h3>📊 Niveau Audit</h3>
                    <ul>
                        <li>Logs de tous les accès</li>
                        <li>Traçabilité des requêtes</li>
                        <li>Monitoring des violations</li>
                        <li>Alertes de sécurité</li>
                    </ul>
                </div>
                
                <div class="test-card">
                    <h3>🚫 Niveau Suppression</h3>
                    <ul>
                        <li>Suppression logique (flags)</li>
                        <li>Respect des préférences utilisateur</li>
                        <li>Pas de suppression physique</li>
                        <li>Récupération possible</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="warning">
            <h2>⚠️ RÈGLES DE CONFIDENTIALITÉ STRICTE</h2>
            <div class="code-block">
🔒 RÈGLE 1: Accès bilatéral uniquement
   - Seuls expéditeur ET destinataire peuvent voir le message
   - Aucun autre utilisateur, même avec le même rôle

🛡️ RÈGLE 2: Isolation complète des conversations  
   - Chaque conversation est un tunnel privé
   - Pas de visibilité croisée entre utilisateurs

🚫 RÈGLE 3: Pas d'accès administrateur global
   - Même les admins ne voient que leurs propres messages
   - Pas de "mode espion" ou de surveillance

📊 RÈGLE 4: Logs de sécurité uniquement
   - Traçabilité pour audit de sécurité
   - Pas d'accès au contenu des messages
            </div>
        </div>
        
        <div style="text-align: center; margin: 40px 0;">
            <a href="guide_correction_final.php" class="btn">🔧 Retour au Guide</a>
            <a href="test_react_simulation.html" class="btn">🧪 Test React</a>
            <a href="http://localhost:3000/messagerie" target="_blank" class="btn">🚀 Utiliser Messagerie</a>
        </div>
    </div>
</body>
</html>
