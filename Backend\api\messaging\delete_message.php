<?php
/**
 * API pour supprimer un message avec système à deux niveaux
 * - sender_only: Supprime pour l'expéditeur uniquement
 * - both_sides: Supprime pour les deux parties (expéditeur seulement)
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Vérification de la méthode HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Méthode non autorisée', 405);
    }
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('Données JSON invalides', 400);
    }
    
    // Validation des paramètres requis
    validateRequiredParams($input, ['message_id', 'user_id', 'delete_type']);
    
    $messageId = (int)$input['message_id'];
    $userId = (int)$input['user_id'];
    $deleteType = $input['delete_type'];
    
    // Validation des données
    if ($messageId <= 0 || $userId <= 0) {
        sendErrorResponse('IDs invalides', 400);
    }
    
    if (!in_array($deleteType, ['sender_only', 'both_sides'])) {
        sendErrorResponse('Type de suppression invalide', 400);
    }
    
    // Vérification des droits d'accès à la messagerie
    verifyMessagingAccess($userId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Vérification que le message existe et récupération des informations
    $stmt = $pdo->prepare("
        SELECT id, expediteur_id, destinataire_id, message
        FROM messages 
        WHERE id = ?
    ");
    
    $stmt->execute([$messageId]);
    $message = $stmt->fetch();
    
    if (!$message) {
        sendErrorResponse('Message non trouvé', 404);
    }
    
    // Vérification des droits de suppression
    $isExpeditor = ($message['expediteur_id'] == $userId);
    $isDestinataire = ($message['destinataire_id'] == $userId);
    
    if (!$isExpeditor && !$isDestinataire) {
        sendErrorResponse('Vous n\'êtes pas autorisé à supprimer ce message', 403);
    }
    
    // Logique de suppression selon le type
    if ($deleteType === 'sender_only') {
        // Suppression pour l'utilisateur uniquement
        if ($isExpeditor) {
            $stmt = $pdo->prepare("
                UPDATE messages 
                SET supprime_par_expediteur = 1 
                WHERE id = ? AND expediteur_id = ?
            ");
            $stmt->execute([$messageId, $userId]);
            $action = 'Message supprimé pour l\'expéditeur';
        } else {
            $stmt = $pdo->prepare("
                UPDATE messages 
                SET supprime_par_destinataire = 1 
                WHERE id = ? AND destinataire_id = ?
            ");
            $stmt->execute([$messageId, $userId]);
            $action = 'Message supprimé pour le destinataire';
        }
    } else { // both_sides
        // Suppression pour tout le monde (seul l'expéditeur peut faire cela)
        if (!$isExpeditor) {
            sendErrorResponse('Seul l\'expéditeur peut supprimer le message pour tout le monde', 403);
        }
        
        $stmt = $pdo->prepare("
            UPDATE messages 
            SET 
                supprime_par_expediteur = 1,
                supprime_par_destinataire = 1,
                supprime_expediteur = 1,
                supprime_destinataire = 1
            WHERE id = ? AND expediteur_id = ?
        ");
        $stmt->execute([$messageId, $userId]);
        $action = 'Message supprimé pour tout le monde';
    }
    
    if ($stmt->rowCount() === 0) {
        sendErrorResponse('Aucune suppression effectuée', 400);
    }
    
    // Log de l'activité
    logActivity($userId, 'DELETE_MESSAGE', "Message ID: $messageId - $action");
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'message' => $action,
        'data' => [
            'message_id' => $messageId,
            'user_id' => $userId,
            'delete_type' => $deleteType,
            'action' => $action
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Erreur delete_message.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors de la suppression du message', 500);
}
?>
