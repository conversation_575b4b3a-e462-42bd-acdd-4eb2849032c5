<?php
/**
 * API pour récupérer les conversations de l'utilisateur connecté
 * Avec confidentialité stricte - seules les conversations de l'utilisateur
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Récupération et validation des paramètres
    $userId = $_GET['user_id'] ?? null;
    
    if (!$userId || !is_numeric($userId)) {
        sendErrorResponse('ID utilisateur manquant ou invalide', 400);
    }
    
    $userId = (int)$userId;
    
    // Vérification des droits d'accès à la messagerie
    verifyMessagingAccess($userId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Requête pour récupérer les conversations avec confidentialité stricte
    // Seules les conversations où l'utilisateur est expéditeur OU destinataire
    $stmt = $pdo->prepare("
        SELECT DISTINCT
            CASE 
                WHEN m.expediteur_id = ? THEN m.destinataire_id
                ELSE m.expediteur_id
            END as other_user_id,
            CASE 
                WHEN m.expediteur_id = ? THEN dest.nom
                ELSE exp.nom
            END as other_user_name,
            CASE 
                WHEN m.expediteur_id = ? THEN dest_role.nom
                ELSE exp_role.nom
            END as other_user_role,
            MAX(m.date_envoi) as last_message_date,
            (
                SELECT m2.message 
                FROM messages m2 
                WHERE (
                    (m2.expediteur_id = ? AND m2.destinataire_id = CASE WHEN m.expediteur_id = ? THEN m.destinataire_id ELSE m.expediteur_id END)
                    OR 
                    (m2.destinataire_id = ? AND m2.expediteur_id = CASE WHEN m.expediteur_id = ? THEN m.destinataire_id ELSE m.expediteur_id END)
                )
                AND (
                    (m2.expediteur_id = ? AND m2.supprime_par_expediteur = 0)
                    OR 
                    (m2.destinataire_id = ? AND m2.supprime_par_destinataire = 0)
                )
                ORDER BY m2.date_envoi DESC 
                LIMIT 1
            ) as last_message,
            (
                SELECT COUNT(*) 
                FROM messages m3 
                WHERE m3.destinataire_id = ? 
                AND m3.expediteur_id = CASE WHEN m.expediteur_id = ? THEN m.destinataire_id ELSE m.expediteur_id END
                AND m3.lu = 0
                AND m3.supprime_par_destinataire = 0
            ) as unread_count
        FROM messages m
        JOIN utilisateurs exp ON m.expediteur_id = exp.id
        JOIN utilisateurs dest ON m.destinataire_id = dest.id
        JOIN roles exp_role ON exp.role_id = exp_role.id
        JOIN roles dest_role ON dest.role_id = dest_role.id
        WHERE (m.expediteur_id = ? OR m.destinataire_id = ?)
        AND (
            (m.expediteur_id = ? AND m.supprime_par_expediteur = 0)
            OR 
            (m.destinataire_id = ? AND m.supprime_par_destinataire = 0)
        )
        GROUP BY other_user_id, other_user_name, other_user_role
        ORDER BY last_message_date DESC
    ");
    
    // Exécution avec tous les paramètres nécessaires
    $stmt->execute([
        $userId, $userId, $userId,  // Pour déterminer l'autre utilisateur
        $userId, $userId, $userId, $userId,  // Pour le dernier message
        $userId, $userId,  // Pour le dernier message (suite)
        $userId, $userId,  // Pour le compteur non lus
        $userId, $userId,  // Pour la condition WHERE principale
        $userId, $userId   // Pour la condition de suppression
    ]);
    
    $conversations = $stmt->fetchAll();
    
    // Formatage des données
    $formattedConversations = [];
    foreach ($conversations as $conv) {
        $formattedConversations[] = [
            'other_user_id' => (int)$conv['other_user_id'],
            'other_user_name' => $conv['other_user_name'],
            'other_user_role' => $conv['other_user_role'],
            'last_message' => $conv['last_message'] ?? '',
            'last_message_date' => $conv['last_message_date'],
            'unread_count' => (int)$conv['unread_count']
        ];
    }
    
    // Log de l'activité
    logActivity($userId, 'GET_CONVERSATIONS', 'Récupération des conversations');
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'conversations' => $formattedConversations,
        'total' => count($formattedConversations),
        'user_id' => $userId
    ]);
    
} catch (Exception $e) {
    error_log("Erreur get_conversations.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors de la récupération des conversations', 500);
}
?>
