import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Retards.css';

const Retards = () => {
    const { user } = useContext(AuthContext);
    const [retards, setRetards] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingRetard, setEditingRetard] = useState(null);
    const [etudiants, setEtudiants] = useState([]);
    const [matieres, setMatieres] = useState([]);
    const [enseignants, setEnseignants] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        etudiant_id: '',
        matiere_id: '',
        enseignant_id: '',
        date_retard: '',
        duree_retard: '',
        justification: ''
    });

    useEffect(() => {
        fetchRetards();
        if (user?.role === 'admin' || user?.role === 'enseignant') {
            fetchEtudiants();
            fetchMatieres();
            if (user?.role === 'admin') {
                fetchenseignants();
            }
        }
    }, []);

    const fetchRetards = async () => {
        try {
            console.log('🔄 Chargement des retards...');

            // Utiliser la même API que les absences (qui fonctionne)
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/retards/index.php');

            console.log('🔍 DEBUG RETARDS API Response:', response.data);
            console.log('🔍 Status:', response.status);

            // Vérifier si la réponse est un tableau (même logique que les absences)
            if (Array.isArray(response.data)) {
                setRetards(response.data);
                console.log('✅ Retards chargés:', response.data.length);
            } else if (response.data && response.data.error) {
                console.error('❌ Erreur API retards:', response.data.error);
                setRetards([]);
            } else {
                console.warn('⚠️ Format de réponse inattendu:', response.data);
                setRetards([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des retards:', error);
            console.error('❌ Détails erreur:', error.response?.data || error.message);
            setRetards([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            console.log('🔄 Chargement des étudiants...');

            // Utiliser la même API que les absences (qui fonctionne)
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');

            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);

            if (response.data && response.data.success) {
                setEtudiants(response.data.etudiants);
                console.log('✅ Étudiants chargés:', response.data.etudiants.length);
                if (response.data.etudiants.length > 0) {
                    console.log('📚 Premier étudiant:', response.data.etudiants[0]);
                }
            } else {
                console.error('❌ Erreur API étudiants:', response.data.error);
                setEtudiants([]);
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des étudiants:', error);
            console.error('❌ Détails erreur:', error.response?.data || error.message);
            setEtudiants([]);
        }
    };

    const fetchMatieres = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setMatieres(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des matières:', error);
        }
    };

    const fetchenseignants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setEnseignants(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des enseignants:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Debug : Vérifier les données avant envoi
        console.log('🔍 DEBUG SUBMIT RETARDS:');
        console.log('FormData avant envoi:', formData);
        console.log('Type de etudiant_id:', typeof formData.etudiant_id);
        console.log('Valeur de etudiant_id:', formData.etudiant_id);
        console.log('Étudiants disponibles:', etudiants);
        console.log('Nombre d\'étudiants:', etudiants.length);

        // Validation des données avec messages détaillés
        if (!formData.etudiant_id || formData.etudiant_id === '') {
            console.error('❌ Erreur: etudiant_id vide ou undefined');
            console.log('FormData complet:', formData);
            Swal.fire('Erreur', 'Veuillez sélectionner un étudiant. Liste disponible: ' + etudiants.length + ' étudiant(s)', 'error');
            return;
        }

        if (!formData.date_retard || formData.date_retard === '') {
            console.error('❌ Erreur: date_retard vide ou undefined');
            Swal.fire('Erreur', 'Veuillez sélectionner une date', 'error');
            return;
        }

        if (!formData.duree_retard || formData.duree_retard === '') {
            console.error('❌ Erreur: duree_retard vide ou undefined');
            Swal.fire('Erreur', 'Veuillez saisir la durée du retard', 'error');
            return;
        }

        // S'assurer que les IDs sont des nombres
        const cleanData = {
            etudiant_id: parseInt(formData.etudiant_id),
            matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,
            enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,
            date_retard: formData.date_retard,
            duree_retard: formData.duree_retard,
            justification: formData.justification || null
        };

        console.log('🔍 Données nettoyées:', cleanData);

        try {
            // Utiliser la même API que les absences (qui fonctionne)
            const url = 'http://localhost/Project_PFE/Backend/pages/retards/index.php';
            const method = editingRetard ? 'PUT' : 'POST';
            const data = editingRetard ? { ...cleanData, id: editingRetard.id } : cleanData;

            console.log('🔍 Données finales envoyées:', data);

            const response = await axios({
                method,
                url,
                data,
                headers: { 'Content-Type': 'application/json' }
            });

            console.log('✅ Réponse serveur:', response.data);

            Swal.fire('Succès', `Retard ${editingRetard ? 'modifié' : 'enregistré'} avec succès`, 'success');
            setShowModal(false);
            setEditingRetard(null);
            resetForm();
            fetchRetards();
        } catch (error) {
            console.error('❌ Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (retard) => {
        setEditingRetard(retard);
        setFormData({
            etudiant_id: retard.etudiant_id,
            matiere_id: retard.matiere_id || '',
            enseignant_id: retard.enseignant_id || '',
            date_retard: retard.date_retard,
            duree_retard: retard.duree_retard,
            justification: retard.justification || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                // Utiliser la même API que les absences (qui fonctionne)
                await axios.delete('http://localhost/Project_PFE/Backend/pages/retards/index.php', {
                    headers: { 'Content-Type': 'application/json' },
                    data: { id }
                });
                Swal.fire('Supprimé!', 'Le retard a été supprimé.', 'success');
                fetchRetards();
            } catch (error) {
                console.error('❌ Erreur:', error);
                Swal.fire('Erreur', 'Impossible de supprimer le retard', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            matiere_id: '',
            enseignant_id: '',
            date_retard: '',
            duree_retard: '',
            justification: ''
        });
    };

    // Fonctions de filtrage et pagination (comme les factures)
    const filteredRetards = retards.filter(retard => {
        const matchesSearch = retard.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            retard.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            retard.enseignant_nom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === 'all' ||
                            (statusFilter === 'justified' && retard.justification) ||
                            (statusFilter === 'unjustified' && !retard.justification);

        return matchesSearch && matchesStatus;
    });

    // Pagination
    const totalPages = Math.ceil(filteredRetards.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const currentRetards = filteredRetards.slice(startIndex, startIndex + itemsPerPage);

    const getJustificationBadge = (justification) => {
        if (justification && justification.trim()) {
            return <span className="badge badge-success">Justifié</span>;
        }
        return <span className="badge badge-danger">Non justifié</span>;
    };

    const formatDate = (dateString) => {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleDateString('fr-FR');
    };

    // Vérifier si l'utilisateur est admin ou enseignant
    const canManageRetards = user?.role === 'admin' || user?.role === 'enseignant';

    const formatDuree = (duree) => {
        // Convertir le format TIME en minutes
        const [hours, minutes] = duree.split(':');
        const totalMinutes = parseInt(hours) * 60 + parseInt(minutes);
        
        if (totalMinutes < 60) {
            return `${totalMinutes} min`;
        } else {
            const h = Math.floor(totalMinutes / 60);
            const m = totalMinutes % 60;
            return m > 0 ? `${h}h ${m}min` : `${h}h`;
        }
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des retards...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>⏰ Gestion des Retards</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredRetards.length} retard(s) trouvé(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {canManageRetards && (
                        <button
                            className="btn btn-primary add-button"
                            onClick={() => setShowModal(true)}
                            title="Ajouter un nouveau retard - Enregistrer le retard d'un étudiant avec date, durée et justification"
                        >
                            <img src="/plus.png" alt="Ajouter" />
                            <span>Nouveau Retard</span>
                            <div className="button-info">
                                <small>📅 Date • ⏱️ Durée • 👤 Étudiant • 💬 Justification</small>
                            </div>
                        </button>
                    )}
                </div>
            </div>

            {/* Filtres et recherche (comme les factures) */}
            <div className="filters-section">
                <div className="search-filters">
                    <div className="search-box">
                        <input
                            type="text"
                            placeholder="🔍 Rechercher par étudiant, matière ou enseignant..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="search-input"
                        />
                    </div>
                    <div className="filter-group">
                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="filter-select"
                        >
                            <option value="all">📊 Tous les statuts</option>
                            <option value="justified">✅ Justifiés</option>
                            <option value="unjustified">❌ Non justifiés</option>
                        </select>
                    </div>
                </div>
                <div className="results-info">
                    <span className="total-count">
                        {filteredRetards.length} retard(s) trouvé(s)
                    </span>
                </div>
            </div>

            <div className="factures-grid">
                {filteredRetards.length === 0 ? (
                    <div className="no-data">
                        <img src="/clock.png" alt="Aucun retard" />
                        <p>Aucun retard trouvé</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👤 Étudiant</th>
                                    <th>📚 Matière</th>
                                    <th>📅 Date</th>
                                    <th>⏱️ Durée</th>
                                    <th>📝 Statut</th>
                                    <th>💬 Justification</th>
                                    {canManageRetards && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentRetards.map((retard) => (
                                    <tr key={retard.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{retard.etudiant_nom}</strong>
                                                <small>{retard.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#e8f5e8',
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {retard.matiere_nom || '-'}
                                            </span>
                                        </td>
                                       
                                        <td>
                                            <strong style={{ color: '#fd7e14', fontSize: '1em' }}>
                                                {formatDate(retard.date_retard)}
                                            </strong>
                                        </td>
                                        <td>
                                            <span style={{
                                                padding: '4px 8px',
                                                backgroundColor: '#ffeaa7',
                                                borderRadius: '4px',
                                                fontSize: '0.9em',
                                                fontWeight: 'bold',
                                                color: '#d63031'
                                            }}>
                                                {formatDuree(retard.duree_retard)}
                                            </span>
                                        </td>
                                        <td>{getJustificationBadge(retard.justification)}</td>
                                        <td>
                                            {retard.justification ? (
                                                <span
                                                    className="justification-text"
                                                    title={retard.justification}
                                                    style={{
                                                        display: 'inline-block',
                                                        maxWidth: '200px',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis',
                                                        whiteSpace: 'nowrap',
                                                        padding: '4px 8px',
                                                        backgroundColor: '#f8f9fa',
                                                        borderRadius: '4px',
                                                        fontSize: '0.9em'
                                                    }}
                                                >
                                                    {retard.justification}
                                                </span>
                                            ) : (
                                                <span style={{ color: '#6c757d', fontStyle: 'italic' }}>
                                                    Aucune justification
                                                </span>
                                            )}
                                        </td>
                                        {canManageRetards && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button
                                                        className="btn btn-sm btn-warning edit-button"
                                                        onClick={() => handleEdit(retard)}
                                                        title={`Modifier le retard de ${retard.etudiant_nom} du ${formatDate(retard.date_retard)} (${formatDuree(retard.duree_retard)})`}
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                        <span className="btn-text">Modifier</span>
                                                        <div className="btn-info">
                                                            <small>✏️ Éditer durée/justification</small>
                                                        </div>
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger delete-button"
                                                        onClick={() => handleDelete(retard.id)}
                                                        title={`Supprimer définitivement le retard de ${retard.etudiant_nom} du ${formatDate(retard.date_retard)} (${formatDuree(retard.duree_retard)})`}
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                        <span className="btn-text">Supprimer</span>
                                                        <div className="btn-info">
                                                            <small>🗑️ Suppression définitive</small>
                                                        </div>
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}

                {/* Pagination (comme les factures) */}
                {filteredRetards.length > itemsPerPage && (
                    <div className="pagination-container">
                        <div className="pagination-info">
                            Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, filteredRetards.length)} sur {filteredRetards.length} retards
                        </div>
                        <div className="pagination">
                            <button
                                className="pagination-btn"
                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                disabled={currentPage === 1}
                            >
                                ← Précédent
                            </button>

                            {[...Array(totalPages)].map((_, index) => {
                                const pageNumber = index + 1;
                                if (
                                    pageNumber === 1 ||
                                    pageNumber === totalPages ||
                                    (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                                ) {
                                    return (
                                        <button
                                            key={pageNumber}
                                            className={`pagination-btn ${currentPage === pageNumber ? 'active' : ''}`}
                                            onClick={() => setCurrentPage(pageNumber)}
                                        >
                                            {pageNumber}
                                        </button>
                                    );
                                } else if (
                                    pageNumber === currentPage - 2 ||
                                    pageNumber === currentPage + 2
                                ) {
                                    return <span key={pageNumber} className="pagination-ellipsis">...</span>;
                                }
                                return null;
                            })}

                            <button
                                className="pagination-btn"
                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                disabled={currentPage === totalPages}
                            >
                                Suivant →
                            </button>
                        </div>
                    </div>
                )}
            </div>

            {/* Modal pour ajouter/modifier un retard */}
            {showModal && canManageRetards && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingRetard ? 'Modifier le retard' : 'Nouveau retard'}</h3>
                            <button 
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingRetard(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                    disabled={editingRetard}
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.etudiant_id || etudiant.id} value={etudiant.etudiant_id || etudiant.id}>
                                            {etudiant.nom} {etudiant.prenom ? ` ${etudiant.prenom}` : ''} - {etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe'}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Matière (optionnel)</label>
                                <select
                                    value={formData.matiere_id}
                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}
                                >
                                    <option value="">Sélectionner une matière</option>
                                    {matieres.map((matiere) => (
                                        <option key={matiere.id} value={matiere.id}>
                                            {matiere.nom}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            {user?.role === 'admin' && (
                                <div className="form-group">
                                    <label>Enseignant (optionnel)</label>
                                    <select
                                        value={formData.enseignant_id}
                                        onChange={(e) => setFormData({...formData, enseignant_id: e.target.value})}
                                    >
                                        <option value="">Sélectionner un enseignant</option>
                                        {enseignants.map((enseignant) => (
                                            <option key={enseignant.id} value={enseignant.id}>
                                                {enseignant.nom}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            )}
                            <div className="form-group">
                                <label>Date du retard</label>
                                <input
                                    type="date"
                                    value={formData.date_retard}
                                    onChange={(e) => setFormData({...formData, date_retard: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Durée du retard</label>
                                <input
                                    type="time"
                                    value={formData.duree_retard}
                                    onChange={(e) => setFormData({...formData, duree_retard: e.target.value})}
                                    required
                                />
                                <small style={{color: '#6c757d', fontSize: '0.85em'}}>
                                    Format: HH:MM (ex: 00:15 pour 15 minutes)
                                </small>
                            </div>
                            <div className="form-group">
                                <label>Justification (optionnel)</label>
                                <textarea
                                    value={formData.justification}
                                    onChange={(e) => setFormData({...formData, justification: e.target.value})}
                                    placeholder="Motif du retard..."
                                    rows="3"
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        resize: 'vertical'
                                    }}
                                />
                            </div>
                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingRetard ? 'Modifier' : 'Enregistrer'}
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingRetard(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Retards;
