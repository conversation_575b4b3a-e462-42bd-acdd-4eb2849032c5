import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const Factures = () => {
    const { user } = useContext(AuthContext);
    const [factures, setFactures] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingFacture, setEditingFacture] = useState(null);
    const [etudiants, setEtudiants] = useState([]);
    const [formData, setFormData] = useState({
        etudiant_id: '',
        mois: '',
        montant: '',
        statut: 'Non payé',
        date_paiement: ''
    });

    useEffect(() => {
        fetchFactures();
        if (user?.role === 'Admin') {
            fetchEtudiants();
        }
    }, []);

    const fetchFactures = async () => {
        try {
            const token = localStorage.getItem('token');
            const res = await axios.get('http://localhost/SchoolProject/pages/utilisateurs/getUsers.php');

            const response = await axios.get('http://localhost:80/Project_PFE/Backend/pages/factures/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setFactures(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des factures:', error);
            Swal.fire('Erreur', 'Impossible de charger les factures', 'error');
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setEtudiants(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des étudiants:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const token = localStorage.getItem('token');
            const url = editingFacture 
                ? 'http://localhost/Project_PFE/Backend/pages/factures/'
                : 'http://localhost/Project_PFE/Backend/pages/factures/';
            
            const method = editingFacture ? 'PUT' : 'POST';
            const data = editingFacture ? { ...formData, id: editingFacture.id } : formData;

            await axios({
                method,
                url,
                data,
                headers: { Authorization: `Bearer ${token}` }
            });

            Swal.fire('Succès', `Facture ${editingFacture ? 'modifiée' : 'créée'} avec succès`, 'success');
            setShowModal(false);
            setEditingFacture(null);
            resetForm();
            fetchFactures();
        } catch (error) {
            console.error('Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (facture) => {
        setEditingFacture(facture);
        setFormData({
            etudiant_id: facture.etudiant_id,
            mois: facture.mois,
            montant: facture.montant,
            statut: facture.statut,
            date_paiement: facture.date_paiement || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                await axios.delete('http://localhost/Project_PFE/Backend/pages/factures/', {
                    headers: { Authorization: `Bearer ${token}` },
                    data: { id }
                });
                Swal.fire('Supprimé!', 'La facture a été supprimée.', 'success');
                fetchFactures();
            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire('Erreur', 'Impossible de supprimer la facture', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            mois: '',
            montant: '',
            statut: 'Non payé',
            date_paiement: ''
        });
    };

    const formatMontant = (montant) => {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'MAD'
        }).format(montant);
    };

    const getStatutBadge = (statut) => {
        const badgeClass = statut === 'Payé' ? 'badge-success' : 'badge-danger';
        return <span className={`badge ${badgeClass}`}>{statut}</span>;
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des factures...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>Gestion des Factures</h1>
                {user?.role === 'Admin' && (
                    <button 
                        className="btn btn-primary"
                        onClick={() => setShowModal(true)}
                    >
                        <img src="/plus.png" alt="Ajouter" /> Nouvelle Facture
                    </button>
                )}
            </div>

            <div className="factures-grid">
                {factures.length === 0 ? (
                    <div className="no-data">
                        <img src="/finance.png" alt="Aucune facture" />
                        <p>Aucune facture trouvée</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>Étudiant</th>
                                    <th>Mois</th>
                                    <th>Montant</th>
                                    <th>Statut</th>
                                    <th>Date de paiement</th>
                                    {user?.role === 'Admin' && <th>Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {factures.map((facture) => (
                                    <tr key={facture.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{facture.etudiant_nom}</strong>
                                                <small>{facture.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>{facture.mois}</td>
                                        <td>{formatMontant(facture.montant)}</td>
                                        <td>{getStatutBadge(facture.statut)}</td>
                                        <td>
                                            {facture.date_paiement 
                                                ? new Date(facture.date_paiement).toLocaleDateString('fr-FR')
                                                : '-'
                                            }
                                        </td>
                                        {user?.role === 'Admin' && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button 
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(facture)}
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button 
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(facture.id)}
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Modal pour ajouter/modifier une facture */}
            {showModal && user?.role === 'Admin' && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingFacture ? 'Modifier la facture' : 'Nouvelle facture'}</h3>
                            <button 
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingFacture(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                    disabled={editingFacture}
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.id} value={etudiant.id}>
                                            {etudiant.nom} - {etudiant.email}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-group">
                                <label>Mois (YYYY-MM)</label>
                                <input
                                    type="month"
                                    value={formData.mois}
                                    onChange={(e) => setFormData({...formData, mois: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Montant (MAD)</label>
                                <input
                                    type="number"
                                    step="0.01"
                                    value={formData.montant}
                                    onChange={(e) => setFormData({...formData, montant: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Statut</label>
                                <select
                                    value={formData.statut}
                                    onChange={(e) => setFormData({...formData, statut: e.target.value})}
                                >
                                    <option value="Non payé">Non payé</option>
                                    <option value="Payé">Payé</option>
                                </select>
                            </div>
                            {formData.statut === 'Payé' && (
                                <div className="form-group">
                                    <label>Date de paiement</label>
                                    <input
                                        type="date"
                                        value={formData.date_paiement}
                                        onChange={(e) => setFormData({...formData, date_paiement: e.target.value})}
                                    />
                                </div>
                            )}
                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingFacture ? 'Modifier' : 'Créer'}
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingFacture(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Factures;
