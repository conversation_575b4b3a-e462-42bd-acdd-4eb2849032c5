{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\Enseignants.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport '../css/Factures.css';\nconst Enseignants = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [enseignants, setEnseignants] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingEnseignant, setEditingEnseignant] = useState(null);\n  const [utilisateursEnseignants, setUtilisateursEnseignants] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [formData, setFormData] = useState({\n    utilisateur_id: '',\n    nom_prenom: '',\n    email: '',\n    telephone: '',\n    specialite: '',\n    date_embauche: '',\n    salaire: '',\n    statut: 'actif'\n  });\n\n  // Vérifier si l'utilisateur est Admin\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n  useEffect(() => {\n    fetchEnseignants();\n    if (isAdmin) {\n      fetchUtilisateursEnseignants();\n    }\n  }, [isAdmin]);\n  const fetchEnseignants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🔄 Chargement des enseignants...');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('✅ Réponse API enseignants:', response.data);\n      if (response.data.success) {\n        setEnseignants(Array.isArray(response.data.enseignants) ? response.data.enseignants : []);\n      } else {\n        setEnseignants(Array.isArray(response.data) ? response.data : []);\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des enseignants:', error);\n      Swal.fire('Erreur', 'Impossible de charger les enseignants', 'error');\n      setEnseignants([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchUtilisateursEnseignants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🔄 Chargement des utilisateurs enseignants...');\n\n      // Récupérer tous les utilisateurs\n      const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Récupérer les enseignants existants pour les exclure\n      const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Filtrer pour ne garder que les utilisateurs avec le rôle \"enseignant\"\n      const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n      const enseignantsExistants = Array.isArray(responseEnseignants.data) ? responseEnseignants.data : [];\n      const utilisateursEnseignantsFiltered = utilisateurs.filter(user => {\n        const roleNom = (user.role_nom || user.role || '').toLowerCase();\n        return roleNom === 'enseignant' || roleNom === 'enseignants';\n      });\n\n      // Exclure les utilisateurs déjà enseignants\n      const enseignantsExistantsIds = enseignantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n      const utilisateursDisponibles = utilisateursEnseignantsFiltered.filter(user => !enseignantsExistantsIds.includes(user.id));\n      console.log('✅ Utilisateurs enseignants disponibles:', utilisateursDisponibles.length);\n      setUtilisateursEnseignants(utilisateursDisponibles);\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des utilisateurs enseignants:', error);\n      setUtilisateursEnseignants([]);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des enseignants', 'error');\n      return;\n    }\n    if (!formData.utilisateur_id || !formData.nom_prenom || !formData.email) {\n      Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php';\n      const method = editingEnseignant ? 'PUT' : 'POST';\n      const data = editingEnseignant ? {\n        ...formData,\n        id: editingEnseignant.id\n      } : formData;\n      console.log('🔄 Envoi requête enseignant:', {\n        method,\n        data\n      });\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('✅ Réponse:', response.data);\n      if (response.data.success) {\n        Swal.fire('Succès', response.data.message, 'success');\n        setShowModal(false);\n        setEditingEnseignant(null);\n        resetForm();\n        fetchEnseignants();\n        if (isAdmin) fetchUtilisateursEnseignants();\n      } else {\n        Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Erreur:', error);\n      Swal.fire('Erreur', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = async enseignant => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des enseignants', 'error');\n      return;\n    }\n\n    // Recharger les utilisateurs enseignants en incluant l'utilisateur actuel\n    await fetchUtilisateursEnseignantsAvecActuel(enseignant.utilisateur_id);\n    setEditingEnseignant(enseignant);\n    setFormData({\n      utilisateur_id: enseignant.utilisateur_id || '',\n      nom_prenom: enseignant.nom_prenom || '',\n      email: enseignant.email || '',\n      telephone: enseignant.telephone || '',\n      specialite: enseignant.specialite || '',\n      date_embauche: enseignant.date_embauche || '',\n      salaire: enseignant.salaire || '',\n      statut: enseignant.statut || 'actif'\n    });\n    setShowModal(true);\n  };\n  const fetchUtilisateursEnseignantsAvecActuel = async currentUserId => {\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🔄 Chargement des utilisateurs enseignants avec actuel...');\n\n      // Récupérer tous les utilisateurs\n      const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Récupérer les enseignants existants pour les exclure\n      const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n      const enseignantsExistants = Array.isArray(responseEnseignants.data) ? responseEnseignants.data : [];\n      const utilisateursEnseignantsFiltered = utilisateurs.filter(user => {\n        const roleNom = (user.role_nom || user.role || '').toLowerCase();\n        return roleNom === 'enseignant' || roleNom === 'enseignants';\n      });\n\n      // Exclure les utilisateurs déjà enseignants SAUF l'utilisateur actuel\n      const enseignantsExistantsIds = enseignantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n      const utilisateursDisponibles = utilisateursEnseignantsFiltered.filter(user => !enseignantsExistantsIds.includes(user.id) || user.id === currentUserId);\n      console.log('✅ Utilisateurs enseignants avec actuel:', utilisateursDisponibles.length);\n      setUtilisateursEnseignants(utilisateursDisponibles);\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des utilisateurs enseignants avec actuel:', error);\n      setUtilisateursEnseignants([]);\n    }\n  };\n  const handleDelete = async id => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des enseignants', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr ?',\n      text: 'Cette action est irréversible !',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer !',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        const token = localStorage.getItem('token');\n        console.log('🗑️ Suppression enseignant ID:', id);\n        const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        console.log('✅ Réponse suppression:', response.data);\n        if (response.data.success) {\n          Swal.fire('Supprimé!', response.data.message, 'success');\n          fetchEnseignants();\n          if (isAdmin) fetchUtilisateursEnseignants();\n        } else {\n          Swal.fire('Erreur', response.data.error || 'Impossible de supprimer l\\'enseignant', 'error');\n        }\n      } catch (error) {\n        var _error$response2, _error$response2$data;\n        console.error('❌ Erreur suppression:', error);\n        Swal.fire('Erreur', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || 'Impossible de supprimer l\\'enseignant', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      utilisateur_id: '',\n      nom_prenom: '',\n      email: '',\n      telephone: '',\n      specialite: '',\n      date_embauche: '',\n      salaire: '',\n      statut: 'actif'\n    });\n  };\n\n  // Filtrage des enseignants\n  const filteredEnseignants = enseignants.filter(enseignant => {\n    const searchLower = searchTerm.toLowerCase();\n    return (enseignant.nom_prenom || '').toLowerCase().includes(searchLower) || (enseignant.email || '').toLowerCase().includes(searchLower) || (enseignant.specialite || '').toLowerCase().includes(searchLower) || (enseignant.telephone || '').toLowerCase().includes(searchLower);\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentEnseignants = filteredEnseignants.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredEnseignants.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when search changes\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm]);\n  const getStatutBadge = statut => {\n    const style = {\n      padding: '4px 12px',\n      borderRadius: '20px',\n      fontSize: '0.8em',\n      fontWeight: 'bold',\n      textTransform: 'uppercase'\n    };\n    if (statut === 'actif') {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: {\n          ...style,\n          backgroundColor: '#d4edda',\n          color: '#155724'\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 20\n        }\n      }, \"Actif\");\n    } else {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        style: {\n          ...style,\n          backgroundColor: '#f8d7da',\n          color: '#721c24'\n        },\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 20\n        }\n      }, \"Inactif\");\n    }\n  };\n  const formatSalaire = salaire => {\n    if (!salaire) return 'Non spécifié';\n    return new Intl.NumberFormat('fr-MA', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(salaire);\n  };\n  const styles = {\n    infoMessage: {\n      backgroundColor: '#e3f2fd',\n      border: '1px solid #2196f3',\n      borderRadius: '8px',\n      padding: '15px',\n      margin: '20px 0',\n      color: '#1565c0'\n    },\n    idBadge: {\n      backgroundColor: '#007bff',\n      color: 'white',\n      padding: '4px 8px',\n      borderRadius: '12px',\n      fontSize: '0.8em',\n      fontWeight: 'bold'\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 17\n      }\n    }, \"Chargement des enseignants...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDC68\\u200D\\uD83C\\uDFEB Gestion des Enseignants\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 21\n    }\n  }, filteredEnseignants.length, \" enseignant(s) trouv\\xE9(s)\"), isAdmin && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 29\n    }\n  }), \" Nouvel Enseignant\"))), !isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.infoMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 21\n    }\n  }, \"\\u2139\\uFE0F Vous consultez les enseignants en mode lecture seule. Seul l'administrateur peut cr\\xE9er, modifier ou supprimer des enseignants.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-bar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher par nom, email, sp\\xE9cialit\\xE9, t\\xE9l\\xE9phone...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 21\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 13\n    }\n  }, filteredEnseignants.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/empty.png\",\n    alt: \"Aucune donn\\xE9e\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 25\n    }\n  }, \"Aucun enseignant trouv\\xE9\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 25\n    }\n  }, \"Essayez de modifier vos crit\\xE8res de recherche\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDD94 ID\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC64 Nom Complet\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE7 Email\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDE T\\xE9l\\xE9phone\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 403,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDF93 Sp\\xE9cialit\\xE9\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCB0 Salaire\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Statut\"), isAdmin && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 49\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 29\n    }\n  }, currentEnseignants.map(enseignant => /*#__PURE__*/React.createElement(\"tr\", {\n    key: enseignant.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.idBadge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 45\n    }\n  }, \"#\", enseignant.id)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 49\n    }\n  }, enseignant.nom_prenom || 'Nom non disponible'), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 49\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 49\n    }\n  }, enseignant.utilisateur_id ? `Utilisateur ID: ${enseignant.utilisateur_id}` : 'Pas d\\'utilisateur lié'))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 45\n    }\n  }, enseignant.email || 'Email non disponible')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 45\n    }\n  }, enseignant.telephone || 'Non renseigné')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 45\n    }\n  }, enseignant.specialite || 'Non spécifiée')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#2c3e50',\n      fontSize: '1.1em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 45\n    }\n  }, formatSalaire(enseignant.salaire))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 41\n    }\n  }, getStatutBadge(enseignant.statut)), isAdmin && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(enseignant),\n    title: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(enseignant.id),\n    title: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 21\n    }\n  }, \"Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 21\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    className: \"btn btn-outline-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 21\n    }\n  }, \"Suivant\")), showModal && isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 29\n    }\n  }, editingEnseignant ? 'Modifier l\\'enseignant' : 'Nouvel enseignant'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingEnseignant(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 33\n    }\n  }, \"Utilisateur (Enseignant) *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.utilisateur_id,\n    onChange: e => {\n      const selectedUser = utilisateursEnseignants.find(u => u.id == e.target.value);\n      setFormData({\n        ...formData,\n        utilisateur_id: e.target.value,\n        nom_prenom: selectedUser ? selectedUser.nom : formData.nom_prenom,\n        email: selectedUser ? selectedUser.email : formData.email\n      });\n    },\n    required: true,\n    disabled: editingEnseignant // Empêcher la modification de l'utilisateur lors de l'édition\n    ,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur enseignant...\"), utilisateursEnseignants.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 41\n    }\n  }, user.nom, \" - \", user.email, \" (ID: \", user.id, \")\"))), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 33\n    }\n  }, \"Seuls les utilisateurs avec le r\\xF4le \\\"enseignant\\\" non encore assign\\xE9s sont affich\\xE9s\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 33\n    }\n  }, \"Nom Complet *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    value: formData.nom_prenom,\n    onChange: e => setFormData({\n      ...formData,\n      nom_prenom: e.target.value\n    }),\n    placeholder: \"Nom complet de l'enseignant\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 33\n    }\n  }, \"Email *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"email\",\n    value: formData.email,\n    onChange: e => setFormData({\n      ...formData,\n      email: e.target.value\n    }),\n    placeholder: \"Email de l'enseignant\",\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 33\n    }\n  }, \"T\\xE9l\\xE9phone\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"tel\",\n    value: formData.telephone,\n    onChange: e => setFormData({\n      ...formData,\n      telephone: e.target.value\n    }),\n    placeholder: \"Num\\xE9ro de t\\xE9l\\xE9phone\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 33\n    }\n  }, \"Sp\\xE9cialit\\xE9\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    value: formData.specialite,\n    onChange: e => setFormData({\n      ...formData,\n      specialite: e.target.value\n    }),\n    placeholder: \"Sp\\xE9cialit\\xE9 de l'enseignant\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 33\n    }\n  }, \"Date d'embauche\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_embauche,\n    onChange: e => setFormData({\n      ...formData,\n      date_embauche: e.target.value\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 33\n    }\n  }, \"Salaire (MAD)\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    step: \"0.01\",\n    value: formData.salaire,\n    onChange: e => setFormData({\n      ...formData,\n      salaire: e.target.value\n    }),\n    placeholder: \"Salaire en dirhams\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 604,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 613,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 33\n    }\n  }, \"Statut *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.statut,\n    onChange: e => setFormData({\n      ...formData,\n      statut: e.target.value\n    }),\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"actif\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 37\n    }\n  }, \"Actif\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"inactif\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 37\n    }\n  }, \"Inactif\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 33\n    }\n  }, editingEnseignant ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingEnseignant(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default Enseignants;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "axios", "<PERSON><PERSON>", "AuthContext", "Enseignants", "user", "enseignants", "setEnseignants", "loading", "setLoading", "showModal", "setShowModal", "editingEnseignant", "setEditingEnseignant", "utilisateursEnseignants", "setUtilisateursEnseignants", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "utilisateur_id", "nom_prenom", "email", "telephone", "specialite", "date_embauche", "salaire", "statut", "isAdmin", "role", "fetchEnseignants", "fetchUtilisateursEnseignants", "token", "localStorage", "getItem", "console", "log", "response", "get", "headers", "Authorization", "data", "success", "Array", "isArray", "error", "fire", "responseUtilisateurs", "responseEnseignants", "utilisateurs", "enseignants<PERSON><PERSON><PERSON>s", "utilisateursEnseignantsFiltered", "filter", "roleNom", "role_nom", "toLowerCase", "enseignantsExistantsIds", "map", "e", "id", "utilisateursDisponibles", "includes", "length", "handleSubmit", "preventDefault", "url", "method", "message", "resetForm", "_error$response", "_error$response$data", "handleEdit", "enseignant", "fetchUtilisateursEnseignantsAvecActuel", "currentUserId", "handleDelete", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "delete", "_error$response2", "_error$response2$data", "filteredEnseignants", "searchLower", "indexOfLastItem", "indexOfFirstItem", "currentEnseignants", "slice", "totalPages", "Math", "ceil", "paginate", "pageNumber", "getStatutBadge", "style", "padding", "borderRadius", "fontSize", "fontWeight", "textTransform", "createElement", "backgroundColor", "color", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatSalaire", "Intl", "NumberFormat", "currency", "format", "styles", "infoMessage", "border", "margin", "idBadge", "className", "onClick", "src", "alt", "type", "placeholder", "value", "onChange", "target", "key", "disabled", "onSubmit", "selected<PERSON>ser", "find", "u", "nom", "required", "step"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/Enseignants.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport { AuthContext } from '../context/AuthContext';\nimport '../css/Factures.css';\n\nconst Enseignants = () => {\n    const { user } = useContext(AuthContext);\n    const [enseignants, setEnseignants] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingEnseignant, setEditingEnseignant] = useState(null);\n    const [utilisateursEnseignants, setUtilisateursEnseignants] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [formData, setFormData] = useState({\n        utilisateur_id: '',\n        nom_prenom: '',\n        email: '',\n        telephone: '',\n        specialite: '',\n        date_embauche: '',\n        salaire: '',\n        statut: 'actif'\n    });\n\n    // Vérifier si l'utilisateur est Admin\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';\n\n    useEffect(() => {\n        fetchEnseignants();\n        if (isAdmin) {\n            fetchUtilisateursEnseignants();\n        }\n    }, [isAdmin]);\n\n    const fetchEnseignants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            console.log('🔄 Chargement des enseignants...');\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('✅ Réponse API enseignants:', response.data);\n\n            if (response.data.success) {\n                setEnseignants(Array.isArray(response.data.enseignants) ? response.data.enseignants : []);\n            } else {\n                setEnseignants(Array.isArray(response.data) ? response.data : []);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des enseignants:', error);\n            Swal.fire('Erreur', 'Impossible de charger les enseignants', 'error');\n            setEnseignants([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchUtilisateursEnseignants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            console.log('🔄 Chargement des utilisateurs enseignants...');\n\n            // Récupérer tous les utilisateurs\n            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            // Récupérer les enseignants existants pour les exclure\n            const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            // Filtrer pour ne garder que les utilisateurs avec le rôle \"enseignant\"\n            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n            const enseignantsExistants = Array.isArray(responseEnseignants.data) ? responseEnseignants.data : [];\n\n            const utilisateursEnseignantsFiltered = utilisateurs.filter(user => {\n                const roleNom = (user.role_nom || user.role || '').toLowerCase();\n                return roleNom === 'enseignant' || roleNom === 'enseignants';\n            });\n\n            // Exclure les utilisateurs déjà enseignants\n            const enseignantsExistantsIds = enseignantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n            const utilisateursDisponibles = utilisateursEnseignantsFiltered.filter(user =>\n                !enseignantsExistantsIds.includes(user.id)\n            );\n\n            console.log('✅ Utilisateurs enseignants disponibles:', utilisateursDisponibles.length);\n            setUtilisateursEnseignants(utilisateursDisponibles);\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des utilisateurs enseignants:', error);\n            setUtilisateursEnseignants([]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des enseignants', 'error');\n            return;\n        }\n\n        if (!formData.utilisateur_id || !formData.nom_prenom || !formData.email) {\n            Swal.fire('Erreur', 'Veuillez remplir tous les champs obligatoires', 'error');\n            return;\n        }\n\n        try {\n            const token = localStorage.getItem('token');\n            const url = 'http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php';\n            const method = editingEnseignant ? 'PUT' : 'POST';\n            const data = editingEnseignant ? { ...formData, id: editingEnseignant.id } : formData;\n\n            console.log('🔄 Envoi requête enseignant:', { method, data });\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: {\n                    Authorization: `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            console.log('✅ Réponse:', response.data);\n\n            if (response.data.success) {\n                Swal.fire('Succès', response.data.message, 'success');\n                setShowModal(false);\n                setEditingEnseignant(null);\n                resetForm();\n                fetchEnseignants();\n                if (isAdmin) fetchUtilisateursEnseignants();\n            } else {\n                Swal.fire('Erreur', response.data.error || 'Une erreur est survenue', 'error');\n            }\n        } catch (error) {\n            console.error('❌ Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = async (enseignant) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des enseignants', 'error');\n            return;\n        }\n\n        // Recharger les utilisateurs enseignants en incluant l'utilisateur actuel\n        await fetchUtilisateursEnseignantsAvecActuel(enseignant.utilisateur_id);\n\n        setEditingEnseignant(enseignant);\n        setFormData({\n            utilisateur_id: enseignant.utilisateur_id || '',\n            nom_prenom: enseignant.nom_prenom || '',\n            email: enseignant.email || '',\n            telephone: enseignant.telephone || '',\n            specialite: enseignant.specialite || '',\n            date_embauche: enseignant.date_embauche || '',\n            salaire: enseignant.salaire || '',\n            statut: enseignant.statut || 'actif'\n        });\n        setShowModal(true);\n    };\n\n    const fetchUtilisateursEnseignantsAvecActuel = async (currentUserId) => {\n        try {\n            const token = localStorage.getItem('token');\n            console.log('🔄 Chargement des utilisateurs enseignants avec actuel...');\n\n            // Récupérer tous les utilisateurs\n            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=enseignant', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            // Récupérer les enseignants existants pour les exclure\n            const responseEnseignants = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n            const enseignantsExistants = Array.isArray(responseEnseignants.data) ? responseEnseignants.data : [];\n\n            const utilisateursEnseignantsFiltered = utilisateurs.filter(user => {\n                const roleNom = (user.role_nom || user.role || '').toLowerCase();\n                return roleNom === 'enseignant' || roleNom === 'enseignants';\n            });\n\n            // Exclure les utilisateurs déjà enseignants SAUF l'utilisateur actuel\n            const enseignantsExistantsIds = enseignantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n            const utilisateursDisponibles = utilisateursEnseignantsFiltered.filter(user =>\n                !enseignantsExistantsIds.includes(user.id) || user.id === currentUserId\n            );\n\n            console.log('✅ Utilisateurs enseignants avec actuel:', utilisateursDisponibles.length);\n            setUtilisateursEnseignants(utilisateursDisponibles);\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des utilisateurs enseignants avec actuel:', error);\n            setUtilisateursEnseignants([]);\n        }\n    };\n\n    const handleDelete = async (id) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des enseignants', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr ?',\n            text: 'Cette action est irréversible !',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer !',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                const token = localStorage.getItem('token');\n                console.log('🗑️ Suppression enseignant ID:', id);\n\n                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n                    headers: {\n                        Authorization: `Bearer ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n\n                console.log('✅ Réponse suppression:', response.data);\n\n                if (response.data.success) {\n                    Swal.fire('Supprimé!', response.data.message, 'success');\n                    fetchEnseignants();\n                    if (isAdmin) fetchUtilisateursEnseignants();\n                } else {\n                    Swal.fire('Erreur', response.data.error || 'Impossible de supprimer l\\'enseignant', 'error');\n                }\n            } catch (error) {\n                console.error('❌ Erreur suppression:', error);\n                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer l\\'enseignant', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            utilisateur_id: '',\n            nom_prenom: '',\n            email: '',\n            telephone: '',\n            specialite: '',\n            date_embauche: '',\n            salaire: '',\n            statut: 'actif'\n        });\n    };\n\n\n\n    // Filtrage des enseignants\n    const filteredEnseignants = enseignants.filter(enseignant => {\n        const searchLower = searchTerm.toLowerCase();\n        return (\n            (enseignant.nom_prenom || '').toLowerCase().includes(searchLower) ||\n            (enseignant.email || '').toLowerCase().includes(searchLower) ||\n            (enseignant.specialite || '').toLowerCase().includes(searchLower) ||\n            (enseignant.telephone || '').toLowerCase().includes(searchLower)\n        );\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentEnseignants = filteredEnseignants.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredEnseignants.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    // Reset pagination when search changes\n    React.useEffect(() => {\n        setCurrentPage(1);\n    }, [searchTerm]);\n\n    const getStatutBadge = (statut) => {\n        const style = {\n            padding: '4px 12px',\n            borderRadius: '20px',\n            fontSize: '0.8em',\n            fontWeight: 'bold',\n            textTransform: 'uppercase'\n        };\n\n        if (statut === 'actif') {\n            return <span style={{...style, backgroundColor: '#d4edda', color: '#155724'}}>Actif</span>;\n        } else {\n            return <span style={{...style, backgroundColor: '#f8d7da', color: '#721c24'}}>Inactif</span>;\n        }\n    };\n\n    const formatSalaire = (salaire) => {\n        if (!salaire) return 'Non spécifié';\n        return new Intl.NumberFormat('fr-MA', {\n            style: 'currency',\n            currency: 'MAD'\n        }).format(salaire);\n    };\n\n    const styles = {\n        infoMessage: {\n            backgroundColor: '#e3f2fd',\n            border: '1px solid #2196f3',\n            borderRadius: '8px',\n            padding: '15px',\n            margin: '20px 0',\n            color: '#1565c0'\n        },\n        idBadge: {\n            backgroundColor: '#007bff',\n            color: 'white',\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '0.8em',\n            fontWeight: 'bold'\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des enseignants...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>👨‍🏫 Gestion des Enseignants</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredEnseignants.length} enseignant(s) trouvé(s)\n                    </span>\n                    {isAdmin && (\n                        <button\n                            className=\"btn btn-primary\"\n                            onClick={() => setShowModal(true)}\n                        >\n                            <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouvel Enseignant\n                        </button>\n                    )}\n                </div>\n            </div>\n\n            {/* Message d'information pour les non-admins */}\n            {!isAdmin && (\n                <div style={styles.infoMessage}>\n                    <p style={{ margin: '0' }}>ℹ️ Vous consultez les enseignants en mode lecture seule. Seul l'administrateur peut créer, modifier ou supprimer des enseignants.</p>\n                </div>\n            )}\n\n            {/* Barre de recherche */}\n            <div className=\"search-section\">\n                <div className=\"search-bar\">\n                   \n                    <input\n                        type=\"text\"\n                        placeholder=\"🔍 Rechercher par nom, email, spécialité, téléphone...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                    />\n                </div>\n            </div>\n\n            {/* Tableau des enseignants */}\n            <div className=\"table-container\">\n                {filteredEnseignants.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/empty.png\" alt=\"Aucune donnée\" />\n                        <p>Aucun enseignant trouvé</p>\n                        <p>Essayez de modifier vos critères de recherche</p>\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>🆔 ID</th>\n                                    <th>👤 Nom Complet</th>\n                                    <th>📧 Email</th>\n                                    <th>📞 Téléphone</th>\n                                    <th>🎓 Spécialité</th>\n                                    <th>💰 Salaire</th>\n                                    <th>📊 Statut</th>\n                                    {isAdmin && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentEnseignants.map((enseignant) => (\n                                    <tr key={enseignant.id}>\n                                        <td>\n                                            <span style={styles.idBadge}>\n                                                #{enseignant.id}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            <div className=\"user-info\">\n                                                <strong>{enseignant.nom_prenom || 'Nom non disponible'}</strong>\n                                                <br />\n                                                <small style={{ color: '#6c757d' }}>\n                                                    {enseignant.utilisateur_id ? `Utilisateur ID: ${enseignant.utilisateur_id}` : 'Pas d\\'utilisateur lié'}\n                                                </small>\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{ fontSize: '0.9em' }}>\n                                                {enseignant.email || 'Email non disponible'}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            <span style={{ fontSize: '0.9em' }}>\n                                                {enseignant.telephone || 'Non renseigné'}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#e3f2fd',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {enseignant.specialite || 'Non spécifiée'}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            <strong style={{ color: '#2c3e50', fontSize: '1.1em' }}>\n                                                {formatSalaire(enseignant.salaire)}\n                                            </strong>\n                                        </td>\n                                        <td>{getStatutBadge(enseignant.statut)}</td>\n                                        {isAdmin && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(enseignant)}\n                                                        title=\"Modifier\"\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(enseignant.id)}\n                                                        title=\"Supprimer\"\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n                <div className=\"pagination\">\n                    <button\n                        onClick={() => paginate(currentPage - 1)}\n                        disabled={currentPage === 1}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Précédent\n                    </button>\n\n                    <div className=\"page-info\">\n                        Page {currentPage} sur {totalPages}\n                    </div>\n\n                    <button\n                        onClick={() => paginate(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                        className=\"btn btn-outline-primary\"\n                    >\n                        Suivant\n                    </button>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier un enseignant */}\n            {showModal && isAdmin && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingEnseignant ? 'Modifier l\\'enseignant' : 'Nouvel enseignant'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingEnseignant(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Utilisateur (Enseignant) *</label>\n                                <select\n                                    value={formData.utilisateur_id}\n                                    onChange={(e) => {\n                                        const selectedUser = utilisateursEnseignants.find(u => u.id == e.target.value);\n                                        setFormData({\n                                            ...formData,\n                                            utilisateur_id: e.target.value,\n                                            nom_prenom: selectedUser ? selectedUser.nom : formData.nom_prenom,\n                                            email: selectedUser ? selectedUser.email : formData.email\n                                        });\n                                    }}\n                                    required\n                                    disabled={editingEnseignant} // Empêcher la modification de l'utilisateur lors de l'édition\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur enseignant...</option>\n                                    {utilisateursEnseignants.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} - {user.email} (ID: {user.id})\n                                        </option>\n                                    ))}\n                                </select>\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\n                                    Seuls les utilisateurs avec le rôle \"enseignant\" non encore assignés sont affichés\n                                </small>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Nom Complet *</label>\n                                <input\n                                    type=\"text\"\n                                    value={formData.nom_prenom}\n                                    onChange={(e) => setFormData({...formData, nom_prenom: e.target.value})}\n                                    placeholder=\"Nom complet de l'enseignant\"\n                                    required\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Email *</label>\n                                <input\n                                    type=\"email\"\n                                    value={formData.email}\n                                    onChange={(e) => setFormData({...formData, email: e.target.value})}\n                                    placeholder=\"Email de l'enseignant\"\n                                    required\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Téléphone</label>\n                                <input\n                                    type=\"tel\"\n                                    value={formData.telephone}\n                                    onChange={(e) => setFormData({...formData, telephone: e.target.value})}\n                                    placeholder=\"Numéro de téléphone\"\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Spécialité</label>\n                                <input\n                                    type=\"text\"\n                                    value={formData.specialite}\n                                    onChange={(e) => setFormData({...formData, specialite: e.target.value})}\n                                    placeholder=\"Spécialité de l'enseignant\"\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Date d'embauche</label>\n                                <input\n                                    type=\"date\"\n                                    value={formData.date_embauche}\n                                    onChange={(e) => setFormData({...formData, date_embauche: e.target.value})}\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Salaire (MAD)</label>\n                                <input\n                                    type=\"number\"\n                                    step=\"0.01\"\n                                    value={formData.salaire}\n                                    onChange={(e) => setFormData({...formData, salaire: e.target.value})}\n                                    placeholder=\"Salaire en dirhams\"\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Statut *</label>\n                                <select\n                                    value={formData.statut}\n                                    onChange={(e) => setFormData({...formData, statut: e.target.value})}\n                                    required\n                                >\n                                    <option value=\"actif\">Actif</option>\n                                    <option value=\"inactif\">Inactif</option>\n                                </select>\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingEnseignant ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingEnseignant(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Enseignants;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,qBAAqB;AAE5B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACG,WAAW,CAAC;EACxC,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1E,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsB,YAAY,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IACrCyB,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,EAAE;IACdC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,MAAK,OAAO,IAAI,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2B,IAAI,MAAK,OAAO;EAEhEjC,SAAS,CAAC,MAAM;IACZkC,gBAAgB,CAAC,CAAC;IAClB,IAAIF,OAAO,EAAE;MACTG,4BAA4B,CAAC,CAAC;IAClC;EACJ,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;EAEb,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAE/C,MAAMC,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,uEAAuE,EAAE;QACtGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,CAACI,IAAI,CAAC;MAExD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBtC,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACI,IAAI,CAACtC,WAAW,CAAC,GAAGkC,QAAQ,CAACI,IAAI,CAACtC,WAAW,GAAG,EAAE,CAAC;MAC7F,CAAC,MAAM;QACHC,cAAc,CAACuC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACI,IAAI,CAAC,GAAGJ,QAAQ,CAACI,IAAI,GAAG,EAAE,CAAC;MACrE;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;MACpE9C,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,uCAAuC,EAAE,OAAO,CAAC;MACrE1C,cAAc,CAAC,EAAE,CAAC;IACtB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMyB,4BAA4B,GAAG,MAAAA,CAAA,KAAY;IAC7C,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;MAE5D;MACA,MAAMW,oBAAoB,GAAG,MAAMjD,KAAK,CAACwC,GAAG,CAAC,yFAAyF,EAAE;QACpIC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;;MAEF;MACA,MAAMgB,mBAAmB,GAAG,MAAMlD,KAAK,CAACwC,GAAG,CAAC,uEAAuE,EAAE;QACjHC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;;MAEF;MACA,MAAMiB,YAAY,GAAGN,KAAK,CAACC,OAAO,CAACG,oBAAoB,CAACN,IAAI,CAAC,GAAGM,oBAAoB,CAACN,IAAI,GAAG,EAAE;MAC9F,MAAMS,oBAAoB,GAAGP,KAAK,CAACC,OAAO,CAACI,mBAAmB,CAACP,IAAI,CAAC,GAAGO,mBAAmB,CAACP,IAAI,GAAG,EAAE;MAEpG,MAAMU,+BAA+B,GAAGF,YAAY,CAACG,MAAM,CAAClD,IAAI,IAAI;QAChE,MAAMmD,OAAO,GAAG,CAACnD,IAAI,CAACoD,QAAQ,IAAIpD,IAAI,CAAC2B,IAAI,IAAI,EAAE,EAAE0B,WAAW,CAAC,CAAC;QAChE,OAAOF,OAAO,KAAK,YAAY,IAAIA,OAAO,KAAK,aAAa;MAChE,CAAC,CAAC;;MAEF;MACA,MAAMG,uBAAuB,GAAGN,oBAAoB,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACtC,cAAc,CAAC,CAACgC,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;MACzG,MAAMC,uBAAuB,GAAGT,+BAA+B,CAACC,MAAM,CAAClD,IAAI,IACvE,CAACsD,uBAAuB,CAACK,QAAQ,CAAC3D,IAAI,CAACyD,EAAE,CAC7C,CAAC;MAEDxB,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEwB,uBAAuB,CAACE,MAAM,CAAC;MACtFlD,0BAA0B,CAACgD,uBAAuB,CAAC;IACvD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,2DAA2D,EAAEA,KAAK,CAAC;MACjFjC,0BAA0B,CAAC,EAAE,CAAC;IAClC;EACJ,CAAC;EAED,MAAMmD,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAI,CAACpC,OAAO,EAAE;MACV7B,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,4DAA4D,EAAE,OAAO,CAAC;MAC1F;IACJ;IAEA,IAAI,CAAC5B,QAAQ,CAACE,cAAc,IAAI,CAACF,QAAQ,CAACG,UAAU,IAAI,CAACH,QAAQ,CAACI,KAAK,EAAE;MACrEvB,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,+CAA+C,EAAE,OAAO,CAAC;MAC7E;IACJ;IAEA,IAAI;MACA,MAAMd,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM+B,GAAG,GAAG,uEAAuE;MACnF,MAAMC,MAAM,GAAGzD,iBAAiB,GAAG,KAAK,GAAG,MAAM;MACjD,MAAMgC,IAAI,GAAGhC,iBAAiB,GAAG;QAAE,GAAGS,QAAQ;QAAEyC,EAAE,EAAElD,iBAAiB,CAACkD;MAAG,CAAC,GAAGzC,QAAQ;MAErFiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;QAAE8B,MAAM;QAAEzB;MAAK,CAAC,CAAC;MAE7D,MAAMJ,QAAQ,GAAG,MAAMvC,KAAK,CAAC;QACzBoE,MAAM;QACND,GAAG;QACHxB,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUR,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEC,QAAQ,CAACI,IAAI,CAAC;MAExC,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvB3C,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAET,QAAQ,CAACI,IAAI,CAAC0B,OAAO,EAAE,SAAS,CAAC;QACrD3D,YAAY,CAAC,KAAK,CAAC;QACnBE,oBAAoB,CAAC,IAAI,CAAC;QAC1B0D,SAAS,CAAC,CAAC;QACXtC,gBAAgB,CAAC,CAAC;QAClB,IAAIF,OAAO,EAAEG,4BAA4B,CAAC,CAAC;MAC/C,CAAC,MAAM;QACHhC,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAET,QAAQ,CAACI,IAAI,CAACI,KAAK,IAAI,yBAAyB,EAAE,OAAO,CAAC;MAClF;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAwB,eAAA,EAAAC,oBAAA;MACZnC,OAAO,CAACU,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9C,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,EAAAuB,eAAA,GAAAxB,KAAK,CAACR,QAAQ,cAAAgC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB5B,IAAI,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBzB,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAM0B,UAAU,GAAG,MAAOC,UAAU,IAAK;IACrC,IAAI,CAAC5C,OAAO,EAAE;MACV7B,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,sDAAsD,EAAE,OAAO,CAAC;MACpF;IACJ;;IAEA;IACA,MAAM2B,sCAAsC,CAACD,UAAU,CAACpD,cAAc,CAAC;IAEvEV,oBAAoB,CAAC8D,UAAU,CAAC;IAChCrD,WAAW,CAAC;MACRC,cAAc,EAAEoD,UAAU,CAACpD,cAAc,IAAI,EAAE;MAC/CC,UAAU,EAAEmD,UAAU,CAACnD,UAAU,IAAI,EAAE;MACvCC,KAAK,EAAEkD,UAAU,CAAClD,KAAK,IAAI,EAAE;MAC7BC,SAAS,EAAEiD,UAAU,CAACjD,SAAS,IAAI,EAAE;MACrCC,UAAU,EAAEgD,UAAU,CAAChD,UAAU,IAAI,EAAE;MACvCC,aAAa,EAAE+C,UAAU,CAAC/C,aAAa,IAAI,EAAE;MAC7CC,OAAO,EAAE8C,UAAU,CAAC9C,OAAO,IAAI,EAAE;MACjCC,MAAM,EAAE6C,UAAU,CAAC7C,MAAM,IAAI;IACjC,CAAC,CAAC;IACFnB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMiE,sCAAsC,GAAG,MAAOC,aAAa,IAAK;IACpE,IAAI;MACA,MAAM1C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CC,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;;MAExE;MACA,MAAMW,oBAAoB,GAAG,MAAMjD,KAAK,CAACwC,GAAG,CAAC,yFAAyF,EAAE;QACpIC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;;MAEF;MACA,MAAMgB,mBAAmB,GAAG,MAAMlD,KAAK,CAACwC,GAAG,CAAC,uEAAuE,EAAE;QACjHC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,MAAMiB,YAAY,GAAGN,KAAK,CAACC,OAAO,CAACG,oBAAoB,CAACN,IAAI,CAAC,GAAGM,oBAAoB,CAACN,IAAI,GAAG,EAAE;MAC9F,MAAMS,oBAAoB,GAAGP,KAAK,CAACC,OAAO,CAACI,mBAAmB,CAACP,IAAI,CAAC,GAAGO,mBAAmB,CAACP,IAAI,GAAG,EAAE;MAEpG,MAAMU,+BAA+B,GAAGF,YAAY,CAACG,MAAM,CAAClD,IAAI,IAAI;QAChE,MAAMmD,OAAO,GAAG,CAACnD,IAAI,CAACoD,QAAQ,IAAIpD,IAAI,CAAC2B,IAAI,IAAI,EAAE,EAAE0B,WAAW,CAAC,CAAC;QAChE,OAAOF,OAAO,KAAK,YAAY,IAAIA,OAAO,KAAK,aAAa;MAChE,CAAC,CAAC;;MAEF;MACA,MAAMG,uBAAuB,GAAGN,oBAAoB,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACtC,cAAc,CAAC,CAACgC,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;MACzG,MAAMC,uBAAuB,GAAGT,+BAA+B,CAACC,MAAM,CAAClD,IAAI,IACvE,CAACsD,uBAAuB,CAACK,QAAQ,CAAC3D,IAAI,CAACyD,EAAE,CAAC,IAAIzD,IAAI,CAACyD,EAAE,KAAKe,aAC9D,CAAC;MAEDvC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEwB,uBAAuB,CAACE,MAAM,CAAC;MACtFlD,0BAA0B,CAACgD,uBAAuB,CAAC;IACvD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,uEAAuE,EAAEA,KAAK,CAAC;MAC7FjC,0BAA0B,CAAC,EAAE,CAAC;IAClC;EACJ,CAAC;EAED,MAAM+D,YAAY,GAAG,MAAOhB,EAAE,IAAK;IAC/B,IAAI,CAAC/B,OAAO,EAAE;MACV7B,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,uDAAuD,EAAE,OAAO,CAAC;MACrF;IACJ;IAEA,MAAM8B,MAAM,GAAG,MAAM7E,IAAI,CAAC+C,IAAI,CAAC;MAC3B+B,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,iCAAiC;MACvCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,kBAAkB;MACrCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA,MAAMrD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuB,EAAE,CAAC;QAEjD,MAAMtB,QAAQ,GAAG,MAAMvC,KAAK,CAACwF,MAAM,CAAC,uEAAuE,EAAE;UACzG/C,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUR,KAAK,EAAE;YAChC,cAAc,EAAE;UACpB,CAAC;UACDS,IAAI,EAAE;YAAEkB;UAAG;QACf,CAAC,CAAC;QAEFxB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACI,IAAI,CAAC;QAEpD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvB3C,IAAI,CAAC+C,IAAI,CAAC,WAAW,EAAET,QAAQ,CAACI,IAAI,CAAC0B,OAAO,EAAE,SAAS,CAAC;UACxDrC,gBAAgB,CAAC,CAAC;UAClB,IAAIF,OAAO,EAAEG,4BAA4B,CAAC,CAAC;QAC/C,CAAC,MAAM;UACHhC,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAET,QAAQ,CAACI,IAAI,CAACI,KAAK,IAAI,uCAAuC,EAAE,OAAO,CAAC;QAChG;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAA0C,gBAAA,EAAAC,qBAAA;QACZrD,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C9C,IAAI,CAAC+C,IAAI,CAAC,QAAQ,EAAE,EAAAyC,gBAAA,GAAA1C,KAAK,CAACR,QAAQ,cAAAkD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9C,IAAI,cAAA+C,qBAAA,uBAApBA,qBAAA,CAAsB3C,KAAK,KAAI,uCAAuC,EAAE,OAAO,CAAC;MACxG;IACJ;EACJ,CAAC;EAED,MAAMuB,SAAS,GAAGA,CAAA,KAAM;IACpBjD,WAAW,CAAC;MACRC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;;EAID;EACA,MAAM8D,mBAAmB,GAAGtF,WAAW,CAACiD,MAAM,CAACoB,UAAU,IAAI;IACzD,MAAMkB,WAAW,GAAG7E,UAAU,CAAC0C,WAAW,CAAC,CAAC;IAC5C,OACI,CAACiB,UAAU,CAACnD,UAAU,IAAI,EAAE,EAAEkC,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC6B,WAAW,CAAC,IACjE,CAAClB,UAAU,CAAClD,KAAK,IAAI,EAAE,EAAEiC,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC6B,WAAW,CAAC,IAC5D,CAAClB,UAAU,CAAChD,UAAU,IAAI,EAAE,EAAE+B,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC6B,WAAW,CAAC,IACjE,CAAClB,UAAU,CAACjD,SAAS,IAAI,EAAE,EAAEgC,WAAW,CAAC,CAAC,CAACM,QAAQ,CAAC6B,WAAW,CAAC;EAExE,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG5E,WAAW,GAAGE,YAAY;EAClD,MAAM2E,gBAAgB,GAAGD,eAAe,GAAG1E,YAAY;EACvD,MAAM4E,kBAAkB,GAAGJ,mBAAmB,CAACK,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EACvF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACR,mBAAmB,CAAC3B,MAAM,GAAG7C,YAAY,CAAC;EAEvE,MAAMiF,QAAQ,GAAIC,UAAU,IAAKnF,cAAc,CAACmF,UAAU,CAAC;;EAE3D;EACAzG,KAAK,CAACE,SAAS,CAAC,MAAM;IAClBoB,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACH,UAAU,CAAC,CAAC;EAEhB,MAAMuF,cAAc,GAAIzE,MAAM,IAAK;IAC/B,MAAM0E,KAAK,GAAG;MACVC,OAAO,EAAE,UAAU;MACnBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE,MAAM;MAClBC,aAAa,EAAE;IACnB,CAAC;IAED,IAAI/E,MAAM,KAAK,OAAO,EAAE;MACpB,oBAAOjC,KAAA,CAAAiH,aAAA;QAAMN,KAAK,EAAE;UAAC,GAAGA,KAAK;UAAEO,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAS,CAAE;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,OAAW,CAAC;IAC9F,CAAC,MAAM;MACH,oBAAOzH,KAAA,CAAAiH,aAAA;QAAMN,KAAK,EAAE;UAAC,GAAGA,KAAK;UAAEO,eAAe,EAAE,SAAS;UAAEC,KAAK,EAAE;QAAS,CAAE;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,SAAa,CAAC;IAChG;EACJ,CAAC;EAED,MAAMC,aAAa,GAAI1F,OAAO,IAAK;IAC/B,IAAI,CAACA,OAAO,EAAE,OAAO,cAAc;IACnC,OAAO,IAAI2F,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCjB,KAAK,EAAE,UAAU;MACjBkB,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAAC9F,OAAO,CAAC;EACtB,CAAC;EAED,MAAM+F,MAAM,GAAG;IACXC,WAAW,EAAE;MACTd,eAAe,EAAE,SAAS;MAC1Be,MAAM,EAAE,mBAAmB;MAC3BpB,YAAY,EAAE,KAAK;MACnBD,OAAO,EAAE,MAAM;MACfsB,MAAM,EAAE,QAAQ;MAChBf,KAAK,EAAE;IACX,CAAC;IACDgB,OAAO,EAAE;MACLjB,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE,OAAO;MACdP,OAAO,EAAE,SAAS;MAClBC,YAAY,EAAE,MAAM;MACpBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB;EACJ,CAAC;EAED,IAAIpG,OAAO,EAAE;IACT,oBACIX,KAAA,CAAAiH,aAAA;MAAKmB,SAAS,EAAC,mBAAmB;MAAAhB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BzH,KAAA,CAAAiH,aAAA;MAAKmB,SAAS,EAAC,SAAS;MAAAhB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BzH,KAAA,CAAAiH,aAAA;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,+BAAgC,CAClC,CAAC;EAEd;EAEA,oBACIzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,oBAAoB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,aAAa;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,wDAAiC,CAAC,eACtCzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,aAAa;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBzH,KAAA,CAAAiH,aAAA;IAAMmB,SAAS,EAAC,aAAa;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB1B,mBAAmB,CAAC3B,MAAM,EAAC,6BAC1B,CAAC,EACNlC,OAAO,iBACJlC,KAAA,CAAAiH,aAAA;IACImB,SAAS,EAAC,iBAAiB;IAC3BC,OAAO,EAAEA,CAAA,KAAMvH,YAAY,CAAC,IAAI,CAAE;IAAAsG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCzH,KAAA,CAAAiH,aAAA;IAAKqB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,sBACjC,CAEX,CACJ,CAAC,EAGL,CAACvF,OAAO,iBACLlC,KAAA,CAAAiH,aAAA;IAAKN,KAAK,EAAEoB,MAAM,CAACC,WAAY;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BzH,KAAA,CAAAiH,aAAA;IAAGN,KAAK,EAAE;MAAEuB,MAAM,EAAE;IAAI,CAAE;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gJAAoI,CAC9J,CACR,eAGDzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,gBAAgB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,8EAAwD;IACpEC,KAAK,EAAEvH,UAAW;IAClBwH,QAAQ,EAAG3E,CAAC,IAAK5C,aAAa,CAAC4C,CAAC,CAAC4E,MAAM,CAACF,KAAK,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAClD,CACA,CACJ,CAAC,eAGNzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,iBAAiB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B1B,mBAAmB,CAAC3B,MAAM,KAAK,CAAC,gBAC7BpE,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,SAAS;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBzH,KAAA,CAAAiH,aAAA;IAAKqB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,kBAAe;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC5CzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,4BAA0B,CAAC,eAC9BzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,kDAAgD,CAClD,CAAC,gBAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,kBAAkB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BzH,KAAA,CAAAiH,aAAA;IAAOmB,SAAS,EAAC,OAAO;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAS,CAAC,eACdzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAkB,CAAC,eACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oBAAY,CAAC,eACjBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,8BAAgB,CAAC,eACrBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+BAAiB,CAAC,eACtBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAAC,eACnBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,EACjBvF,OAAO,iBAAIlC,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAC9B,CACD,CAAC,eACRzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKtB,kBAAkB,CAACpC,GAAG,CAAEe,UAAU,iBAC/B9E,KAAA,CAAAiH,aAAA;IAAI4B,GAAG,EAAE/D,UAAU,CAACb,EAAG;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAMN,KAAK,EAAEoB,MAAM,CAACI,OAAQ;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GACxB,EAAC3C,UAAU,CAACb,EACX,CACN,CAAC,eACLjE,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,WAAW;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAS3C,UAAU,CAACnD,UAAU,IAAI,oBAA6B,CAAC,eAChE3B,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNzH,KAAA,CAAAiH,aAAA;IAAON,KAAK,EAAE;MAAEQ,KAAK,EAAE;IAAU,CAAE;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B3C,UAAU,CAACpD,cAAc,GAAG,mBAAmBoD,UAAU,CAACpD,cAAc,EAAE,GAAG,wBAC3E,CACN,CACL,CAAC,eACL1B,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAMN,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B3C,UAAU,CAAClD,KAAK,IAAI,sBACnB,CACN,CAAC,eACL5B,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAMN,KAAK,EAAE;MAAEG,QAAQ,EAAE;IAAQ,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B3C,UAAU,CAACjD,SAAS,IAAI,eACvB,CACN,CAAC,eACL7B,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAMN,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBM,eAAe,EAAE,SAAS;MAC1BL,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG3C,UAAU,CAAChD,UAAU,IAAI,eACxB,CACN,CAAC,eACL9B,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAQN,KAAK,EAAE;MAAEQ,KAAK,EAAE,SAAS;MAAEL,QAAQ,EAAE;IAAQ,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClDC,aAAa,CAAC5C,UAAU,CAAC9C,OAAO,CAC7B,CACR,CAAC,eACLhC,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKf,cAAc,CAAC5B,UAAU,CAAC7C,MAAM,CAAM,CAAC,EAC3CC,OAAO,iBACJlC,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,gBAAgB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BzH,KAAA,CAAAiH,aAAA;IACImB,SAAS,EAAC,wBAAwB;IAClCC,OAAO,EAAEA,CAAA,KAAMxD,UAAU,CAACC,UAAU,CAAE;IACtCK,KAAK,EAAC,UAAU;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhBzH,KAAA,CAAAiH,aAAA;IAAKqB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACTzH,KAAA,CAAAiH,aAAA;IACImB,SAAS,EAAC,uBAAuB;IACjCC,OAAO,EAAEA,CAAA,KAAMpD,YAAY,CAACH,UAAU,CAACb,EAAE,CAAE;IAC3CkB,KAAK,EAAC,WAAW;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjBzH,KAAA,CAAAiH,aAAA;IAAKqB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLpB,UAAU,GAAG,CAAC,iBACXrG,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IACIoB,OAAO,EAAEA,CAAA,KAAM7B,QAAQ,CAACnF,WAAW,GAAG,CAAC,CAAE;IACzCyH,QAAQ,EAAEzH,WAAW,KAAK,CAAE;IAC5B+G,SAAS,EAAC,yBAAyB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,iBAEO,CAAC,eAETzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,WAAW;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAClB,EAACpG,WAAW,EAAC,OAAK,EAACgF,UACvB,CAAC,eAENrG,KAAA,CAAAiH,aAAA;IACIoB,OAAO,EAAEA,CAAA,KAAM7B,QAAQ,CAACnF,WAAW,GAAG,CAAC,CAAE;IACzCyH,QAAQ,EAAEzH,WAAW,KAAKgF,UAAW;IACrC+B,SAAS,EAAC,yBAAyB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtC,SAEO,CACP,CACR,EAGA5G,SAAS,IAAIqB,OAAO,iBACjBlC,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,eAAe;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,eAAe;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,cAAc;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK1G,iBAAiB,GAAG,wBAAwB,GAAG,mBAAwB,CAAC,eAC7Ef,KAAA,CAAAiH,aAAA;IACImB,SAAS,EAAC,WAAW;IACrBC,OAAO,EAAEA,CAAA,KAAM;MACXvH,YAAY,CAAC,KAAK,CAAC;MACnBE,oBAAoB,CAAC,IAAI,CAAC;MAC1B0D,SAAS,CAAC,CAAC;IACf,CAAE;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFzH,KAAA,CAAAiH,aAAA;IAAKqB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNzH,KAAA,CAAAiH,aAAA;IAAM8B,QAAQ,EAAE1E,YAAa;IAAA+C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,4BAAiC,CAAC,eACzCzH,KAAA,CAAAiH,aAAA;IACIyB,KAAK,EAAElH,QAAQ,CAACE,cAAe;IAC/BiH,QAAQ,EAAG3E,CAAC,IAAK;MACb,MAAMgF,YAAY,GAAG/H,uBAAuB,CAACgI,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjF,EAAE,IAAID,CAAC,CAAC4E,MAAM,CAACF,KAAK,CAAC;MAC9EjH,WAAW,CAAC;QACR,GAAGD,QAAQ;QACXE,cAAc,EAAEsC,CAAC,CAAC4E,MAAM,CAACF,KAAK;QAC9B/G,UAAU,EAAEqH,YAAY,GAAGA,YAAY,CAACG,GAAG,GAAG3H,QAAQ,CAACG,UAAU;QACjEC,KAAK,EAAEoH,YAAY,GAAGA,YAAY,CAACpH,KAAK,GAAGJ,QAAQ,CAACI;MACxD,CAAC,CAAC;IACN,CAAE;IACFwH,QAAQ;IACRN,QAAQ,EAAE/H,iBAAkB,CAAC;IAAA;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE7BzH,KAAA,CAAAiH,aAAA;IAAQyB,KAAK,EAAC,EAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8CAAiD,CAAC,EAClExG,uBAAuB,CAAC8C,GAAG,CAACvD,IAAI,iBAC7BR,KAAA,CAAAiH,aAAA;IAAQ4B,GAAG,EAAErI,IAAI,CAACyD,EAAG;IAACyE,KAAK,EAAElI,IAAI,CAACyD,EAAG;IAAAmD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCjH,IAAI,CAAC2I,GAAG,EAAC,KAAG,EAAC3I,IAAI,CAACoB,KAAK,EAAC,QAAM,EAACpB,IAAI,CAACyD,EAAE,EAAC,GACpC,CACX,CACG,CAAC,eACTjE,KAAA,CAAAiH,aAAA;IAAON,KAAK,EAAE;MAAEQ,KAAK,EAAE,SAAS;MAAEL,QAAQ,EAAE;IAAO,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+FAE/C,CACN,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAoB,CAAC,eAC5BzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAElH,QAAQ,CAACG,UAAW;IAC3BgH,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,UAAU,EAAEqC,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IACxED,WAAW,EAAC,6BAA6B;IACzCW,QAAQ;IAAAhC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CACA,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,SAAc,CAAC,eACtBzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,OAAO;IACZE,KAAK,EAAElH,QAAQ,CAACI,KAAM;IACtB+G,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,KAAK,EAAEoC,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IACnED,WAAW,EAAC,uBAAuB;IACnCW,QAAQ;IAAAhC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CACA,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,iBAAgB,CAAC,eACxBzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,KAAK;IACVE,KAAK,EAAElH,QAAQ,CAACK,SAAU;IAC1B8G,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,SAAS,EAAEmC,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IACvED,WAAW,EAAC,8BAAqB;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACpC,CACA,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,kBAAiB,CAAC,eACzBzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAElH,QAAQ,CAACM,UAAW;IAC3B6G,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEM,UAAU,EAAEkC,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IACxED,WAAW,EAAC,kCAA4B;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3C,CACA,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,iBAAsB,CAAC,eAC9BzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAElH,QAAQ,CAACO,aAAc;IAC9B4G,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEO,aAAa,EAAEiC,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC9E,CACA,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAoB,CAAC,eAC5BzH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,QAAQ;IACba,IAAI,EAAC,MAAM;IACXX,KAAK,EAAElH,QAAQ,CAACQ,OAAQ;IACxB2G,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEQ,OAAO,EAAEgC,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IACrED,WAAW,EAAC,oBAAoB;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACnC,CACA,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzH,KAAA,CAAAiH,aAAA;IAAAG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,UAAe,CAAC,eACvBzH,KAAA,CAAAiH,aAAA;IACIyB,KAAK,EAAElH,QAAQ,CAACS,MAAO;IACvB0G,QAAQ,EAAG3E,CAAC,IAAKvC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAES,MAAM,EAAE+B,CAAC,CAAC4E,MAAM,CAACF;IAAK,CAAC,CAAE;IACpEU,QAAQ;IAAAhC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAERzH,KAAA,CAAAiH,aAAA;IAAQyB,KAAK,EAAC,OAAO;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OAAa,CAAC,eACpCzH,KAAA,CAAAiH,aAAA;IAAQyB,KAAK,EAAC,SAAS;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAe,CACnC,CACP,CAAC,eAENzH,KAAA,CAAAiH,aAAA;IAAKmB,SAAS,EAAC,eAAe;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzH,KAAA,CAAAiH,aAAA;IAAQuB,IAAI,EAAC,QAAQ;IAACJ,SAAS,EAAC,iBAAiB;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C1G,iBAAiB,GAAG,aAAa,GAAG,SACjC,CAAC,eACTf,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,QAAQ;IACbJ,SAAS,EAAC,mBAAmB;IAC7BC,OAAO,EAAEA,CAAA,KAAM;MACXvH,YAAY,CAAC,KAAK,CAAC;MACnBE,oBAAoB,CAAC,IAAI,CAAC;MAC1B0D,SAAS,CAAC,CAAC;IACf,CAAE;IAAA0C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAelH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module"}