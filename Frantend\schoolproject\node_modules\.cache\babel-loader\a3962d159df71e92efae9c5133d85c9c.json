{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\MessagingSystem.js\";\nimport React, { useState, useEffect, useRef, useContext, useCallback } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\nconst MessagingSystem = () => {\n  // 🔐 Contexte d'authentification\n  const {\n    user,\n    isLoading\n  } = useContext(AuthContext);\n\n  // 📊 États principaux\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [users, setUsers] = useState([]);\n\n  // 🎨 États UI\n  const [showNewConversation, setShowNewConversation] = useState(false);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [contextMenu, setContextMenu] = useState({\n    show: false,\n    x: 0,\n    y: 0,\n    messageId: null\n  });\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editText, setEditText] = useState('');\n\n  // 📱 Références\n  const messagesEndRef = useRef(null);\n  const contextMenuRef = useRef(null);\n  const messageInputRef = useRef(null);\n\n  // 🔍 Fonction pour obtenir l'ID utilisateur sécurisé\n  const getCurrentUserId = useCallback(() => {\n    if (user) {\n      // Vérifier différentes propriétés possibles pour l'ID\n      const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];\n      for (const id of possibleIds) {\n        if (id && !isNaN(id)) {\n          return parseInt(id);\n        }\n      }\n    }\n\n    // Fallback localStorage\n    try {\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        const userData = JSON.parse(storedUser);\n        const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];\n        for (const id of possibleIds) {\n          if (id && !isNaN(id)) {\n            return parseInt(id);\n          }\n        }\n      }\n    } catch (error) {\n      console.warn('Erreur localStorage:', error);\n    }\n    return null;\n  }, [user]);\n\n  // 🔐 Vérification des droits d'accès\n  const hasMessagingAccess = useCallback(() => {\n    if (!user || !user.role_id) return false;\n\n    // Seuls les Parents (4), Enseignants (2), et Admin (1) ont accès\n    const allowedRoles = [1, 2, 4];\n    return allowedRoles.includes(parseInt(user.role_id));\n  }, [user]);\n\n  // 📡 Charger les utilisateurs autorisés\n  const loadAuthorizedUsers = useCallback(async () => {\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_users.php?current_user=${currentUserId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setUsers(data.users);\n      } else {\n        console.error('Erreur chargement utilisateurs:', data.error);\n      }\n    } catch (error) {\n      console.error('Erreur réseau utilisateurs:', error);\n    }\n  }, [getCurrentUserId]);\n\n  // 📡 Charger les conversations\n  const loadConversations = useCallback(async () => {\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_conversations.php?user_id=${currentUserId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setConversations(data.conversations);\n      } else {\n        setError(data.error || 'Erreur lors du chargement des conversations');\n      }\n    } catch (error) {\n      setError('Erreur de connexion au serveur');\n      console.error('Erreur:', error);\n    }\n  }, [getCurrentUserId]);\n\n  // 📡 Charger les messages d'une conversation\n  const loadMessages = useCallback(async otherUserId => {\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_messages.php?user_id=${currentUserId}&other_user_id=${otherUserId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success) {\n        setMessages(data.messages);\n        // Marquer les messages comme lus\n        markMessagesAsRead(otherUserId);\n      } else {\n        console.error('Erreur chargement messages:', data.error);\n      }\n    } catch (error) {\n      console.error('Erreur réseau messages:', error);\n    }\n  }, [getCurrentUserId]);\n\n  // 📡 Marquer les messages comme lus\n  const markMessagesAsRead = useCallback(async otherUserId => {\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      await fetch('http://localhost/Project_PFE/Backend/api/messaging/mark_read.php', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          user_id: currentUserId,\n          other_user_id: otherUserId\n        })\n      });\n    } catch (error) {\n      console.error('Erreur marquage lu:', error);\n    }\n  }, [getCurrentUserId]);\n\n  // 📤 Envoyer un message\n  const sendMessage = useCallback(async () => {\n    if (!newMessage.trim() || !selectedConversation) return;\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/send_message.php', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          expediteur_id: currentUserId,\n          destinataire_id: selectedConversation.other_user_id,\n          message: newMessage.trim()\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setNewMessage('');\n        loadMessages(selectedConversation.other_user_id);\n        loadConversations();\n      } else {\n        alert('Erreur lors de l\\'envoi: ' + data.error);\n      }\n    } catch (error) {\n      alert('Erreur de connexion lors de l\\'envoi');\n      console.error('Erreur:', error);\n    }\n  }, [newMessage, selectedConversation, getCurrentUserId, loadMessages, loadConversations]);\n\n  // ✏️ Modifier un message\n  const editMessage = (messageId, currentText) => {\n    setEditingMessage(messageId);\n    setEditText(currentText);\n    setContextMenu({\n      show: false,\n      x: 0,\n      y: 0,\n      messageId: null\n    });\n  };\n\n  // 💾 Sauvegarder la modification d'un message\n  const saveMessageEdit = useCallback(async messageId => {\n    if (!editText.trim()) return;\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/edit_message.php', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          message_id: messageId,\n          user_id: currentUserId,\n          new_message: editText.trim()\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setEditingMessage(null);\n        setEditText('');\n        loadMessages(selectedConversation.other_user_id);\n      } else {\n        alert('Erreur lors de la modification: ' + data.error);\n      }\n    } catch (error) {\n      alert('Erreur de connexion lors de la modification');\n      console.error('Erreur:', error);\n    }\n  }, [editText, selectedConversation, getCurrentUserId, loadMessages]);\n\n  // 🗑️ Supprimer un message\n  const deleteMessage = useCallback(async (messageId, deleteType) => {\n    try {\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) return;\n      const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/delete_message.php', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          message_id: messageId,\n          user_id: currentUserId,\n          delete_type: deleteType // 'sender_only' ou 'both_sides'\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        loadMessages(selectedConversation.other_user_id);\n        loadConversations();\n      } else {\n        alert('Erreur lors de la suppression: ' + data.error);\n      }\n    } catch (error) {\n      alert('Erreur de connexion lors de la suppression');\n      console.error('Erreur:', error);\n    }\n    setContextMenu({\n      show: false,\n      x: 0,\n      y: 0,\n      messageId: null\n    });\n  }, [selectedConversation, getCurrentUserId, loadMessages, loadConversations]);\n\n  // ⌨️ Gestion de la touche Entrée (corrigée pour éviter la dépréciation)\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  // 📜 Scroll automatique vers le bas\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n\n  // 🔄 Effets\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    if (!isLoading && hasMessagingAccess()) {\n      loadAuthorizedUsers();\n      loadConversations();\n    }\n  }, [isLoading, hasMessagingAccess, loadAuthorizedUsers, loadConversations]);\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n        setContextMenu({\n          show: false,\n          x: 0,\n          y: 0,\n          messageId: null\n        });\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // 🚫 Vérifications d'accès\n  const currentUserId = getCurrentUserId();\n  if (isLoading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"messaging-system\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 21\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 322,\n        columnNumber: 21\n      }\n    }, \"Chargement du syst\\xE8me de messagerie...\")));\n  }\n  if (!currentUserId) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"messaging-system\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-error-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-error-content\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 25\n      }\n    }, \"\\uD83D\\uDD10 Authentification Requise\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 25\n      }\n    }, \"Impossible d'acc\\xE9der \\xE0 la messagerie sans identification utilisateur.\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: () => window.location.href = '/login',\n      className: \"btn btn-primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 25\n      }\n    }, \"\\uD83D\\uDD11 Se Connecter\"))));\n  }\n  if (!hasMessagingAccess()) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"messaging-system\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"access-denied-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"access-denied-content\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 25\n      }\n    }, \"\\uD83D\\uDEAB Acc\\xE8s Refus\\xE9\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 25\n      }\n    }, \"Seuls les Parents, Enseignants et Administrateurs peuvent acc\\xE9der \\xE0 la messagerie.\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 25\n      }\n    }, \"Votre r\\xF4le actuel ne vous permet pas d'utiliser cette fonctionnalit\\xE9.\"))));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-system\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-sidebar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"sidebar-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h2\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 25\n    }\n  }, \"\\uD83D\\uDCAC Messages\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"new-conversation-btn\",\n    onClick: () => setShowNewConversation(true),\n    title: \"Nouvelle conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-list\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 21\n    }\n  }, conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"empty-conversations\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 33\n    }\n  }, \"Aucune conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"start-conversation-btn\",\n    onClick: () => setShowNewConversation(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 33\n    }\n  }, \"Commencer une conversation\")) : conversations.map(conv => /*#__PURE__*/React.createElement(\"div\", {\n    key: conv.other_user_id,\n    className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.other_user_id) === conv.other_user_id ? 'active' : ''}`,\n    onClick: () => {\n      setSelectedConversation(conv);\n      loadMessages(conv.other_user_id);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 37\n    }\n  }, conv.other_user_name.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 41\n    }\n  }, conv.other_user_name), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-preview\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 41\n    }\n  }, conv.last_message || 'Aucun message')), conv.unread_count > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"unread-badge\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 41\n    }\n  }, conv.unread_count))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-area\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 17\n    }\n  }, selectedConversation ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 37\n    }\n  }, selectedConversation.other_user_name.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-user-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 37\n    }\n  }, selectedConversation.other_user_name))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messages-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 29\n    }\n  }, messages.map(message => {\n    const isOwnMessage = message.expediteur_id === currentUserId;\n    const isEditing = editingMessage === message.id;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: message.id,\n      className: `message-wrapper ${isOwnMessage ? 'own-message' : 'other-message'}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 41\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-bubble ${isOwnMessage ? 'sent' : 'received'}`,\n      onContextMenu: e => {\n        if (isOwnMessage) {\n          e.preventDefault();\n          setContextMenu({\n            show: true,\n            x: e.clientX,\n            y: e.clientY,\n            messageId: message.id\n          });\n        }\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 45\n      }\n    }, isEditing ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"edit-message-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 53\n      }\n    }, /*#__PURE__*/React.createElement(\"textarea\", {\n      value: editText,\n      onChange: e => setEditText(e.target.value),\n      className: \"edit-message-input\",\n      autoFocus: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 57\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"edit-message-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 57\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      className: \"save-edit-btn\",\n      onClick: () => saveMessageEdit(message.id),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 61\n      }\n    }, \"\\u2705 Sauvegarder\"), /*#__PURE__*/React.createElement(\"button\", {\n      className: \"cancel-edit-btn\",\n      onClick: () => {\n        setEditingMessage(null);\n        setEditText('');\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 61\n      }\n    }, \"\\u274C Annuler\"))) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-content\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 57\n      }\n    }, message.message), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-meta\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 57\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-time\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 61\n      }\n    }, new Date(message.date_envoi).toLocaleTimeString('fr-FR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    })), message.modifie === 1 && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-edited\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 65\n      }\n    }, \"Message modifi\\xE9 le \", new Date(message.date_modification).toLocaleDateString('fr-FR')), isOwnMessage && /*#__PURE__*/React.createElement(\"span\", {\n      className: `message-status ${message.lu === 1 ? 'read' : 'unread'}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 65\n      }\n    }, message.lu === 1 ? '✓✓' : '✓')))));\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: messagesEndRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 513,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    ref: messageInputRef,\n    value: newMessage,\n    onChange: e => setNewMessage(e.target.value),\n    onKeyDown: handleKeyDown,\n    placeholder: \"Tapez votre message...\",\n    className: \"message-input\",\n    rows: \"1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: sendMessage,\n    className: \"send-button\",\n    disabled: !newMessage.trim(),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE4\")))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-conversation-selected\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"welcome-message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCAC Bienvenue dans la messagerie\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 33\n    }\n  }, \"S\\xE9lectionnez une conversation ou commencez-en une nouvelle\"))))));\n};\nexport default MessagingSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "useCallback", "AuthContext", "MessagingSystem", "user", "isLoading", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "loading", "setLoading", "error", "setError", "users", "setUsers", "showNewConversation", "setShowNewConversation", "selected<PERSON>ser", "setSelectedUser", "contextMenu", "setContextMenu", "show", "x", "y", "messageId", "editingMessage", "setEditingMessage", "editText", "setEditText", "messagesEndRef", "contextMenuRef", "messageInputRef", "getCurrentUserId", "possibleIds", "id", "user_id", "utilisateur_id", "ID", "User_ID", "isNaN", "parseInt", "storedUser", "localStorage", "getItem", "userData", "JSON", "parse", "console", "warn", "hasMessagingAccess", "role_id", "allowedRoles", "includes", "loadAuthorizedUsers", "currentUserId", "response", "fetch", "headers", "data", "json", "success", "loadConversations", "loadMessages", "otherUserId", "markMessagesAsRead", "method", "body", "stringify", "other_user_id", "sendMessage", "trim", "expediteur_id", "destinataire_id", "message", "alert", "editMessage", "currentText", "saveMessageEdit", "message_id", "new_message", "deleteMessage", "deleteType", "delete_type", "handleKeyDown", "e", "key", "shift<PERSON>ey", "preventDefault", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "href", "title", "length", "map", "conv", "other_user_name", "char<PERSON>t", "toUpperCase", "last_message", "unread_count", "Fragment", "isOwnMessage", "isEditing", "onContextMenu", "clientX", "clientY", "value", "onChange", "autoFocus", "Date", "date_envoi", "toLocaleTimeString", "hour", "minute", "modifie", "date_modification", "toLocaleDateString", "lu", "ref", "onKeyDown", "placeholder", "rows", "disabled"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/MessagingSystem.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useContext, useCallback } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\n\nconst MessagingSystem = () => {\n    // 🔐 Contexte d'authentification\n    const { user, isLoading } = useContext(AuthContext);\n    \n    // 📊 États principaux\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [users, setUsers] = useState([]);\n    \n    // 🎨 États UI\n    const [showNewConversation, setShowNewConversation] = useState(false);\n    const [selectedUser, setSelectedUser] = useState('');\n    const [contextMenu, setContextMenu] = useState({ show: false, x: 0, y: 0, messageId: null });\n    const [editingMessage, setEditingMessage] = useState(null);\n    const [editText, setEditText] = useState('');\n    \n    // 📱 Références\n    const messagesEndRef = useRef(null);\n    const contextMenuRef = useRef(null);\n    const messageInputRef = useRef(null);\n    \n    // 🔍 Fonction pour obtenir l'ID utilisateur sécurisé\n    const getCurrentUserId = useCallback(() => {\n        if (user) {\n            // Vérifier différentes propriétés possibles pour l'ID\n            const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];\n            for (const id of possibleIds) {\n                if (id && !isNaN(id)) {\n                    return parseInt(id);\n                }\n            }\n        }\n        \n        // Fallback localStorage\n        try {\n            const storedUser = localStorage.getItem('user');\n            if (storedUser) {\n                const userData = JSON.parse(storedUser);\n                const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];\n                for (const id of possibleIds) {\n                    if (id && !isNaN(id)) {\n                        return parseInt(id);\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn('Erreur localStorage:', error);\n        }\n        \n        return null;\n    }, [user]);\n    \n    // 🔐 Vérification des droits d'accès\n    const hasMessagingAccess = useCallback(() => {\n        if (!user || !user.role_id) return false;\n        \n        // Seuls les Parents (4), Enseignants (2), et Admin (1) ont accès\n        const allowedRoles = [1, 2, 4];\n        return allowedRoles.includes(parseInt(user.role_id));\n    }, [user]);\n    \n    // 📡 Charger les utilisateurs autorisés\n    const loadAuthorizedUsers = useCallback(async () => {\n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n            \n            const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_users.php?current_user=${currentUserId}`, {\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            \n            const data = await response.json();\n            if (data.success) {\n                setUsers(data.users);\n            } else {\n                console.error('Erreur chargement utilisateurs:', data.error);\n            }\n        } catch (error) {\n            console.error('Erreur réseau utilisateurs:', error);\n        }\n    }, [getCurrentUserId]);\n    \n    // 📡 Charger les conversations\n    const loadConversations = useCallback(async () => {\n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n            \n            const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_conversations.php?user_id=${currentUserId}`, {\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            \n            const data = await response.json();\n            if (data.success) {\n                setConversations(data.conversations);\n            } else {\n                setError(data.error || 'Erreur lors du chargement des conversations');\n            }\n        } catch (error) {\n            setError('Erreur de connexion au serveur');\n            console.error('Erreur:', error);\n        }\n    }, [getCurrentUserId]);\n    \n    // 📡 Charger les messages d'une conversation\n    const loadMessages = useCallback(async (otherUserId) => {\n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n            \n            const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_messages.php?user_id=${currentUserId}&other_user_id=${otherUserId}`, {\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n            \n            const data = await response.json();\n            if (data.success) {\n                setMessages(data.messages);\n                // Marquer les messages comme lus\n                markMessagesAsRead(otherUserId);\n            } else {\n                console.error('Erreur chargement messages:', data.error);\n            }\n        } catch (error) {\n            console.error('Erreur réseau messages:', error);\n        }\n    }, [getCurrentUserId]);\n    \n    // 📡 Marquer les messages comme lus\n    const markMessagesAsRead = useCallback(async (otherUserId) => {\n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n            \n            await fetch('http://localhost/Project_PFE/Backend/api/messaging/mark_read.php', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    user_id: currentUserId,\n                    other_user_id: otherUserId\n                })\n            });\n        } catch (error) {\n            console.error('Erreur marquage lu:', error);\n        }\n    }, [getCurrentUserId]);\n    \n    // 📤 Envoyer un message\n    const sendMessage = useCallback(async () => {\n        if (!newMessage.trim() || !selectedConversation) return;\n        \n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n            \n            const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/send_message.php', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    expediteur_id: currentUserId,\n                    destinataire_id: selectedConversation.other_user_id,\n                    message: newMessage.trim()\n                })\n            });\n            \n            const data = await response.json();\n            if (data.success) {\n                setNewMessage('');\n                loadMessages(selectedConversation.other_user_id);\n                loadConversations();\n            } else {\n                alert('Erreur lors de l\\'envoi: ' + data.error);\n            }\n        } catch (error) {\n            alert('Erreur de connexion lors de l\\'envoi');\n            console.error('Erreur:', error);\n        }\n    }, [newMessage, selectedConversation, getCurrentUserId, loadMessages, loadConversations]);\n    \n    // ✏️ Modifier un message\n    const editMessage = (messageId, currentText) => {\n        setEditingMessage(messageId);\n        setEditText(currentText);\n        setContextMenu({ show: false, x: 0, y: 0, messageId: null });\n    };\n\n    // 💾 Sauvegarder la modification d'un message\n    const saveMessageEdit = useCallback(async (messageId) => {\n        if (!editText.trim()) return;\n\n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n\n            const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/edit_message.php', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message_id: messageId,\n                    user_id: currentUserId,\n                    new_message: editText.trim()\n                })\n            });\n\n            const data = await response.json();\n            if (data.success) {\n                setEditingMessage(null);\n                setEditText('');\n                loadMessages(selectedConversation.other_user_id);\n            } else {\n                alert('Erreur lors de la modification: ' + data.error);\n            }\n        } catch (error) {\n            alert('Erreur de connexion lors de la modification');\n            console.error('Erreur:', error);\n        }\n    }, [editText, selectedConversation, getCurrentUserId, loadMessages]);\n\n    // 🗑️ Supprimer un message\n    const deleteMessage = useCallback(async (messageId, deleteType) => {\n        try {\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) return;\n\n            const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/delete_message.php', {\n                method: 'POST',\n                headers: {\n                    'Authorization': `Bearer ${localStorage.getItem('token')}`,\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message_id: messageId,\n                    user_id: currentUserId,\n                    delete_type: deleteType // 'sender_only' ou 'both_sides'\n                })\n            });\n\n            const data = await response.json();\n            if (data.success) {\n                loadMessages(selectedConversation.other_user_id);\n                loadConversations();\n            } else {\n                alert('Erreur lors de la suppression: ' + data.error);\n            }\n        } catch (error) {\n            alert('Erreur de connexion lors de la suppression');\n            console.error('Erreur:', error);\n        }\n\n        setContextMenu({ show: false, x: 0, y: 0, messageId: null });\n    }, [selectedConversation, getCurrentUserId, loadMessages, loadConversations]);\n\n    // ⌨️ Gestion de la touche Entrée (corrigée pour éviter la dépréciation)\n    const handleKeyDown = (e) => {\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            sendMessage();\n        }\n    };\n    \n    // 📜 Scroll automatique vers le bas\n    const scrollToBottom = () => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    };\n    \n    // 🔄 Effets\n    useEffect(() => {\n        scrollToBottom();\n    }, [messages]);\n    \n    useEffect(() => {\n        if (!isLoading && hasMessagingAccess()) {\n            loadAuthorizedUsers();\n            loadConversations();\n        }\n    }, [isLoading, hasMessagingAccess, loadAuthorizedUsers, loadConversations]);\n    \n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n                setContextMenu({ show: false, x: 0, y: 0, messageId: null });\n            }\n        };\n        \n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    \n    // 🚫 Vérifications d'accès\n    const currentUserId = getCurrentUserId();\n    \n    if (isLoading) {\n        return (\n            <div className=\"messaging-system\">\n                <div className=\"loading-container\">\n                    <div className=\"loading-spinner\"></div>\n                    <p>Chargement du système de messagerie...</p>\n                </div>\n            </div>\n        );\n    }\n    \n    if (!currentUserId) {\n        return (\n            <div className=\"messaging-system\">\n                <div className=\"auth-error-container\">\n                    <div className=\"auth-error-content\">\n                        <h2>🔐 Authentification Requise</h2>\n                        <p>Impossible d'accéder à la messagerie sans identification utilisateur.</p>\n                        <button onClick={() => window.location.href = '/login'} className=\"btn btn-primary\">\n                            🔑 Se Connecter\n                        </button>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n    \n    if (!hasMessagingAccess()) {\n        return (\n            <div className=\"messaging-system\">\n                <div className=\"access-denied-container\">\n                    <div className=\"access-denied-content\">\n                        <h2>🚫 Accès Refusé</h2>\n                        <p>Seuls les Parents, Enseignants et Administrateurs peuvent accéder à la messagerie.</p>\n                        <p>Votre rôle actuel ne vous permet pas d'utiliser cette fonctionnalité.</p>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n    \n    return (\n        <div className=\"messaging-system\">\n            <div className=\"messaging-container\">\n                {/* 📋 Sidebar - Liste des conversations */}\n                <div className=\"conversations-sidebar\">\n                    <div className=\"sidebar-header\">\n                        <h2>💬 Messages</h2>\n                        <button \n                            className=\"new-conversation-btn\"\n                            onClick={() => setShowNewConversation(true)}\n                            title=\"Nouvelle conversation\"\n                        >\n                            ✏️\n                        </button>\n                    </div>\n                    \n                    <div className=\"conversations-list\">\n                        {conversations.length === 0 ? (\n                            <div className=\"empty-conversations\">\n                                <p>Aucune conversation</p>\n                                <button \n                                    className=\"start-conversation-btn\"\n                                    onClick={() => setShowNewConversation(true)}\n                                >\n                                    Commencer une conversation\n                                </button>\n                            </div>\n                        ) : (\n                            conversations.map(conv => (\n                                <div \n                                    key={conv.other_user_id}\n                                    className={`conversation-item ${selectedConversation?.other_user_id === conv.other_user_id ? 'active' : ''}`}\n                                    onClick={() => {\n                                        setSelectedConversation(conv);\n                                        loadMessages(conv.other_user_id);\n                                    }}\n                                >\n                                    <div className=\"conversation-avatar\">\n                                        {conv.other_user_name.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"conversation-info\">\n                                        <div className=\"conversation-name\">{conv.other_user_name}</div>\n                                        <div className=\"conversation-preview\">\n                                            {conv.last_message || 'Aucun message'}\n                                        </div>\n                                    </div>\n                                    {conv.unread_count > 0 && (\n                                        <div className=\"unread-badge\">{conv.unread_count}</div>\n                                    )}\n                                </div>\n                            ))\n                        )}\n                    </div>\n                </div>\n                \n                {/* 💬 Zone de chat principale */}\n                <div className=\"chat-area\">\n                    {selectedConversation ? (\n                        <>\n                            {/* En-tête du chat */}\n                            <div className=\"chat-header\">\n                                <div className=\"chat-user-info\">\n                                    <div className=\"chat-avatar\">\n                                        {selectedConversation.other_user_name.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"chat-user-name\">\n                                        {selectedConversation.other_user_name}\n                                    </div>\n                                </div>\n                            </div>\n                            \n                            {/* Messages avec fonctionnalités complètes */}\n                            <div className=\"messages-container\">\n                                {messages.map(message => {\n                                    const isOwnMessage = message.expediteur_id === currentUserId;\n                                    const isEditing = editingMessage === message.id;\n\n                                    return (\n                                        <div\n                                            key={message.id}\n                                            className={`message-wrapper ${isOwnMessage ? 'own-message' : 'other-message'}`}\n                                        >\n                                            <div\n                                                className={`message-bubble ${isOwnMessage ? 'sent' : 'received'}`}\n                                                onContextMenu={(e) => {\n                                                    if (isOwnMessage) {\n                                                        e.preventDefault();\n                                                        setContextMenu({\n                                                            show: true,\n                                                            x: e.clientX,\n                                                            y: e.clientY,\n                                                            messageId: message.id\n                                                        });\n                                                    }\n                                                }}\n                                            >\n                                                {isEditing ? (\n                                                    <div className=\"edit-message-container\">\n                                                        <textarea\n                                                            value={editText}\n                                                            onChange={(e) => setEditText(e.target.value)}\n                                                            className=\"edit-message-input\"\n                                                            autoFocus\n                                                        />\n                                                        <div className=\"edit-message-actions\">\n                                                            <button\n                                                                className=\"save-edit-btn\"\n                                                                onClick={() => saveMessageEdit(message.id)}\n                                                            >\n                                                                ✅ Sauvegarder\n                                                            </button>\n                                                            <button\n                                                                className=\"cancel-edit-btn\"\n                                                                onClick={() => {\n                                                                    setEditingMessage(null);\n                                                                    setEditText('');\n                                                                }}\n                                                            >\n                                                                ❌ Annuler\n                                                            </button>\n                                                        </div>\n                                                    </div>\n                                                ) : (\n                                                    <>\n                                                        <div className=\"message-content\">\n                                                            {message.message}\n                                                        </div>\n                                                        <div className=\"message-meta\">\n                                                            <span className=\"message-time\">\n                                                                {new Date(message.date_envoi).toLocaleTimeString('fr-FR', {\n                                                                    hour: '2-digit',\n                                                                    minute: '2-digit'\n                                                                })}\n                                                            </span>\n                                                            {message.modifie === 1 && (\n                                                                <span className=\"message-edited\">\n                                                                    Message modifié le {new Date(message.date_modification).toLocaleDateString('fr-FR')}\n                                                                </span>\n                                                            )}\n                                                            {isOwnMessage && (\n                                                                <span className={`message-status ${message.lu === 1 ? 'read' : 'unread'}`}>\n                                                                    {message.lu === 1 ? '✓✓' : '✓'}\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                    </>\n                                                )}\n                                            </div>\n                                        </div>\n                                    );\n                                })}\n                                <div ref={messagesEndRef} />\n                            </div>\n                            \n                            {/* Zone de saisie */}\n                            <div className=\"message-input-container\">\n                                <div className=\"message-input-wrapper\">\n                                    <textarea\n                                        ref={messageInputRef}\n                                        value={newMessage}\n                                        onChange={(e) => setNewMessage(e.target.value)}\n                                        onKeyDown={handleKeyDown}\n                                        placeholder=\"Tapez votre message...\"\n                                        className=\"message-input\"\n                                        rows=\"1\"\n                                    />\n                                    <button \n                                        onClick={sendMessage}\n                                        className=\"send-button\"\n                                        disabled={!newMessage.trim()}\n                                    >\n                                        📤\n                                    </button>\n                                </div>\n                            </div>\n                        </>\n                    ) : (\n                        <div className=\"no-conversation-selected\">\n                            <div className=\"welcome-message\">\n                                <h3>💬 Bienvenue dans la messagerie</h3>\n                                <p>Sélectionnez une conversation ou commencez-en une nouvelle</p>\n                            </div>\n                        </div>\n                    )}\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default MessagingSystem;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,QAAQ,OAAO;AACnF,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,+BAA+B;AAEtC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B;EACA,MAAM;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGL,UAAU,CAACE,WAAW,CAAC;;EAEnD;EACA,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACW,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAM,CAACuB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC;IAAE6B,IAAI,EAAE,KAAK;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;EAC5F,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;;EAE5C;EACA,MAAMqC,cAAc,GAAGnC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMoC,cAAc,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqC,eAAe,GAAGrC,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMsC,gBAAgB,GAAGpC,WAAW,CAAC,MAAM;IACvC,IAAIG,IAAI,EAAE;MACN;MACA,MAAMkC,WAAW,GAAG,CAAClC,IAAI,CAACmC,EAAE,EAAEnC,IAAI,CAACoC,OAAO,EAAEpC,IAAI,CAACqC,cAAc,EAAErC,IAAI,CAACsC,EAAE,EAAEtC,IAAI,CAACuC,OAAO,CAAC;MACvF,KAAK,MAAMJ,EAAE,IAAID,WAAW,EAAE;QAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;UAClB,OAAOM,QAAQ,CAACN,EAAE,CAAC;QACvB;MACJ;IACJ;;IAEA;IACA,IAAI;MACA,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACZ,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACvC,MAAMR,WAAW,GAAG,CAACW,QAAQ,CAACV,EAAE,EAAEU,QAAQ,CAACT,OAAO,EAAES,QAAQ,CAACR,cAAc,EAAEQ,QAAQ,CAACP,EAAE,EAAEO,QAAQ,CAACN,OAAO,CAAC;QAC3G,KAAK,MAAMJ,EAAE,IAAID,WAAW,EAAE;UAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;YAClB,OAAOM,QAAQ,CAACN,EAAE,CAAC;UACvB;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACZoC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAErC,KAAK,CAAC;IAC/C;IAEA,OAAO,IAAI;EACf,CAAC,EAAE,CAACZ,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMkD,kBAAkB,GAAGrD,WAAW,CAAC,MAAM;IACzC,IAAI,CAACG,IAAI,IAAI,CAACA,IAAI,CAACmD,OAAO,EAAE,OAAO,KAAK;;IAExC;IACA,MAAMC,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9B,OAAOA,YAAY,CAACC,QAAQ,CAACZ,QAAQ,CAACzC,IAAI,CAACmD,OAAO,CAAC,CAAC;EACxD,CAAC,EAAE,CAACnD,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMsD,mBAAmB,GAAGzD,WAAW,CAAC,YAAY;IAChD,IAAI;MACA,MAAM0D,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iFAAiFF,aAAa,EAAE,EAAE;QAC3HG,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,MAAMe,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QACd9C,QAAQ,CAAC4C,IAAI,CAAC7C,KAAK,CAAC;MACxB,CAAC,MAAM;QACHkC,OAAO,CAACpC,KAAK,CAAC,iCAAiC,EAAE+C,IAAI,CAAC/C,KAAK,CAAC;MAChE;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZoC,OAAO,CAACpC,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACvD;EACJ,CAAC,EAAE,CAACqB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAM6B,iBAAiB,GAAGjE,WAAW,CAAC,YAAY;IAC9C,IAAI;MACA,MAAM0D,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,oFAAoFF,aAAa,EAAE,EAAE;QAC9HG,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,MAAMe,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QACd1D,gBAAgB,CAACwD,IAAI,CAACzD,aAAa,CAAC;MACxC,CAAC,MAAM;QACHW,QAAQ,CAAC8C,IAAI,CAAC/C,KAAK,IAAI,6CAA6C,CAAC;MACzE;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZC,QAAQ,CAAC,gCAAgC,CAAC;MAC1CmC,OAAO,CAACpC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACnC;EACJ,CAAC,EAAE,CAACqB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAM8B,YAAY,GAAGlE,WAAW,CAAC,MAAOmE,WAAW,IAAK;IACpD,IAAI;MACA,MAAMT,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,+EAA+EF,aAAa,kBAAkBS,WAAW,EAAE,EAAE;QACtJN,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,MAAMe,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QACdtD,WAAW,CAACoD,IAAI,CAACrD,QAAQ,CAAC;QAC1B;QACA2D,kBAAkB,CAACD,WAAW,CAAC;MACnC,CAAC,MAAM;QACHhB,OAAO,CAACpC,KAAK,CAAC,6BAA6B,EAAE+C,IAAI,CAAC/C,KAAK,CAAC;MAC5D;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZoC,OAAO,CAACpC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACnD;EACJ,CAAC,EAAE,CAACqB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMgC,kBAAkB,GAAGpE,WAAW,CAAC,MAAOmE,WAAW,IAAK;IAC1D,IAAI;MACA,MAAMT,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAME,KAAK,CAAC,kEAAkE,EAAE;QAC5ES,MAAM,EAAE,MAAM;QACdR,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB,CAAC;QACDuB,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;UACjBhC,OAAO,EAAEmB,aAAa;UACtBc,aAAa,EAAEL;QACnB,CAAC;MACL,CAAC,CAAC;IACN,CAAC,CAAC,OAAOpD,KAAK,EAAE;MACZoC,OAAO,CAACpC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC/C;EACJ,CAAC,EAAE,CAACqB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMqC,WAAW,GAAGzE,WAAW,CAAC,YAAY;IACxC,IAAI,CAACW,UAAU,CAAC+D,IAAI,CAAC,CAAC,IAAI,CAACnE,oBAAoB,EAAE;IAEjD,IAAI;MACA,MAAMmD,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAChGS,MAAM,EAAE,MAAM;QACdR,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB,CAAC;QACDuB,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;UACjBI,aAAa,EAAEjB,aAAa;UAC5BkB,eAAe,EAAErE,oBAAoB,CAACiE,aAAa;UACnDK,OAAO,EAAElE,UAAU,CAAC+D,IAAI,CAAC;QAC7B,CAAC;MACL,CAAC,CAAC;MAEF,MAAMZ,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QACdpD,aAAa,CAAC,EAAE,CAAC;QACjBsD,YAAY,CAAC3D,oBAAoB,CAACiE,aAAa,CAAC;QAChDP,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHa,KAAK,CAAC,2BAA2B,GAAGhB,IAAI,CAAC/C,KAAK,CAAC;MACnD;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZ+D,KAAK,CAAC,sCAAsC,CAAC;MAC7C3B,OAAO,CAACpC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACnC;EACJ,CAAC,EAAE,CAACJ,UAAU,EAAEJ,oBAAoB,EAAE6B,gBAAgB,EAAE8B,YAAY,EAAED,iBAAiB,CAAC,CAAC;;EAEzF;EACA,MAAMc,WAAW,GAAGA,CAACnD,SAAS,EAAEoD,WAAW,KAAK;IAC5ClD,iBAAiB,CAACF,SAAS,CAAC;IAC5BI,WAAW,CAACgD,WAAW,CAAC;IACxBxD,cAAc,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMqD,eAAe,GAAGjF,WAAW,CAAC,MAAO4B,SAAS,IAAK;IACrD,IAAI,CAACG,QAAQ,CAAC2C,IAAI,CAAC,CAAC,EAAE;IAEtB,IAAI;MACA,MAAMhB,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,qEAAqE,EAAE;QAChGS,MAAM,EAAE,MAAM;QACdR,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB,CAAC;QACDuB,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;UACjBW,UAAU,EAAEtD,SAAS;UACrBW,OAAO,EAAEmB,aAAa;UACtByB,WAAW,EAAEpD,QAAQ,CAAC2C,IAAI,CAAC;QAC/B,CAAC;MACL,CAAC,CAAC;MAEF,MAAMZ,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QACdlC,iBAAiB,CAAC,IAAI,CAAC;QACvBE,WAAW,CAAC,EAAE,CAAC;QACfkC,YAAY,CAAC3D,oBAAoB,CAACiE,aAAa,CAAC;MACpD,CAAC,MAAM;QACHM,KAAK,CAAC,kCAAkC,GAAGhB,IAAI,CAAC/C,KAAK,CAAC;MAC1D;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZ+D,KAAK,CAAC,6CAA6C,CAAC;MACpD3B,OAAO,CAACpC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACnC;EACJ,CAAC,EAAE,CAACgB,QAAQ,EAAExB,oBAAoB,EAAE6B,gBAAgB,EAAE8B,YAAY,CAAC,CAAC;;EAEpE;EACA,MAAMkB,aAAa,GAAGpF,WAAW,CAAC,OAAO4B,SAAS,EAAEyD,UAAU,KAAK;IAC/D,IAAI;MACA,MAAM3B,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACsB,aAAa,EAAE;MAEpB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,uEAAuE,EAAE;QAClGS,MAAM,EAAE,MAAM;QACdR,OAAO,EAAE;UACL,eAAe,EAAE,UAAUf,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;UAC1D,cAAc,EAAE;QACpB,CAAC;QACDuB,IAAI,EAAErB,IAAI,CAACsB,SAAS,CAAC;UACjBW,UAAU,EAAEtD,SAAS;UACrBW,OAAO,EAAEmB,aAAa;UACtB4B,WAAW,EAAED,UAAU,CAAC;QAC5B,CAAC;MACL,CAAC,CAAC;MAEF,MAAMvB,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;MAClC,IAAID,IAAI,CAACE,OAAO,EAAE;QACdE,YAAY,CAAC3D,oBAAoB,CAACiE,aAAa,CAAC;QAChDP,iBAAiB,CAAC,CAAC;MACvB,CAAC,MAAM;QACHa,KAAK,CAAC,iCAAiC,GAAGhB,IAAI,CAAC/C,KAAK,CAAC;MACzD;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZ+D,KAAK,CAAC,4CAA4C,CAAC;MACnD3B,OAAO,CAACpC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACnC;IAEAS,cAAc,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;EAChE,CAAC,EAAE,CAACrB,oBAAoB,EAAE6B,gBAAgB,EAAE8B,YAAY,EAAED,iBAAiB,CAAC,CAAC;;EAE7E;EACA,MAAMsB,aAAa,GAAIC,CAAC,IAAK;IACzB,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MAClCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBlB,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC;;EAED;EACA,MAAMmB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACzB,CAAAA,qBAAA,GAAA5D,cAAc,CAAC6D,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAClE,CAAC;;EAED;EACAnG,SAAS,CAAC,MAAM;IACZ+F,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACnF,QAAQ,CAAC,CAAC;EAEdZ,SAAS,CAAC,MAAM;IACZ,IAAI,CAACO,SAAS,IAAIiD,kBAAkB,CAAC,CAAC,EAAE;MACpCI,mBAAmB,CAAC,CAAC;MACrBQ,iBAAiB,CAAC,CAAC;IACvB;EACJ,CAAC,EAAE,CAAC7D,SAAS,EAAEiD,kBAAkB,EAAEI,mBAAmB,EAAEQ,iBAAiB,CAAC,CAAC;EAE3EpE,SAAS,CAAC,MAAM;IACZ,MAAMoG,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIhE,cAAc,CAAC4D,OAAO,IAAI,CAAC5D,cAAc,CAAC4D,OAAO,CAACK,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAC1E5E,cAAc,CAAC;UAAEC,IAAI,EAAE,KAAK;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAK,CAAC,CAAC;MAChE;IACJ,CAAC;IAEDyE,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMvC,aAAa,GAAGtB,gBAAgB,CAAC,CAAC;EAExC,IAAIhC,SAAS,EAAE;IACX,oBACIT,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC7BpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eACvCpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,2CAAyC,CAC3C,CACJ,CAAC;EAEd;EAEA,IAAI,CAACrD,aAAa,EAAE;IAChB,oBACI/D,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC7BpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,sBAAsB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACjCpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/BpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,uCAA+B,CAAC,eACpCpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,6EAAwE,CAAC,eAC5EpH,KAAA,CAAA6G,aAAA;MAAQQ,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;MAACV,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,2BAE5E,CACP,CACJ,CACJ,CAAC;EAEd;EAEA,IAAI,CAAC1D,kBAAkB,CAAC,CAAC,EAAE;IACvB,oBACI1D,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC7BpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,yBAAyB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACpCpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,uBAAuB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAClCpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iCAAmB,CAAC,eACxBpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,0FAAqF,CAAC,eACzFpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,6EAAwE,CAC1E,CACJ,CACJ,CAAC;EAEd;EAEA,oBACIpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhCpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAe,CAAC,eACpBpH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,sBAAsB;IAChCO,OAAO,EAAEA,CAAA,KAAM5F,sBAAsB,CAAC,IAAI,CAAE;IAC5CgG,KAAK,EAAC,uBAAuB;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CAAC,eAENpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B1G,aAAa,CAACgH,MAAM,KAAK,CAAC,gBACvB1H,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qBAAsB,CAAC,eAC1BpH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,wBAAwB;IAClCO,OAAO,EAAEA,CAAA,KAAM5F,sBAAsB,CAAC,IAAI,CAAE;IAAAsF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/C,4BAEO,CACP,CAAC,GAEN1G,aAAa,CAACiH,GAAG,CAACC,IAAI,iBAClB5H,KAAA,CAAA6G,aAAA;IACIf,GAAG,EAAE8B,IAAI,CAAC/C,aAAc;IACxBiC,SAAS,EAAE,qBAAqB,CAAAlG,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEiE,aAAa,MAAK+C,IAAI,CAAC/C,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC7GwC,OAAO,EAAEA,CAAA,KAAM;MACXxG,uBAAuB,CAAC+G,IAAI,CAAC;MAC7BrD,YAAY,CAACqD,IAAI,CAAC/C,aAAa,CAAC;IACpC,CAAE;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BQ,IAAI,CAACC,eAAe,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC3C,CAAC,eACN/H,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEQ,IAAI,CAACC,eAAqB,CAAC,eAC/D7H,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCQ,IAAI,CAACI,YAAY,IAAI,eACrB,CACJ,CAAC,EACLJ,IAAI,CAACK,YAAY,GAAG,CAAC,iBAClBjI,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEQ,IAAI,CAACK,YAAkB,CAEzD,CACR,CAEJ,CACJ,CAAC,eAGNjI,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACrBxG,oBAAoB,gBACjBZ,KAAA,CAAA6G,aAAA,CAAA7G,KAAA,CAAAkI,QAAA,qBAEIlI,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvBxG,oBAAoB,CAACiH,eAAe,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC3D,CAAC,eACN/H,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BxG,oBAAoB,CAACiH,eACrB,CACJ,CACJ,CAAC,eAGN7H,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BtG,QAAQ,CAAC6G,GAAG,CAACzC,OAAO,IAAI;IACrB,MAAMiD,YAAY,GAAGjD,OAAO,CAACF,aAAa,KAAKjB,aAAa;IAC5D,MAAMqE,SAAS,GAAGlG,cAAc,KAAKgD,OAAO,CAACvC,EAAE;IAE/C,oBACI3C,KAAA,CAAA6G,aAAA;MACIf,GAAG,EAAEZ,OAAO,CAACvC,EAAG;MAChBmE,SAAS,EAAE,mBAAmBqB,YAAY,GAAG,aAAa,GAAG,eAAe,EAAG;MAAApB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAE/EpH,KAAA,CAAA6G,aAAA;MACIC,SAAS,EAAE,kBAAkBqB,YAAY,GAAG,MAAM,GAAG,UAAU,EAAG;MAClEE,aAAa,EAAGxC,CAAC,IAAK;QAClB,IAAIsC,YAAY,EAAE;UACdtC,CAAC,CAACG,cAAc,CAAC,CAAC;UAClBnE,cAAc,CAAC;YACXC,IAAI,EAAE,IAAI;YACVC,CAAC,EAAE8D,CAAC,CAACyC,OAAO;YACZtG,CAAC,EAAE6D,CAAC,CAAC0C,OAAO;YACZtG,SAAS,EAAEiD,OAAO,CAACvC;UACvB,CAAC,CAAC;QACN;MACJ,CAAE;MAAAoE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEDgB,SAAS,gBACNpI,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,wBAAwB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACnCpH,KAAA,CAAA6G,aAAA;MACI2B,KAAK,EAAEpG,QAAS;MAChBqG,QAAQ,EAAG5C,CAAC,IAAKxD,WAAW,CAACwD,CAAC,CAACY,MAAM,CAAC+B,KAAK,CAAE;MAC7C1B,SAAS,EAAC,oBAAoB;MAC9B4B,SAAS;MAAA3B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACZ,CAAC,eACFpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,sBAAsB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACjCpH,KAAA,CAAA6G,aAAA;MACIC,SAAS,EAAC,eAAe;MACzBO,OAAO,EAAEA,CAAA,KAAM/B,eAAe,CAACJ,OAAO,CAACvC,EAAE,CAAE;MAAAoE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9C,oBAEO,CAAC,eACTpH,KAAA,CAAA6G,aAAA;MACIC,SAAS,EAAC,iBAAiB;MAC3BO,OAAO,EAAEA,CAAA,KAAM;QACXlF,iBAAiB,CAAC,IAAI,CAAC;QACvBE,WAAW,CAAC,EAAE,CAAC;MACnB,CAAE;MAAA0E,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACL,gBAEO,CACP,CACJ,CAAC,gBAENpH,KAAA,CAAA6G,aAAA,CAAA7G,KAAA,CAAAkI,QAAA,qBACIlI,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC3BlC,OAAO,CAACA,OACR,CAAC,eACNlF,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBpH,KAAA,CAAA6G,aAAA;MAAMC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACzB,IAAIuB,IAAI,CAACzD,OAAO,CAAC0D,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CACC,CAAC,EACN7D,OAAO,CAAC8D,OAAO,KAAK,CAAC,iBAClBhJ,KAAA,CAAA6G,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,wBACV,EAAC,IAAIuB,IAAI,CAACzD,OAAO,CAAC+D,iBAAiB,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAChF,CACT,EACAf,YAAY,iBACTnI,KAAA,CAAA6G,aAAA;MAAMC,SAAS,EAAE,kBAAkB5B,OAAO,CAACiE,EAAE,KAAK,CAAC,GAAG,MAAM,GAAG,QAAQ,EAAG;MAAApC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACrElC,OAAO,CAACiE,EAAE,KAAK,CAAC,GAAG,IAAI,GAAG,GACzB,CAET,CACP,CAEL,CACJ,CAAC;EAEd,CAAC,CAAC,eACFnJ,KAAA,CAAA6G,aAAA;IAAKuC,GAAG,EAAE9G,cAAe;IAAAyE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC1B,CAAC,eAGNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpCpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCpH,KAAA,CAAA6G,aAAA;IACIuC,GAAG,EAAE5G,eAAgB;IACrBgG,KAAK,EAAExH,UAAW;IAClByH,QAAQ,EAAG5C,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACY,MAAM,CAAC+B,KAAK,CAAE;IAC/Ca,SAAS,EAAEzD,aAAc;IACzB0D,WAAW,EAAC,wBAAwB;IACpCxC,SAAS,EAAC,eAAe;IACzByC,IAAI,EAAC,GAAG;IAAAxC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eACFpH,KAAA,CAAA6G,aAAA;IACIQ,OAAO,EAAEvC,WAAY;IACrBgC,SAAS,EAAC,aAAa;IACvB0C,QAAQ,EAAE,CAACxI,UAAU,CAAC+D,IAAI,CAAC,CAAE;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CACJ,CACP,CAAC,gBAEHpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrCpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,2CAAmC,CAAC,eACxCpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,+DAA6D,CAC/D,CACJ,CAER,CACJ,CACJ,CAAC;AAEd,CAAC;AAED,eAAe7G,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}