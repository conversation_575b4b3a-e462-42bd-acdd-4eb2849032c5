{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\FacturesCRUD.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\nconst FacturesCRUD = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [factures, setFactures] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingFacture, setEditingFacture] = useState(null);\n  const [etudiants, setEtudiants] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    mois: '',\n    montant: '',\n    statut: 'Non payé',\n    date_paiement: ''\n  });\n\n  // Vérifier si l'utilisateur est Admin\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'responsable';\n  useEffect(() => {\n    fetchFactures();\n    if (isAdmin) {\n      fetchEtudiants();\n    }\n  }, [isAdmin]);\n  const fetchFactures = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/factures/', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setFactures(response.data);\n    } catch (error) {\n      console.error('Erreur lors du chargement des factures:', error);\n      Swal.fire('Erreur', 'Impossible de charger les factures', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchEtudiants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n      if (response.data.success) {\n        setEtudiants(response.data.etudiants);\n        console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n      } else {\n        console.error('❌ Erreur API étudiants:', response.data.error);\n        setEtudiants([]);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des étudiants:', error);\n      setEtudiants([]);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des factures', 'error');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/factures/';\n      const method = editingFacture ? 'PUT' : 'POST';\n      const data = editingFacture ? {\n        ...formData,\n        id: editingFacture.id\n      } : formData;\n      await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      Swal.fire('Succès', `Facture ${editingFacture ? 'modifiée' : 'créée'} avec succès`, 'success');\n      setShowModal(false);\n      setEditingFacture(null);\n      resetForm();\n      fetchFactures();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur:', error);\n      Swal.fire('Erreur', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = facture => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des factures', 'error');\n      return;\n    }\n    setEditingFacture(facture);\n    setFormData({\n      etudiant_id: facture.etudiant_id,\n      mois: facture.mois,\n      montant: facture.montant,\n      statut: facture.statut,\n      date_paiement: facture.date_paiement || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des factures', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action est irréversible!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        const token = localStorage.getItem('token');\n        await axios.delete('http://localhost/Project_PFE/Backend/pages/factures/', {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        Swal.fire('Supprimé!', 'La facture a été supprimée.', 'success');\n        fetchFactures();\n      } catch (error) {\n        console.error('Erreur:', error);\n        Swal.fire('Erreur', 'Impossible de supprimer la facture', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      mois: '',\n      montant: '',\n      statut: 'Non payé',\n      date_paiement: ''\n    });\n  };\n  const formatMontant = montant => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'MAD'\n    }).format(montant);\n  };\n  const getStatutBadge = statut => {\n    const badgeClass = statut === 'Payé' ? 'badge-success' : 'badge-danger';\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: `badge ${badgeClass}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 16\n      }\n    }, statut);\n  };\n\n  // Filtrage des données\n  const filteredFactures = factures.filter(facture => {\n    var _facture$etudiant_nom, _facture$etudiant_ema, _facture$mois;\n    const matchesSearch = ((_facture$etudiant_nom = facture.etudiant_nom) === null || _facture$etudiant_nom === void 0 ? void 0 : _facture$etudiant_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_facture$etudiant_ema = facture.etudiant_email) === null || _facture$etudiant_ema === void 0 ? void 0 : _facture$etudiant_ema.toLowerCase().includes(searchTerm.toLowerCase())) || ((_facture$mois = facture.mois) === null || _facture$mois === void 0 ? void 0 : _facture$mois.includes(searchTerm));\n    const matchesStatus = statusFilter === 'all' || facture.statut === statusFilter;\n    return matchesSearch && matchesStatus;\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentFactures = filteredFactures.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredFactures.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when filters change\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, statusFilter]);\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 17\n      }\n    }, \"Chargement des factures...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCB0 Gestion des Factures\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 21\n    }\n  }, filteredFactures.length, \" facture(s) trouv\\xE9e(s)\", totalPages > 1 && ` • Page ${currentPage}/${totalPages}`), isAdmin && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 29\n    }\n  }), \" Nouvelle Facture\"))), !isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '15px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '8px',\n      marginBottom: '20px',\n      border: '1px solid #bbdefb'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      color: '#1976d2'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 21\n    }\n  }, \"\\u2139\\uFE0F Vous consultez vos factures en mode lecture seule. Seul l'administrateur peut cr\\xE9er, modifier ou supprimer des factures.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filters-section\",\n    style: {\n      display: 'flex',\n      gap: '15px',\n      marginBottom: '20px',\n      padding: '15px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-box\",\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher par nom, email ou mois...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"status-filter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: statusFilter,\n    onChange: e => setStatusFilter(e.target.value),\n    style: {\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px',\n      minWidth: '150px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 25\n    }\n  }, \"Tous les statuts\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"Pay\\xE9\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 25\n    }\n  }, \"Pay\\xE9\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"Non pay\\xE9\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 25\n    }\n  }, \"Non pay\\xE9\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 13\n    }\n  }, filteredFactures.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/finance.png\",\n    alt: \"Aucune facture\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 25\n    }\n  }, \"Aucune facture trouv\\xE9e\"), searchTerm && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setSearchTerm(''),\n    className: \"btn btn-secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 29\n    }\n  }, \"Effacer la recherche\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC64 \\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Mois\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCB0 Montant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Statut\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCB3 Date de paiement\"), isAdmin && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 49\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 29\n    }\n  }, currentFactures.map(facture => /*#__PURE__*/React.createElement(\"tr\", {\n    key: facture.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"student-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 49\n    }\n  }, facture.etudiant_nom), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 49\n    }\n  }, facture.etudiant_email))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 45\n    }\n  }, facture.mois)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#2c3e50',\n      fontSize: '1.1em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 45\n    }\n  }, formatMontant(facture.montant))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 41\n    }\n  }, getStatutBadge(facture.statut)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 41\n    }\n  }, facture.date_paiement ? /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      color: '#28a745'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 49\n    }\n  }, \"\\u2705 \", new Date(facture.date_paiement).toLocaleDateString('fr-FR')) : /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 49\n    }\n  }, \"\\u23F3 En attente\")), isAdmin && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(facture),\n    title: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(facture.id),\n    title: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginTop: '20px',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    style: {\n      opacity: currentPage === 1 ? 0.5 : 1,\n      cursor: currentPage === 1 ? 'not-allowed' : 'pointer'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 21\n    }\n  }, \"\\u2B05\\uFE0F Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '5px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 21\n    }\n  }, Array.from({\n    length: Math.min(5, totalPages)\n  }, (_, i) => {\n    let pageNumber;\n    if (totalPages <= 5) {\n      pageNumber = i + 1;\n    } else if (currentPage <= 3) {\n      pageNumber = i + 1;\n    } else if (currentPage >= totalPages - 2) {\n      pageNumber = totalPages - 4 + i;\n    } else {\n      pageNumber = currentPage - 2 + i;\n    }\n    return /*#__PURE__*/React.createElement(\"button\", {\n      key: pageNumber,\n      onClick: () => paginate(pageNumber),\n      style: {\n        padding: '8px 12px',\n        border: '1px solid #ddd',\n        borderRadius: '4px',\n        backgroundColor: currentPage === pageNumber ? '#007bff' : 'white',\n        color: currentPage === pageNumber ? 'white' : '#007bff',\n        cursor: 'pointer',\n        fontWeight: currentPage === pageNumber ? 'bold' : 'normal'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 33\n      }\n    }, pageNumber);\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    style: {\n      opacity: currentPage === totalPages ? 0.5 : 1,\n      cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 21\n    }\n  }, \"Suivant \\u27A1\\uFE0F\")), filteredFactures.length > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'center',\n      marginTop: '15px',\n      padding: '10px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '4px',\n      fontSize: '14px',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 17\n    }\n  }, \"Affichage de \", indexOfFirstItem + 1, \" \\xE0 \", Math.min(indexOfLastItem, filteredFactures.length), \" sur \", filteredFactures.length, \" facture(s)\", totalPages > 1 && ` • Page ${currentPage} sur ${totalPages}`), filteredFactures.length > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-section\",\n    style: {\n      marginTop: '30px',\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#28a745',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 25\n    }\n  }, filteredFactures.filter(f => f.statut === 'Payé').length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 25\n    }\n  }, \"Factures pay\\xE9es\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 487,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#dc3545',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 25\n    }\n  }, filteredFactures.filter(f => f.statut === 'Non payé').length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 25\n    }\n  }, \"Factures impay\\xE9es\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#007bff',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 25\n    }\n  }, formatMontant(filteredFactures.filter(f => f.statut === 'Payé').reduce((sum, f) => sum + parseFloat(f.montant), 0))), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 25\n    }\n  }, \"Total encaiss\\xE9\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#ffc107',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 25\n    }\n  }, formatMontant(filteredFactures.filter(f => f.statut === 'Non payé').reduce((sum, f) => sum + parseFloat(f.montant), 0))), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 25\n    }\n  }, \"En attente\"))), showModal && isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 518,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 29\n    }\n  }, editingFacture ? 'Modifier la facture' : 'Nouvelle facture'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingFacture(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 533,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => setFormData({\n      ...formData,\n      etudiant_id: e.target.value\n    }),\n    required: true,\n    disabled: editingFacture,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px',\n      backgroundColor: editingFacture ? '#e9ecef' : 'white'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 536,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.etudiant_id,\n    value: etudiant.etudiant_id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 41\n    }\n  }, etudiant.nom, \" - \", etudiant.email, etudiant.classe_nom && ` (${etudiant.classe_nom})`))), editingFacture && /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 37\n    }\n  }, \"L'\\xE9tudiant ne peut pas \\xEAtre modifi\\xE9 apr\\xE8s cr\\xE9ation\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 33\n    }\n  }, \"Mois (YYYY-MM) *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"month\",\n    value: formData.mois,\n    onChange: e => setFormData({\n      ...formData,\n      mois: e.target.value\n    }),\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 33\n    }\n  }, \"Montant (MAD) *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"number\",\n    step: \"0.01\",\n    min: \"0\",\n    value: formData.montant,\n    onChange: e => setFormData({\n      ...formData,\n      montant: e.target.value\n    }),\n    placeholder: \"Ex: 1500.00\",\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 33\n    }\n  }, \"Statut de paiement\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.statut,\n    onChange: e => setFormData({\n      ...formData,\n      statut: e.target.value\n    }),\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 604,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"Non pay\\xE9\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 615,\n      columnNumber: 37\n    }\n  }, \"Non pay\\xE9\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"Pay\\xE9\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 37\n    }\n  }, \"Pay\\xE9\"))), formData.statut === 'Payé' && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 37\n    }\n  }, \"Date de paiement\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_paiement,\n    onChange: e => setFormData({\n      ...formData,\n      date_paiement: e.target.value\n    }),\n    max: new Date().toISOString().split('T')[0],\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 636,\n      columnNumber: 37\n    }\n  }, \"Laissez vide pour utiliser la date actuelle\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 33\n    }\n  }, editingFacture ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingFacture(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default FacturesCRUD;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "FacturesCRUD", "user", "factures", "setFactures", "loading", "setLoading", "showModal", "setShowModal", "editingFacture", "setEditingFacture", "etudiants", "setEtudiants", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "etudiant_id", "mois", "montant", "statut", "date_paiement", "isAdmin", "role", "fetchFactures", "fetchEtudiants", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "error", "console", "fire", "log", "success", "length", "handleSubmit", "e", "preventDefault", "url", "method", "id", "resetForm", "_error$response", "_error$response$data", "handleEdit", "facture", "handleDelete", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "delete", "formatMontant", "Intl", "NumberFormat", "style", "currency", "format", "getStatutBadge", "badgeClass", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "filteredFactures", "filter", "_facture$etudiant_nom", "_facture$etudiant_ema", "_facture$mois", "matchesSearch", "etudiant_nom", "toLowerCase", "includes", "etudiant_email", "matchesStatus", "indexOfLastItem", "indexOfFirstItem", "currentFactures", "slice", "totalPages", "Math", "ceil", "paginate", "pageNumber", "onClick", "src", "alt", "padding", "backgroundColor", "borderRadius", "marginBottom", "border", "margin", "color", "display", "gap", "flex", "type", "placeholder", "value", "onChange", "target", "width", "min<PERSON><PERSON><PERSON>", "map", "key", "fontSize", "Date", "toLocaleDateString", "justifyContent", "alignItems", "marginTop", "disabled", "opacity", "cursor", "Array", "from", "min", "_", "i", "fontWeight", "textAlign", "gridTemplateColumns", "f", "reduce", "sum", "parseFloat", "onSubmit", "required", "etudiant", "nom", "email", "classe_nom", "step", "max", "toISOString", "split"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/FacturesCRUD.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\n\nconst FacturesCRUD = () => {\n    const { user } = useContext(AuthContext);\n    const [factures, setFactures] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingFacture, setEditingFacture] = useState(null);\n    const [etudiants, setEtudiants] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('all');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        mois: '',\n        montant: '',\n        statut: 'Non payé',\n        date_paiement: ''\n    });\n\n    // Vérifier si l'utilisateur est Admin\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';\n\n    useEffect(() => {\n        fetchFactures();\n        if (isAdmin) {\n            fetchEtudiants();\n        }\n    }, [isAdmin]);\n\n    const fetchFactures = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/factures/', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            setFactures(response.data);\n        } catch (error) {\n            console.error('Erreur lors du chargement des factures:', error);\n            Swal.fire('Erreur', 'Impossible de charger les factures', 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchEtudiants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n\n            if (response.data.success) {\n                setEtudiants(response.data.etudiants);\n                console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n            } else {\n                console.error('❌ Erreur API étudiants:', response.data.error);\n                setEtudiants([]);\n            }\n        } catch (error) {\n            console.error('Erreur lors du chargement des étudiants:', error);\n            setEtudiants([]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des factures', 'error');\n            return;\n        }\n\n        try {\n            const token = localStorage.getItem('token');\n            const url = 'http://localhost/Project_PFE/Backend/pages/factures/';\n            const method = editingFacture ? 'PUT' : 'POST';\n            const data = editingFacture ? { ...formData, id: editingFacture.id } : formData;\n\n            await axios({\n                method,\n                url,\n                data,\n                headers: { \n                    Authorization: `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            Swal.fire('Succès', `Facture ${editingFacture ? 'modifiée' : 'créée'} avec succès`, 'success');\n            setShowModal(false);\n            setEditingFacture(null);\n            resetForm();\n            fetchFactures();\n        } catch (error) {\n            console.error('Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (facture) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des factures', 'error');\n            return;\n        }\n\n        setEditingFacture(facture);\n        setFormData({\n            etudiant_id: facture.etudiant_id,\n            mois: facture.mois,\n            montant: facture.montant,\n            statut: facture.statut,\n            date_paiement: facture.date_paiement || ''\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des factures', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr?',\n            text: 'Cette action est irréversible!',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer!',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                const token = localStorage.getItem('token');\n                await axios.delete('http://localhost/Project_PFE/Backend/pages/factures/', {\n                    headers: { \n                        Authorization: `Bearer ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n                Swal.fire('Supprimé!', 'La facture a été supprimée.', 'success');\n                fetchFactures();\n            } catch (error) {\n                console.error('Erreur:', error);\n                Swal.fire('Erreur', 'Impossible de supprimer la facture', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            mois: '',\n            montant: '',\n            statut: 'Non payé',\n            date_paiement: ''\n        });\n    };\n\n    const formatMontant = (montant) => {\n        return new Intl.NumberFormat('fr-FR', {\n            style: 'currency',\n            currency: 'MAD'\n        }).format(montant);\n    };\n\n    const getStatutBadge = (statut) => {\n        const badgeClass = statut === 'Payé' ? 'badge-success' : 'badge-danger';\n        return <span className={`badge ${badgeClass}`}>{statut}</span>;\n    };\n\n    // Filtrage des données\n    const filteredFactures = factures.filter(facture => {\n        const matchesSearch = facture.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                             facture.etudiant_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                             facture.mois?.includes(searchTerm);\n\n        const matchesStatus = statusFilter === 'all' || facture.statut === statusFilter;\n\n        return matchesSearch && matchesStatus;\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentFactures = filteredFactures.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredFactures.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    // Reset pagination when filters change\n    React.useEffect(() => {\n        setCurrentPage(1);\n    }, [searchTerm, statusFilter]);\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des factures...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>💰 Gestion des Factures</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredFactures.length} facture(s) trouvée(s)\n                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}\n                    </span>\n                    {isAdmin && (\n                        <button \n                            className=\"btn btn-primary\"\n                            onClick={() => setShowModal(true)}\n                        >\n                            <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouvelle Facture\n                        </button>\n                    )}\n                </div>\n            </div>\n\n            {/* Message d'information pour les non-admins */}\n            {!isAdmin && (\n                <div style={{\n                    padding: '15px',\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: '8px',\n                    marginBottom: '20px',\n                    border: '1px solid #bbdefb'\n                }}>\n                    <p style={{ margin: '0', color: '#1976d2' }}>\n                        ℹ️ Vous consultez vos factures en mode lecture seule. \n                        Seul l'administrateur peut créer, modifier ou supprimer des factures.\n                    </p>\n                </div>\n            )}\n\n            {/* Filtres */}\n            <div className=\"filters-section\" style={{\n                display: 'flex',\n                gap: '15px',\n                marginBottom: '20px',\n                padding: '15px',\n                backgroundColor: '#f8f9fa',\n                borderRadius: '8px'\n            }}>\n                <div className=\"search-box\" style={{ flex: 1 }}>\n                    <input\n                        type=\"text\"\n                        placeholder=\"🔍 Rechercher par nom, email ou mois...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                        style={{\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '6px'\n                        }}\n                    />\n                </div>\n                <div className=\"status-filter\">\n                    <select\n                        value={statusFilter}\n                        onChange={(e) => setStatusFilter(e.target.value)}\n                        style={{\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '6px',\n                            minWidth: '150px'\n                        }}\n                    >\n                        <option value=\"all\">Tous les statuts</option>\n                        <option value=\"Payé\">Payé</option>\n                        <option value=\"Non payé\">Non payé</option>\n                    </select>\n                </div>\n            </div>\n\n            <div className=\"factures-grid\">\n                {filteredFactures.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/finance.png\" alt=\"Aucune facture\" />\n                        <p>Aucune facture trouvée</p>\n                        {searchTerm && (\n                            <button \n                                onClick={() => setSearchTerm('')}\n                                className=\"btn btn-secondary\"\n                            >\n                                Effacer la recherche\n                            </button>\n                        )}\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>👤 Étudiant</th>\n                                    <th>📅 Mois</th>\n                                    <th>💰 Montant</th>\n                                    <th>📊 Statut</th>\n                                    <th>💳 Date de paiement</th>\n                                    {isAdmin && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentFactures.map((facture) => (\n                                    <tr key={facture.id}>\n                                        <td>\n                                            <div className=\"student-info\">\n                                                <strong>{facture.etudiant_nom}</strong>\n                                                <small>{facture.etudiant_email}</small>\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{ \n                                                padding: '4px 8px', \n                                                backgroundColor: '#e3f2fd', \n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {facture.mois}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            <strong style={{ color: '#2c3e50', fontSize: '1.1em' }}>\n                                                {formatMontant(facture.montant)}\n                                            </strong>\n                                        </td>\n                                        <td>{getStatutBadge(facture.statut)}</td>\n                                        <td>\n                                            {facture.date_paiement ? (\n                                                <span style={{ color: '#28a745' }}>\n                                                    ✅ {new Date(facture.date_paiement).toLocaleDateString('fr-FR')}\n                                                </span>\n                                            ) : (\n                                                <span style={{ color: '#6c757d' }}>\n                                                    ⏳ En attente\n                                                </span>\n                                            )}\n                                        </td>\n                                        {isAdmin && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button \n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(facture)}\n                                                        title=\"Modifier\"\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                    </button>\n                                                    <button \n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(facture.id)}\n                                                        title=\"Supprimer\"\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n                <div style={{\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    marginTop: '20px',\n                    gap: '10px'\n                }}>\n                    <button\n                        className=\"btn btn-secondary\"\n                        onClick={() => paginate(currentPage - 1)}\n                        disabled={currentPage === 1}\n                        style={{\n                            opacity: currentPage === 1 ? 0.5 : 1,\n                            cursor: currentPage === 1 ? 'not-allowed' : 'pointer'\n                        }}\n                    >\n                        ⬅️ Précédent\n                    </button>\n\n                    {/* Numéros de pages */}\n                    <div style={{ display: 'flex', gap: '5px' }}>\n                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\n                            let pageNumber;\n                            if (totalPages <= 5) {\n                                pageNumber = i + 1;\n                            } else if (currentPage <= 3) {\n                                pageNumber = i + 1;\n                            } else if (currentPage >= totalPages - 2) {\n                                pageNumber = totalPages - 4 + i;\n                            } else {\n                                pageNumber = currentPage - 2 + i;\n                            }\n\n                            return (\n                                <button\n                                    key={pageNumber}\n                                    onClick={() => paginate(pageNumber)}\n                                    style={{\n                                        padding: '8px 12px',\n                                        border: '1px solid #ddd',\n                                        borderRadius: '4px',\n                                        backgroundColor: currentPage === pageNumber ? '#007bff' : 'white',\n                                        color: currentPage === pageNumber ? 'white' : '#007bff',\n                                        cursor: 'pointer',\n                                        fontWeight: currentPage === pageNumber ? 'bold' : 'normal'\n                                    }}\n                                >\n                                    {pageNumber}\n                                </button>\n                            );\n                        })}\n                    </div>\n\n                    <button\n                        className=\"btn btn-secondary\"\n                        onClick={() => paginate(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                        style={{\n                            opacity: currentPage === totalPages ? 0.5 : 1,\n                            cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'\n                        }}\n                    >\n                        Suivant ➡️\n                    </button>\n                </div>\n            )}\n\n            {/* Informations de pagination */}\n            {filteredFactures.length > 0 && (\n                <div style={{\n                    textAlign: 'center',\n                    marginTop: '15px',\n                    padding: '10px',\n                    backgroundColor: '#f8f9fa',\n                    borderRadius: '4px',\n                    fontSize: '14px',\n                    color: '#6c757d'\n                }}>\n                    Affichage de {indexOfFirstItem + 1} à {Math.min(indexOfLastItem, filteredFactures.length)} sur {filteredFactures.length} facture(s)\n                    {totalPages > 1 && ` • Page ${currentPage} sur ${totalPages}`}\n                </div>\n            )}\n\n            {/* Statistiques */}\n            {filteredFactures.length > 0 && (\n                <div className=\"stats-section\" style={{\n                    marginTop: '30px',\n                    padding: '20px',\n                    backgroundColor: '#f8f9fa',\n                    borderRadius: '8px',\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '15px'\n                }}>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#28a745', margin: '0' }}>\n                            {filteredFactures.filter(f => f.statut === 'Payé').length}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Factures payées</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#dc3545', margin: '0' }}>\n                            {filteredFactures.filter(f => f.statut === 'Non payé').length}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Factures impayées</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#007bff', margin: '0' }}>\n                            {formatMontant(\n                                filteredFactures\n                                    .filter(f => f.statut === 'Payé')\n                                    .reduce((sum, f) => sum + parseFloat(f.montant), 0)\n                            )}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total encaissé</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#ffc107', margin: '0' }}>\n                            {formatMontant(\n                                filteredFactures\n                                    .filter(f => f.statut === 'Non payé')\n                                    .reduce((sum, f) => sum + parseFloat(f.montant), 0)\n                            )}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>En attente</p>\n                    </div>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier une facture */}\n            {showModal && isAdmin && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingFacture ? 'Modifier la facture' : 'Nouvelle facture'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingFacture(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant *</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n                                    required\n                                    disabled={editingFacture}\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px',\n                                        backgroundColor: editingFacture ? '#e9ecef' : 'white'\n                                    }}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant</option>\n                                    {etudiants.map((etudiant) => (\n                                        <option key={etudiant.etudiant_id} value={etudiant.etudiant_id}>\n                                            {etudiant.nom} - {etudiant.email}\n                                            {etudiant.classe_nom && ` (${etudiant.classe_nom})`}\n                                        </option>\n                                    ))}\n                                </select>\n                                {editingFacture && (\n                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                        L'étudiant ne peut pas être modifié après création\n                                    </small>\n                                )}\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Mois (YYYY-MM) *</label>\n                                <input\n                                    type=\"month\"\n                                    value={formData.mois}\n                                    onChange={(e) => setFormData({...formData, mois: e.target.value})}\n                                    required\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Montant (MAD) *</label>\n                                <input\n                                    type=\"number\"\n                                    step=\"0.01\"\n                                    min=\"0\"\n                                    value={formData.montant}\n                                    onChange={(e) => setFormData({...formData, montant: e.target.value})}\n                                    placeholder=\"Ex: 1500.00\"\n                                    required\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Statut de paiement</label>\n                                <select\n                                    value={formData.statut}\n                                    onChange={(e) => setFormData({...formData, statut: e.target.value})}\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                >\n                                    <option value=\"Non payé\">Non payé</option>\n                                    <option value=\"Payé\">Payé</option>\n                                </select>\n                            </div>\n\n                            {formData.statut === 'Payé' && (\n                                <div className=\"form-group\">\n                                    <label>Date de paiement</label>\n                                    <input\n                                        type=\"date\"\n                                        value={formData.date_paiement}\n                                        onChange={(e) => setFormData({...formData, date_paiement: e.target.value})}\n                                        max={new Date().toISOString().split('T')[0]}\n                                        style={{\n                                            width: '100%',\n                                            padding: '10px',\n                                            border: '1px solid #ced4da',\n                                            borderRadius: '4px',\n                                            fontSize: '14px'\n                                        }}\n                                    />\n                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                        Laissez vide pour utiliser la date actuelle\n                                    </small>\n                                </div>\n                            )}\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingFacture ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingFacture(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default FacturesCRUD;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwB,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACrC2B,WAAW,EAAE,EAAE;IACfC,IAAI,EAAE,EAAE;IACRC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,UAAU;IAClBC,aAAa,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAAzB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,MAAK,OAAO,IAAI,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,MAAK,OAAO,IAAI,CAAA1B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,MAAK,aAAa;EAEhGhC,SAAS,CAAC,MAAM;IACZiC,aAAa,CAAC,CAAC;IACf,IAAIF,OAAO,EAAE;MACTG,cAAc,CAAC,CAAC;IACpB;EACJ,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;EAEb,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,sDAAsD,EAAE;QACrFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MACF3B,WAAW,CAAC8B,QAAQ,CAACI,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DvC,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;IACtE,CAAC,SAAS;MACNnC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,mEAAmE,EAAE;QAClGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MAEFS,OAAO,CAACE,GAAG,CAAC,kCAAkC,EAAER,QAAQ,CAACI,IAAI,CAAC;MAE9D,IAAIJ,QAAQ,CAACI,IAAI,CAACK,OAAO,EAAE;QACvB/B,YAAY,CAACsB,QAAQ,CAACI,IAAI,CAAC3B,SAAS,CAAC;QACrC6B,OAAO,CAACE,GAAG,CAAC,sBAAsB,EAAER,QAAQ,CAACI,IAAI,CAAC3B,SAAS,CAACiC,MAAM,CAAC;MACvE,CAAC,MAAM;QACHJ,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEL,QAAQ,CAACI,IAAI,CAACC,KAAK,CAAC;QAC7D3B,YAAY,CAAC,EAAE,CAAC;MACpB;IACJ,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE3B,YAAY,CAAC,EAAE,CAAC;IACpB;EACJ,CAAC;EAED,MAAMiC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACpB,OAAO,EAAE;MACV3B,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,yDAAyD,EAAE,OAAO,CAAC;MACvF;IACJ;IAEA,IAAI;MACA,MAAMV,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMe,GAAG,GAAG,sDAAsD;MAClE,MAAMC,MAAM,GAAGxC,cAAc,GAAG,KAAK,GAAG,MAAM;MAC9C,MAAM6B,IAAI,GAAG7B,cAAc,GAAG;QAAE,GAAGW,QAAQ;QAAE8B,EAAE,EAAEzC,cAAc,CAACyC;MAAG,CAAC,GAAG9B,QAAQ;MAE/E,MAAMrB,KAAK,CAAC;QACRkD,MAAM;QACND,GAAG;QACHV,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF/B,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,WAAWhC,cAAc,GAAG,UAAU,GAAG,OAAO,cAAc,EAAE,SAAS,CAAC;MAC9FD,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvByC,SAAS,CAAC,CAAC;MACXtB,aAAa,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAa,eAAA,EAAAC,oBAAA;MACZb,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BvC,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,EAAAW,eAAA,GAAAb,KAAK,CAACL,QAAQ,cAAAkB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBd,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAMe,UAAU,GAAIC,OAAO,IAAK;IAC5B,IAAI,CAAC5B,OAAO,EAAE;MACV3B,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,mDAAmD,EAAE,OAAO,CAAC;MACjF;IACJ;IAEA/B,iBAAiB,CAAC6C,OAAO,CAAC;IAC1BlC,WAAW,CAAC;MACRC,WAAW,EAAEiC,OAAO,CAACjC,WAAW;MAChCC,IAAI,EAAEgC,OAAO,CAAChC,IAAI;MAClBC,OAAO,EAAE+B,OAAO,CAAC/B,OAAO;MACxBC,MAAM,EAAE8B,OAAO,CAAC9B,MAAM;MACtBC,aAAa,EAAE6B,OAAO,CAAC7B,aAAa,IAAI;IAC5C,CAAC,CAAC;IACFlB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMgD,YAAY,GAAG,MAAON,EAAE,IAAK;IAC/B,IAAI,CAACvB,OAAO,EAAE;MACV3B,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,oDAAoD,EAAE,OAAO,CAAC;MAClF;IACJ;IAEA,MAAMgB,MAAM,GAAG,MAAMzD,IAAI,CAACyC,IAAI,CAAC;MAC3BiB,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA,MAAMnC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAMlC,KAAK,CAACoE,MAAM,CAAC,sDAAsD,EAAE;UACvE/B,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;YAChC,cAAc,EAAE;UACpB,CAAC;UACDO,IAAI,EAAE;YAAEY;UAAG;QACf,CAAC,CAAC;QACFlD,IAAI,CAACyC,IAAI,CAAC,WAAW,EAAE,6BAA6B,EAAE,SAAS,CAAC;QAChEZ,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOU,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BvC,IAAI,CAACyC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;MACtE;IACJ;EACJ,CAAC;EAED,MAAMU,SAAS,GAAGA,CAAA,KAAM;IACpB9B,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,IAAI,EAAE,EAAE;MACRC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,UAAU;MAClBC,aAAa,EAAE;IACnB,CAAC,CAAC;EACN,CAAC;EAED,MAAM0C,aAAa,GAAI5C,OAAO,IAAK;IAC/B,OAAO,IAAI6C,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MAClCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACd,CAAC,CAAC,CAACC,MAAM,CAACjD,OAAO,CAAC;EACtB,CAAC;EAED,MAAMkD,cAAc,GAAIjD,MAAM,IAAK;IAC/B,MAAMkD,UAAU,GAAGlD,MAAM,KAAK,MAAM,GAAG,eAAe,GAAG,cAAc;IACvE,oBAAO/B,KAAA,CAAAkF,aAAA;MAAMC,SAAS,EAAE,SAASF,UAAU,EAAG;MAAAG,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAE1D,MAAa,CAAC;EAClE,CAAC;;EAED;EACA,MAAM2D,gBAAgB,GAAGjF,QAAQ,CAACkF,MAAM,CAAC9B,OAAO,IAAI;IAAA,IAAA+B,qBAAA,EAAAC,qBAAA,EAAAC,aAAA;IAChD,MAAMC,aAAa,GAAG,EAAAH,qBAAA,GAAA/B,OAAO,CAACmC,YAAY,cAAAJ,qBAAA,uBAApBA,qBAAA,CAAsBK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/E,UAAU,CAAC8E,WAAW,CAAC,CAAC,CAAC,OAAAJ,qBAAA,GACvEhC,OAAO,CAACsC,cAAc,cAAAN,qBAAA,uBAAtBA,qBAAA,CAAwBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/E,UAAU,CAAC8E,WAAW,CAAC,CAAC,CAAC,OAAAH,aAAA,GACxEjC,OAAO,CAAChC,IAAI,cAAAiE,aAAA,uBAAZA,aAAA,CAAcI,QAAQ,CAAC/E,UAAU,CAAC;IAEvD,MAAMiF,aAAa,GAAG/E,YAAY,KAAK,KAAK,IAAIwC,OAAO,CAAC9B,MAAM,KAAKV,YAAY;IAE/E,OAAO0E,aAAa,IAAIK,aAAa;EACzC,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG9E,WAAW,GAAGE,YAAY;EAClD,MAAM6E,gBAAgB,GAAGD,eAAe,GAAG5E,YAAY;EACvD,MAAM8E,eAAe,GAAGb,gBAAgB,CAACc,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EACjF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACjB,gBAAgB,CAACxC,MAAM,GAAGzB,YAAY,CAAC;EAEpE,MAAMmF,QAAQ,GAAIC,UAAU,IAAKrF,cAAc,CAACqF,UAAU,CAAC;;EAE3D;EACA7G,KAAK,CAACE,SAAS,CAAC,MAAM;IAClBsB,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACL,UAAU,EAAEE,YAAY,CAAC,CAAC;EAE9B,IAAIV,OAAO,EAAE;IACT,oBACIX,KAAA,CAAAkF,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BzF,KAAA,CAAAkF,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BzF,KAAA,CAAAkF,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,4BAA6B,CAC/B,CAAC;EAEd;EAEA,oBACIzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mCAA2B,CAAC,eAChCzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBzF,KAAA,CAAAkF,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxBC,gBAAgB,CAACxC,MAAM,EAAC,2BACzB,EAACuD,UAAU,GAAG,CAAC,IAAI,WAAWlF,WAAW,IAAIkF,UAAU,EACrD,CAAC,EACNxE,OAAO,iBACJjC,KAAA,CAAAkF,aAAA;IACIC,SAAS,EAAC,iBAAiB;IAC3B2B,OAAO,EAAEA,CAAA,KAAMhG,YAAY,CAAC,IAAI,CAAE;IAAAsE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCzF,KAAA,CAAAkF,aAAA;IAAK6B,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,qBACjC,CAEX,CACJ,CAAC,EAGL,CAACxD,OAAO,iBACLjC,KAAA,CAAAkF,aAAA;IAAKL,KAAK,EAAE;MACRoC,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACZ,CAAE;IAAAjC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzF,KAAA,CAAAkF,aAAA;IAAGL,KAAK,EAAE;MAAEyC,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,0IAG1C,CACF,CACR,eAGDzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAACN,KAAK,EAAE;MACpC2C,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXL,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE;IAClB,CAAE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACN,KAAK,EAAE;MAAE6C,IAAI,EAAE;IAAE,CAAE;IAAAtC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CzF,KAAA,CAAAkF,aAAA;IACIyC,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,mDAAyC;IACrDC,KAAK,EAAE1G,UAAW;IAClB2G,QAAQ,EAAG1E,CAAC,IAAKhC,aAAa,CAACgC,CAAC,CAAC2E,MAAM,CAACF,KAAK,CAAE;IAC/ChD,KAAK,EAAE;MACHmD,KAAK,EAAE,MAAM;MACbf,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE;IAClB,CAAE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eACNzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzF,KAAA,CAAAkF,aAAA;IACI2C,KAAK,EAAExG,YAAa;IACpByG,QAAQ,EAAG1E,CAAC,IAAK9B,eAAe,CAAC8B,CAAC,CAAC2E,MAAM,CAACF,KAAK,CAAE;IACjDhD,KAAK,EAAE;MACHoC,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE,KAAK;MACnBc,QAAQ,EAAE;IACd,CAAE;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFzF,KAAA,CAAAkF,aAAA;IAAQ2C,KAAK,EAAC,KAAK;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kBAAwB,CAAC,eAC7CzF,KAAA,CAAAkF,aAAA;IAAQ2C,KAAK,EAAC,SAAM;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAAC,eAClCzF,KAAA,CAAAkF,aAAA;IAAQ2C,KAAK,EAAC,aAAU;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAgB,CACrC,CACP,CACJ,CAAC,eAENzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBC,gBAAgB,CAACxC,MAAM,KAAK,CAAC,gBAC1BlD,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBzF,KAAA,CAAAkF,aAAA;IAAK6B,GAAG,EAAC,cAAc;IAACC,GAAG,EAAC,gBAAgB;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC/CzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,2BAAyB,CAAC,EAC5BtE,UAAU,iBACPnB,KAAA,CAAAkF,aAAA;IACI4B,OAAO,EAAEA,CAAA,KAAM1F,aAAa,CAAC,EAAE,CAAE;IACjC+D,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,sBAEO,CAEX,CAAC,gBAENzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BzF,KAAA,CAAAkF,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAe,CAAC,eACpBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,eAChBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAAC,eACnBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,+BAAuB,CAAC,EAC3BxD,OAAO,iBAAIjC,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAC9B,CACD,CAAC,eACRzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKc,eAAe,CAAC2B,GAAG,CAAErE,OAAO,iBACzB7D,KAAA,CAAAkF,aAAA;IAAIiD,GAAG,EAAEtE,OAAO,CAACL,EAAG;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAS5B,OAAO,CAACmC,YAAqB,CAAC,eACvChG,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQ5B,OAAO,CAACsC,cAAsB,CACrC,CACL,CAAC,eACLnG,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzF,KAAA,CAAAkF,aAAA;IAAML,KAAK,EAAE;MACToC,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE;IACd,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG5B,OAAO,CAAChC,IACP,CACN,CAAC,eACL7B,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzF,KAAA,CAAAkF,aAAA;IAAQL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAEa,QAAQ,EAAE;IAAQ,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAClDf,aAAa,CAACb,OAAO,CAAC/B,OAAO,CAC1B,CACR,CAAC,eACL9B,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,cAAc,CAACnB,OAAO,CAAC9B,MAAM,CAAM,CAAC,eACzC/B,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACK5B,OAAO,CAAC7B,aAAa,gBAClBhC,KAAA,CAAAkF,aAAA;IAAML,KAAK,EAAE;MAAE0C,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAC7B,EAAC,IAAI4C,IAAI,CAACxE,OAAO,CAAC7B,aAAa,CAAC,CAACsG,kBAAkB,CAAC,OAAO,CAC3D,CAAC,gBAEPtI,KAAA,CAAAkF,aAAA;IAAML,KAAK,EAAE;MAAE0C,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mBAE7B,CAEV,CAAC,EACJxD,OAAO,iBACJjC,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BzF,KAAA,CAAAkF,aAAA;IACIC,SAAS,EAAC,wBAAwB;IAClC2B,OAAO,EAAEA,CAAA,KAAMlD,UAAU,CAACC,OAAO,CAAE;IACnCG,KAAK,EAAC,UAAU;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhBzF,KAAA,CAAAkF,aAAA;IAAK6B,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACTzF,KAAA,CAAAkF,aAAA;IACIC,SAAS,EAAC,uBAAuB;IACjC2B,OAAO,EAAEA,CAAA,KAAMhD,YAAY,CAACD,OAAO,CAACL,EAAE,CAAE;IACxCQ,KAAK,EAAC,WAAW;IAAAoB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjBzF,KAAA,CAAAkF,aAAA;IAAK6B,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLgB,UAAU,GAAG,CAAC,iBACXzG,KAAA,CAAAkF,aAAA;IAAKL,KAAK,EAAE;MACR2C,OAAO,EAAE,MAAM;MACfe,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE,MAAM;MACjBhB,GAAG,EAAE;IACT,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzF,KAAA,CAAAkF,aAAA;IACIC,SAAS,EAAC,mBAAmB;IAC7B2B,OAAO,EAAEA,CAAA,KAAMF,QAAQ,CAACrF,WAAW,GAAG,CAAC,CAAE;IACzCmH,QAAQ,EAAEnH,WAAW,KAAK,CAAE;IAC5BsD,KAAK,EAAE;MACH8D,OAAO,EAAEpH,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;MACpCqH,MAAM,EAAErH,WAAW,KAAK,CAAC,GAAG,aAAa,GAAG;IAChD,CAAE;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,8BAEO,CAAC,eAGTzF,KAAA,CAAAkF,aAAA;IAAKL,KAAK,EAAE;MAAE2C,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAM,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvCoD,KAAK,CAACC,IAAI,CAAC;IAAE5F,MAAM,EAAEwD,IAAI,CAACqC,GAAG,CAAC,CAAC,EAAEtC,UAAU;EAAE,CAAC,EAAE,CAACuC,CAAC,EAAEC,CAAC,KAAK;IACvD,IAAIpC,UAAU;IACd,IAAIJ,UAAU,IAAI,CAAC,EAAE;MACjBI,UAAU,GAAGoC,CAAC,GAAG,CAAC;IACtB,CAAC,MAAM,IAAI1H,WAAW,IAAI,CAAC,EAAE;MACzBsF,UAAU,GAAGoC,CAAC,GAAG,CAAC;IACtB,CAAC,MAAM,IAAI1H,WAAW,IAAIkF,UAAU,GAAG,CAAC,EAAE;MACtCI,UAAU,GAAGJ,UAAU,GAAG,CAAC,GAAGwC,CAAC;IACnC,CAAC,MAAM;MACHpC,UAAU,GAAGtF,WAAW,GAAG,CAAC,GAAG0H,CAAC;IACpC;IAEA,oBACIjJ,KAAA,CAAAkF,aAAA;MACIiD,GAAG,EAAEtB,UAAW;MAChBC,OAAO,EAAEA,CAAA,KAAMF,QAAQ,CAACC,UAAU,CAAE;MACpChC,KAAK,EAAE;QACHoC,OAAO,EAAE,UAAU;QACnBI,MAAM,EAAE,gBAAgB;QACxBF,YAAY,EAAE,KAAK;QACnBD,eAAe,EAAE3F,WAAW,KAAKsF,UAAU,GAAG,SAAS,GAAG,OAAO;QACjEU,KAAK,EAAEhG,WAAW,KAAKsF,UAAU,GAAG,OAAO,GAAG,SAAS;QACvD+B,MAAM,EAAE,SAAS;QACjBM,UAAU,EAAE3H,WAAW,KAAKsF,UAAU,GAAG,MAAM,GAAG;MACtD,CAAE;MAAAzB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAEDoB,UACG,CAAC;EAEjB,CAAC,CACA,CAAC,eAEN7G,KAAA,CAAAkF,aAAA;IACIC,SAAS,EAAC,mBAAmB;IAC7B2B,OAAO,EAAEA,CAAA,KAAMF,QAAQ,CAACrF,WAAW,GAAG,CAAC,CAAE;IACzCmH,QAAQ,EAAEnH,WAAW,KAAKkF,UAAW;IACrC5B,KAAK,EAAE;MACH8D,OAAO,EAAEpH,WAAW,KAAKkF,UAAU,GAAG,GAAG,GAAG,CAAC;MAC7CmC,MAAM,EAAErH,WAAW,KAAKkF,UAAU,GAAG,aAAa,GAAG;IACzD,CAAE;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,sBAEO,CACP,CACR,EAGAC,gBAAgB,CAACxC,MAAM,GAAG,CAAC,iBACxBlD,KAAA,CAAAkF,aAAA;IAAKL,KAAK,EAAE;MACRsE,SAAS,EAAE,QAAQ;MACnBV,SAAS,EAAE,MAAM;MACjBxB,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE,MAAM;MAChBb,KAAK,EAAE;IACX,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eACc,EAACa,gBAAgB,GAAG,CAAC,EAAC,QAAG,EAACI,IAAI,CAACqC,GAAG,CAAC1C,eAAe,EAAEX,gBAAgB,CAACxC,MAAM,CAAC,EAAC,OAAK,EAACwC,gBAAgB,CAACxC,MAAM,EAAC,aACxH,EAACuD,UAAU,GAAG,CAAC,IAAI,WAAWlF,WAAW,QAAQkF,UAAU,EAC1D,CACR,EAGAf,gBAAgB,CAACxC,MAAM,GAAG,CAAC,iBACxBlD,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,eAAe;IAACN,KAAK,EAAE;MAClC4D,SAAS,EAAE,MAAM;MACjBxB,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBK,OAAO,EAAE,MAAM;MACf4B,mBAAmB,EAAE,sCAAsC;MAC3D3B,GAAG,EAAE;IACT,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACN,KAAK,EAAE;MAAEsE,SAAS,EAAE;IAAS,CAAE;IAAA/D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDzF,KAAA,CAAAkF,aAAA;IAAIL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCC,gBAAgB,CAACC,MAAM,CAAC0D,CAAC,IAAIA,CAAC,CAACtH,MAAM,KAAK,MAAM,CAAC,CAACmB,MACnD,CAAC,eACLlD,KAAA,CAAAkF,aAAA;IAAGL,KAAK,EAAE;MAAEyC,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,oBAAkB,CACtE,CAAC,eACNzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACN,KAAK,EAAE;MAAEsE,SAAS,EAAE;IAAS,CAAE;IAAA/D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDzF,KAAA,CAAAkF,aAAA;IAAIL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCC,gBAAgB,CAACC,MAAM,CAAC0D,CAAC,IAAIA,CAAC,CAACtH,MAAM,KAAK,UAAU,CAAC,CAACmB,MACvD,CAAC,eACLlD,KAAA,CAAAkF,aAAA;IAAGL,KAAK,EAAE;MAAEyC,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAAoB,CACxE,CAAC,eACNzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACN,KAAK,EAAE;MAAEsE,SAAS,EAAE;IAAS,CAAE;IAAA/D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDzF,KAAA,CAAAkF,aAAA;IAAIL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCf,aAAa,CACVgB,gBAAgB,CACXC,MAAM,CAAC0D,CAAC,IAAIA,CAAC,CAACtH,MAAM,KAAK,MAAM,CAAC,CAChCuH,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGC,UAAU,CAACH,CAAC,CAACvH,OAAO,CAAC,EAAE,CAAC,CAC1D,CACA,CAAC,eACL9B,KAAA,CAAAkF,aAAA;IAAGL,KAAK,EAAE;MAAEyC,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mBAAiB,CACrE,CAAC,eACNzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACN,KAAK,EAAE;MAAEsE,SAAS,EAAE;IAAS,CAAE;IAAA/D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDzF,KAAA,CAAAkF,aAAA;IAAIL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCf,aAAa,CACVgB,gBAAgB,CACXC,MAAM,CAAC0D,CAAC,IAAIA,CAAC,CAACtH,MAAM,KAAK,UAAU,CAAC,CACpCuH,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGC,UAAU,CAACH,CAAC,CAACvH,OAAO,CAAC,EAAE,CAAC,CAC1D,CACA,CAAC,eACL9B,KAAA,CAAAkF,aAAA;IAAGL,KAAK,EAAE;MAAEyC,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,YAAa,CACjE,CACJ,CACR,EAGA5E,SAAS,IAAIoB,OAAO,iBACjBjC,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK1E,cAAc,GAAG,qBAAqB,GAAG,kBAAuB,CAAC,eACtEf,KAAA,CAAAkF,aAAA;IACIC,SAAS,EAAC,WAAW;IACrB2B,OAAO,EAAEA,CAAA,KAAM;MACXhG,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvByC,SAAS,CAAC,CAAC;IACf,CAAE;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFzF,KAAA,CAAAkF,aAAA;IAAK6B,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNzF,KAAA,CAAAkF,aAAA;IAAMuE,QAAQ,EAAEtG,YAAa;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAiB,CAAC,eACzBzF,KAAA,CAAAkF,aAAA;IACI2C,KAAK,EAAEnG,QAAQ,CAACE,WAAY;IAC5BkG,QAAQ,EAAG1E,CAAC,IAAKzB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,WAAW,EAAEwB,CAAC,CAAC2E,MAAM,CAACF;IAAK,CAAC,CAAE;IACzE6B,QAAQ;IACRhB,QAAQ,EAAE3H,cAAe;IACzB8D,KAAK,EAAE;MACHmD,KAAK,EAAE,MAAM;MACbf,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE,MAAM;MAChBlB,eAAe,EAAEnG,cAAc,GAAG,SAAS,GAAG;IAClD,CAAE;IAAAqE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFzF,KAAA,CAAAkF,aAAA;IAAQ2C,KAAK,EAAC,EAAE;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjDxE,SAAS,CAACiH,GAAG,CAAEyB,QAAQ,iBACpB3J,KAAA,CAAAkF,aAAA;IAAQiD,GAAG,EAAEwB,QAAQ,CAAC/H,WAAY;IAACiG,KAAK,EAAE8B,QAAQ,CAAC/H,WAAY;IAAAwD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DkE,QAAQ,CAACC,GAAG,EAAC,KAAG,EAACD,QAAQ,CAACE,KAAK,EAC/BF,QAAQ,CAACG,UAAU,IAAI,KAAKH,QAAQ,CAACG,UAAU,GAC5C,CACX,CACG,CAAC,EACR/I,cAAc,iBACXf,KAAA,CAAAkF,aAAA;IAAOL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAEa,QAAQ,EAAE;IAAQ,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mEAEhD,CAEV,CAAC,eAENzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,kBAAuB,CAAC,eAC/BzF,KAAA,CAAAkF,aAAA;IACIyC,IAAI,EAAC,OAAO;IACZE,KAAK,EAAEnG,QAAQ,CAACG,IAAK;IACrBiG,QAAQ,EAAG1E,CAAC,IAAKzB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,IAAI,EAAEuB,CAAC,CAAC2E,MAAM,CAACF;IAAK,CAAC,CAAE;IAClE6B,QAAQ;IACR7E,KAAK,EAAE;MACHmD,KAAK,EAAE,MAAM;MACbf,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE;IACd,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,iBAAsB,CAAC,eAC9BzF,KAAA,CAAAkF,aAAA;IACIyC,IAAI,EAAC,QAAQ;IACboC,IAAI,EAAC,MAAM;IACXhB,GAAG,EAAC,GAAG;IACPlB,KAAK,EAAEnG,QAAQ,CAACI,OAAQ;IACxBgG,QAAQ,EAAG1E,CAAC,IAAKzB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,OAAO,EAAEsB,CAAC,CAAC2E,MAAM,CAACF;IAAK,CAAC,CAAE;IACrED,WAAW,EAAC,aAAa;IACzB8B,QAAQ;IACR7E,KAAK,EAAE;MACHmD,KAAK,EAAE,MAAM;MACbf,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE;IACd,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,oBAAyB,CAAC,eACjCzF,KAAA,CAAAkF,aAAA;IACI2C,KAAK,EAAEnG,QAAQ,CAACK,MAAO;IACvB+F,QAAQ,EAAG1E,CAAC,IAAKzB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,MAAM,EAAEqB,CAAC,CAAC2E,MAAM,CAACF;IAAK,CAAC,CAAE;IACpEhD,KAAK,EAAE;MACHmD,KAAK,EAAE,MAAM;MACbf,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE;IACd,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFzF,KAAA,CAAAkF,aAAA;IAAQ2C,KAAK,EAAC,aAAU;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,aAAgB,CAAC,eAC1CzF,KAAA,CAAAkF,aAAA;IAAQ2C,KAAK,EAAC,SAAM;IAAAzC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAY,CAC7B,CACP,CAAC,EAEL/D,QAAQ,CAACK,MAAM,KAAK,MAAM,iBACvB/B,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBzF,KAAA,CAAAkF,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,kBAAuB,CAAC,eAC/BzF,KAAA,CAAAkF,aAAA;IACIyC,IAAI,EAAC,MAAM;IACXE,KAAK,EAAEnG,QAAQ,CAACM,aAAc;IAC9B8F,QAAQ,EAAG1E,CAAC,IAAKzB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEM,aAAa,EAAEoB,CAAC,CAAC2E,MAAM,CAACF;IAAK,CAAC,CAAE;IAC3EmC,GAAG,EAAE,IAAI3B,IAAI,CAAC,CAAC,CAAC4B,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;IAC5CrF,KAAK,EAAE;MACHmD,KAAK,EAAE,MAAM;MACbf,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBiB,QAAQ,EAAE;IACd,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACFzF,KAAA,CAAAkF,aAAA;IAAOL,KAAK,EAAE;MAAE0C,KAAK,EAAE,SAAS;MAAEa,QAAQ,EAAE;IAAQ,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6CAEhD,CACN,CACR,eAEDzF,KAAA,CAAAkF,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BzF,KAAA,CAAAkF,aAAA;IAAQyC,IAAI,EAAC,QAAQ;IAACxC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C1E,cAAc,GAAG,aAAa,GAAG,SAC9B,CAAC,eACTf,KAAA,CAAAkF,aAAA;IACIyC,IAAI,EAAC,QAAQ;IACbxC,SAAS,EAAC,mBAAmB;IAC7B2B,OAAO,EAAEA,CAAA,KAAM;MACXhG,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvByC,SAAS,CAAC,CAAC;IACf,CAAE;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAelF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}