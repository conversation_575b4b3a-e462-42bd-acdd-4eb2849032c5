import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const Etudiants = () => {
    const { user } = useContext(AuthContext);
    const [etudiants, setEtudiants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingEtudiant, setEditingEtudiant] = useState(null);
    const [utilisateursEtudiants, setUtilisateursEtudiants] = useState([]);
    const [groupes, setGroupes] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        utilisateur_id: '',
        groupe_id: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';

    useEffect(() => {
        fetchEtudiants();
        if (isAdmin) {
            fetchUtilisateursEtudiants();
            fetchGroupes();
        }
    }, [isAdmin]);

    const fetchEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des étudiants...');

            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            console.log('✅ Réponse API étudiants:', response.data);
            setEtudiants(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des étudiants:', error);
            Swal.fire('Erreur', 'Impossible de charger les étudiants', 'error');
            setEtudiants([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchUtilisateursEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            console.log('🔄 Chargement des utilisateurs étudiants...');

            // Récupérer tous les utilisateurs
            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=etudiant', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les étudiants existants pour les exclure
            const responseEtudiants = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Filtrer pour ne garder que les utilisateurs avec le rôle "etudiant"
            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];
            const etudiantsExistants = Array.isArray(responseEtudiants.data) ? responseEtudiants.data : [];
            
            const utilisateursEtudiantsFiltered = utilisateurs.filter(user => {
                const roleNom = (user.role_nom || user.role || '').toLowerCase();
                return roleNom === 'etudiant' || roleNom === 'étudiant';
            });

            // Exclure les utilisateurs déjà étudiants
            const etudiantsExistantsIds = etudiantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);
            const utilisateursDisponibles = utilisateursEtudiantsFiltered.filter(user => 
                !etudiantsExistantsIds.includes(user.id)
            );

            console.log('✅ Utilisateurs étudiants disponibles:', utilisateursDisponibles.length);
            setUtilisateursEtudiants(utilisateursDisponibles);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des utilisateurs étudiants:', error);
            setUtilisateursEtudiants([]);
        }
    };

    const fetchGroupes = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/groupes/groupe.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setGroupes(Array.isArray(response.data) ? response.data : []);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des groupes:', error);
            setGroupes([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des étudiants', 'error');
            return;
        }

        if (!formData.utilisateur_id) {
            Swal.fire('Erreur', 'Veuillez sélectionner un utilisateur', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php';
            const method = editingEtudiant ? 'PUT' : 'POST';
            const data = editingEtudiant ? { ...formData, id: editingEtudiant.id } : formData;

            console.log('🔄 Envoi requête étudiant:', { method, data });

            const response = await axios({
                method,
                url,
                data,
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.data.success) {
                Swal.fire('Succès', `Étudiant ${editingEtudiant ? 'modifié' : 'créé'} avec succès`, 'success');
                setShowModal(false);
                setEditingEtudiant(null);
                resetForm();
                fetchEtudiants();
                fetchUtilisateursEtudiants(); // Recharger pour exclure l'utilisateur ajouté
            } else {
                throw new Error(response.data.error || 'Erreur inconnue');
            }
        } catch (error) {
            console.error('❌ Erreur étudiant:', error);
            const errorMessage = error.response?.data?.error || error.message || 'Une erreur est survenue';
            Swal.fire('Erreur', errorMessage, 'error');
        }
    };

    const handleEdit = async (etudiant) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des étudiants', 'error');
            return;
        }

        // Recharger les utilisateurs étudiants en incluant l'utilisateur actuel
        await fetchUtilisateursEtudiantsAvecActuel(etudiant.utilisateur_id);

        setEditingEtudiant(etudiant);
        setFormData({
            utilisateur_id: etudiant.utilisateur_id || '',
            groupe_id: etudiant.groupe_id || ''
        });
        setShowModal(true);
    };

    const fetchUtilisateursEtudiantsAvecActuel = async (currentUserId) => {
        try {
            const token = localStorage.getItem('token');

            // Récupérer tous les utilisateurs
            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            // Récupérer les étudiants existants
            const responseEtudiants = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];
            const etudiantsExistants = Array.isArray(responseEtudiants.data) ? responseEtudiants.data : [];

            const utilisateursEtudiantsFiltered = utilisateurs.filter(user => {
                const roleNom = (user.role_nom || user.role || '').toLowerCase();
                return roleNom === 'etudiant' || roleNom === 'étudiant';
            });

            // Exclure les utilisateurs déjà étudiants SAUF l'utilisateur actuel
            const etudiantsExistantsIds = etudiantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);
            const utilisateursDisponibles = utilisateursEtudiantsFiltered.filter(user =>
                !etudiantsExistantsIds.includes(user.id) || user.id === currentUserId
            );

            setUtilisateursEtudiants(utilisateursDisponibles);
        } catch (error) {
            console.error('❌ Erreur lors du chargement des utilisateurs étudiants avec actuel:', error);
        }
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des étudiants', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });

                if (response.data.success) {
                    Swal.fire('Supprimé!', 'L\'étudiant a été supprimé.', 'success');
                    fetchEtudiants();
                    fetchUtilisateursEtudiants(); // Recharger pour rendre l'utilisateur disponible
                } else {
                    throw new Error(response.data.error || 'Erreur lors de la suppression');
                }
            } catch (error) {
                console.error('❌ Erreur suppression:', error);
                const errorMessage = error.response?.data?.error || error.message || 'Impossible de supprimer l\'étudiant';
                Swal.fire('Erreur', errorMessage, 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            utilisateur_id: '',
            groupe_id: ''
        });
    };

    const getStatutBadge = (statut) => {
        return <span className="badge badge-success">Actif</span>;
    };

    // Styles inline pour les badges et éléments spécifiques
    const styles = {
        idBadge: {
            padding: '4px 8px',
            backgroundColor: '#e3f2fd',
            borderRadius: '4px',
            fontSize: '0.9em',
            fontWeight: 'bold'
        },
        groupeBadge: {
            padding: '4px 8px',
            backgroundColor: '#d4edda',
            borderRadius: '4px',
            fontSize: '0.8em',
            color: '#155724'
        },
        infoMessage: {
            padding: '15px',
            backgroundColor: '#e3f2fd',
            borderRadius: '8px',
            marginBottom: '20px',
            border: '1px solid #bbdefb',
            color: '#1976d2'
        }
    };

    // Filtrage des données
    const filteredEtudiants = etudiants.filter(etudiant => {
        const searchLower = searchTerm.toLowerCase();
        return (etudiant.nom && etudiant.nom.toLowerCase().includes(searchLower)) ||
               (etudiant.email && etudiant.email.toLowerCase().includes(searchLower)) ||
               (etudiant.groupe_nom && etudiant.groupe_nom.toLowerCase().includes(searchLower));
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentEtudiants = filteredEtudiants.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredEtudiants.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des étudiants...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>🎓 Gestion des Étudiants</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredEtudiants.length} étudiant(s) trouvé(s)
                    </span>
                    {isAdmin && (
                        <button
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouvel Étudiant
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={styles.infoMessage}>
                    <p style={{ margin: '0' }}>ℹ️ Vous consultez les étudiants en mode lecture seule. Seul l'administrateur peut créer, modifier ou supprimer des étudiants.</p>
                </div>
            )}

            {/* Barre de recherche */}
            <div className="search-section" style={{ marginBottom: '20px' }}>
                <input
                    type="text"
                    placeholder="🔍 Rechercher un étudiant (nom, email, groupe)..."
                    value={searchTerm}
                    onChange={(e) => {
                        setSearchTerm(e.target.value);
                        setCurrentPage(1);
                    }}
                    className="search-input"
                    style={{
                        width: '100%',
                        padding: '12px',
                        border: '1px solid #ddd',
                        borderRadius: '8px',
                        fontSize: '16px'
                    }}
                />
            </div>

            <div className="factures-grid">
                {filteredEtudiants.length === 0 ? (
                    <div className="no-data">
                        <img src="/student.png" alt="Aucun étudiant" />
                        <p>Aucun étudiant trouvé</p>
                        {searchTerm && (
                            <button
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>🆔 ID</th>
                                    <th>👤 Nom de l'Étudiant</th>
                                    <th>📧 Email</th>
                                    <th>👥 Groupe</th>
                                    <th>📊 Statut</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentEtudiants.map((etudiant) => (
                                    <tr key={etudiant.id}>
                                        <td>
                                            <span style={styles.idBadge}>
                                                #{etudiant.id}
                                            </span>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{etudiant.nom || 'Nom non disponible'}</strong>
                                                <br />
                                                <small>ID Utilisateur: {etudiant.utilisateur_id}</small>
                                            </div>
                                        </td>
                                        <td>{etudiant.email || 'Email non disponible'}</td>
                                        <td>
                                            <span style={styles.groupeBadge}>
                                                {etudiant.groupe_nom || 'Aucun groupe'}
                                            </span>
                                        </td>
                                        <td>{getStatutBadge('Actif')}</td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button 
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(etudiant)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button 
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(etudiant.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                    >
                        ⬅️ Précédent
                    </button>

                    <span style={{
                        padding: '8px 16px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '4px',
                        fontSize: '14px'
                    }}>
                        Page {currentPage} sur {totalPages}
                    </span>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Modal pour ajouter/modifier un étudiant */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingEtudiant ? 'Modifier l\'étudiant' : 'Nouvel étudiant'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingEtudiant(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Utilisateur (Étudiant) *</label>
                                <select
                                    value={formData.utilisateur_id}
                                    onChange={(e) => setFormData({...formData, utilisateur_id: e.target.value})}
                                    required
                                    disabled={editingEtudiant} // Empêcher la modification de l'utilisateur lors de l'édition
                                >
                                    <option value="">Sélectionner un utilisateur étudiant...</option>
                                    {utilisateursEtudiants.map(user => (
                                        <option key={user.id} value={user.id}>
                                            {user.nom} - {user.email} (ID: {user.id})
                                        </option>
                                    ))}
                                    {/* Si on édite, inclure l'utilisateur actuel même s'il est déjà étudiant */}
                                    {editingEtudiant && editingEtudiant.utilisateur_id && (
                                        <option value={editingEtudiant.utilisateur_id}>
                                            {editingEtudiant.nom} - {editingEtudiant.email} (ID: {editingEtudiant.utilisateur_id})
                                        </option>
                                    )}
                                </select>
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    Seuls les utilisateurs avec le rôle "etudiant" non encore assignés sont affichés
                                </small>
                            </div>

                            <div className="form-group">
                                <label>Groupe (Optionnel)</label>
                                <select
                                    value={formData.groupe_id}
                                    onChange={(e) => setFormData({...formData, groupe_id: e.target.value})}
                                >
                                    <option value="">Aucun groupe</option>
                                    {groupes.map((groupe) => (
                                        <option key={groupe.id} value={groupe.id}>
                                            {groupe.nom}
                                        </option>
                                    ))}
                                </select>
                                <small style={{ color: '#6c757d', fontSize: '12px' }}>
                                    L'étudiant peut être assigné à un groupe ou rester sans groupe
                                </small>
                            </div>

                            <div className="modal-actions">
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingEtudiant(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingEtudiant ? 'Modifier' : 'Créer'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default Etudiants;
