// src/components/NavbarTop.jsx
import React, { useContext, useState } from 'react';
import { Link } from 'react-router-dom';
import {
  FaUserCircle,
  FaSignInAlt,
  FaSignOutAlt,
  FaGraduationCap,
  FaCog,
  FaChevronDown
} from 'react-icons/fa';
import { AuthContext } from '../context/AuthContext';
import NotificationCenter from './NotificationCenter';

const NavbarTop = () => {
  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = () => {
    logout();
    setShowUserMenu(false);
  };

  const navStyles = {
    nav: {
      backgroundColor: 'var(--cerulean)',
      padding: '15px 25px',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      position: 'fixed',
      top: 0,
      left: '70px',
      right: 0,
      height: '70px',
      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
      zIndex: 999,
      transition: 'all 0.3s ease',
      backdropFilter: 'blur(10px)'
    },
    brandSection: {
      display: 'flex',
      alignItems: 'center',
      gap: '15px'
    },
    brandIcon: {
      fontSize: '1.8rem',
      color: 'rgb(1, 167, 194)',
      animation: 'pulse 2s infinite'
    },
    brandText: {
      color: 'var(--antiflash-white)',
      fontSize: '1.2rem',
      fontWeight: 'bold',
      textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
    },
    userInfo: {
      color: 'var(--antiflash-white)',
      fontSize: '14px',
      padding: '8px 15px',
      backgroundColor: 'rgba(255,255,255,0.1)',
      borderRadius: '20px',
      border: '1px solid rgba(255,255,255,0.2)',
      backdropFilter: 'blur(5px)'
    },
    menuSection: {
      display: 'flex',
      alignItems: 'center',
      gap: '10px'
    },
    menuItem: {
      color: 'var(--antiflash-white)',
      textDecoration: 'none',
      padding: '10px 15px',
      borderRadius: '10px',
      fontSize: '14px',
      fontWeight: '500',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      transition: 'all 0.3s ease',
      position: 'relative',
      overflow: 'hidden'
    },
    userMenuContainer: {
      position: 'relative'
    },
    userMenuButton: {
      background: 'none',
      border: 'none',
      color: 'var(--antiflash-white)',
      padding: '10px 15px',
      borderRadius: '10px',
      fontSize: '14px',
      fontWeight: '500',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      cursor: 'pointer',
      transition: 'all 0.3s ease'
    },
    userDropdown: {
      position: 'absolute',
      top: '100%',
      right: 0,
      marginTop: '10px',
      backgroundColor: 'rgba(255,255,255,0.95)',
      backdropFilter: 'blur(10px)',
      borderRadius: '12px',
      boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
      border: '1px solid rgba(255,255,255,0.3)',
      minWidth: '200px',
      opacity: showUserMenu ? 1 : 0,
      transform: showUserMenu ? 'translateY(0) scale(1)' : 'translateY(-10px) scale(0.95)',
      transition: 'all 0.3s ease',
      pointerEvents: showUserMenu ? 'auto' : 'none',
      zIndex: 1000
    },
    dropdownItem: {
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      padding: '12px 15px',
      color: '#333',
      textDecoration: 'none',
      fontSize: '14px',
      transition: 'all 0.2s ease',
      borderRadius: '8px',
      margin: '5px'
    }
  };

  return (
    <nav style={navStyles.nav}>
      {/* Section marque/logo */}
    <Link to="/" style={{ textDecoration: 'none' }}>
  <div style={navStyles.brandSection}>
    <FaGraduationCap style={navStyles.brandIcon} />
    <span style={navStyles.brandText}>École Moderne</span>
  </div>
</Link>

      {/* Informations utilisateur (centre) */}
      {isAuthenticated && (
        <div style={navStyles.userInfo}>
          Connecté en tant que: <strong>{user?.role || 'Utilisateur'}</strong>
          {user?.email && <span style={{ marginLeft: '10px', opacity: 0.8 }}>({user.email})</span>}
        </div>
      )}

      {/* Menu de navigation (droite) */}
      <div style={navStyles.menuSection}>
        {isAuthenticated ? (
          <>
            {/* Composant de notifications */}
            <NotificationCenter />

            {/* Menu utilisateur avec dropdown */}
            <div style={navStyles.userMenuContainer}>
              <button
                style={navStyles.userMenuButton}
                onClick={() => setShowUserMenu(!showUserMenu)}
                onMouseEnter={(e) => {
                  e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
                  e.target.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.backgroundColor = 'transparent';
                  e.target.style.transform = 'translateY(0)';
                }}
              >
                <FaUserCircle />
                <span>Mon Compte</span>
                <FaChevronDown style={{
                  transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s ease'
                }} />
              </button>

              {/* Dropdown menu */}
              <div style={navStyles.userDropdown}>
                <Link
                  to="/profil"
                  style={navStyles.dropdownItem}
                  onClick={() => setShowUserMenu(false)}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
                    e.target.style.transform = 'translateX(5px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent';
                    e.target.style.transform = 'translateX(0)';
                  }}
                >
                  <FaUserCircle />
                  <span>Mon Profil</span>
                </Link>

                <Link
                  to="/settings"
                  style={navStyles.dropdownItem}
                  onClick={() => setShowUserMenu(false)}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
                    e.target.style.transform = 'translateX(5px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent';
                    e.target.style.transform = 'translateX(0)';
                  }}
                >
                  <FaCog />
                  <span>Paramètres</span>
                </Link>

                <hr style={{ margin: '5px 0', border: 'none', borderTop: '1px solid rgba(0,0,0,0.1)' }} />

                <button
                  onClick={handleLogout}
                  style={{
                    ...navStyles.dropdownItem,
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    width: '100%',
                    textAlign: 'left',
                    color: '#e74c3c'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = 'rgba(231, 76, 60, 0.1)';
                    e.target.style.transform = 'translateX(5px)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = 'transparent';
                    e.target.style.transform = 'translateX(0)';
                  }}
                >
                  <FaSignOutAlt />
                  <span>Déconnexion</span>
                </button>
              </div>
            </div>
          </>
        ) : (
          <Link
            to="/login"
            style={navStyles.menuItem}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = 'none';
            }}
          >
            <FaSignInAlt />
            <span>Connexion</span>
          </Link>
        )}
      </div>

      {/* Styles CSS pour les animations */}
      <style jsx>{`
        @keyframes pulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }
      `}</style>
    </nav>
  );
};

export default NavbarTop;
