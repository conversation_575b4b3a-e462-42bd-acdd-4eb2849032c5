<?php
// API Étudiants - CRUD complet avec interface similaire aux factures
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS, PUT, DELETE");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

// Fonction de logging
function logDebug($message, $data = null) {
    $timestamp = date('Y-m-d H:i:s');
    $log = "[$timestamp] ETUDIANTS: $message";
    if ($data !== null) {
        $log .= " | Data: " . json_encode($data);
    }
    error_log($log);
}

// Fonction de réponse sécurisée
function jsonResponse($data, $code = 200) {
    logDebug("Sending response", ['code' => $code, 'data' => $data]);
    http_response_code($code);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    logDebug("OPTIONS request received");
    jsonResponse(['success' => true]);
}

try {
    // Connexion DB
    $pdo = new PDO("mysql:host=localhost;dbname=gestionscolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    logDebug("Database connected");

    $method = $_SERVER['REQUEST_METHOD'];
    logDebug("Request method: $method");

    if ($method === 'POST') {
        // CREATE - Ajouter un nouvel étudiant
        logDebug("Processing POST request");
        
        $input = file_get_contents("php://input");
        logDebug("Raw input received", ['input' => $input]);
        
        if (empty($input)) {
            jsonResponse(['error' => 'Aucune donnée reçue'], 400);
        }
        
        $data = json_decode($input, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'JSON invalide: ' . json_last_error_msg()], 400);
        }
        
        logDebug("Data decoded successfully", $data);
        
        // Validation des champs requis
        if (!isset($data['utilisateur_id']) || empty($data['utilisateur_id'])) {
            jsonResponse(['error' => 'L\'ID utilisateur est requis'], 400);
        }
        
        $utilisateur_id = intval($data['utilisateur_id']);
        $groupe_id = !empty($data['groupe_id']) ? intval($data['groupe_id']) : null;
        
        logDebug("Processing creation", ['utilisateur_id' => $utilisateur_id, 'groupe_id' => $groupe_id]);
        
        // Vérifier que l'utilisateur existe et a le rôle étudiant
        $checkUserStmt = $pdo->prepare("
            SELECT u.id, u.nom, u.email, r.nom as role_nom 
            FROM utilisateurs u 
            LEFT JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ?
        ");
        $checkUserStmt->execute([$utilisateur_id]);
        $user = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            jsonResponse(['error' => 'Utilisateur non trouvé'], 404);
        }
        
        if (strtolower($user['role_nom']) !== 'etudiant') {
            jsonResponse(['error' => 'L\'utilisateur doit avoir le rôle "etudiant"'], 400);
        }
        
        // Vérifier que l'utilisateur n'est pas déjà étudiant
        $checkExistingStmt = $pdo->prepare("SELECT id FROM etudiants WHERE utilisateur_id = ?");
        $checkExistingStmt->execute([$utilisateur_id]);
        if ($checkExistingStmt->fetch()) {
            jsonResponse(['error' => 'Cet utilisateur est déjà enregistré comme étudiant'], 400);
        }
        
        // Vérifier que le groupe existe (si fourni)
        if ($groupe_id !== null) {
            $checkGroupStmt = $pdo->prepare("SELECT id FROM groupes WHERE id = ?");
            $checkGroupStmt->execute([$groupe_id]);
            if (!$checkGroupStmt->fetch()) {
                jsonResponse(['error' => 'Groupe non trouvé'], 404);
            }
        }
        
        // Insérer l'étudiant
        try {
            $stmt = $pdo->prepare("
                INSERT INTO etudiants (utilisateur_id, groupe_id)
                VALUES (:utilisateur_id, :groupe_id)
            ");
            
            $result = $stmt->execute([
                'utilisateur_id' => $utilisateur_id,
                'groupe_id' => $groupe_id
            ]);
            
            if ($result) {
                $etudiantId = $pdo->lastInsertId();
                logDebug("Étudiant created successfully", ['id' => $etudiantId]);
                
                jsonResponse([
                    'success' => true,
                    'message' => 'Étudiant créé avec succès',
                    'id' => $etudiantId
                ]);
            } else {
                jsonResponse(['error' => 'Échec de la création de l\'étudiant'], 500);
            }
            
        } catch (PDOException $e) {
            logDebug("Database error during creation", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } elseif ($method === 'GET') {
        // READ - Récupérer tous les étudiants avec informations complètes
        logDebug("Processing GET request");
        
        try {
            $stmt = $pdo->query("
                SELECT 
                    e.id,
                    e.utilisateur_id,
                    e.groupe_id,
                    u.nom,
                    u.email,
                    g.nom as groupe_nom
                FROM etudiants e
                LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
                LEFT JOIN groupes g ON e.groupe_id = g.id
                ORDER BY e.id DESC
            ");
            
            $etudiants = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            logDebug("Étudiants retrieved", [
                'total' => count($etudiants)
            ]);
            
            jsonResponse($etudiants);
            
        } catch (PDOException $e) {
            logDebug("Database error during read", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } elseif ($method === 'PUT') {
        // UPDATE - Modifier un étudiant
        logDebug("Processing PUT request");
        
        $input = file_get_contents("php://input");
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'JSON invalide: ' . json_last_error_msg()], 400);
        }
        
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            jsonResponse(['error' => 'ID de l\'étudiant requis'], 400);
        }
        
        $id = intval($data['id']);
        $utilisateur_id = !empty($data['utilisateur_id']) ? intval($data['utilisateur_id']) : null;
        $groupe_id = !empty($data['groupe_id']) ? intval($data['groupe_id']) : null;
        
        logDebug("Processing update", ['id' => $id, 'utilisateur_id' => $utilisateur_id, 'groupe_id' => $groupe_id]);
        
        // Vérifier que l'étudiant existe
        $checkStmt = $pdo->prepare("SELECT id, utilisateur_id FROM etudiants WHERE id = ?");
        $checkStmt->execute([$id]);
        $existingEtudiant = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$existingEtudiant) {
            jsonResponse(['error' => 'Étudiant non trouvé'], 404);
        }
        
        // Si l'utilisateur_id est modifié, faire les vérifications
        if ($utilisateur_id !== null && $utilisateur_id !== $existingEtudiant['utilisateur_id']) {
            // Vérifier que le nouvel utilisateur existe et a le rôle étudiant
            $checkUserStmt = $pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role_nom 
                FROM utilisateurs u 
                LEFT JOIN roles r ON u.role_id = r.id 
                WHERE u.id = ?
            ");
            $checkUserStmt->execute([$utilisateur_id]);
            $user = $checkUserStmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                jsonResponse(['error' => 'Utilisateur non trouvé'], 404);
            }
            
            if (strtolower($user['role_nom']) !== 'etudiant') {
                jsonResponse(['error' => 'L\'utilisateur doit avoir le rôle "etudiant"'], 400);
            }
            
            // Vérifier que le nouvel utilisateur n'est pas déjà étudiant
            $checkExistingStmt = $pdo->prepare("SELECT id FROM etudiants WHERE utilisateur_id = ? AND id != ?");
            $checkExistingStmt->execute([$utilisateur_id, $id]);
            if ($checkExistingStmt->fetch()) {
                jsonResponse(['error' => 'Cet utilisateur est déjà enregistré comme étudiant'], 400);
            }
        } else {
            // Si utilisateur_id n'est pas fourni, garder l'ancien
            $utilisateur_id = $existingEtudiant['utilisateur_id'];
        }
        
        // Vérifier que le groupe existe (si fourni)
        if ($groupe_id !== null) {
            $checkGroupStmt = $pdo->prepare("SELECT id FROM groupes WHERE id = ?");
            $checkGroupStmt->execute([$groupe_id]);
            if (!$checkGroupStmt->fetch()) {
                jsonResponse(['error' => 'Groupe non trouvé'], 404);
            }
        }
        
        // Mettre à jour l'étudiant
        try {
            $stmt = $pdo->prepare("
                UPDATE etudiants 
                SET utilisateur_id = :utilisateur_id, groupe_id = :groupe_id
                WHERE id = :id
            ");
            
            $result = $stmt->execute([
                'utilisateur_id' => $utilisateur_id,
                'groupe_id' => $groupe_id,
                'id' => $id
            ]);
            
            if ($result) {
                logDebug("Étudiant updated successfully", ['id' => $id]);
                jsonResponse([
                    'success' => true,
                    'message' => 'Étudiant modifié avec succès'
                ]);
            } else {
                jsonResponse(['error' => 'Aucune modification effectuée'], 400);
            }
            
        } catch (PDOException $e) {
            logDebug("Database error during update", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } elseif ($method === 'DELETE') {
        // DELETE - Supprimer un étudiant
        logDebug("Processing DELETE request");
        
        $input = file_get_contents("php://input");
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'JSON invalide: ' . json_last_error_msg()], 400);
        }
        
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            jsonResponse(['error' => 'ID de l\'étudiant requis'], 400);
        }
        
        $id = intval($data['id']);
        logDebug("Processing deletion", ['id' => $id]);
        
        // Vérifier que l'étudiant existe
        $checkStmt = $pdo->prepare("SELECT id FROM etudiants WHERE id = ?");
        $checkStmt->execute([$id]);
        if (!$checkStmt->fetch()) {
            jsonResponse(['error' => 'Étudiant non trouvé'], 404);
        }
        
        // Supprimer l'étudiant
        try {
            $stmt = $pdo->prepare("DELETE FROM etudiants WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if ($result && $stmt->rowCount() > 0) {
                logDebug("Étudiant deleted successfully", ['id' => $id]);
                jsonResponse([
                    'success' => true,
                    'message' => 'Étudiant supprimé avec succès'
                ]);
            } else {
                jsonResponse(['error' => 'Aucune suppression effectuée'], 400);
            }
            
        } catch (PDOException $e) {
            logDebug("Database error during deletion", ['error' => $e->getMessage()]);
            jsonResponse(['error' => 'Erreur base de données : ' . $e->getMessage()], 500);
        }
        
    } else {
        jsonResponse(['error' => 'Méthode non autorisée'], 405);
    }
    
} catch (Exception $e) {
    logDebug("Fatal exception", [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    jsonResponse(['error' => 'Erreur fatale : ' . $e->getMessage()], 500);
}
?>
