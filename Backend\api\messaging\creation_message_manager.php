<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Création MessageManager Complet</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1400px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #667eea; font-size: 2.8rem; margin-bottom: 10px; }
        .success { background: #d4edda; border-left: 5px solid #28a745; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #f8d7da; border-left: 5px solid #dc3545; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { background: #d1ecf1; border-left: 5px solid #17a2b8; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; }
        .btn-success { background: #28a745; }
        pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 11px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 CRÉATION MESSAGEMANAGER COMPLET</h1>
            <p>Système de messagerie avec toutes les fonctionnalités WhatsApp</p>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <h3>🔒 Confidentialité Stricte</h3>
                <p>Messages visibles uniquement par expéditeur et destinataire</p>
            </div>
            <div class="feature-card">
                <h3>✏️ Modification Messages</h3>
                <p>Édition avec sauvegarde original et timestamps</p>
            </div>
            <div class="feature-card">
                <h3>🗑️ Suppression WhatsApp</h3>
                <p>Suppression pour soi ou pour tous</p>
            </div>
            <div class="feature-card">
                <h3>📱 Interface Moderne</h3>
                <p>Design inspiré des apps de messagerie modernes</p>
            </div>
        </div>
        
        <?php
        $creationSteps = [];
        $errors = [];
        
        try {
            echo '<div class="info">🔧 Création du MessageManager complet avec toutes les fonctionnalités...</div>';
            
            // CRÉATION DU MESSAGEMANAGER COMPLET
            $messageManagerCode = '<?php
require_once \'AuthManager.php\';

class MessageManager {
    private $pdo;
    private $currentUser;
    
    public function __construct($user) {
        $host = \'localhost\';
        $dbname = \'GestionScolaire\';
        $username = \'root\';
        $password = \'\';
        
        try {
            $this->pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception("Erreur connexion MessageManager: " . $e->getMessage());
        }
        
        $this->currentUser = $user;
        
        // Vérifier que l\'utilisateur peut accéder à la messagerie
        if (!in_array($user[\'role\'], [\'parent\', \'enseignant\', \'admin\', \'responsable\'])) {
            throw new Exception("Accès messagerie non autorisé pour le rôle: " . $user[\'role\']);
        }
    }
    
    /**
     * 💬 RÉCUPÉRER LES CONVERSATIONS
     * Retourne la liste des conversations de l\'utilisateur avec confidentialité stricte
     */
    public function getConversations() {
        try {
            $userId = (int)$this->currentUser[\'id\'];
            
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT 
                    CASE 
                        WHEN m.expediteur_id = :user_id THEN m.destinataire_id 
                        ELSE m.expediteur_id 
                    END as contact_id,
                    CASE 
                        WHEN m.expediteur_id = :user_id2 THEN COALESCE(u2.nom, \'Utilisateur inconnu\')
                        ELSE COALESCE(u1.nom, \'Utilisateur inconnu\')
                    END as contact_nom,
                    CASE 
                        WHEN m.expediteur_id = :user_id3 THEN COALESCE(u2.email, \'<EMAIL>\')
                        ELSE COALESCE(u1.email, \'<EMAIL>\')
                    END as contact_email,
                    CASE 
                        WHEN m.expediteur_id = :user_id4 THEN COALESCE(r2.nom, \'role_inconnu\')
                        ELSE COALESCE(r1.nom, \'role_inconnu\')
                    END as contact_role,
                    MAX(m.date_envoi) as derniere_activite,
                    COUNT(CASE 
                        WHEN m.destinataire_id = :user_id5 
                        AND m.lu = 0 
                        AND m.supprime_par_destinataire = 0 
                        THEN 1 
                    END) as messages_non_lus,
                    (SELECT m2.message 
                     FROM messages m2 
                     WHERE ((m2.expediteur_id = :user_id6 AND m2.destinataire_id = contact_id AND m2.supprime_par_expediteur = 0) OR 
                            (m2.expediteur_id = contact_id AND m2.destinataire_id = :user_id7 AND m2.supprime_par_destinataire = 0))
                     ORDER BY m2.date_envoi DESC 
                     LIMIT 1
                    ) as dernier_message,
                    (SELECT m2.date_envoi 
                     FROM messages m2 
                     WHERE ((m2.expediteur_id = :user_id8 AND m2.destinataire_id = contact_id AND m2.supprime_par_expediteur = 0) OR 
                            (m2.expediteur_id = contact_id AND m2.destinataire_id = :user_id9 AND m2.supprime_par_destinataire = 0))
                     ORDER BY m2.date_envoi DESC 
                     LIMIT 1
                    ) as dernier_message_date
                FROM messages m
                LEFT JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                LEFT JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                LEFT JOIN roles r1 ON u1.role_id = r1.id
                LEFT JOIN roles r2 ON u2.role_id = r2.id
                WHERE (m.expediteur_id = :user_id10 OR m.destinataire_id = :user_id11)
                AND ((m.expediteur_id = :user_id12 AND m.supprime_par_expediteur = 0) OR 
                     (m.destinataire_id = :user_id13 AND m.supprime_par_destinataire = 0))
                GROUP BY contact_id, contact_nom, contact_email, contact_role
                HAVING contact_id IS NOT NULL AND contact_id != :user_id14
                ORDER BY derniere_activite DESC
            ");
            
            $stmt->execute([
                \':user_id\' => $userId, \':user_id2\' => $userId, \':user_id3\' => $userId, \':user_id4\' => $userId,
                \':user_id5\' => $userId, \':user_id6\' => $userId, \':user_id7\' => $userId, \':user_id8\' => $userId,
                \':user_id9\' => $userId, \':user_id10\' => $userId, \':user_id11\' => $userId, \':user_id12\' => $userId,
                \':user_id13\' => $userId, \':user_id14\' => $userId
            ]);
            
            $conversations = $stmt->fetchAll();
            
            logError(\'Conversations récupérées\', [
                \'user_id\' => $userId,
                \'count\' => count($conversations)
            ]);
            
            return $conversations;
            
        } catch (Exception $e) {
            logError(\'Erreur getConversations\', [
                \'user_id\' => $this->currentUser[\'id\'], 
                \'error\' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * 📨 RÉCUPÉRER LES MESSAGES D\'UNE CONVERSATION
     * Avec gestion de la confidentialité stricte et des suppressions
     */
    public function getMessages($contactId) {
        try {
            $contactId = (int)$contactId;
            $userId = (int)$this->currentUser[\'id\'];
            
            $stmt = $this->pdo->prepare("
                SELECT m.*, 
                       COALESCE(u1.nom, \'Utilisateur inconnu\') as expediteur_nom, 
                       COALESCE(u1.email, \'<EMAIL>\') as expediteur_email,
                       COALESCE(u2.nom, \'Utilisateur inconnu\') as destinataire_nom, 
                       COALESCE(u2.email, \'<EMAIL>\') as destinataire_email,
                       CASE 
                           WHEN m.expediteur_id = :user_id THEN \'sent\'
                           ELSE \'received\'
                       END as message_type,
                       CASE 
                           WHEN m.expediteur_id = :user_id2 THEN 1
                           ELSE 0
                       END as can_modify,
                       CASE 
                           WHEN m.expediteur_id = :user_id3 THEN 1
                           ELSE 0
                       END as can_delete_for_all
                FROM messages m
                LEFT JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                LEFT JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                WHERE ((m.expediteur_id = :user_id4 AND m.destinataire_id = :contact_id) OR 
                       (m.expediteur_id = :contact_id2 AND m.destinataire_id = :user_id5))
                AND ((m.expediteur_id = :user_id6 AND m.supprime_par_expediteur = 0) OR 
                     (m.destinataire_id = :user_id7 AND m.supprime_par_destinataire = 0))
                ORDER BY m.date_envoi ASC
            ");
            
            $stmt->execute([
                \':user_id\' => $userId, \':user_id2\' => $userId, \':user_id3\' => $userId,
                \':user_id4\' => $userId, \':user_id5\' => $userId, \':user_id6\' => $userId,
                \':user_id7\' => $userId, \':contact_id\' => $contactId, \':contact_id2\' => $contactId
            ]);
            
            $messages = $stmt->fetchAll();
            
            // Marquer les messages comme lus
            $this->markMessagesAsRead($contactId);
            
            return $messages;
            
        } catch (Exception $e) {
            logError(\'Erreur getMessages\', [
                \'user_id\' => $userId, 
                \'contact_id\' => $contactId, 
                \'error\' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * ✉️ ENVOYER UN MESSAGE
     */
    public function sendMessage($destinataireId, $message) {
        try {
            $destinataireId = (int)$destinataireId;
            $userId = (int)$this->currentUser[\'id\'];
            
            // Vérifier que le destinataire existe et est autorisé
            $stmt = $this->pdo->prepare("
                SELECT u.id, r.nom as role
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id = ? AND r.nom IN (\'parent\', \'enseignant\', \'admin\', \'responsable\')
            ");
            $stmt->execute([$destinataireId]);
            $destinataire = $stmt->fetch();
            
            if (!$destinataire) {
                throw new Exception(\'Destinataire non autorisé ou inexistant\');
            }
            
            $stmt = $this->pdo->prepare("
                INSERT INTO messages (
                    expediteur_id, destinataire_id, message, date_envoi, lu,
                    modifie, date_modification, supprime_par_expediteur, supprime_par_destinataire,
                    supprime_expediteur, supprime_destinataire, message_original
                ) VALUES (?, ?, ?, NOW(), 0, 0, NULL, 0, 0, 0, 0, NULL)
            ");
            
            $stmt->execute([$userId, $destinataireId, $message]);
            $messageId = $this->pdo->lastInsertId();
            
            logError(\'Message envoyé\', [
                \'message_id\' => $messageId,
                \'from\' => $userId,
                \'to\' => $destinataireId
            ]);
            
            return $messageId;
            
        } catch (Exception $e) {
            logError(\'Erreur sendMessage\', [
                \'user_id\' => $this->currentUser[\'id\'], 
                \'destinataire_id\' => $destinataireId,
                \'error\' => $e->getMessage()
            ]);
            throw new Exception(\'Erreur lors de l\\\'envoi du message\');
        }
    }
    
    /**
     * ✏️ MODIFIER UN MESSAGE (style WhatsApp)
     * Seul l\'expéditeur peut modifier son message
     */
    public function editMessage($messageId, $newContent) {
        try {
            $messageId = (int)$messageId;
            $userId = (int)$this->currentUser[\'id\'];
            
            // Vérifier que le message existe et appartient à l\'utilisateur
            $stmt = $this->pdo->prepare("
                SELECT * FROM messages 
                WHERE id = ? AND expediteur_id = ?
            ");
            $stmt->execute([$messageId, $userId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                throw new Exception(\'Message introuvable ou non autorisé\');
            }
            
            // Sauvegarder l\'original si c\'est la première modification
            $originalMessage = $message[\'modifie\'] ? $message[\'message_original\'] : $message[\'message\'];
            
            $stmt = $this->pdo->prepare("
                UPDATE messages 
                SET message = ?, 
                    modifie = 1, 
                    date_modification = NOW(),
                    message_original = ?
                WHERE id = ? AND expediteur_id = ?
            ");
            
            $stmt->execute([$newContent, $originalMessage, $messageId, $userId]);
            
            logError(\'Message modifié\', [
                \'message_id\' => $messageId,
                \'user_id\' => $userId
            ]);
            
            return true;
            
        } catch (Exception $e) {
            logError(\'Erreur editMessage\', [
                \'message_id\' => $messageId,
                \'user_id\' => $this->currentUser[\'id\'],
                \'error\' => $e->getMessage()
            ]);
            throw new Exception(\'Erreur lors de la modification du message\');
        }
    }
    
    /**
     * 🗑️ SUPPRIMER UN MESSAGE (style WhatsApp)
     * $deleteType: \'for_me\' ou \'for_everyone\'
     */
    public function deleteMessage($messageId, $deleteType = \'for_me\') {
        try {
            $messageId = (int)$messageId;
            $userId = (int)$this->currentUser[\'id\'];
            
            // Vérifier que le message existe et que l\'utilisateur y a accès
            $stmt = $this->pdo->prepare("
                SELECT * FROM messages 
                WHERE id = ? AND (expediteur_id = ? OR destinataire_id = ?)
            ");
            $stmt->execute([$messageId, $userId, $userId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                throw new Exception(\'Message introuvable ou non autorisé\');
            }
            
            if ($deleteType === \'for_everyone\') {
                // Seul l\'expéditeur peut supprimer pour tout le monde
                if ($message[\'expediteur_id\'] != $userId) {
                    throw new Exception(\'Seul l\\\'expéditeur peut supprimer pour tout le monde\');
                }
                
                $stmt = $this->pdo->prepare("
                    UPDATE messages 
                    SET supprime_expediteur = 1, supprime_destinataire = 1
                    WHERE id = ?
                ");
                $stmt->execute([$messageId]);
                
            } else {
                // Suppression pour soi uniquement
                if ($message[\'expediteur_id\'] == $userId) {
                    $stmt = $this->pdo->prepare("
                        UPDATE messages 
                        SET supprime_par_expediteur = 1
                        WHERE id = ?
                    ");
                } else {
                    $stmt = $this->pdo->prepare("
                        UPDATE messages 
                        SET supprime_par_destinataire = 1
                        WHERE id = ?
                    ");
                }
                $stmt->execute([$messageId]);
            }
            
            logError(\'Message supprimé\', [
                \'message_id\' => $messageId,
                \'user_id\' => $userId,
                \'delete_type\' => $deleteType
            ]);
            
            return true;
            
        } catch (Exception $e) {
            logError(\'Erreur deleteMessage\', [
                \'message_id\' => $messageId,
                \'user_id\' => $this->currentUser[\'id\'],
                \'delete_type\' => $deleteType,
                \'error\' => $e->getMessage()
            ]);
            throw new Exception(\'Erreur lors de la suppression du message\');
        }
    }
    
    /**
     * 👁️ MARQUER LES MESSAGES COMME LUS
     */
    private function markMessagesAsRead($contactId) {
        try {
            $contactId = (int)$contactId;
            $userId = (int)$this->currentUser[\'id\'];
            
            $stmt = $this->pdo->prepare("
                UPDATE messages 
                SET lu = 1 
                WHERE expediteur_id = ? AND destinataire_id = ? AND lu = 0
            ");
            $stmt->execute([$contactId, $userId]);
            
        } catch (Exception $e) {
            logError(\'Erreur markMessagesAsRead\', [
                \'contact_id\' => $contactId,
                \'user_id\' => $userId,
                \'error\' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 👥 RÉCUPÉRER LES UTILISATEURS AUTORISÉS
     */
    public function getAuthorizedUsers() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.id, 
                       COALESCE(u.nom, \'Utilisateur inconnu\') as nom, 
                       COALESCE(u.email, \'<EMAIL>\') as email, 
                       COALESCE(r.nom, \'role_inconnu\') as role
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id != ? 
                AND r.nom IN (\'parent\', \'enseignant\', \'admin\', \'responsable\')
                ORDER BY u.nom
            ");
            
            $stmt->execute([$this->currentUser[\'id\']]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            logError(\'Erreur getAuthorizedUsers\', [
                \'user_id\' => $this->currentUser[\'id\'], 
                \'error\' => $e->getMessage()
            ]);
            return [];
        }
    }
    
    /**
     * 📊 RÉCUPÉRER LES STATISTIQUES
     */
    public function getStats() {
        try {
            $userId = $this->currentUser[\'id\'];
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN expediteur_id = ? AND supprime_par_expediteur = 0 THEN 1 END) as messages_envoyes,
                    COUNT(CASE WHEN destinataire_id = ? AND supprime_par_destinataire = 0 THEN 1 END) as messages_recus,
                    COUNT(CASE WHEN destinataire_id = ? AND lu = 0 AND supprime_par_destinataire = 0 THEN 1 END) as messages_non_lus,
                    COUNT(CASE WHEN modifie = 1 AND (expediteur_id = ? OR destinataire_id = ?) THEN 1 END) as messages_modifies
                FROM messages 
                WHERE (expediteur_id = ? OR destinataire_id = ?)
                AND ((expediteur_id = ? AND supprime_par_expediteur = 0) OR 
                     (destinataire_id = ? AND supprime_par_destinataire = 0))
            ");
            
            $stmt->execute([
                $userId, $userId, $userId, $userId, $userId, $userId, $userId, $userId, $userId
            ]);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            logError(\'Erreur getStats\', [
                \'user_id\' => $this->currentUser[\'id\'], 
                \'error\' => $e->getMessage()
            ]);
            return [
                \'total_messages\' => 0,
                \'messages_envoyes\' => 0,
                \'messages_recus\' => 0,
                \'messages_non_lus\' => 0,
                \'messages_modifies\' => 0
            ];
        }
    }
}
?>';
            
            // Sauvegarder le MessageManager
            try {
                file_put_contents('MessageManager.php', $messageManagerCode);
                echo '<div class="success">✅ MessageManager.php complet créé avec succès</div>';
                $creationSteps[] = 'MessageManager complet avec toutes les fonctionnalités WhatsApp';
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur création MessageManager: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur création MessageManager';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ ERREUR GÉNÉRALE: ' . $e->getMessage() . '</div>';
            $errors[] = 'Erreur générale de création';
        }
        ?>
        
        <div style="margin-top: 40px;">
            <h2>📊 Fonctionnalités Implémentées</h2>
            
            <?php if (empty($errors)): ?>
                <div class="success">
                    <h3>✅ MessageManager Complet Créé !</h3>
                    <p><strong>Fonctionnalités incluses :</strong></p>
                    <ul>
                        <li>🔒 <strong>Confidentialité stricte</strong> - Messages visibles uniquement par expéditeur/destinataire</li>
                        <li>💬 <strong>Conversations</strong> - Liste des conversations avec derniers messages</li>
                        <li>📨 <strong>Messages</strong> - Récupération avec gestion des suppressions</li>
                        <li>✉️ <strong>Envoi</strong> - Envoi de nouveaux messages avec validation</li>
                        <li>✏️ <strong>Modification</strong> - Édition style WhatsApp avec sauvegarde original</li>
                        <li>🗑️ <strong>Suppression</strong> - Pour soi ou pour tous (style WhatsApp)</li>
                        <li>👁️ <strong>Messages lus</strong> - Marquage automatique comme lu</li>
                        <li>👥 <strong>Utilisateurs autorisés</strong> - Exclusion des étudiants</li>
                        <li>📊 <strong>Statistiques</strong> - Compteurs de messages</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="creation_api_endpoints.php" class="btn btn-success">➡️ CONTINUER - API ENDPOINTS</a>
                </div>
                
            <?php else: ?>
                <div class="error">
                    <h3>❌ Erreurs Détectées</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li>❌ <?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="?" class="btn">🔄 RELANCER CRÉATION</a>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="reconstruction_systeme_messagerie.php" class="btn">⬅️ RETOUR RECONSTRUCTION</a>
                <a href="diagnostic_table_messages.php" class="btn">🔍 DIAGNOSTIC</a>
            </div>
        </div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>🎯 Prochaines Étapes</h3>
            <ol>
                <li><strong>Créer les API endpoints</strong> pour toutes les fonctionnalités</li>
                <li><strong>Implémenter l'interface React</strong> moderne</li>
                <li><strong>Ajouter les fonctionnalités avancées</strong> (temps réel, notifications)</li>
                <li><strong>Tester la sécurité</strong> et la confidentialité</li>
                <li><strong>Valider l'expérience utilisateur</strong> complète</li>
            </ol>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <h3>🔧 Fonctionnalités Techniques</h3>
            <ul>
                <li><strong>Confidentialité</strong> : Requêtes SQL avec filtres stricts sur expediteur_id/destinataire_id</li>
                <li><strong>Suppression WhatsApp</strong> : Champs supprime_par_expediteur/destinataire et supprime_expediteur/destinataire</li>
                <li><strong>Modification</strong> : Sauvegarde dans message_original avec timestamps</li>
                <li><strong>Sécurité</strong> : Validation des rôles et vérification des permissions</li>
                <li><strong>Performance</strong> : Requêtes optimisées avec LEFT JOIN et indexation</li>
            </ul>
        </div>
    </div>
</body>
</html>
