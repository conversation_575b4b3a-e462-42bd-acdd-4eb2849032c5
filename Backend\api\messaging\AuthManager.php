<?php
class AuthManager {
    private $pdo;
    
    public function __construct() {
        $host = 'localhost';
        $dbname = 'GestionScolaire';
        $username = 'root';
        $password = '';
        
        try {
            $this->pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception("Erreur connexion AuthManager: " . $e->getMessage());
        }
    }
    
    /**
     * Authentifier un utilisateur par token
     */
    public function authenticateUser($token) {
        try {
            // Pour les tests, accepter les tokens de test
            if (strpos($token, 'test_') === 0) {
                return $this->getTestUser($token);
            }
            
            // Ici vous pouvez implémenter votre logique JWT réelle
            // Pour l'instant, simulation avec token simple
            if (preg_match('/^user_(\d+)$/', $token, $matches)) {
                $userId = (int)$matches[1];
                return $this->getUserById($userId);
            }
            
            throw new Exception('Token invalide');
            
        } catch (Exception $e) {
            logError('Erreur authentification', ['token' => $token, 'error' => $e->getMessage()]);
            throw new Exception('Authentification échouée');
        }
    }
    
    /**
     * Récupérer un utilisateur par ID
     */
    public function getUserById($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id = ?
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception('Utilisateur introuvable');
            }
            
            // Vérifier que l'utilisateur a un rôle autorisé
            if (!in_array($user['role'], ['parent', 'enseignant', 'admin', 'responsable'])) {
                throw new Exception('Rôle non autorisé pour la messagerie');
            }
            
            return $user;
            
        } catch (Exception $e) {
            logError('Erreur getUserById', ['user_id' => $userId, 'error' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Utilisateur de test pour le développement
     */
    private function getTestUser($token) {
        try {
            // Récupérer le premier utilisateur autorisé pour les tests
            $stmt = $this->pdo->query("
                SELECT u.id, u.nom, u.email, r.nom as role
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE r.nom IN ('parent', 'enseignant', 'admin', 'responsable')
                LIMIT 1
            ");
            $user = $stmt->fetch();
            
            if (!$user) {
                // Créer un utilisateur de test si aucun n'existe
                $stmt = $this->pdo->prepare("
                    INSERT INTO utilisateurs (nom, email, mot_de_passe, role_id) 
                    VALUES (?, ?, ?, (SELECT id FROM roles WHERE nom = 'admin' LIMIT 1))
                ");
                $stmt->execute(['Test Admin', '<EMAIL>', password_hash('test123', PASSWORD_DEFAULT)]);
                
                $userId = $this->pdo->lastInsertId();
                return $this->getUserById($userId);
            }
            
            return $user;
            
        } catch (Exception $e) {
            logError('Erreur getTestUser', ['token' => $token, 'error' => $e->getMessage()]);
            throw new Exception('Impossible de créer utilisateur de test');
        }
    }
    
    /**
     * Vérifier si un utilisateur peut accéder à la messagerie
     */
    public function canAccessMessaging($user) {
        return in_array($user['role'], ['parent', 'enseignant', 'admin', 'responsable']);
    }
    
    /**
     * Vérifier si un utilisateur peut voir un message spécifique
     */
    public function canAccessMessage($user, $message) {
        return ($message['expediteur_id'] == $user['id'] || $message['destinataire_id'] == $user['id']);
    }
}

/**
 * Fonction de logging pour debug
 */
function logError($message, $data = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'data' => $data
    ];
    
    // Écrire dans un fichier de log (optionnel)
    // file_put_contents('messaging_debug.log', json_encode($logEntry) . "\n", FILE_APPEND);
}
?>