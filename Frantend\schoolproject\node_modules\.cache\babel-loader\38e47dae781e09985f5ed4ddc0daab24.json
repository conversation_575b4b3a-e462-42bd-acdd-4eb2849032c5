{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\components\\\\NavbarTop.js\";\n// src/components/NavbarTop.jsx\nimport React, { useContext, useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaUserCircle, FaSignInAlt, FaSignOutAlt, FaGraduationCap, FaCog, FaChevronDown, FaUsers } from 'react-icons/fa';\nimport { AuthContext } from '../context/AuthContext';\nimport NotificationCenter from './NotificationCenter';\nconst NavbarTop = () => {\n  const {\n    isAuthenticated,\n    user,\n    logout\n  } = useContext(AuthContext);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const handleLogout = () => {\n    logout();\n    setShowUserMenu(false);\n  };\n  const navStyles = {\n    nav: {\n      backgroundColor: 'var(--cerulean)',\n      padding: '15px 25px',\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      position: 'fixed',\n      top: 0,\n      left: '70px',\n      right: 0,\n      height: '70px',\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n      zIndex: 999,\n      transition: 'all 0.3s ease',\n      backdropFilter: 'blur(10px)'\n    },\n    brandSection: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '15px'\n    },\n    brandIcon: {\n      fontSize: '1.8rem',\n      color: 'rgb(1, 167, 194)',\n      animation: 'pulse 2s infinite'\n    },\n    brandText: {\n      color: 'var(--antiflash-white)',\n      fontSize: '1.2rem',\n      fontWeight: 'bold',\n      textShadow: '1px 1px 2px rgba(0,0,0,0.3)'\n    },\n    userInfo: {\n      color: 'var(--antiflash-white)',\n      fontSize: '14px',\n      padding: '8px 15px',\n      backgroundColor: 'rgba(255,255,255,0.1)',\n      borderRadius: '20px',\n      border: '1px solid rgba(255,255,255,0.2)',\n      backdropFilter: 'blur(5px)'\n    },\n    menuSection: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '10px'\n    },\n    menuItem: {\n      color: 'var(--antiflash-white)',\n      textDecoration: 'none',\n      padding: '10px 15px',\n      borderRadius: '10px',\n      fontSize: '14px',\n      fontWeight: '500',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      transition: 'all 0.3s ease',\n      position: 'relative',\n      overflow: 'hidden'\n    },\n    userMenuContainer: {\n      position: 'relative'\n    },\n    userMenuButton: {\n      background: 'none',\n      border: 'none',\n      color: 'var(--antiflash-white)',\n      padding: '10px 15px',\n      borderRadius: '10px',\n      fontSize: '14px',\n      fontWeight: '500',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px',\n      cursor: 'pointer',\n      transition: 'all 0.3s ease'\n    },\n    userDropdown: {\n      position: 'absolute',\n      top: '100%',\n      right: 0,\n      marginTop: '10px',\n      backgroundColor: 'rgba(255,255,255,0.95)',\n      backdropFilter: 'blur(10px)',\n      borderRadius: '12px',\n      boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n      border: '1px solid rgba(255,255,255,0.3)',\n      minWidth: '200px',\n      opacity: showUserMenu ? 1 : 0,\n      transform: showUserMenu ? 'translateY(0) scale(1)' : 'translateY(-10px) scale(0.95)',\n      transition: 'all 0.3s ease',\n      pointerEvents: showUserMenu ? 'auto' : 'none',\n      zIndex: 1000\n    },\n    dropdownItem: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '10px',\n      padding: '12px 15px',\n      color: '#333',\n      textDecoration: 'none',\n      fontSize: '14px',\n      transition: 'all 0.2s ease',\n      borderRadius: '8px',\n      margin: '5px'\n    }\n  };\n  return /*#__PURE__*/React.createElement(\"nav\", {\n    style: navStyles.nav,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    to: \"/\",\n    style: {\n      textDecoration: 'none'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: navStyles.brandSection,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 3\n    }\n  }, /*#__PURE__*/React.createElement(FaGraduationCap, {\n    style: navStyles.brandIcon,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 5\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    style: navStyles.brandText,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 5\n    }\n  }, \"\\xC9cole Moderne\"))), isAuthenticated && /*#__PURE__*/React.createElement(\"div\", {\n    style: navStyles.userInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 9\n    }\n  }, \"Connect\\xE9 en tant que: \", /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 33\n    }\n  }, (user === null || user === void 0 ? void 0 : user.role) || 'Utilisateur'), (user === null || user === void 0 ? void 0 : user.email) && /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      marginLeft: '10px',\n      opacity: 0.8\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 27\n    }\n  }, \"(\", user.email, \")\")), /*#__PURE__*/React.createElement(\"div\", {\n    style: navStyles.menuSection,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }\n  }, isAuthenticated ? /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(NotificationCenter, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    style: navStyles.userMenuContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    style: navStyles.userMenuButton,\n    onClick: () => setShowUserMenu(!showUserMenu),\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';\n      e.target.style.transform = 'translateY(-2px)';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = 'transparent';\n      e.target.style.transform = 'translateY(0)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(FaUserCircle, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 17\n    }\n  }, \"Mon Compte\"), /*#__PURE__*/React.createElement(FaChevronDown, {\n    style: {\n      transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\n      transition: 'transform 0.3s ease'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    style: navStyles.userDropdown,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(Link, {\n    to: \"/profil\",\n    style: navStyles.dropdownItem,\n    onClick: () => setShowUserMenu(false),\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';\n      e.target.style.transform = 'translateX(5px)';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = 'transparent';\n      e.target.style.transform = 'translateX(0)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaUserCircle, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 19\n    }\n  }, \"Mon Profil\")), /*#__PURE__*/React.createElement(Link, {\n    to: \"/settings\",\n    style: navStyles.dropdownItem,\n    onClick: () => setShowUserMenu(false),\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';\n      e.target.style.transform = 'translateX(5px)';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = 'transparent';\n      e.target.style.transform = 'translateX(0)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaCog, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 19\n    }\n  }, \"Param\\xE8tres\")), /*#__PURE__*/React.createElement(\"hr\", {\n    style: {\n      margin: '5px 0',\n      border: 'none',\n      borderTop: '1px solid rgba(0,0,0,0.1)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: handleLogout,\n    style: {\n      ...navStyles.dropdownItem,\n      background: 'none',\n      border: 'none',\n      cursor: 'pointer',\n      width: '100%',\n      textAlign: 'left',\n      color: '#e74c3c'\n    },\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = 'rgba(231, 76, 60, 0.1)';\n      e.target.style.transform = 'translateX(5px)';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = 'transparent';\n      e.target.style.transform = 'translateX(0)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FaSignOutAlt, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 19\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 19\n    }\n  }, \"D\\xE9connexion\"))))) : /*#__PURE__*/React.createElement(Link, {\n    to: \"/login\",\n    style: navStyles.menuItem,\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';\n      e.target.style.transform = 'translateY(-2px)';\n      e.target.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = 'transparent';\n      e.target.style.transform = 'translateY(0)';\n      e.target.style.boxShadow = 'none';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FaSignInAlt, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 13\n    }\n  }, \"Connexion\"))), /*#__PURE__*/React.createElement(\"style\", {\n    jsx: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }\n  }, `\n        @keyframes pulse {\n          0% { transform: scale(1); }\n          50% { transform: scale(1.05); }\n          100% { transform: scale(1); }\n        }\n      `));\n};\nexport default NavbarTop;", "map": {"version": 3, "names": ["React", "useContext", "useState", "Link", "FaUserCircle", "FaSignInAlt", "FaSignOutAlt", "FaGraduationCap", "FaCog", "FaChevronDown", "FaUsers", "AuthContext", "NotificationCenter", "NavbarTop", "isAuthenticated", "user", "logout", "showUserMenu", "setShowUserMenu", "handleLogout", "navStyles", "nav", "backgroundColor", "padding", "display", "justifyContent", "alignItems", "position", "top", "left", "right", "height", "boxShadow", "zIndex", "transition", "<PERSON><PERSON>ilter", "brandSection", "gap", "brandIcon", "fontSize", "color", "animation", "brandText", "fontWeight", "textShadow", "userInfo", "borderRadius", "border", "menuSection", "menuItem", "textDecoration", "overflow", "userMenuContainer", "userMenuButton", "background", "cursor", "userDropdown", "marginTop", "min<PERSON><PERSON><PERSON>", "opacity", "transform", "pointerEvents", "dropdownItem", "margin", "createElement", "style", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "role", "email", "marginLeft", "Fragment", "onClick", "onMouseEnter", "e", "target", "onMouseLeave", "borderTop", "width", "textAlign", "jsx"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/components/NavbarTop.js"], "sourcesContent": ["// src/components/NavbarTop.jsx\r\nimport React, { useContext, useState } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport {\r\n  FaUserCircle,\r\n  FaSignInAlt,\r\n  FaSignOutAlt,\r\n  FaGraduationCap,\r\n  FaCog,\r\n  FaChevronDown,\r\n  FaUsers\r\n} from 'react-icons/fa';\r\nimport { AuthContext } from '../context/AuthContext';\r\nimport NotificationCenter from './NotificationCenter';\r\n\r\nconst NavbarTop = () => {\r\n  const { isAuthenticated, user, logout } = useContext(AuthContext);\r\n  const [showUserMenu, setShowUserMenu] = useState(false);\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n    setShowUserMenu(false);\r\n  };\r\n\r\n  const navStyles = {\r\n    nav: {\r\n      backgroundColor: 'var(--cerulean)',\r\n      padding: '15px 25px',\r\n      display: 'flex',\r\n      justifyContent: 'space-between',\r\n      alignItems: 'center',\r\n      position: 'fixed',\r\n      top: 0,\r\n      left: '70px',\r\n      right: 0,\r\n      height: '70px',\r\n      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\r\n      zIndex: 999,\r\n      transition: 'all 0.3s ease',\r\n      backdropFilter: 'blur(10px)'\r\n    },\r\n    brandSection: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '15px'\r\n    },\r\n    brandIcon: {\r\n      fontSize: '1.8rem',\r\n      color: 'rgb(1, 167, 194)',\r\n      animation: 'pulse 2s infinite'\r\n    },\r\n    brandText: {\r\n      color: 'var(--antiflash-white)',\r\n      fontSize: '1.2rem',\r\n      fontWeight: 'bold',\r\n      textShadow: '1px 1px 2px rgba(0,0,0,0.3)'\r\n    },\r\n    userInfo: {\r\n      color: 'var(--antiflash-white)',\r\n      fontSize: '14px',\r\n      padding: '8px 15px',\r\n      backgroundColor: 'rgba(255,255,255,0.1)',\r\n      borderRadius: '20px',\r\n      border: '1px solid rgba(255,255,255,0.2)',\r\n      backdropFilter: 'blur(5px)'\r\n    },\r\n    menuSection: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '10px'\r\n    },\r\n    menuItem: {\r\n      color: 'var(--antiflash-white)',\r\n      textDecoration: 'none',\r\n      padding: '10px 15px',\r\n      borderRadius: '10px',\r\n      fontSize: '14px',\r\n      fontWeight: '500',\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '8px',\r\n      transition: 'all 0.3s ease',\r\n      position: 'relative',\r\n      overflow: 'hidden'\r\n    },\r\n    userMenuContainer: {\r\n      position: 'relative'\r\n    },\r\n    userMenuButton: {\r\n      background: 'none',\r\n      border: 'none',\r\n      color: 'var(--antiflash-white)',\r\n      padding: '10px 15px',\r\n      borderRadius: '10px',\r\n      fontSize: '14px',\r\n      fontWeight: '500',\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '8px',\r\n      cursor: 'pointer',\r\n      transition: 'all 0.3s ease'\r\n    },\r\n    userDropdown: {\r\n      position: 'absolute',\r\n      top: '100%',\r\n      right: 0,\r\n      marginTop: '10px',\r\n      backgroundColor: 'rgba(255,255,255,0.95)',\r\n      backdropFilter: 'blur(10px)',\r\n      borderRadius: '12px',\r\n      boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\r\n      border: '1px solid rgba(255,255,255,0.3)',\r\n      minWidth: '200px',\r\n      opacity: showUserMenu ? 1 : 0,\r\n      transform: showUserMenu ? 'translateY(0) scale(1)' : 'translateY(-10px) scale(0.95)',\r\n      transition: 'all 0.3s ease',\r\n      pointerEvents: showUserMenu ? 'auto' : 'none',\r\n      zIndex: 1000\r\n    },\r\n    dropdownItem: {\r\n      display: 'flex',\r\n      alignItems: 'center',\r\n      gap: '10px',\r\n      padding: '12px 15px',\r\n      color: '#333',\r\n      textDecoration: 'none',\r\n      fontSize: '14px',\r\n      transition: 'all 0.2s ease',\r\n      borderRadius: '8px',\r\n      margin: '5px'\r\n    }\r\n  };\r\n\r\n  return (\r\n    <nav style={navStyles.nav}>\r\n      {/* Section marque/logo */}\r\n    <Link to=\"/\" style={{ textDecoration: 'none' }}>\r\n  <div style={navStyles.brandSection}>\r\n    <FaGraduationCap style={navStyles.brandIcon} />\r\n    <span style={navStyles.brandText}>École Moderne</span>\r\n  </div>\r\n</Link>\r\n\r\n      {/* Informations utilisateur (centre) */}\r\n      {isAuthenticated && (\r\n        <div style={navStyles.userInfo}>\r\n          Connecté en tant que: <strong>{user?.role || 'Utilisateur'}</strong>\r\n          {user?.email && <span style={{ marginLeft: '10px', opacity: 0.8 }}>({user.email})</span>}\r\n        </div>\r\n      )}\r\n\r\n      {/* Menu de navigation (droite) */}\r\n      <div style={navStyles.menuSection}>\r\n        {isAuthenticated ? (\r\n          <>\r\n            {/* Composant de notifications */}\r\n            <NotificationCenter />\r\n\r\n            {/* Menu utilisateur avec dropdown */}\r\n            <div style={navStyles.userMenuContainer}>\r\n              <button\r\n                style={navStyles.userMenuButton}\r\n                onClick={() => setShowUserMenu(!showUserMenu)}\r\n                onMouseEnter={(e) => {\r\n                  e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';\r\n                  e.target.style.transform = 'translateY(-2px)';\r\n                }}\r\n                onMouseLeave={(e) => {\r\n                  e.target.style.backgroundColor = 'transparent';\r\n                  e.target.style.transform = 'translateY(0)';\r\n                }}\r\n              >\r\n                <FaUserCircle />\r\n                <span>Mon Compte</span>\r\n                <FaChevronDown style={{\r\n                  transform: showUserMenu ? 'rotate(180deg)' : 'rotate(0deg)',\r\n                  transition: 'transform 0.3s ease'\r\n                }} />\r\n              </button>\r\n\r\n              {/* Dropdown menu */}\r\n              <div style={navStyles.userDropdown}>\r\n                <Link\r\n                  to=\"/profil\"\r\n                  style={navStyles.dropdownItem}\r\n                  onClick={() => setShowUserMenu(false)}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';\r\n                    e.target.style.transform = 'translateX(5px)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.backgroundColor = 'transparent';\r\n                    e.target.style.transform = 'translateX(0)';\r\n                  }}\r\n                >\r\n                  <FaUserCircle />\r\n                  <span>Mon Profil</span>\r\n                </Link>\r\n\r\n                <Link\r\n                  to=\"/settings\"\r\n                  style={navStyles.dropdownItem}\r\n                  onClick={() => setShowUserMenu(false)}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';\r\n                    e.target.style.transform = 'translateX(5px)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.backgroundColor = 'transparent';\r\n                    e.target.style.transform = 'translateX(0)';\r\n                  }}\r\n                >\r\n                  <FaCog />\r\n                  <span>Paramètres</span>\r\n                </Link>\r\n\r\n                <hr style={{ margin: '5px 0', border: 'none', borderTop: '1px solid rgba(0,0,0,0.1)' }} />\r\n\r\n                <button\r\n                  onClick={handleLogout}\r\n                  style={{\r\n                    ...navStyles.dropdownItem,\r\n                    background: 'none',\r\n                    border: 'none',\r\n                    cursor: 'pointer',\r\n                    width: '100%',\r\n                    textAlign: 'left',\r\n                    color: '#e74c3c'\r\n                  }}\r\n                  onMouseEnter={(e) => {\r\n                    e.target.style.backgroundColor = 'rgba(231, 76, 60, 0.1)';\r\n                    e.target.style.transform = 'translateX(5px)';\r\n                  }}\r\n                  onMouseLeave={(e) => {\r\n                    e.target.style.backgroundColor = 'transparent';\r\n                    e.target.style.transform = 'translateX(0)';\r\n                  }}\r\n                >\r\n                  <FaSignOutAlt />\r\n                  <span>Déconnexion</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <Link\r\n            to=\"/login\"\r\n            style={navStyles.menuItem}\r\n            onMouseEnter={(e) => {\r\n              e.target.style.backgroundColor = 'rgba(255,255,255,0.1)';\r\n              e.target.style.transform = 'translateY(-2px)';\r\n              e.target.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';\r\n            }}\r\n            onMouseLeave={(e) => {\r\n              e.target.style.backgroundColor = 'transparent';\r\n              e.target.style.transform = 'translateY(0)';\r\n              e.target.style.boxShadow = 'none';\r\n            }}\r\n          >\r\n            <FaSignInAlt />\r\n            <span>Connexion</span>\r\n          </Link>\r\n        )}\r\n      </div>\r\n\r\n      {/* Styles CSS pour les animations */}\r\n      <style jsx>{`\r\n        @keyframes pulse {\r\n          0% { transform: scale(1); }\r\n          50% { transform: scale(1.05); }\r\n          100% { transform: scale(1); }\r\n        }\r\n      `}</style>\r\n    </nav>\r\n  );\r\n};\r\n\r\nexport default NavbarTop;\r\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SACEC,YAAY,EACZC,WAAW,EACXC,YAAY,EACZC,eAAe,EACfC,KAAK,EACLC,aAAa,EACbC,OAAO,QACF,gBAAgB;AACvB,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,kBAAkB,MAAM,sBAAsB;AAErD,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGf,UAAU,CAACU,WAAW,CAAC;EACjE,MAAM,CAACM,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMiB,YAAY,GAAGA,CAAA,KAAM;IACzBH,MAAM,CAAC,CAAC;IACRE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAME,SAAS,GAAG;IAChBC,GAAG,EAAE;MACHC,eAAe,EAAE,iBAAiB;MAClCC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,eAAe;MAC/BC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,MAAM;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,4BAA4B;MACvCC,MAAM,EAAE,GAAG;MACXC,UAAU,EAAE,eAAe;MAC3BC,cAAc,EAAE;IAClB,CAAC;IACDC,YAAY,EAAE;MACZZ,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,GAAG,EAAE;IACP,CAAC;IACDC,SAAS,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE,kBAAkB;MACzBC,SAAS,EAAE;IACb,CAAC;IACDC,SAAS,EAAE;MACTF,KAAK,EAAE,wBAAwB;MAC/BD,QAAQ,EAAE,QAAQ;MAClBI,UAAU,EAAE,MAAM;MAClBC,UAAU,EAAE;IACd,CAAC;IACDC,QAAQ,EAAE;MACRL,KAAK,EAAE,wBAAwB;MAC/BD,QAAQ,EAAE,MAAM;MAChBhB,OAAO,EAAE,UAAU;MACnBD,eAAe,EAAE,uBAAuB;MACxCwB,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,iCAAiC;MACzCZ,cAAc,EAAE;IAClB,CAAC;IACDa,WAAW,EAAE;MACXxB,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,GAAG,EAAE;IACP,CAAC;IACDY,QAAQ,EAAE;MACRT,KAAK,EAAE,wBAAwB;MAC/BU,cAAc,EAAE,MAAM;MACtB3B,OAAO,EAAE,WAAW;MACpBuB,YAAY,EAAE,MAAM;MACpBP,QAAQ,EAAE,MAAM;MAChBI,UAAU,EAAE,KAAK;MACjBnB,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,GAAG,EAAE,KAAK;MACVH,UAAU,EAAE,eAAe;MAC3BP,QAAQ,EAAE,UAAU;MACpBwB,QAAQ,EAAE;IACZ,CAAC;IACDC,iBAAiB,EAAE;MACjBzB,QAAQ,EAAE;IACZ,CAAC;IACD0B,cAAc,EAAE;MACdC,UAAU,EAAE,MAAM;MAClBP,MAAM,EAAE,MAAM;MACdP,KAAK,EAAE,wBAAwB;MAC/BjB,OAAO,EAAE,WAAW;MACpBuB,YAAY,EAAE,MAAM;MACpBP,QAAQ,EAAE,MAAM;MAChBI,UAAU,EAAE,KAAK;MACjBnB,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,GAAG,EAAE,KAAK;MACVkB,MAAM,EAAE,SAAS;MACjBrB,UAAU,EAAE;IACd,CAAC;IACDsB,YAAY,EAAE;MACZ7B,QAAQ,EAAE,UAAU;MACpBC,GAAG,EAAE,MAAM;MACXE,KAAK,EAAE,CAAC;MACR2B,SAAS,EAAE,MAAM;MACjBnC,eAAe,EAAE,wBAAwB;MACzCa,cAAc,EAAE,YAAY;MAC5BW,YAAY,EAAE,MAAM;MACpBd,SAAS,EAAE,6BAA6B;MACxCe,MAAM,EAAE,iCAAiC;MACzCW,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE1C,YAAY,GAAG,CAAC,GAAG,CAAC;MAC7B2C,SAAS,EAAE3C,YAAY,GAAG,wBAAwB,GAAG,+BAA+B;MACpFiB,UAAU,EAAE,eAAe;MAC3B2B,aAAa,EAAE5C,YAAY,GAAG,MAAM,GAAG,MAAM;MAC7CgB,MAAM,EAAE;IACV,CAAC;IACD6B,YAAY,EAAE;MACZtC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBW,GAAG,EAAE,MAAM;MACXd,OAAO,EAAE,WAAW;MACpBiB,KAAK,EAAE,MAAM;MACbU,cAAc,EAAE,MAAM;MACtBX,QAAQ,EAAE,MAAM;MAChBL,UAAU,EAAE,eAAe;MAC3BY,YAAY,EAAE,KAAK;MACnBiB,MAAM,EAAE;IACV;EACF,CAAC;EAED,oBACE/D,KAAA,CAAAgE,aAAA;IAAKC,KAAK,EAAE7C,SAAS,CAACC,GAAI;IAAA6C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE1BvE,KAAA,CAAAgE,aAAA,CAAC7D,IAAI;IAACqE,EAAE,EAAC,GAAG;IAACP,KAAK,EAAE;MAAEf,cAAc,EAAE;IAAO,CAAE;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjDvE,KAAA,CAAAgE,aAAA;IAAKC,KAAK,EAAE7C,SAAS,CAACgB,YAAa;IAAA8B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCvE,KAAA,CAAAgE,aAAA,CAACzD,eAAe;IAAC0D,KAAK,EAAE7C,SAAS,CAACkB,SAAU;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC/CvE,KAAA,CAAAgE,aAAA;IAAMC,KAAK,EAAE7C,SAAS,CAACsB,SAAU;IAAAwB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kBAAmB,CAClD,CACD,CAAC,EAGAzD,eAAe,iBACdd,KAAA,CAAAgE,aAAA;IAAKC,KAAK,EAAE7C,SAAS,CAACyB,QAAS;IAAAqB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2BACR,eAAAvE,KAAA,CAAAgE,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAS,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0D,IAAI,KAAI,aAAsB,CAAC,EACnE,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,KAAK,kBAAI1E,KAAA,CAAAgE,aAAA;IAAMC,KAAK,EAAE;MAAEU,UAAU,EAAE,MAAM;MAAEhB,OAAO,EAAE;IAAI,CAAE;IAAAO,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GAAC,EAACxD,IAAI,CAAC2D,KAAK,EAAC,GAAO,CACpF,CACN,eAGD1E,KAAA,CAAAgE,aAAA;IAAKC,KAAK,EAAE7C,SAAS,CAAC4B,WAAY;IAAAkB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BzD,eAAe,gBACdd,KAAA,CAAAgE,aAAA,CAAAhE,KAAA,CAAA4E,QAAA,qBAEE5E,KAAA,CAAAgE,aAAA,CAACpD,kBAAkB;IAAAsD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAGtBvE,KAAA,CAAAgE,aAAA;IAAKC,KAAK,EAAE7C,SAAS,CAACgC,iBAAkB;IAAAc,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtCvE,KAAA,CAAAgE,aAAA;IACEC,KAAK,EAAE7C,SAAS,CAACiC,cAAe;IAChCwB,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC,CAACD,YAAY,CAAE;IAC9C6D,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,uBAAuB;MACxDyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,kBAAkB;IAC/C,CAAE;IACFqB,YAAY,EAAGF,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,aAAa;MAC9CyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,eAAe;IAC5C,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFvE,KAAA,CAAAgE,aAAA,CAAC5D,YAAY;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAChBvE,KAAA,CAAAgE,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,YAAgB,CAAC,eACvBvE,KAAA,CAAAgE,aAAA,CAACvD,aAAa;IAACwD,KAAK,EAAE;MACpBL,SAAS,EAAE3C,YAAY,GAAG,gBAAgB,GAAG,cAAc;MAC3DiB,UAAU,EAAE;IACd,CAAE;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACE,CAAC,eAGTvE,KAAA,CAAAgE,aAAA;IAAKC,KAAK,EAAE7C,SAAS,CAACoC,YAAa;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCvE,KAAA,CAAAgE,aAAA,CAAC7D,IAAI;IACHqE,EAAE,EAAC,SAAS;IACZP,KAAK,EAAE7C,SAAS,CAAC0C,YAAa;IAC9Be,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC,KAAK,CAAE;IACtC4D,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,0BAA0B;MAC3DyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,iBAAiB;IAC9C,CAAE;IACFqB,YAAY,EAAGF,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,aAAa;MAC9CyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,eAAe;IAC5C,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFvE,KAAA,CAAAgE,aAAA,CAAC5D,YAAY;IAAA8D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAChBvE,KAAA,CAAAgE,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,YAAgB,CAClB,CAAC,eAEPvE,KAAA,CAAAgE,aAAA,CAAC7D,IAAI;IACHqE,EAAE,EAAC,WAAW;IACdP,KAAK,EAAE7C,SAAS,CAAC0C,YAAa;IAC9Be,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC,KAAK,CAAE;IACtC4D,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,0BAA0B;MAC3DyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,iBAAiB;IAC9C,CAAE;IACFqB,YAAY,EAAGF,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,aAAa;MAC9CyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,eAAe;IAC5C,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFvE,KAAA,CAAAgE,aAAA,CAACxD,KAAK;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACTvE,KAAA,CAAAgE,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,eAAgB,CAClB,CAAC,eAEPvE,KAAA,CAAAgE,aAAA;IAAIC,KAAK,EAAE;MAAEF,MAAM,EAAE,OAAO;MAAEhB,MAAM,EAAE,MAAM;MAAEmC,SAAS,EAAE;IAA4B,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAE1FvE,KAAA,CAAAgE,aAAA;IACEa,OAAO,EAAE1D,YAAa;IACtB8C,KAAK,EAAE;MACL,GAAG7C,SAAS,CAAC0C,YAAY;MACzBR,UAAU,EAAE,MAAM;MAClBP,MAAM,EAAE,MAAM;MACdQ,MAAM,EAAE,SAAS;MACjB4B,KAAK,EAAE,MAAM;MACbC,SAAS,EAAE,MAAM;MACjB5C,KAAK,EAAE;IACT,CAAE;IACFsC,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,wBAAwB;MACzDyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,iBAAiB;IAC9C,CAAE;IACFqB,YAAY,EAAGF,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,aAAa;MAC9CyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,eAAe;IAC5C,CAAE;IAAAM,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFvE,KAAA,CAAAgE,aAAA,CAAC1D,YAAY;IAAA4D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAChBvE,KAAA,CAAAgE,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,gBAAiB,CACjB,CACL,CACF,CACL,CAAC,gBAEHvE,KAAA,CAAAgE,aAAA,CAAC7D,IAAI;IACHqE,EAAE,EAAC,QAAQ;IACXP,KAAK,EAAE7C,SAAS,CAAC6B,QAAS;IAC1B6B,YAAY,EAAGC,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,uBAAuB;MACxDyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,kBAAkB;MAC7CmB,CAAC,CAACC,MAAM,CAACf,KAAK,CAACjC,SAAS,GAAG,4BAA4B;IACzD,CAAE;IACFiD,YAAY,EAAGF,CAAC,IAAK;MACnBA,CAAC,CAACC,MAAM,CAACf,KAAK,CAAC3C,eAAe,GAAG,aAAa;MAC9CyD,CAAC,CAACC,MAAM,CAACf,KAAK,CAACL,SAAS,GAAG,eAAe;MAC1CmB,CAAC,CAACC,MAAM,CAACf,KAAK,CAACjC,SAAS,GAAG,MAAM;IACnC,CAAE;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFvE,KAAA,CAAAgE,aAAA,CAAC3D,WAAW;IAAA6D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACfvE,KAAA,CAAAgE,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,WAAe,CACjB,CAEL,CAAC,eAGNvE,KAAA,CAAAgE,aAAA;IAAOqB,GAAG;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE;AAClB;AACA;AACA;AACA;AACA;AACA,OAAe,CACN,CAAC;AAEV,CAAC;AAED,eAAe1D,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}