<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Test Authentification - Système de Messagerie</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-section h2 {
            color: #667eea;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status {
            padding: 10px 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .info-card h3 {
            margin: 0 0 15px 0;
            color: #667eea;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border-left: 4px solid #667eea;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .log {
            background: #000;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Test d'Authentification - Système de Messagerie</h1>
            <p>Diagnostic complet des problèmes d'authentification</p>
        </div>
        
        <div class="test-section">
            <h2>🔐 État de l'Authentification</h2>
            <div id="auth-status"></div>
            <div class="info-grid">
                <div class="info-card">
                    <h3>📱 LocalStorage</h3>
                    <div id="localStorage-info"></div>
                </div>
                <div class="info-card">
                    <h3>🍪 SessionStorage</h3>
                    <div id="sessionStorage-info"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧪 Tests de Simulation</h2>
            <p>Simuler différents scénarios d'authentification :</p>
            <button class="btn" onclick="simulateValidUser()">✅ Simuler Utilisateur Valide</button>
            <button class="btn" onclick="simulateInvalidUser()">❌ Simuler Utilisateur Invalide</button>
            <button class="btn btn-danger" onclick="clearAllAuth()">🗑️ Effacer Toute Authentification</button>
            <button class="btn" onclick="testMessagingAPI()">📡 Tester API Messagerie</button>
        </div>
        
        <div class="test-section">
            <h2>📊 Résultats des Tests</h2>
            <div id="test-results"></div>
        </div>
        
        <div class="test-section">
            <h2>📝 Journal des Événements</h2>
            <div class="log" id="event-log"></div>
            <button class="btn" onclick="clearLog()">🧹 Effacer Journal</button>
        </div>
    </div>

    <script>
        // 📝 Système de logging
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('event-log');
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#00ff00';
            logElement.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        // 🔍 Analyser l'état d'authentification
        function analyzeAuthState() {
            log('🔍 Analyse de l\'état d\'authentification...', 'info');
            
            const user = localStorage.getItem('user');
            const token = localStorage.getItem('token');
            
            let authStatus = '';
            let localStorageInfo = '';
            let sessionStorageInfo = '';
            
            // Vérifier localStorage
            if (user && token) {
                try {
                    const userData = JSON.parse(user);
                    authStatus = `<div class="status success">✅ Authentification Valide</div>`;
                    localStorageInfo = `
                        <div class="info-item"><span>Token:</span><span>✅ Présent</span></div>
                        <div class="info-item"><span>Utilisateur:</span><span>✅ ${userData.nom || 'Nom non défini'}</span></div>
                        <div class="info-item"><span>ID:</span><span>${userData.id || 'ID non défini'}</span></div>
                        <div class="info-item"><span>Email:</span><span>${userData.email || 'Email non défini'}</span></div>
                        <div class="info-item"><span>Rôle:</span><span>${userData.role_id || 'Rôle non défini'}</span></div>
                    `;
                    log(`✅ Utilisateur trouvé: ${userData.nom} (ID: ${userData.id})`, 'success');
                } catch (error) {
                    authStatus = `<div class="status error">❌ Données Utilisateur Corrompues</div>`;
                    localStorageInfo = `<div class="info-item"><span>Erreur:</span><span>${error.message}</span></div>`;
                    log(`❌ Erreur parsing utilisateur: ${error.message}`, 'error');
                }
            } else {
                authStatus = `<div class="status error">❌ Authentification Manquante</div>`;
                localStorageInfo = `
                    <div class="info-item"><span>Token:</span><span>${token ? '✅ Présent' : '❌ Absent'}</span></div>
                    <div class="info-item"><span>Utilisateur:</span><span>${user ? '✅ Présent' : '❌ Absent'}</span></div>
                `;
                log('❌ Authentification manquante', 'error');
            }
            
            // Vérifier sessionStorage
            const sessionUser = sessionStorage.getItem('user');
            const sessionToken = sessionStorage.getItem('token');
            sessionStorageInfo = `
                <div class="info-item"><span>Token:</span><span>${sessionToken ? '✅ Présent' : '❌ Absent'}</span></div>
                <div class="info-item"><span>Utilisateur:</span><span>${sessionUser ? '✅ Présent' : '❌ Absent'}</span></div>
            `;
            
            document.getElementById('auth-status').innerHTML = authStatus;
            document.getElementById('localStorage-info').innerHTML = localStorageInfo;
            document.getElementById('sessionStorage-info').innerHTML = sessionStorageInfo;
        }

        // ✅ Simuler un utilisateur valide
        function simulateValidUser() {
            log('✅ Simulation d\'un utilisateur valide...', 'info');
            
            const mockUser = {
                id: 1,
                nom: 'Test User',
                email: '<EMAIL>',
                role_id: 1
            };
            
            const mockToken = 'mock_token_123_1';
            
            localStorage.setItem('user', JSON.stringify(mockUser));
            localStorage.setItem('token', mockToken);
            
            log('✅ Utilisateur simulé créé avec succès', 'success');
            analyzeAuthState();
        }

        // ❌ Simuler un utilisateur invalide
        function simulateInvalidUser() {
            log('❌ Simulation d\'un utilisateur invalide...', 'info');
            
            localStorage.setItem('user', 'invalid_json_data');
            localStorage.setItem('token', '');
            
            log('❌ Utilisateur invalide simulé', 'error');
            analyzeAuthState();
        }

        // 🗑️ Effacer toute authentification
        function clearAllAuth() {
            log('🗑️ Effacement de toute authentification...', 'info');
            
            localStorage.removeItem('user');
            localStorage.removeItem('token');
            sessionStorage.removeItem('user');
            sessionStorage.removeItem('token');
            
            log('🗑️ Authentification effacée', 'info');
            analyzeAuthState();
        }

        // 📡 Tester l'API de messagerie
        async function testMessagingAPI() {
            log('📡 Test de l\'API de messagerie...', 'info');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ Aucun token trouvé pour le test API', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/?action=conversations', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                log(`📡 Réponse API: ${JSON.stringify(result)}`, 'info');
                
                if (result.success) {
                    log('✅ API de messagerie accessible', 'success');
                } else {
                    log(`❌ Erreur API: ${result.error}`, 'error');
                }
                
            } catch (error) {
                log(`❌ Erreur de connexion API: ${error.message}`, 'error');
            }
        }

        // 🧹 Effacer le journal
        function clearLog() {
            document.getElementById('event-log').innerHTML = '';
            log('🧹 Journal effacé', 'info');
        }

        // 🚀 Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Initialisation du test d\'authentification', 'info');
            analyzeAuthState();
        });
    </script>
</body>
</html>
