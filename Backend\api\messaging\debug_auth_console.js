// 🔧 Script de Debug Authentification - À exécuter dans la console du navigateur
// Co<PERSON>z et collez ce code dans la console de votre navigateur (F12) pour diagnostiquer les problèmes d'authentification

console.log('🔧 === SCRIPT DE DEBUG AUTHENTIFICATION ===');

// 🔍 Fonction de diagnostic complète
function debugAuthentication() {
    console.log('\n🔍 1. ANALYSE DU CONTEXTE REACT');
    
    // Essayer de récupérer le contexte React (si accessible)
    try {
        const reactFiber = document.querySelector('[data-reactroot]')?._reactInternalFiber;
        if (reactFiber) {
            console.log('✅ React détecté');
        } else {
            console.log('⚠️ React non détecté ou version différente');
        }
    } catch (e) {
        console.log('⚠️ Impossible d\'accéder au contexte React:', e.message);
    }
    
    console.log('\n📦 2. ANALYSE DU LOCALSTORAGE');
    
    // Analyser localStorage
    const user = localStorage.getItem('user');
    const token = localStorage.getItem('token');
    
    console.log('Token brut:', token);
    console.log('User brut:', user);
    
    if (user) {
        try {
            const userData = JSON.parse(user);
            console.log('✅ Données utilisateur parsées:', userData);
            console.log('📊 Propriétés disponibles:', Object.keys(userData));
            
            // Chercher les IDs possibles
            const possibleIds = ['id', 'user_id', 'utilisateur_id', 'ID', 'User_ID', 'userId'];
            console.log('\n🔍 Recherche d\'ID utilisateur:');
            
            possibleIds.forEach(prop => {
                if (userData.hasOwnProperty(prop)) {
                    console.log(`   - ${prop}: ${userData[prop]} (type: ${typeof userData[prop]})`);
                }
            });
            
            // Identifier l'ID le plus probable
            const foundId = possibleIds.find(prop => userData[prop] && !isNaN(userData[prop]));
            if (foundId) {
                console.log(`✅ ID utilisateur trouvé: ${userData[foundId]} (propriété: ${foundId})`);
            } else {
                console.log('❌ Aucun ID utilisateur valide trouvé');
            }
            
        } catch (e) {
            console.log('❌ Erreur parsing user:', e.message);
        }
    } else {
        console.log('❌ Aucune donnée utilisateur dans localStorage');
    }
    
    console.log('\n🔑 3. ANALYSE DU TOKEN');
    
    if (token) {
        // Analyser le format du token
        if (token.includes('_')) {
            const parts = token.split('_');
            console.log('📝 Token format underscore:', parts);
            const lastPart = parts[parts.length - 1];
            if (!isNaN(lastPart)) {
                console.log(`✅ ID potentiel dans token: ${lastPart}`);
            }
        }
        
        if (token.includes('.')) {
            console.log('🔐 Token format JWT détecté');
            try {
                const parts = token.split('.');
                if (parts.length === 3) {
                    const payload = JSON.parse(atob(parts[1]));
                    console.log('📊 Payload JWT:', payload);
                    
                    const jwtIds = ['id', 'user_id', 'sub', 'userId'];
                    jwtIds.forEach(prop => {
                        if (payload[prop]) {
                            console.log(`   - ${prop}: ${payload[prop]}`);
                        }
                    });
                }
            } catch (e) {
                console.log('❌ Erreur décodage JWT:', e.message);
            }
        }
    } else {
        console.log('❌ Aucun token trouvé');
    }
    
    console.log('\n🗂️ 4. TOUTES LES CLÉS LOCALSTORAGE');
    
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        const value = localStorage.getItem(key);
        console.log(`   - ${key}: ${value.length > 100 ? value.substring(0, 100) + '...' : value}`);
    }
    
    console.log('\n🔧 5. SUGGESTIONS DE CORRECTION');
    
    if (user) {
        try {
            const userData = JSON.parse(user);
            const possibleIds = ['id', 'user_id', 'utilisateur_id', 'ID', 'User_ID'];
            const foundId = possibleIds.find(prop => userData[prop] && !isNaN(userData[prop]));
            
            if (foundId && foundId !== 'id') {
                console.log(`💡 Suggestion: Standardiser l'ID sur la propriété 'id'`);
                console.log(`   Exécuter: fixUserIdProperty('${foundId}')`);
            } else if (foundId === 'id') {
                console.log('✅ La propriété id est correctement définie');
            } else {
                console.log('❌ Aucun ID valide trouvé - reconnexion nécessaire');
            }
        } catch (e) {
            console.log('❌ Données utilisateur corrompues - reconnexion nécessaire');
        }
    }
    
    console.log('\n🔧 === FIN DU DIAGNOSTIC ===');
}

// 🔧 Fonction pour corriger la propriété ID
function fixUserIdProperty(sourceProperty) {
    try {
        const user = localStorage.getItem('user');
        if (user) {
            const userData = JSON.parse(user);
            if (userData[sourceProperty]) {
                userData.id = parseInt(userData[sourceProperty]);
                localStorage.setItem('user', JSON.stringify(userData));
                console.log(`✅ Propriété 'id' mise à jour avec la valeur de '${sourceProperty}': ${userData.id}`);
                console.log('🔄 Rechargez la page pour appliquer les changements');
                return true;
            }
        }
        return false;
    } catch (e) {
        console.log('❌ Erreur lors de la correction:', e.message);
        return false;
    }
}

// 🧹 Fonction pour nettoyer l'authentification
function clearAuthentication() {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    sessionStorage.removeItem('user');
    sessionStorage.removeItem('token');
    console.log('🧹 Authentification nettoyée');
}

// 🎯 Fonction pour créer un utilisateur de test
function createTestUser() {
    const testUser = {
        id: 1,
        nom: 'Utilisateur Test',
        email: '<EMAIL>',
        role_id: 1
    };
    
    const testToken = 'test_token_123_1';
    
    localStorage.setItem('user', JSON.stringify(testUser));
    localStorage.setItem('token', testToken);
    
    console.log('✅ Utilisateur de test créé:', testUser);
    console.log('🔄 Rechargez la page pour tester');
}

// 🚀 Exécution automatique
debugAuthentication();

// 📋 Afficher les fonctions disponibles
console.log('\n📋 FONCTIONS DISPONIBLES:');
console.log('   - debugAuthentication() : Relancer le diagnostic');
console.log('   - fixUserIdProperty(sourceProperty) : Corriger la propriété ID');
console.log('   - clearAuthentication() : Nettoyer l\'authentification');
console.log('   - createTestUser() : Créer un utilisateur de test');

console.log('\n💡 EXEMPLE D\'UTILISATION:');
console.log('   fixUserIdProperty("user_id") // Si l\'ID est dans user_id');
console.log('   fixUserIdProperty("utilisateur_id") // Si l\'ID est dans utilisateur_id');
