{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\Etudiants.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\nconst Etudiants = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [etudiants, setEtudiants] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingEtudiant, setEditingEtudiant] = useState(null);\n  const [utilisateursEtudiants, setUtilisateursEtudiants] = useState([]);\n  const [groupes, setGroupes] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [formData, setFormData] = useState({\n    utilisateur_id: '',\n    groupe_id: ''\n  });\n\n  // Vérifier si l'utilisateur est Admin\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n  useEffect(() => {\n    fetchEtudiants();\n    if (isAdmin) {\n      fetchUtilisateursEtudiants();\n      fetchGroupes();\n    }\n  }, [isAdmin]);\n  const fetchEtudiants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🔄 Chargement des étudiants...');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('✅ Réponse API étudiants:', response.data);\n      if (response.data.success) {\n        setEtudiants(Array.isArray(response.data.etudiants) ? response.data.etudiants : []);\n      } else {\n        setEtudiants(Array.isArray(response.data) ? response.data : []);\n      }\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des étudiants:', error);\n      Swal.fire('Erreur', 'Impossible de charger les étudiants', 'error');\n      setEtudiants([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchUtilisateursEtudiants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🔄 Chargement des utilisateurs étudiants...');\n\n      // Récupérer tous les utilisateurs\n      const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=etudiant', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Récupérer les étudiants existants pour les exclure\n      const responseEtudiants = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Filtrer pour ne garder que les utilisateurs avec le rôle \"etudiant\"\n      const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n      const etudiantsExistants = Array.isArray(responseEtudiants.data) ? responseEtudiants.data : [];\n      const utilisateursEtudiantsFiltered = utilisateurs.filter(user => {\n        const roleNom = (user.role_nom || user.role || '').toLowerCase();\n        return roleNom === 'etudiant' || roleNom === 'étudiant';\n      });\n\n      // Exclure les utilisateurs déjà étudiants\n      const etudiantsExistantsIds = etudiantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n      const utilisateursDisponibles = utilisateursEtudiantsFiltered.filter(user => !etudiantsExistantsIds.includes(user.id));\n      console.log('✅ Utilisateurs étudiants disponibles:', utilisateursDisponibles.length);\n      setUtilisateursEtudiants(utilisateursDisponibles);\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des utilisateurs étudiants:', error);\n      setUtilisateursEtudiants([]);\n    }\n  };\n  const fetchGroupes = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/groupes/groupe.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setGroupes(Array.isArray(response.data) ? response.data : []);\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des groupes:', error);\n      setGroupes([]);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des étudiants', 'error');\n      return;\n    }\n    if (!formData.utilisateur_id) {\n      Swal.fire('Erreur', 'Veuillez sélectionner un utilisateur', 'error');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php';\n      const method = editingEtudiant ? 'PUT' : 'POST';\n      const data = editingEtudiant ? {\n        ...formData,\n        id: editingEtudiant.id\n      } : formData;\n      console.log('🔄 Envoi requête étudiant:', {\n        method,\n        data\n      });\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if (response.data.success) {\n        Swal.fire('Succès', `Étudiant ${editingEtudiant ? 'modifié' : 'créé'} avec succès`, 'success');\n        setShowModal(false);\n        setEditingEtudiant(null);\n        resetForm();\n        fetchEtudiants();\n        fetchUtilisateursEtudiants(); // Recharger pour exclure l'utilisateur ajouté\n      } else {\n        throw new Error(response.data.error || 'Erreur inconnue');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Erreur étudiant:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message || 'Une erreur est survenue';\n      Swal.fire('Erreur', errorMessage, 'error');\n    }\n  };\n  const handleEdit = async etudiant => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des étudiants', 'error');\n      return;\n    }\n\n    // Recharger les utilisateurs étudiants en incluant l'utilisateur actuel\n    await fetchUtilisateursEtudiantsAvecActuel(etudiant.utilisateur_id);\n    setEditingEtudiant(etudiant);\n    setFormData({\n      utilisateur_id: etudiant.utilisateur_id || '',\n      groupe_id: etudiant.groupe_id || ''\n    });\n    setShowModal(true);\n  };\n  const fetchUtilisateursEtudiantsAvecActuel = async currentUserId => {\n    try {\n      const token = localStorage.getItem('token');\n\n      // Récupérer tous les utilisateurs\n      const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n\n      // Récupérer les étudiants existants\n      const responseEtudiants = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n      const etudiantsExistants = Array.isArray(responseEtudiants.data) ? responseEtudiants.data : [];\n      const utilisateursEtudiantsFiltered = utilisateurs.filter(user => {\n        const roleNom = (user.role_nom || user.role || '').toLowerCase();\n        return roleNom === 'etudiant' || roleNom === 'étudiant';\n      });\n\n      // Exclure les utilisateurs déjà étudiants SAUF l'utilisateur actuel\n      const etudiantsExistantsIds = etudiantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n      const utilisateursDisponibles = utilisateursEtudiantsFiltered.filter(user => !etudiantsExistantsIds.includes(user.id) || user.id === currentUserId);\n      setUtilisateursEtudiants(utilisateursDisponibles);\n    } catch (error) {\n      console.error('❌ Erreur lors du chargement des utilisateurs étudiants avec actuel:', error);\n    }\n  };\n  const handleDelete = async id => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des étudiants', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action est irréversible!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        const token = localStorage.getItem('token');\n        const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        if (response.data.success) {\n          Swal.fire('Supprimé!', 'L\\'étudiant a été supprimé.', 'success');\n          fetchEtudiants();\n          fetchUtilisateursEtudiants(); // Recharger pour rendre l'utilisateur disponible\n        } else {\n          throw new Error(response.data.error || 'Erreur lors de la suppression');\n        }\n      } catch (error) {\n        var _error$response2, _error$response2$data;\n        console.error('❌ Erreur suppression:', error);\n        const errorMessage = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || 'Impossible de supprimer l\\'étudiant';\n        Swal.fire('Erreur', errorMessage, 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      utilisateur_id: '',\n      groupe_id: ''\n    });\n  };\n  const getStatutBadge = statut => {\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge badge-success\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 16\n      }\n    }, \"Actif\");\n  };\n\n  // Styles inline pour les badges et éléments spécifiques\n  const styles = {\n    idBadge: {\n      padding: '4px 8px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '4px',\n      fontSize: '0.9em',\n      fontWeight: 'bold'\n    },\n    groupeBadge: {\n      padding: '4px 8px',\n      backgroundColor: '#d4edda',\n      borderRadius: '4px',\n      fontSize: '0.8em',\n      color: '#155724'\n    },\n    infoMessage: {\n      padding: '15px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '8px',\n      marginBottom: '20px',\n      border: '1px solid #bbdefb',\n      color: '#1976d2'\n    }\n  };\n\n  // Filtrage des données\n  const filteredEtudiants = etudiants.filter(etudiant => {\n    const searchLower = searchTerm.toLowerCase();\n    return etudiant.nom && etudiant.nom.toLowerCase().includes(searchLower) || etudiant.email && etudiant.email.toLowerCase().includes(searchLower) || etudiant.groupe_nom && etudiant.groupe_nom.toLowerCase().includes(searchLower);\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentEtudiants = filteredEtudiants.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredEtudiants.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 17\n      }\n    }, \"Chargement des \\xE9tudiants...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 17\n    }\n  }, \"\\uD83C\\uDF93 Gestion des \\xC9tudiants\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 21\n    }\n  }, filteredEtudiants.length, \" \\xE9tudiant(s) trouv\\xE9(s)\"), isAdmin && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 29\n    }\n  }), \" Nouvel \\xC9tudiant\"))), !isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    style: styles.infoMessage,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 21\n    }\n  }, \"\\u2139\\uFE0F Vous consultez les \\xE9tudiants en mode lecture seule. Seul l'administrateur peut cr\\xE9er, modifier ou supprimer des \\xE9tudiants.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-section\",\n    style: {\n      marginBottom: '20px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher un \\xE9tudiant (nom, email, groupe)...\",\n    value: searchTerm,\n    onChange: e => {\n      setSearchTerm(e.target.value);\n      setCurrentPage(1);\n    },\n    className: \"search-input\",\n    style: {\n      width: '100%',\n      padding: '12px',\n      border: '1px solid #ddd',\n      borderRadius: '8px',\n      fontSize: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 13\n    }\n  }, filteredEtudiants.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/student.png\",\n    alt: \"Aucun \\xE9tudiant\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 25\n    }\n  }, \"Aucun \\xE9tudiant trouv\\xE9\"), searchTerm && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setSearchTerm(''),\n    className: \"btn btn-secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 29\n    }\n  }, \"Effacer la recherche\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDD94 ID\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC64 Nom de l'\\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE7 Email\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC65 Groupe\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCCA Statut\"), isAdmin && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 49\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 29\n    }\n  }, currentEtudiants.map(etudiant => /*#__PURE__*/React.createElement(\"tr\", {\n    key: etudiant.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.idBadge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 45\n    }\n  }, \"#\", etudiant.id)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"student-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 49\n    }\n  }, etudiant.nom || 'Nom non disponible'), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 49\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 49\n    }\n  }, \"ID Utilisateur: \", etudiant.utilisateur_id))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 41\n    }\n  }, etudiant.email || 'Email non disponible'), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: styles.groupeBadge,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 45\n    }\n  }, etudiant.groupe_nom || 'Aucun groupe')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 41\n    }\n  }, getStatutBadge('Actif')), isAdmin && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 412,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(etudiant),\n    title: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 57\n    }\n  })), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(etudiant.id),\n    title: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 57\n    }\n  })))))))))), totalPages > 1 && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      marginTop: '20px',\n      gap: '10px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    onClick: () => paginate(currentPage - 1),\n    disabled: currentPage === 1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 21\n    }\n  }, \"\\u2B05\\uFE0F Pr\\xE9c\\xE9dent\"), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '8px 16px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 21\n    }\n  }, \"Page \", currentPage, \" sur \", totalPages), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-secondary\",\n    onClick: () => paginate(currentPage + 1),\n    disabled: currentPage === totalPages,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 21\n    }\n  }, \"Suivant \\u27A1\\uFE0F\")), showModal && isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 29\n    }\n  }, editingEtudiant ? 'Modifier l\\'étudiant' : 'Nouvel étudiant'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingEtudiant(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 492,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 493,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 33\n    }\n  }, \"Utilisateur (\\xC9tudiant) *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.utilisateur_id,\n    onChange: e => setFormData({\n      ...formData,\n      utilisateur_id: e.target.value\n    }),\n    required: true,\n    disabled: editingEtudiant // Empêcher la modification de l'utilisateur lors de l'édition\n    ,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur \\xE9tudiant...\"), utilisateursEtudiants.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 503,\n      columnNumber: 41\n    }\n  }, user.nom, \" - \", user.email, \" (ID: \", user.id, \")\")), editingEtudiant && editingEtudiant.utilisateur_id && /*#__PURE__*/React.createElement(\"option\", {\n    value: editingEtudiant.utilisateur_id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 41\n    }\n  }, editingEtudiant.nom, \" - \", editingEtudiant.email, \" (ID: \", editingEtudiant.utilisateur_id, \")\")), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 33\n    }\n  }, \"Seuls les utilisateurs avec le r\\xF4le \\\"etudiant\\\" non encore assign\\xE9s sont affich\\xE9s\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 519,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 33\n    }\n  }, \"Groupe (Optionnel)\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.groupe_id,\n    onChange: e => setFormData({\n      ...formData,\n      groupe_id: e.target.value\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 521,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 37\n    }\n  }, \"Aucun groupe\"), groupes.map(groupe => /*#__PURE__*/React.createElement(\"option\", {\n    key: groupe.id,\n    value: groupe.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 41\n    }\n  }, groupe.nom))), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '12px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 33\n    }\n  }, \"L'\\xE9tudiant peut \\xEAtre assign\\xE9 \\xE0 un groupe ou rester sans groupe\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingEtudiant(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 33\n    }\n  }, \"Annuler\"), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 33\n    }\n  }, editingEtudiant ? 'Modifier' : 'Créer'))))));\n};\nexport default Etudiants;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "Etudiants", "user", "etudiants", "setEtudiants", "loading", "setLoading", "showModal", "setShowModal", "editingEtudiant", "setEditingEtudiant", "utilisateursEtudiants", "setUtilisateursEtudiants", "groupes", "setGroupes", "searchTerm", "setSearchTerm", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "utilisateur_id", "groupe_id", "isAdmin", "role", "fetchEtudiants", "fetchUtilisateursEtudiants", "fetchGroupes", "token", "localStorage", "getItem", "console", "log", "response", "get", "headers", "Authorization", "data", "success", "Array", "isArray", "error", "fire", "responseUtilisateurs", "responseEtudiants", "utilisateurs", "etudiantsExistants", "utilisateursEtudiantsFiltered", "filter", "roleNom", "role_nom", "toLowerCase", "etudiantsExistantsIds", "map", "e", "id", "utilisateursDisponibles", "includes", "length", "handleSubmit", "preventDefault", "url", "method", "resetForm", "Error", "_error$response", "_error$response$data", "errorMessage", "message", "handleEdit", "etudiant", "fetchUtilisateursEtudiantsAvecActuel", "currentUserId", "handleDelete", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "delete", "_error$response2", "_error$response2$data", "getStatutBadge", "statut", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "styles", "idBadge", "padding", "backgroundColor", "borderRadius", "fontSize", "fontWeight", "groupeBadge", "color", "infoMessage", "marginBottom", "border", "filteredEtudiants", "searchLower", "nom", "email", "groupe_nom", "indexOfLastItem", "indexOfFirstItem", "currentEtudiants", "slice", "totalPages", "Math", "ceil", "paginate", "pageNumber", "onClick", "src", "alt", "style", "margin", "type", "placeholder", "value", "onChange", "target", "width", "key", "display", "justifyContent", "alignItems", "marginTop", "gap", "disabled", "onSubmit", "required", "groupe"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/Etudiants.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\n\nconst Etudiants = () => {\n    const { user } = useContext(AuthContext);\n    const [etudiants, setEtudiants] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingEtudiant, setEditingEtudiant] = useState(null);\n    const [utilisateursEtudiants, setUtilisateursEtudiants] = useState([]);\n    const [groupes, setGroupes] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [formData, setFormData] = useState({\n        utilisateur_id: '',\n        groupe_id: ''\n    });\n\n    // Vérifier si l'utilisateur est Admin\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';\n\n    useEffect(() => {\n        fetchEtudiants();\n        if (isAdmin) {\n            fetchUtilisateursEtudiants();\n            fetchGroupes();\n        }\n    }, [isAdmin]);\n\n    const fetchEtudiants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            console.log('🔄 Chargement des étudiants...');\n\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('✅ Réponse API étudiants:', response.data);\n\n            if (response.data.success) {\n                setEtudiants(Array.isArray(response.data.etudiants) ? response.data.etudiants : []);\n            } else {\n                setEtudiants(Array.isArray(response.data) ? response.data : []);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des étudiants:', error);\n            Swal.fire('Erreur', 'Impossible de charger les étudiants', 'error');\n            setEtudiants([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchUtilisateursEtudiants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            console.log('🔄 Chargement des utilisateurs étudiants...');\n\n            // Récupérer tous les utilisateurs\n            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php?role=etudiant', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            // Récupérer les étudiants existants pour les exclure\n            const responseEtudiants = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            // Filtrer pour ne garder que les utilisateurs avec le rôle \"etudiant\"\n            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n            const etudiantsExistants = Array.isArray(responseEtudiants.data) ? responseEtudiants.data : [];\n            \n            const utilisateursEtudiantsFiltered = utilisateurs.filter(user => {\n                const roleNom = (user.role_nom || user.role || '').toLowerCase();\n                return roleNom === 'etudiant' || roleNom === 'étudiant';\n            });\n\n            // Exclure les utilisateurs déjà étudiants\n            const etudiantsExistantsIds = etudiantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n            const utilisateursDisponibles = utilisateursEtudiantsFiltered.filter(user => \n                !etudiantsExistantsIds.includes(user.id)\n            );\n\n            console.log('✅ Utilisateurs étudiants disponibles:', utilisateursDisponibles.length);\n            setUtilisateursEtudiants(utilisateursDisponibles);\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des utilisateurs étudiants:', error);\n            setUtilisateursEtudiants([]);\n        }\n    };\n\n    const fetchGroupes = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/groupes/groupe.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            setGroupes(Array.isArray(response.data) ? response.data : []);\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des groupes:', error);\n            setGroupes([]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des étudiants', 'error');\n            return;\n        }\n\n        if (!formData.utilisateur_id) {\n            Swal.fire('Erreur', 'Veuillez sélectionner un utilisateur', 'error');\n            return;\n        }\n\n        try {\n            const token = localStorage.getItem('token');\n            const url = 'http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php';\n            const method = editingEtudiant ? 'PUT' : 'POST';\n            const data = editingEtudiant ? { ...formData, id: editingEtudiant.id } : formData;\n\n            console.log('🔄 Envoi requête étudiant:', { method, data });\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: {\n                    Authorization: `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            if (response.data.success) {\n                Swal.fire('Succès', `Étudiant ${editingEtudiant ? 'modifié' : 'créé'} avec succès`, 'success');\n                setShowModal(false);\n                setEditingEtudiant(null);\n                resetForm();\n                fetchEtudiants();\n                fetchUtilisateursEtudiants(); // Recharger pour exclure l'utilisateur ajouté\n            } else {\n                throw new Error(response.data.error || 'Erreur inconnue');\n            }\n        } catch (error) {\n            console.error('❌ Erreur étudiant:', error);\n            const errorMessage = error.response?.data?.error || error.message || 'Une erreur est survenue';\n            Swal.fire('Erreur', errorMessage, 'error');\n        }\n    };\n\n    const handleEdit = async (etudiant) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des étudiants', 'error');\n            return;\n        }\n\n        // Recharger les utilisateurs étudiants en incluant l'utilisateur actuel\n        await fetchUtilisateursEtudiantsAvecActuel(etudiant.utilisateur_id);\n\n        setEditingEtudiant(etudiant);\n        setFormData({\n            utilisateur_id: etudiant.utilisateur_id || '',\n            groupe_id: etudiant.groupe_id || ''\n        });\n        setShowModal(true);\n    };\n\n    const fetchUtilisateursEtudiantsAvecActuel = async (currentUserId) => {\n        try {\n            const token = localStorage.getItem('token');\n\n            // Récupérer tous les utilisateurs\n            const responseUtilisateurs = await axios.get('http://localhost/Project_PFE/Backend/pages/utilisateurs/utilisateur.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            // Récupérer les étudiants existants\n            const responseEtudiants = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            const utilisateurs = Array.isArray(responseUtilisateurs.data) ? responseUtilisateurs.data : [];\n            const etudiantsExistants = Array.isArray(responseEtudiants.data) ? responseEtudiants.data : [];\n\n            const utilisateursEtudiantsFiltered = utilisateurs.filter(user => {\n                const roleNom = (user.role_nom || user.role || '').toLowerCase();\n                return roleNom === 'etudiant' || roleNom === 'étudiant';\n            });\n\n            // Exclure les utilisateurs déjà étudiants SAUF l'utilisateur actuel\n            const etudiantsExistantsIds = etudiantsExistants.map(e => e.utilisateur_id).filter(id => id !== null);\n            const utilisateursDisponibles = utilisateursEtudiantsFiltered.filter(user =>\n                !etudiantsExistantsIds.includes(user.id) || user.id === currentUserId\n            );\n\n            setUtilisateursEtudiants(utilisateursDisponibles);\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des utilisateurs étudiants avec actuel:', error);\n        }\n    };\n\n    const handleDelete = async (id) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des étudiants', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr?',\n            text: 'Cette action est irréversible!',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer!',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                const token = localStorage.getItem('token');\n                const response = await axios.delete('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {\n                    headers: {\n                        Authorization: `Bearer ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n\n                if (response.data.success) {\n                    Swal.fire('Supprimé!', 'L\\'étudiant a été supprimé.', 'success');\n                    fetchEtudiants();\n                    fetchUtilisateursEtudiants(); // Recharger pour rendre l'utilisateur disponible\n                } else {\n                    throw new Error(response.data.error || 'Erreur lors de la suppression');\n                }\n            } catch (error) {\n                console.error('❌ Erreur suppression:', error);\n                const errorMessage = error.response?.data?.error || error.message || 'Impossible de supprimer l\\'étudiant';\n                Swal.fire('Erreur', errorMessage, 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            utilisateur_id: '',\n            groupe_id: ''\n        });\n    };\n\n    const getStatutBadge = (statut) => {\n        return <span className=\"badge badge-success\">Actif</span>;\n    };\n\n    // Styles inline pour les badges et éléments spécifiques\n    const styles = {\n        idBadge: {\n            padding: '4px 8px',\n            backgroundColor: '#e3f2fd',\n            borderRadius: '4px',\n            fontSize: '0.9em',\n            fontWeight: 'bold'\n        },\n        groupeBadge: {\n            padding: '4px 8px',\n            backgroundColor: '#d4edda',\n            borderRadius: '4px',\n            fontSize: '0.8em',\n            color: '#155724'\n        },\n        infoMessage: {\n            padding: '15px',\n            backgroundColor: '#e3f2fd',\n            borderRadius: '8px',\n            marginBottom: '20px',\n            border: '1px solid #bbdefb',\n            color: '#1976d2'\n        }\n    };\n\n    // Filtrage des données\n    const filteredEtudiants = etudiants.filter(etudiant => {\n        const searchLower = searchTerm.toLowerCase();\n        return (etudiant.nom && etudiant.nom.toLowerCase().includes(searchLower)) ||\n               (etudiant.email && etudiant.email.toLowerCase().includes(searchLower)) ||\n               (etudiant.groupe_nom && etudiant.groupe_nom.toLowerCase().includes(searchLower));\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentEtudiants = filteredEtudiants.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredEtudiants.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des étudiants...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>🎓 Gestion des Étudiants</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredEtudiants.length} étudiant(s) trouvé(s)\n                    </span>\n                    {isAdmin && (\n                        <button\n                            className=\"btn btn-primary\"\n                            onClick={() => setShowModal(true)}\n                        >\n                            <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouvel Étudiant\n                        </button>\n                    )}\n                </div>\n            </div>\n\n            {/* Message d'information pour les non-admins */}\n            {!isAdmin && (\n                <div style={styles.infoMessage}>\n                    <p style={{ margin: '0' }}>ℹ️ Vous consultez les étudiants en mode lecture seule. Seul l'administrateur peut créer, modifier ou supprimer des étudiants.</p>\n                </div>\n            )}\n\n            {/* Barre de recherche */}\n            <div className=\"search-section\" style={{ marginBottom: '20px' }}>\n                <input\n                    type=\"text\"\n                    placeholder=\"🔍 Rechercher un étudiant (nom, email, groupe)...\"\n                    value={searchTerm}\n                    onChange={(e) => {\n                        setSearchTerm(e.target.value);\n                        setCurrentPage(1);\n                    }}\n                    className=\"search-input\"\n                    style={{\n                        width: '100%',\n                        padding: '12px',\n                        border: '1px solid #ddd',\n                        borderRadius: '8px',\n                        fontSize: '16px'\n                    }}\n                />\n            </div>\n\n            <div className=\"factures-grid\">\n                {filteredEtudiants.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/student.png\" alt=\"Aucun étudiant\" />\n                        <p>Aucun étudiant trouvé</p>\n                        {searchTerm && (\n                            <button\n                                onClick={() => setSearchTerm('')}\n                                className=\"btn btn-secondary\"\n                            >\n                                Effacer la recherche\n                            </button>\n                        )}\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>🆔 ID</th>\n                                    <th>👤 Nom de l'Étudiant</th>\n                                    <th>📧 Email</th>\n                                    <th>👥 Groupe</th>\n                                    <th>📊 Statut</th>\n                                    {isAdmin && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentEtudiants.map((etudiant) => (\n                                    <tr key={etudiant.id}>\n                                        <td>\n                                            <span style={styles.idBadge}>\n                                                #{etudiant.id}\n                                            </span>\n                                        </td>\n                                        <td>\n                                            <div className=\"student-info\">\n                                                <strong>{etudiant.nom || 'Nom non disponible'}</strong>\n                                                <br />\n                                                <small>ID Utilisateur: {etudiant.utilisateur_id}</small>\n                                            </div>\n                                        </td>\n                                        <td>{etudiant.email || 'Email non disponible'}</td>\n                                        <td>\n                                            <span style={styles.groupeBadge}>\n                                                {etudiant.groupe_nom || 'Aucun groupe'}\n                                            </span>\n                                        </td>\n                                        <td>{getStatutBadge('Actif')}</td>\n                                        {isAdmin && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button \n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(etudiant)}\n                                                        title=\"Modifier\"\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                    </button>\n                                                    <button \n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(etudiant.id)}\n                                                        title=\"Supprimer\"\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n                <div style={{\n                    display: 'flex',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    marginTop: '20px',\n                    gap: '10px'\n                }}>\n                    <button\n                        className=\"btn btn-secondary\"\n                        onClick={() => paginate(currentPage - 1)}\n                        disabled={currentPage === 1}\n                    >\n                        ⬅️ Précédent\n                    </button>\n\n                    <span style={{\n                        padding: '8px 16px',\n                        backgroundColor: '#f8f9fa',\n                        borderRadius: '4px',\n                        fontSize: '14px'\n                    }}>\n                        Page {currentPage} sur {totalPages}\n                    </span>\n\n                    <button\n                        className=\"btn btn-secondary\"\n                        onClick={() => paginate(currentPage + 1)}\n                        disabled={currentPage === totalPages}\n                    >\n                        Suivant ➡️\n                    </button>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier un étudiant */}\n            {showModal && isAdmin && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingEtudiant ? 'Modifier l\\'étudiant' : 'Nouvel étudiant'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingEtudiant(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Utilisateur (Étudiant) *</label>\n                                <select\n                                    value={formData.utilisateur_id}\n                                    onChange={(e) => setFormData({...formData, utilisateur_id: e.target.value})}\n                                    required\n                                    disabled={editingEtudiant} // Empêcher la modification de l'utilisateur lors de l'édition\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur étudiant...</option>\n                                    {utilisateursEtudiants.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} - {user.email} (ID: {user.id})\n                                        </option>\n                                    ))}\n                                    {/* Si on édite, inclure l'utilisateur actuel même s'il est déjà étudiant */}\n                                    {editingEtudiant && editingEtudiant.utilisateur_id && (\n                                        <option value={editingEtudiant.utilisateur_id}>\n                                            {editingEtudiant.nom} - {editingEtudiant.email} (ID: {editingEtudiant.utilisateur_id})\n                                        </option>\n                                    )}\n                                </select>\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\n                                    Seuls les utilisateurs avec le rôle \"etudiant\" non encore assignés sont affichés\n                                </small>\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Groupe (Optionnel)</label>\n                                <select\n                                    value={formData.groupe_id}\n                                    onChange={(e) => setFormData({...formData, groupe_id: e.target.value})}\n                                >\n                                    <option value=\"\">Aucun groupe</option>\n                                    {groupes.map((groupe) => (\n                                        <option key={groupe.id} value={groupe.id}>\n                                            {groupe.nom}\n                                        </option>\n                                    ))}\n                                </select>\n                                <small style={{ color: '#6c757d', fontSize: '12px' }}>\n                                    L'étudiant peut être assigné à un groupe ou rester sans groupe\n                                </small>\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingEtudiant(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    Annuler\n                                </button>\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingEtudiant ? 'Modifier' : 'Créer'}\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Etudiants;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAE5B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACK,SAAS,EAAEC,YAAY,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwB,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACrC2B,cAAc,EAAE,EAAE;IAClBC,SAAS,EAAE;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAAtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,IAAI,MAAK,OAAO,IAAI,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuB,IAAI,MAAK,OAAO;EAEhE7B,SAAS,CAAC,MAAM;IACZ8B,cAAc,CAAC,CAAC;IAChB,IAAIF,OAAO,EAAE;MACTG,0BAA0B,CAAC,CAAC;MAC5BC,YAAY,CAAC,CAAC;IAClB;EACJ,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EAEb,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C,MAAMC,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,mEAAmE,EAAE;QAClGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;MAEFG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEC,QAAQ,CAACI,IAAI,CAAC;MAEtD,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBnC,YAAY,CAACoC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACI,IAAI,CAACnC,SAAS,CAAC,GAAG+B,QAAQ,CAACI,IAAI,CAACnC,SAAS,GAAG,EAAE,CAAC;MACvF,CAAC,MAAM;QACHC,YAAY,CAACoC,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACI,IAAI,CAAC,GAAGJ,QAAQ,CAACI,IAAI,GAAG,EAAE,CAAC;MACnE;IACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE1C,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAE,qCAAqC,EAAE,OAAO,CAAC;MACnEvC,YAAY,CAAC,EAAE,CAAC;IACpB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqB,0BAA0B,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;MAE1D;MACA,MAAMW,oBAAoB,GAAG,MAAM7C,KAAK,CAACoC,GAAG,CAAC,uFAAuF,EAAE;QAClIC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;;MAEF;MACA,MAAMgB,iBAAiB,GAAG,MAAM9C,KAAK,CAACoC,GAAG,CAAC,mEAAmE,EAAE;QAC3GC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;;MAEF;MACA,MAAMiB,YAAY,GAAGN,KAAK,CAACC,OAAO,CAACG,oBAAoB,CAACN,IAAI,CAAC,GAAGM,oBAAoB,CAACN,IAAI,GAAG,EAAE;MAC9F,MAAMS,kBAAkB,GAAGP,KAAK,CAACC,OAAO,CAACI,iBAAiB,CAACP,IAAI,CAAC,GAAGO,iBAAiB,CAACP,IAAI,GAAG,EAAE;MAE9F,MAAMU,6BAA6B,GAAGF,YAAY,CAACG,MAAM,CAAC/C,IAAI,IAAI;QAC9D,MAAMgD,OAAO,GAAG,CAAChD,IAAI,CAACiD,QAAQ,IAAIjD,IAAI,CAACuB,IAAI,IAAI,EAAE,EAAE2B,WAAW,CAAC,CAAC;QAChE,OAAOF,OAAO,KAAK,UAAU,IAAIA,OAAO,KAAK,UAAU;MAC3D,CAAC,CAAC;;MAEF;MACA,MAAMG,qBAAqB,GAAGN,kBAAkB,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjC,cAAc,CAAC,CAAC2B,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;MACrG,MAAMC,uBAAuB,GAAGT,6BAA6B,CAACC,MAAM,CAAC/C,IAAI,IACrE,CAACmD,qBAAqB,CAACK,QAAQ,CAACxD,IAAI,CAACsD,EAAE,CAC3C,CAAC;MAEDxB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEwB,uBAAuB,CAACE,MAAM,CAAC;MACpF/C,wBAAwB,CAAC6C,uBAAuB,CAAC;IACrD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;MAC/E9B,wBAAwB,CAAC,EAAE,CAAC;IAChC;EACJ,CAAC;EAED,MAAMgB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMG,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,+DAA+D,EAAE;QAC9FC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;MACFf,UAAU,CAAC0B,KAAK,CAACC,OAAO,CAACP,QAAQ,CAACI,IAAI,CAAC,GAAGJ,QAAQ,CAACI,IAAI,GAAG,EAAE,CAAC;IACjE,CAAC,CAAC,OAAOI,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChE5B,UAAU,CAAC,EAAE,CAAC;IAClB;EACJ,CAAC;EAED,MAAM8C,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAElB,IAAI,CAACrC,OAAO,EAAE;MACVxB,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAE,0DAA0D,EAAE,OAAO,CAAC;MACxF;IACJ;IAEA,IAAI,CAACvB,QAAQ,CAACE,cAAc,EAAE;MAC1BtB,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAE,sCAAsC,EAAE,OAAO,CAAC;MACpE;IACJ;IAEA,IAAI;MACA,MAAMd,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAM+B,GAAG,GAAG,mEAAmE;MAC/E,MAAMC,MAAM,GAAGtD,eAAe,GAAG,KAAK,GAAG,MAAM;MAC/C,MAAM6B,IAAI,GAAG7B,eAAe,GAAG;QAAE,GAAGW,QAAQ;QAAEoC,EAAE,EAAE/C,eAAe,CAAC+C;MAAG,CAAC,GAAGpC,QAAQ;MAEjFY,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;QAAE8B,MAAM;QAAEzB;MAAK,CAAC,CAAC;MAE3D,MAAMJ,QAAQ,GAAG,MAAMnC,KAAK,CAAC;QACzBgE,MAAM;QACND,GAAG;QACHxB,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUR,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;MAEF,IAAIK,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACvBvC,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAE,YAAYlC,eAAe,GAAG,SAAS,GAAG,MAAM,cAAc,EAAE,SAAS,CAAC;QAC9FD,YAAY,CAAC,KAAK,CAAC;QACnBE,kBAAkB,CAAC,IAAI,CAAC;QACxBsD,SAAS,CAAC,CAAC;QACXtC,cAAc,CAAC,CAAC;QAChBC,0BAA0B,CAAC,CAAC,CAAC,CAAC;MAClC,CAAC,MAAM;QACH,MAAM,IAAIsC,KAAK,CAAC/B,QAAQ,CAACI,IAAI,CAACI,KAAK,IAAI,iBAAiB,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAwB,eAAA,EAAAC,oBAAA;MACZnC,OAAO,CAACU,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,MAAM0B,YAAY,GAAG,EAAAF,eAAA,GAAAxB,KAAK,CAACR,QAAQ,cAAAgC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB5B,IAAI,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBzB,KAAK,KAAIA,KAAK,CAAC2B,OAAO,IAAI,yBAAyB;MAC9FrE,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAEyB,YAAY,EAAE,OAAO,CAAC;IAC9C;EACJ,CAAC;EAED,MAAME,UAAU,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI,CAAC/C,OAAO,EAAE;MACVxB,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAE,oDAAoD,EAAE,OAAO,CAAC;MAClF;IACJ;;IAEA;IACA,MAAM6B,oCAAoC,CAACD,QAAQ,CAACjD,cAAc,CAAC;IAEnEZ,kBAAkB,CAAC6D,QAAQ,CAAC;IAC5BlD,WAAW,CAAC;MACRC,cAAc,EAAEiD,QAAQ,CAACjD,cAAc,IAAI,EAAE;MAC7CC,SAAS,EAAEgD,QAAQ,CAAChD,SAAS,IAAI;IACrC,CAAC,CAAC;IACFf,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMgE,oCAAoC,GAAG,MAAOC,aAAa,IAAK;IAClE,IAAI;MACA,MAAM5C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;;MAE3C;MACA,MAAMa,oBAAoB,GAAG,MAAM7C,KAAK,CAACoC,GAAG,CAAC,yEAAyE,EAAE;QACpHC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;;MAEF;MACA,MAAMgB,iBAAiB,GAAG,MAAM9C,KAAK,CAACoC,GAAG,CAAC,mEAAmE,EAAE;QAC3GC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUR,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,MAAMiB,YAAY,GAAGN,KAAK,CAACC,OAAO,CAACG,oBAAoB,CAACN,IAAI,CAAC,GAAGM,oBAAoB,CAACN,IAAI,GAAG,EAAE;MAC9F,MAAMS,kBAAkB,GAAGP,KAAK,CAACC,OAAO,CAACI,iBAAiB,CAACP,IAAI,CAAC,GAAGO,iBAAiB,CAACP,IAAI,GAAG,EAAE;MAE9F,MAAMU,6BAA6B,GAAGF,YAAY,CAACG,MAAM,CAAC/C,IAAI,IAAI;QAC9D,MAAMgD,OAAO,GAAG,CAAChD,IAAI,CAACiD,QAAQ,IAAIjD,IAAI,CAACuB,IAAI,IAAI,EAAE,EAAE2B,WAAW,CAAC,CAAC;QAChE,OAAOF,OAAO,KAAK,UAAU,IAAIA,OAAO,KAAK,UAAU;MAC3D,CAAC,CAAC;;MAEF;MACA,MAAMG,qBAAqB,GAAGN,kBAAkB,CAACO,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACjC,cAAc,CAAC,CAAC2B,MAAM,CAACO,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;MACrG,MAAMC,uBAAuB,GAAGT,6BAA6B,CAACC,MAAM,CAAC/C,IAAI,IACrE,CAACmD,qBAAqB,CAACK,QAAQ,CAACxD,IAAI,CAACsD,EAAE,CAAC,IAAItD,IAAI,CAACsD,EAAE,KAAKiB,aAC5D,CAAC;MAED7D,wBAAwB,CAAC6C,uBAAuB,CAAC;IACrD,CAAC,CAAC,OAAOf,KAAK,EAAE;MACZV,OAAO,CAACU,KAAK,CAAC,qEAAqE,EAAEA,KAAK,CAAC;IAC/F;EACJ,CAAC;EAED,MAAMgC,YAAY,GAAG,MAAOlB,EAAE,IAAK;IAC/B,IAAI,CAAChC,OAAO,EAAE;MACVxB,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAE,qDAAqD,EAAE,OAAO,CAAC;MACnF;IACJ;IAEA,MAAMgC,MAAM,GAAG,MAAM3E,IAAI,CAAC2C,IAAI,CAAC;MAC3BiC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA,MAAMvD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAMG,QAAQ,GAAG,MAAMnC,KAAK,CAACsF,MAAM,CAAC,mEAAmE,EAAE;UACrGjD,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUR,KAAK,EAAE;YAChC,cAAc,EAAE;UACpB,CAAC;UACDS,IAAI,EAAE;YAAEkB;UAAG;QACf,CAAC,CAAC;QAEF,IAAItB,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;UACvBvC,IAAI,CAAC2C,IAAI,CAAC,WAAW,EAAE,6BAA6B,EAAE,SAAS,CAAC;UAChEjB,cAAc,CAAC,CAAC;UAChBC,0BAA0B,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,MAAM;UACH,MAAM,IAAIsC,KAAK,CAAC/B,QAAQ,CAACI,IAAI,CAACI,KAAK,IAAI,+BAA+B,CAAC;QAC3E;MACJ,CAAC,CAAC,OAAOA,KAAK,EAAE;QAAA,IAAA4C,gBAAA,EAAAC,qBAAA;QACZvD,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,MAAM0B,YAAY,GAAG,EAAAkB,gBAAA,GAAA5C,KAAK,CAACR,QAAQ,cAAAoD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhD,IAAI,cAAAiD,qBAAA,uBAApBA,qBAAA,CAAsB7C,KAAK,KAAIA,KAAK,CAAC2B,OAAO,IAAI,qCAAqC;QAC1GrE,IAAI,CAAC2C,IAAI,CAAC,QAAQ,EAAEyB,YAAY,EAAE,OAAO,CAAC;MAC9C;IACJ;EACJ,CAAC;EAED,MAAMJ,SAAS,GAAGA,CAAA,KAAM;IACpB3C,WAAW,CAAC;MACRC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE;IACf,CAAC,CAAC;EACN,CAAC;EAED,MAAMiE,cAAc,GAAIC,MAAM,IAAK;IAC/B,oBAAO/F,KAAA,CAAAgG,aAAA;MAAMC,SAAS,EAAC,qBAAqB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,OAAW,CAAC;EAC7D,CAAC;;EAED;EACA,MAAMC,MAAM,GAAG;IACXC,OAAO,EAAE;MACLC,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,OAAO;MACjBC,UAAU,EAAE;IAChB,CAAC;IACDC,WAAW,EAAE;MACTL,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,OAAO;MACjBG,KAAK,EAAE;IACX,CAAC;IACDC,WAAW,EAAE;MACTP,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBM,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,mBAAmB;MAC3BH,KAAK,EAAE;IACX;EACJ,CAAC;;EAED;EACA,MAAMI,iBAAiB,GAAG3G,SAAS,CAAC8C,MAAM,CAACsB,QAAQ,IAAI;IACnD,MAAMwC,WAAW,GAAGhG,UAAU,CAACqC,WAAW,CAAC,CAAC;IAC5C,OAAQmB,QAAQ,CAACyC,GAAG,IAAIzC,QAAQ,CAACyC,GAAG,CAAC5D,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACqD,WAAW,CAAC,IAChExC,QAAQ,CAAC0C,KAAK,IAAI1C,QAAQ,CAAC0C,KAAK,CAAC7D,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACqD,WAAW,CAAE,IACrExC,QAAQ,CAAC2C,UAAU,IAAI3C,QAAQ,CAAC2C,UAAU,CAAC9D,WAAW,CAAC,CAAC,CAACM,QAAQ,CAACqD,WAAW,CAAE;EAC3F,CAAC,CAAC;;EAEF;EACA,MAAMI,eAAe,GAAGlG,WAAW,GAAGE,YAAY;EAClD,MAAMiG,gBAAgB,GAAGD,eAAe,GAAGhG,YAAY;EACvD,MAAMkG,gBAAgB,GAAGP,iBAAiB,CAACQ,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EACnF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACX,iBAAiB,CAACnD,MAAM,GAAGxC,YAAY,CAAC;EAErE,MAAMuG,QAAQ,GAAIC,UAAU,IAAKzG,cAAc,CAACyG,UAAU,CAAC;EAE3D,IAAItH,OAAO,EAAE;IACT,oBACIX,KAAA,CAAAgG,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BvG,KAAA,CAAAgG,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BvG,KAAA,CAAAgG,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,gCAA8B,CAChC,CAAC;EAEd;EAEA,oBACIvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uCAA4B,CAAC,eACjCvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBvG,KAAA,CAAAgG,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxBa,iBAAiB,CAACnD,MAAM,EAAC,8BACxB,CAAC,EACNnC,OAAO,iBACJ9B,KAAA,CAAAgG,aAAA;IACIC,SAAS,EAAC,iBAAiB;IAC3BiC,OAAO,EAAEA,CAAA,KAAMpH,YAAY,CAAC,IAAI,CAAE;IAAAoF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCvG,KAAA,CAAAgG,aAAA;IAAKmC,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,uBACjC,CAEX,CACJ,CAAC,EAGL,CAACzE,OAAO,iBACL9B,KAAA,CAAAgG,aAAA;IAAKqC,KAAK,EAAE7B,MAAM,CAACS,WAAY;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvG,KAAA,CAAAgG,aAAA;IAAGqC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAI,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,kJAAgI,CAC1J,CACR,eAGDvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAACoC,KAAK,EAAE;MAAEnB,YAAY,EAAE;IAAO,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5DvG,KAAA,CAAAgG,aAAA;IACIuC,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,gEAAmD;IAC/DC,KAAK,EAAEpH,UAAW;IAClBqH,QAAQ,EAAG7E,CAAC,IAAK;MACbvC,aAAa,CAACuC,CAAC,CAAC8E,MAAM,CAACF,KAAK,CAAC;MAC7BjH,cAAc,CAAC,CAAC,CAAC;IACrB,CAAE;IACFyE,SAAS,EAAC,cAAc;IACxBoC,KAAK,EAAE;MACHO,KAAK,EAAE,MAAM;MACblC,OAAO,EAAE,MAAM;MACfS,MAAM,EAAE,gBAAgB;MACxBP,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBa,iBAAiB,CAACnD,MAAM,KAAK,CAAC,gBAC3BjE,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBvG,KAAA,CAAAgG,aAAA;IAAKmC,GAAG,EAAC,cAAc;IAACC,GAAG,EAAC,mBAAgB;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC/CvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,6BAAwB,CAAC,EAC3BlF,UAAU,iBACPrB,KAAA,CAAAgG,aAAA;IACIkC,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,EAAE,CAAE;IACjC2E,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,sBAEO,CAEX,CAAC,gBAENvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BvG,KAAA,CAAAgG,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,iBAAS,CAAC,eACdvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mCAAwB,CAAC,eAC7BvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,oBAAY,CAAC,eACjBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,EACjBzE,OAAO,iBAAI9B,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CAC9B,CACD,CAAC,eACRvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKoB,gBAAgB,CAAC/D,GAAG,CAAEiB,QAAQ,iBAC3B7E,KAAA,CAAAgG,aAAA;IAAI6C,GAAG,EAAEhE,QAAQ,CAACf,EAAG;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIvG,KAAA,CAAAgG,aAAA;IAAMqC,KAAK,EAAE7B,MAAM,CAACC,OAAQ;IAAAP,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,GACxB,EAAC1B,QAAQ,CAACf,EACT,CACN,CAAC,eACL9D,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAS1B,QAAQ,CAACyC,GAAG,IAAI,oBAA6B,CAAC,eACvDtH,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,kBAAgB,EAAC1B,QAAQ,CAACjD,cAAsB,CACtD,CACL,CAAC,eACL5B,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK1B,QAAQ,CAAC0C,KAAK,IAAI,sBAA2B,CAAC,eACnDvH,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIvG,KAAA,CAAAgG,aAAA;IAAMqC,KAAK,EAAE7B,MAAM,CAACO,WAAY;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3B1B,QAAQ,CAAC2C,UAAU,IAAI,cACtB,CACN,CAAC,eACLxH,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,cAAc,CAAC,OAAO,CAAM,CAAC,EACjChE,OAAO,iBACJ9B,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BvG,KAAA,CAAAgG,aAAA;IACIC,SAAS,EAAC,wBAAwB;IAClCiC,OAAO,EAAEA,CAAA,KAAMtD,UAAU,CAACC,QAAQ,CAAE;IACpCK,KAAK,EAAC,UAAU;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhBvG,KAAA,CAAAgG,aAAA;IAAKmC,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACjC,CAAC,eACTvG,KAAA,CAAAgG,aAAA;IACIC,SAAS,EAAC,uBAAuB;IACjCiC,OAAO,EAAEA,CAAA,KAAMlD,YAAY,CAACH,QAAQ,CAACf,EAAE,CAAE;IACzCoB,KAAK,EAAC,WAAW;IAAAgB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjBvG,KAAA,CAAAgG,aAAA;IAAKmC,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CACpC,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CAER,CAAC,EAGLsB,UAAU,GAAG,CAAC,iBACX7H,KAAA,CAAAgG,aAAA;IAAKqC,KAAK,EAAE;MACRS,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,SAAS,EAAE,MAAM;MACjBC,GAAG,EAAE;IACT,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACEvG,KAAA,CAAAgG,aAAA;IACIC,SAAS,EAAC,mBAAmB;IAC7BiC,OAAO,EAAEA,CAAA,KAAMF,QAAQ,CAACzG,WAAW,GAAG,CAAC,CAAE;IACzC4H,QAAQ,EAAE5H,WAAW,KAAK,CAAE;IAAA2E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/B,8BAEO,CAAC,eAETvG,KAAA,CAAAgG,aAAA;IAAMqC,KAAK,EAAE;MACT3B,OAAO,EAAE,UAAU;MACnBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,OACM,EAAChF,WAAW,EAAC,OAAK,EAACsG,UACtB,CAAC,eAEP7H,KAAA,CAAAgG,aAAA;IACIC,SAAS,EAAC,mBAAmB;IAC7BiC,OAAO,EAAEA,CAAA,KAAMF,QAAQ,CAACzG,WAAW,GAAG,CAAC,CAAE;IACzC4H,QAAQ,EAAE5H,WAAW,KAAKsG,UAAW;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,sBAEO,CACP,CACR,EAGA1F,SAAS,IAAIiB,OAAO,iBACjB9B,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKxF,eAAe,GAAG,sBAAsB,GAAG,iBAAsB,CAAC,eACvEf,KAAA,CAAAgG,aAAA;IACIC,SAAS,EAAC,WAAW;IACrBiC,OAAO,EAAEA,CAAA,KAAM;MACXpH,YAAY,CAAC,KAAK,CAAC;MACnBE,kBAAkB,CAAC,IAAI,CAAC;MACxBsD,SAAS,CAAC,CAAC;IACf,CAAE;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFvG,KAAA,CAAAgG,aAAA;IAAKmC,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAlC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNvG,KAAA,CAAAgG,aAAA;IAAMoD,QAAQ,EAAElF,YAAa;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,6BAA+B,CAAC,eACvCvG,KAAA,CAAAgG,aAAA;IACIyC,KAAK,EAAE/G,QAAQ,CAACE,cAAe;IAC/B8G,QAAQ,EAAG7E,CAAC,IAAKlC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,cAAc,EAAEiC,CAAC,CAAC8E,MAAM,CAACF;IAAK,CAAC,CAAE;IAC5EY,QAAQ;IACRF,QAAQ,EAAEpI,eAAgB,CAAC;IAAA;IAAAmF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3BvG,KAAA,CAAAgG,aAAA;IAAQyC,KAAK,EAAC,EAAE;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+CAA+C,CAAC,EAChEtF,qBAAqB,CAAC2C,GAAG,CAACpD,IAAI,iBAC3BR,KAAA,CAAAgG,aAAA;IAAQ6C,GAAG,EAAErI,IAAI,CAACsD,EAAG;IAAC2E,KAAK,EAAEjI,IAAI,CAACsD,EAAG;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC/F,IAAI,CAAC8G,GAAG,EAAC,KAAG,EAAC9G,IAAI,CAAC+G,KAAK,EAAC,QAAM,EAAC/G,IAAI,CAACsD,EAAE,EAAC,GACpC,CACX,CAAC,EAED/C,eAAe,IAAIA,eAAe,CAACa,cAAc,iBAC9C5B,KAAA,CAAAgG,aAAA;IAAQyC,KAAK,EAAE1H,eAAe,CAACa,cAAe;IAAAsE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzCxF,eAAe,CAACuG,GAAG,EAAC,KAAG,EAACvG,eAAe,CAACwG,KAAK,EAAC,QAAM,EAACxG,eAAe,CAACa,cAAc,EAAC,GACjF,CAER,CAAC,eACT5B,KAAA,CAAAgG,aAAA;IAAOqC,KAAK,EAAE;MAAErB,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAO,CAAE;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6FAE/C,CACN,CAAC,eAENvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBvG,KAAA,CAAAgG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,oBAAyB,CAAC,eACjCvG,KAAA,CAAAgG,aAAA;IACIyC,KAAK,EAAE/G,QAAQ,CAACG,SAAU;IAC1B6G,QAAQ,EAAG7E,CAAC,IAAKlC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,SAAS,EAAEgC,CAAC,CAAC8E,MAAM,CAACF;IAAK,CAAC,CAAE;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvEvG,KAAA,CAAAgG,aAAA;IAAQyC,KAAK,EAAC,EAAE;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAoB,CAAC,EACrCpF,OAAO,CAACyC,GAAG,CAAE0F,MAAM,iBAChBtJ,KAAA,CAAAgG,aAAA;IAAQ6C,GAAG,EAAES,MAAM,CAACxF,EAAG;IAAC2E,KAAK,EAAEa,MAAM,CAACxF,EAAG;IAAAoC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpC+C,MAAM,CAAChC,GACJ,CACX,CACG,CAAC,eACTtH,KAAA,CAAAgG,aAAA;IAAOqC,KAAK,EAAE;MAAErB,KAAK,EAAE,SAAS;MAAEH,QAAQ,EAAE;IAAO,CAAE;IAAAX,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,4EAE/C,CACN,CAAC,eAENvG,KAAA,CAAAgG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BvG,KAAA,CAAAgG,aAAA;IACIuC,IAAI,EAAC,QAAQ;IACbtC,SAAS,EAAC,mBAAmB;IAC7BiC,OAAO,EAAEA,CAAA,KAAM;MACXpH,YAAY,CAAC,KAAK,CAAC;MACnBE,kBAAkB,CAAC,IAAI,CAAC;MACxBsD,SAAS,CAAC,CAAC;IACf,CAAE;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,SAEO,CAAC,eACTvG,KAAA,CAAAgG,aAAA;IAAQuC,IAAI,EAAC,QAAQ;IAACtC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5CxF,eAAe,GAAG,UAAU,GAAG,OAC5B,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAeR,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module"}