<?php
/**
 * API pour marquer les messages comme lus
 * Marque tous les messages non lus d'une conversation spécifique
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Vérification de la méthode HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Méthode non autorisée', 405);
    }
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('Données JSON invalides', 400);
    }
    
    // Validation des paramètres requis
    validateRequiredParams($input, ['user_id', 'other_user_id']);
    
    $userId = (int)$input['user_id'];
    $otherUserId = (int)$input['other_user_id'];
    
    // Validation des IDs
    if ($userId <= 0 || $otherUserId <= 0) {
        sendErrorResponse('IDs utilisateur invalides', 400);
    }
    
    if ($userId === $otherUserId) {
        sendErrorResponse('IDs utilisateur identiques', 400);
    }
    
    // Vérification des droits d'accès à la messagerie
    verifyMessagingAccess($userId);
    verifyMessagingAccess($otherUserId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Marquer comme lus tous les messages reçus de l'autre utilisateur
    $stmt = $pdo->prepare("
        UPDATE messages 
        SET lu = 1 
        WHERE destinataire_id = ? 
        AND expediteur_id = ? 
        AND lu = 0
        AND supprime_par_destinataire = 0
    ");
    
    $stmt->execute([$userId, $otherUserId]);
    $affectedRows = $stmt->rowCount();
    
    // Log de l'activité
    logActivity($userId, 'MARK_MESSAGES_READ', "Messages marqués lus avec utilisateur ID: $otherUserId ($affectedRows messages)");
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'message' => 'Messages marqués comme lus',
        'data' => [
            'user_id' => $userId,
            'other_user_id' => $otherUserId,
            'messages_marked' => $affectedRows
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Erreur mark_read.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors du marquage des messages', 500);
}
?>
