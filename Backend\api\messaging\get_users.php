<?php
/**
 * API pour récupérer les utilisateurs autorisés à la messagerie
 * Exclut l'utilisateur connecté et ne retourne que les rôles autorisés
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Récupération et validation des paramètres
    $currentUserId = $_GET['current_user'] ?? null;
    
    if (!$currentUserId || !is_numeric($currentUserId)) {
        sendErrorResponse('ID utilisateur manquant ou invalide', 400);
    }
    
    $currentUserId = (int)$currentUserId;
    
    // Vérification des droits d'accès à la messagerie
    $currentUser = verifyMessagingAccess($currentUserId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Requête pour récupérer les utilisateurs autorisés
    // Exclut l'utilisateur connecté et ne retourne que les rôles autorisés
    $stmt = $pdo->prepare("
        SELECT 
            u.id,
            u.nom,
            u.email,
            r.id as role_id,
            r.nom as role_nom
        FROM utilisateurs u
        JOIN roles r ON u.role_id = r.id
        WHERE u.id != ? 
        AND r.id IN (1, 2, 4)
        ORDER BY r.nom, u.nom
    ");
    
    $stmt->execute([$currentUserId]);
    $users = $stmt->fetchAll();
    
    // Formatage des données pour l'interface
    $formattedUsers = [];
    foreach ($users as $user) {
        $formattedUsers[] = [
            'id' => (int)$user['id'],
            'nom' => $user['nom'],
            'email' => $user['email'],
            'role_id' => (int)$user['role_id'],
            'role_nom' => $user['role_nom']
        ];
    }
    
    // Log de l'activité
    logActivity($currentUserId, 'GET_MESSAGING_USERS', 'Récupération liste utilisateurs messagerie');
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'users' => $formattedUsers,
        'total' => count($formattedUsers),
        'current_user' => [
            'id' => $currentUserId,
            'role' => $currentUser['role_nom']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Erreur get_users.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors de la récupération des utilisateurs', 500);
}
?>
