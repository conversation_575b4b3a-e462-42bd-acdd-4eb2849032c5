{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\MessagingSystem.js\";\nimport React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\nconst MessagingSystem = () => {\n  const {\n    user,\n    isLoading: authLoading,\n    isAuthenticated\n  } = useContext(AuthContext);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [authorizedUsers, setAuthorizedUsers] = useState([]);\n  const [showNewConversation, setShowNewConversation] = useState(false);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [showContextMenu, setShowContextMenu] = useState(null);\n  const [stats, setStats] = useState({});\n  const messagesEndRef = useRef(null);\n  const contextMenuRef = useRef(null);\n\n  // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée\n  const getCurrentUserId = () => {\n    console.log('🔍 Tentative de récupération de l\\'ID utilisateur...');\n    console.log('👤 Contexte user:', user);\n\n    // Essayer d'abord depuis le contexte user avec différentes propriétés possibles\n    if (user) {\n      // Vérifier différentes propriétés possibles pour l'ID\n      const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];\n      for (const id of possibleIds) {\n        if (id && !isNaN(id)) {\n          console.log('✅ ID trouvé dans le contexte:', id);\n          return parseInt(id);\n        }\n      }\n    }\n\n    // Essayer depuis localStorage comme fallback\n    try {\n      const storedUser = localStorage.getItem('user');\n      console.log('📦 Données localStorage user:', storedUser);\n      if (storedUser) {\n        const userData = JSON.parse(storedUser);\n        console.log('📊 Données utilisateur parsées:', userData);\n        if (userData) {\n          // Vérifier différentes propriétés possibles pour l'ID\n          const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];\n          for (const id of possibleIds) {\n            if (id && !isNaN(id)) {\n              console.log('✅ ID trouvé dans localStorage:', id);\n              return parseInt(id);\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.warn('❌ Erreur lors de la récupération de l\\'utilisateur depuis localStorage:', error);\n    }\n\n    // Dernière tentative avec le token\n    const token = localStorage.getItem('token');\n    console.log('🔑 Token localStorage:', token);\n    if (token) {\n      // Essayer différents formats de token\n      if (token.includes('_')) {\n        const userId = token.split('_').pop();\n        if (userId && !isNaN(userId)) {\n          console.log('✅ ID trouvé dans le token:', userId);\n          return parseInt(userId);\n        }\n      }\n\n      // Essayer de décoder le token s'il est en base64 ou JWT\n      try {\n        if (token.includes('.')) {\n          // Format JWT potentiel\n          const payload = JSON.parse(atob(token.split('.')[1]));\n          const possibleIds = [payload.id, payload.user_id, payload.utilisateur_id, payload.sub];\n          for (const id of possibleIds) {\n            if (id && !isNaN(id)) {\n              console.log('✅ ID trouvé dans JWT:', id);\n              return parseInt(id);\n            }\n          }\n        }\n      } catch (error) {\n        console.warn('⚠️ Impossible de décoder le token JWT:', error);\n      }\n    }\n    console.warn('❌ Aucun ID utilisateur trouvé');\n    return null;\n  };\n\n  // 🔍 Fonction pour vérifier si l'utilisateur est valide\n  const isUserValid = () => {\n    const userId = getCurrentUserId();\n    return userId && userId > 0;\n  };\n  const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n\n  // Scroll automatique vers le bas\n  const scrollToBottom = () => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fermer le menu contextuel en cliquant ailleurs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n        setShowContextMenu(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fonction pour faire des requêtes API\n  const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n    try {\n      const token = localStorage.getItem('token') || 'test_user_1';\n      const config = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      };\n      if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n        config.body = JSON.stringify(data);\n      }\n      const url = `${API_BASE_URL}?action=${endpoint}`;\n      console.log('API Request:', {\n        url,\n        method,\n        endpoint,\n        token\n      });\n      const response = await fetch(url, config);\n      console.log('API Response Status:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('API Response Data:', result);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur API');\n      }\n      return result;\n    } catch (error) {\n      console.error('Erreur API complète:', {\n        endpoint,\n        method,\n        error: error.message,\n        stack: error.stack\n      });\n      throw error;\n    }\n  };\n\n  // Charger les conversations avec confidentialité stricte\n  const loadConversations = async () => {\n    try {\n      var _result$data;\n      setLoading(true);\n      setError(''); // Réinitialiser l'erreur\n\n      // 🔍 Vérification robuste de l'utilisateur\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) {\n        console.warn('� Utilisateur non identifié, tentative de récupération...');\n\n        // Attendre un peu pour que le contexte se charge\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        const retryUserId = getCurrentUserId();\n        if (!retryUserId) {\n          throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n        }\n      }\n      const result = await makeAPIRequest('conversations');\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur lors du chargement des conversations');\n      }\n\n      // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n      const finalUserId = getCurrentUserId();\n\n      // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n      const secureConversations = (result.data || []).filter(conversation => {\n        // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n        const contactId = parseInt(conversation.contact_id);\n        return contactId && contactId !== finalUserId && contactId > 0;\n      });\n      console.log('🔒 Conversations sécurisées chargées:', {\n        total_received: ((_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.length) || 0,\n        secure_filtered: secureConversations.length,\n        user_id: finalUserId,\n        user_context: user ? 'Disponible' : 'Non disponible'\n      });\n      setConversations(secureConversations);\n    } catch (error) {\n      const errorMessage = error.message || 'Erreur inconnue';\n      setError('Impossible de charger les conversations: ' + errorMessage);\n      console.error('🚨 Erreur sécurité conversations:', {\n        error: errorMessage,\n        user_id: getCurrentUserId(),\n        user_context: user,\n        localStorage_user: localStorage.getItem('user'),\n        localStorage_token: localStorage.getItem('token')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les messages d'une conversation avec confidentialité stricte\n  const loadMessages = async contactId => {\n    try {\n      var _result$data2;\n      setLoading(true);\n      setError(''); // Réinitialiser l'erreur\n\n      // � Vérification robuste de l'utilisateur\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) {\n        console.warn('🚨 Utilisateur non identifié lors du chargement des messages');\n        throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n      }\n      const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur lors du chargement des messages');\n      }\n\n      // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n      const finalUserId = getCurrentUserId();\n\n      // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n      const secureMessages = (result.data || []).filter(message => {\n        const expediteurId = parseInt(message.expediteur_id);\n        const destinataireId = parseInt(message.destinataire_id);\n\n        // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n        return expediteurId === finalUserId || destinataireId === finalUserId;\n      }).map(message => {\n        // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n        const expediteurId = parseInt(message.expediteur_id);\n        return {\n          ...message,\n          message_type: expediteurId === finalUserId ? 'sent' : 'received',\n          is_own_message: expediteurId === finalUserId\n        };\n      });\n      console.log('🔒 Messages sécurisés chargés:', {\n        total_received: ((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : _result$data2.length) || 0,\n        secure_filtered: secureMessages.length,\n        user_id: finalUserId,\n        contact_id: contactId,\n        user_context: user ? 'Disponible' : 'Non disponible'\n      });\n      setMessages(secureMessages);\n    } catch (error) {\n      const errorMessage = error.message || 'Erreur inconnue';\n      setError('Impossible de charger les messages: ' + errorMessage);\n      console.error('🚨 Erreur sécurité messages:', {\n        error: errorMessage,\n        user_id: getCurrentUserId(),\n        contact_id: contactId,\n        user_context: user,\n        localStorage_user: localStorage.getItem('user'),\n        localStorage_token: localStorage.getItem('token')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les utilisateurs autorisés\n  const loadAuthorizedUsers = async () => {\n    try {\n      const result = await makeAPIRequest('users');\n      setAuthorizedUsers(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les utilisateurs: ' + error.message);\n    }\n  };\n\n  // Charger les statistiques\n  const loadStats = async () => {\n    try {\n      const result = await makeAPIRequest('stats');\n      setStats(result.data || {});\n    } catch (error) {\n      console.error('Erreur chargement stats:', error);\n    }\n  };\n\n  // Envoyer un message\n  const sendMessage = async () => {\n    if (!newMessage.trim()) return;\n    try {\n      const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n      if (!destinataireId) {\n        setError('Veuillez sélectionner un destinataire');\n        return;\n      }\n      await makeAPIRequest('send', 'POST', {\n        destinataire_id: destinataireId,\n        message: newMessage.trim()\n      });\n      setNewMessage('');\n      setShowNewConversation(false);\n\n      // Recharger les conversations et messages\n      await loadConversations();\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible d\\'envoyer le message: ' + error.message);\n    }\n  };\n\n  // Modifier un message\n  const editMessage = async (messageId, newContent) => {\n    try {\n      await makeAPIRequest('edit', 'PUT', {\n        message_id: messageId,\n        message: newContent\n      });\n      setEditingMessage(null);\n      setEditContent('');\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de modifier le message: ' + error.message);\n    }\n  };\n\n  // Supprimer un message\n  const deleteMessage = async (messageId, deleteType = 'for_me') => {\n    try {\n      await makeAPIRequest('delete', 'DELETE', {\n        message_id: messageId,\n        delete_type: deleteType\n      });\n      setShowContextMenu(null);\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de supprimer le message: ' + error.message);\n    }\n  };\n\n  // Sélectionner une conversation\n  const selectConversation = async conversation => {\n    setSelectedConversation(conversation);\n    setShowNewConversation(false);\n    await loadMessages(conversation.contact_id);\n  };\n\n  // Démarrer une nouvelle conversation\n  const startNewConversation = () => {\n    setSelectedConversation(null);\n    setMessages([]);\n    setShowNewConversation(true);\n  };\n\n  // Gérer le menu contextuel\n  const handleContextMenu = (e, message) => {\n    e.preventDefault();\n    setShowContextMenu({\n      x: e.clientX,\n      y: e.clientY,\n      message: message\n    });\n  };\n\n  // Démarrer l'édition d'un message\n  const startEditing = message => {\n    setEditingMessage(message.id);\n    setEditContent(message.message);\n    setShowContextMenu(null);\n  };\n\n  // Annuler l'édition\n  const cancelEditing = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  // Confirmer l'édition\n  const confirmEdit = async () => {\n    if (editContent.trim() && editingMessage) {\n      await editMessage(editingMessage, editContent.trim());\n    }\n  };\n\n  // Formater la date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffDays <= 7) {\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n\n  // Charger les données au montage du composant\n  useEffect(() => {\n    const initializeMessaging = async () => {\n      console.log('🚀 Initialisation du système de messagerie...');\n\n      // Attendre un peu pour que le contexte d'authentification se charge\n      if (!user) {\n        console.log('⏳ Attente du chargement du contexte utilisateur...');\n        await new Promise(resolve => setTimeout(resolve, 500));\n      }\n      const userId = getCurrentUserId();\n      console.log('👤 ID utilisateur détecté:', userId);\n      if (userId) {\n        console.log('✅ Utilisateur valide, chargement des données...');\n        loadConversations();\n        loadAuthorizedUsers();\n        loadStats();\n      } else {\n        console.warn('⚠️ Aucun utilisateur valide trouvé');\n        setError('Utilisateur non identifié. Veuillez vous reconnecter.');\n\n        // Essayer de charger quand même après un délai\n        setTimeout(() => {\n          const retryUserId = getCurrentUserId();\n          if (retryUserId) {\n            console.log('🔄 Retry réussi, chargement des données...');\n            setError(''); // Effacer l'erreur\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n          }\n        }, 2000);\n      }\n    };\n    initializeMessaging();\n  }, [user]);\n\n  // Actualiser périodiquement\n  useEffect(() => {\n    const interval = setInterval(() => {\n      loadConversations();\n      if (selectedConversation) {\n        loadMessages(selectedConversation.contact_id);\n      }\n    }, 30000); // Actualiser toutes les 30 secondes\n\n    return () => clearInterval(interval);\n  }, [selectedConversation]);\n\n  // Vérification de l'utilisateur avant affichage\n  const currentUserId = getCurrentUserId();\n\n  // Affichage d'erreur d'authentification\n  if (!currentUserId && !loading && error.includes('Utilisateur non identifié')) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"messaging-system\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-error-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-error-content\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 25\n      }\n    }, \"\\uD83D\\uDD10 Authentification Requise\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 25\n      }\n    }, \"Impossible d'acc\\xE9der \\xE0 la messagerie sans identification utilisateur.\"), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"debug-info\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"h3\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD0D Informations de Debug :\"), /*#__PURE__*/React.createElement(\"ul\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 33\n      }\n    }, \"Contexte utilisateur: \", user ? '✅ Chargé' : '❌ Non chargé'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 33\n      }\n    }, \"ID utilisateur: \", (user === null || user === void 0 ? void 0 : user.id) || 'Non défini'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 33\n      }\n    }, \"Token localStorage: \", localStorage.getItem('token') ? '✅ Présent' : '❌ Absent'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 33\n      }\n    }, \"Donn\\xE9es utilisateur: \", localStorage.getItem('user') ? '✅ Présentes' : '❌ Absentes'))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      onClick: () => window.location.href = '/login',\n      className: \"btn btn-primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD11 Se Connecter\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: () => window.location.reload(),\n      className: \"btn btn-secondary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 531,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD04 Actualiser\")))));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-system\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"current-user\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDC64 \", (user === null || user === void 0 ? void 0 : user.nom) || 'Utilisateur', \" (ID: \", currentUserId, \")\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-stats\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 25\n    }\n  }, stats.total_messages || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 25\n    }\n  }, \"Messages\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 556,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 25\n    }\n  }, stats.messages_non_lus || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 25\n    }\n  }, \"Non lus\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 25\n    }\n  }, conversations.length), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 25\n    }\n  }, \"Conversations\")))), error && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 21\n    }\n  }, \"\\u274C \", error), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setError(''),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 21\n    }\n  }, \"\\u2715\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-debug\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 572,\n      columnNumber: 25\n    }\n  }, \"Debug: User ID = \", getCurrentUserId(), \", Context = \", user ? 'OK' : 'KO'))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 580,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 25\n    }\n  }, \"Conversations\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"new-conversation-btn\",\n    onClick: startNewConversation,\n    title: \"Nouvelle conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 582,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-list\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 21\n    }\n  }, loading && conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 29\n    }\n  }, \"Chargement...\") : conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-conversations\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 596,\n      columnNumber: 33\n    }\n  }, \"Aucune conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 33\n    }\n  }, \"D\\xE9marrer une conversation\")) : conversations.map(conversation => /*#__PURE__*/React.createElement(\"div\", {\n    key: conversation.contact_id,\n    className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.contact_id) === conversation.contact_id ? 'active' : ''}`,\n    onClick: () => selectConversation(conversation),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 603,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 37\n    }\n  }, conversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 41\n    }\n  }, conversation.contact_nom, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 45\n    }\n  }, conversation.contact_role)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-preview\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 618,\n      columnNumber: 41\n    }\n  }, conversation.dernier_message || 'Aucun message'), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-meta\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 45\n    }\n  }, formatDate(conversation.derniere_activite)), conversation.messages_non_lus > 0 && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"unread-badge\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 626,\n      columnNumber: 49\n    }\n  }, conversation.messages_non_lus))))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 639,\n      columnNumber: 17\n    }\n  }, showNewConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 642,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 643,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setShowNewConversation(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 33\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: selectedUser,\n    onChange: e => setSelectedUser(e.target.value),\n    className: \"user-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur...\"), authorizedUsers.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 41\n    }\n  }, user.nom, \" (\", user.role, \")\"))))) : selectedConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 663,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 33\n    }\n  }, selectedConversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 667,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_nom), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_role)))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-selected\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 678,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 33\n    }\n  }, \"S\\xE9lectionnez une conversation ou d\\xE9marrez-en une nouvelle\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"))), (selectedConversation || showNewConversation) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messages-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 29\n    }\n  }, messages.map(message => {\n    const currentUserId = getCurrentUserId();\n    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n    const messageType = isOwnMessage ? 'sent' : 'received';\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: message.id,\n      className: `message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`,\n      onContextMenu: e => handleContextMenu(e, message),\n      \"data-sender-id\": message.expediteur_id,\n      \"data-receiver-id\": message.destinataire_id,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 699,\n        columnNumber: 41\n      }\n    }, !isOwnMessage && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-sender\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 49\n      }\n    }, message.expediteur_nom || 'Utilisateur'), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-content ${messageType}-content`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 713,\n        columnNumber: 45\n      }\n    }, editingMessage === message.id ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 715,\n        columnNumber: 53\n      }\n    }, /*#__PURE__*/React.createElement(\"textarea\", {\n      value: editContent,\n      onChange: e => setEditContent(e.target.value),\n      className: \"edit-textarea\",\n      autoFocus: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 57\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"edit-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 722,\n        columnNumber: 57\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      onClick: confirmEdit,\n      className: \"confirm-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 61\n      }\n    }, \"\\u2713\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: cancelEditing,\n      className: \"cancel-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 61\n      }\n    }, \"\\u2715\"))) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-text\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 733,\n        columnNumber: 57\n      }\n    }, message.message, message.modifie === '1' && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-edited\",\n      title: `Modifié le ${formatDate(message.date_modification)}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 65\n      }\n    }, \"(modifi\\xE9)\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-time ${messageType}-time`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 57\n      }\n    }, formatDate(message.date_envoi), isOwnMessage && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-status\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 744,\n        columnNumber: 65\n      }\n    }, message.lu === '1' ? '✓✓' : '✓')))), process.env.NODE_ENV === 'development' && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-debug\",\n      title: `Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 755,\n        columnNumber: 49\n      }\n    }, \"\\uD83D\\uDD12\"));\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: messagesEndRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 766,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    value: newMessage,\n    onChange: e => setNewMessage(e.target.value),\n    placeholder: \"Tapez votre message...\",\n    className: \"message-input\",\n    rows: \"1\",\n    onKeyDown: e => {\n      if (e.key === 'Enter' && !e.shiftKey) {\n        e.preventDefault();\n        sendMessage();\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: sendMessage,\n    className: \"send-button\",\n    disabled: !newMessage.trim() || loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE4\")))))), showContextMenu && /*#__PURE__*/React.createElement(\"div\", {\n    ref: contextMenuRef,\n    className: \"context-menu\",\n    style: {\n      left: showContextMenu.x,\n      top: showContextMenu.y\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 797,\n      columnNumber: 17\n    }\n  }, showContextMenu.message.can_modify === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => startEditing(showContextMenu.message),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 806,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_me'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour moi\"), showContextMenu.message.can_delete_for_all === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_everyone'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 814,\n      columnNumber: 25\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour tous\")));\n};\nexport default MessagingSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "AuthContext", "MessagingSystem", "user", "isLoading", "authLoading", "isAuthenticated", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "authorizedUsers", "setAuthorizedUsers", "showNewConversation", "setShowNewConversation", "selected<PERSON>ser", "setSelectedUser", "loading", "setLoading", "error", "setError", "editingMessage", "setEditingMessage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showContextMenu", "setShowContextMenu", "stats", "setStats", "messagesEndRef", "contextMenuRef", "getCurrentUserId", "console", "log", "possibleIds", "id", "user_id", "utilisateur_id", "ID", "User_ID", "isNaN", "parseInt", "storedUser", "localStorage", "getItem", "userData", "JSON", "parse", "warn", "token", "includes", "userId", "split", "pop", "payload", "atob", "sub", "isUserValid", "API_BASE_URL", "scrollToBottom", "current", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "makeAPIRequest", "endpoint", "method", "data", "config", "headers", "body", "stringify", "url", "response", "fetch", "status", "statusText", "result", "json", "success", "Error", "message", "stack", "loadConversations", "_result$data", "currentUserId", "Promise", "resolve", "setTimeout", "retryUserId", "finalUserId", "secureConversations", "filter", "conversation", "contactId", "contact_id", "total_received", "length", "secure_filtered", "user_context", "errorMessage", "localStorage_user", "localStorage_token", "loadMessages", "_result$data2", "secureMessages", "expediteurId", "expediteur_id", "destinataireId", "destinataire_id", "map", "message_type", "is_own_message", "loadAuthorizedUsers", "loadStats", "sendMessage", "trim", "editMessage", "messageId", "newContent", "message_id", "deleteMessage", "deleteType", "delete_type", "selectConversation", "startNewConversation", "handleContextMenu", "e", "preventDefault", "x", "clientX", "y", "clientY", "startEditing", "cancelEditing", "confirmEdit", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "initializeMessaging", "interval", "setInterval", "clearInterval", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "href", "reload", "nom", "total_messages", "messages_non_lus", "title", "key", "contact_nom", "char<PERSON>t", "toUpperCase", "contact_role", "dernier_message", "derniere_activite", "value", "onChange", "role", "Fragment", "isOwnMessage", "messageType", "onContextMenu", "expediteur_nom", "autoFocus", "modifie", "date_modification", "date_envoi", "lu", "process", "env", "NODE_ENV", "ref", "placeholder", "rows", "onKeyDown", "shift<PERSON>ey", "disabled", "style", "left", "top", "can_modify", "can_delete_for_all"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/MessagingSystem.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\n\nconst MessagingSystem = () => {\n    const { user, isLoading: authLoading, isAuthenticated } = useContext(AuthContext);\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [authorizedUsers, setAuthorizedUsers] = useState([]);\n    const [showNewConversation, setShowNewConversation] = useState(false);\n    const [selectedUser, setSelectedUser] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [editingMessage, setEditingMessage] = useState(null);\n    const [editContent, setEditContent] = useState('');\n    const [showContextMenu, setShowContextMenu] = useState(null);\n    const [stats, setStats] = useState({});\n\n    const messagesEndRef = useRef(null);\n    const contextMenuRef = useRef(null);\n\n    // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée\n    const getCurrentUserId = () => {\n        console.log('🔍 Tentative de récupération de l\\'ID utilisateur...');\n        console.log('👤 Contexte user:', user);\n\n        // Essayer d'abord depuis le contexte user avec différentes propriétés possibles\n        if (user) {\n            // Vérifier différentes propriétés possibles pour l'ID\n            const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];\n            for (const id of possibleIds) {\n                if (id && !isNaN(id)) {\n                    console.log('✅ ID trouvé dans le contexte:', id);\n                    return parseInt(id);\n                }\n            }\n        }\n\n        // Essayer depuis localStorage comme fallback\n        try {\n            const storedUser = localStorage.getItem('user');\n            console.log('📦 Données localStorage user:', storedUser);\n\n            if (storedUser) {\n                const userData = JSON.parse(storedUser);\n                console.log('📊 Données utilisateur parsées:', userData);\n\n                if (userData) {\n                    // Vérifier différentes propriétés possibles pour l'ID\n                    const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];\n                    for (const id of possibleIds) {\n                        if (id && !isNaN(id)) {\n                            console.log('✅ ID trouvé dans localStorage:', id);\n                            return parseInt(id);\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn('❌ Erreur lors de la récupération de l\\'utilisateur depuis localStorage:', error);\n        }\n\n        // Dernière tentative avec le token\n        const token = localStorage.getItem('token');\n        console.log('🔑 Token localStorage:', token);\n\n        if (token) {\n            // Essayer différents formats de token\n            if (token.includes('_')) {\n                const userId = token.split('_').pop();\n                if (userId && !isNaN(userId)) {\n                    console.log('✅ ID trouvé dans le token:', userId);\n                    return parseInt(userId);\n                }\n            }\n\n            // Essayer de décoder le token s'il est en base64 ou JWT\n            try {\n                if (token.includes('.')) {\n                    // Format JWT potentiel\n                    const payload = JSON.parse(atob(token.split('.')[1]));\n                    const possibleIds = [payload.id, payload.user_id, payload.utilisateur_id, payload.sub];\n                    for (const id of possibleIds) {\n                        if (id && !isNaN(id)) {\n                            console.log('✅ ID trouvé dans JWT:', id);\n                            return parseInt(id);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.warn('⚠️ Impossible de décoder le token JWT:', error);\n            }\n        }\n\n        console.warn('❌ Aucun ID utilisateur trouvé');\n        return null;\n    };\n\n    // 🔍 Fonction pour vérifier si l'utilisateur est valide\n    const isUserValid = () => {\n        const userId = getCurrentUserId();\n        return userId && userId > 0;\n    };\n    \n    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n    \n    // Scroll automatique vers le bas\n    const scrollToBottom = () => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n        }\n    };\n    \n    useEffect(() => {\n        scrollToBottom();\n    }, [messages]);\n    \n    // Fermer le menu contextuel en cliquant ailleurs\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n                setShowContextMenu(null);\n            }\n        };\n        \n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    \n    // Fonction pour faire des requêtes API\n    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n        try {\n            const token = localStorage.getItem('token') || 'test_user_1';\n\n            const config = {\n                method,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                }\n            };\n\n            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n                config.body = JSON.stringify(data);\n            }\n\n            const url = `${API_BASE_URL}?action=${endpoint}`;\n            console.log('API Request:', { url, method, endpoint, token });\n\n            const response = await fetch(url, config);\n            console.log('API Response Status:', response.status, response.statusText);\n\n            const result = await response.json();\n            console.log('API Response Data:', result);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur API');\n            }\n\n            return result;\n        } catch (error) {\n            console.error('Erreur API complète:', {\n                endpoint,\n                method,\n                error: error.message,\n                stack: error.stack\n            });\n            throw error;\n        }\n    };\n    \n    // Charger les conversations avec confidentialité stricte\n    const loadConversations = async () => {\n        try {\n            setLoading(true);\n            setError(''); // Réinitialiser l'erreur\n\n            // 🔍 Vérification robuste de l'utilisateur\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) {\n                console.warn('� Utilisateur non identifié, tentative de récupération...');\n\n                // Attendre un peu pour que le contexte se charge\n                await new Promise(resolve => setTimeout(resolve, 1000));\n\n                const retryUserId = getCurrentUserId();\n                if (!retryUserId) {\n                    throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n                }\n            }\n\n            const result = await makeAPIRequest('conversations');\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur lors du chargement des conversations');\n            }\n\n            // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n            const finalUserId = getCurrentUserId();\n\n            // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n            const secureConversations = (result.data || []).filter(conversation => {\n                // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n                const contactId = parseInt(conversation.contact_id);\n                return contactId && contactId !== finalUserId && contactId > 0;\n            });\n\n            console.log('🔒 Conversations sécurisées chargées:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureConversations.length,\n                user_id: finalUserId,\n                user_context: user ? 'Disponible' : 'Non disponible'\n            });\n\n            setConversations(secureConversations);\n        } catch (error) {\n            const errorMessage = error.message || 'Erreur inconnue';\n            setError('Impossible de charger les conversations: ' + errorMessage);\n            console.error('🚨 Erreur sécurité conversations:', {\n                error: errorMessage,\n                user_id: getCurrentUserId(),\n                user_context: user,\n                localStorage_user: localStorage.getItem('user'),\n                localStorage_token: localStorage.getItem('token')\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les messages d'une conversation avec confidentialité stricte\n    const loadMessages = async (contactId) => {\n        try {\n            setLoading(true);\n            setError(''); // Réinitialiser l'erreur\n\n            // � Vérification robuste de l'utilisateur\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) {\n                console.warn('🚨 Utilisateur non identifié lors du chargement des messages');\n                throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n            }\n\n            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur lors du chargement des messages');\n            }\n\n            // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n            const finalUserId = getCurrentUserId();\n\n            // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n            const secureMessages = (result.data || []).filter(message => {\n                const expediteurId = parseInt(message.expediteur_id);\n                const destinataireId = parseInt(message.destinataire_id);\n\n                // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n                return (expediteurId === finalUserId || destinataireId === finalUserId);\n            }).map(message => {\n                // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n                const expediteurId = parseInt(message.expediteur_id);\n\n                return {\n                    ...message,\n                    message_type: expediteurId === finalUserId ? 'sent' : 'received',\n                    is_own_message: expediteurId === finalUserId\n                };\n            });\n\n            console.log('🔒 Messages sécurisés chargés:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureMessages.length,\n                user_id: finalUserId,\n                contact_id: contactId,\n                user_context: user ? 'Disponible' : 'Non disponible'\n            });\n\n            setMessages(secureMessages);\n        } catch (error) {\n            const errorMessage = error.message || 'Erreur inconnue';\n            setError('Impossible de charger les messages: ' + errorMessage);\n            console.error('🚨 Erreur sécurité messages:', {\n                error: errorMessage,\n                user_id: getCurrentUserId(),\n                contact_id: contactId,\n                user_context: user,\n                localStorage_user: localStorage.getItem('user'),\n                localStorage_token: localStorage.getItem('token')\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les utilisateurs autorisés\n    const loadAuthorizedUsers = async () => {\n        try {\n            const result = await makeAPIRequest('users');\n            setAuthorizedUsers(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les utilisateurs: ' + error.message);\n        }\n    };\n    \n    // Charger les statistiques\n    const loadStats = async () => {\n        try {\n            const result = await makeAPIRequest('stats');\n            setStats(result.data || {});\n        } catch (error) {\n            console.error('Erreur chargement stats:', error);\n        }\n    };\n    \n    // Envoyer un message\n    const sendMessage = async () => {\n        if (!newMessage.trim()) return;\n        \n        try {\n            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n            \n            if (!destinataireId) {\n                setError('Veuillez sélectionner un destinataire');\n                return;\n            }\n            \n            await makeAPIRequest('send', 'POST', {\n                destinataire_id: destinataireId,\n                message: newMessage.trim()\n            });\n            \n            setNewMessage('');\n            setShowNewConversation(false);\n            \n            // Recharger les conversations et messages\n            await loadConversations();\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible d\\'envoyer le message: ' + error.message);\n        }\n    };\n    \n    // Modifier un message\n    const editMessage = async (messageId, newContent) => {\n        try {\n            await makeAPIRequest('edit', 'PUT', {\n                message_id: messageId,\n                message: newContent\n            });\n            \n            setEditingMessage(null);\n            setEditContent('');\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de modifier le message: ' + error.message);\n        }\n    };\n    \n    // Supprimer un message\n    const deleteMessage = async (messageId, deleteType = 'for_me') => {\n        try {\n            await makeAPIRequest('delete', 'DELETE', {\n                message_id: messageId,\n                delete_type: deleteType\n            });\n            \n            setShowContextMenu(null);\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de supprimer le message: ' + error.message);\n        }\n    };\n    \n    // Sélectionner une conversation\n    const selectConversation = async (conversation) => {\n        setSelectedConversation(conversation);\n        setShowNewConversation(false);\n        await loadMessages(conversation.contact_id);\n    };\n    \n    // Démarrer une nouvelle conversation\n    const startNewConversation = () => {\n        setSelectedConversation(null);\n        setMessages([]);\n        setShowNewConversation(true);\n    };\n    \n    // Gérer le menu contextuel\n    const handleContextMenu = (e, message) => {\n        e.preventDefault();\n        setShowContextMenu({\n            x: e.clientX,\n            y: e.clientY,\n            message: message\n        });\n    };\n    \n    // Démarrer l'édition d'un message\n    const startEditing = (message) => {\n        setEditingMessage(message.id);\n        setEditContent(message.message);\n        setShowContextMenu(null);\n    };\n    \n    // Annuler l'édition\n    const cancelEditing = () => {\n        setEditingMessage(null);\n        setEditContent('');\n    };\n    \n    // Confirmer l'édition\n    const confirmEdit = async () => {\n        if (editContent.trim() && editingMessage) {\n            await editMessage(editingMessage, editContent.trim());\n        }\n    };\n    \n    // Formater la date\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffTime = Math.abs(now - date);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        \n        if (diffDays === 1) {\n            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });\n        } else if (diffDays <= 7) {\n            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });\n        } else {\n            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });\n        }\n    };\n    \n    // Charger les données au montage du composant\n    useEffect(() => {\n        const initializeMessaging = async () => {\n            console.log('🚀 Initialisation du système de messagerie...');\n\n            // Attendre un peu pour que le contexte d'authentification se charge\n            if (!user) {\n                console.log('⏳ Attente du chargement du contexte utilisateur...');\n                await new Promise(resolve => setTimeout(resolve, 500));\n            }\n\n            const userId = getCurrentUserId();\n            console.log('👤 ID utilisateur détecté:', userId);\n\n            if (userId) {\n                console.log('✅ Utilisateur valide, chargement des données...');\n                loadConversations();\n                loadAuthorizedUsers();\n                loadStats();\n            } else {\n                console.warn('⚠️ Aucun utilisateur valide trouvé');\n                setError('Utilisateur non identifié. Veuillez vous reconnecter.');\n\n                // Essayer de charger quand même après un délai\n                setTimeout(() => {\n                    const retryUserId = getCurrentUserId();\n                    if (retryUserId) {\n                        console.log('🔄 Retry réussi, chargement des données...');\n                        setError(''); // Effacer l'erreur\n                        loadConversations();\n                        loadAuthorizedUsers();\n                        loadStats();\n                    }\n                }, 2000);\n            }\n        };\n\n        initializeMessaging();\n    }, [user]);\n    \n    // Actualiser périodiquement\n    useEffect(() => {\n        const interval = setInterval(() => {\n            loadConversations();\n            if (selectedConversation) {\n                loadMessages(selectedConversation.contact_id);\n            }\n        }, 30000); // Actualiser toutes les 30 secondes\n        \n        return () => clearInterval(interval);\n    }, [selectedConversation]);\n    \n    // Vérification de l'utilisateur avant affichage\n    const currentUserId = getCurrentUserId();\n\n    // Affichage d'erreur d'authentification\n    if (!currentUserId && !loading && error.includes('Utilisateur non identifié')) {\n        return (\n            <div className=\"messaging-system\">\n                <div className=\"auth-error-container\">\n                    <div className=\"auth-error-content\">\n                        <h2>🔐 Authentification Requise</h2>\n                        <p>Impossible d'accéder à la messagerie sans identification utilisateur.</p>\n\n                        <div className=\"debug-info\">\n                            <h3>🔍 Informations de Debug :</h3>\n                            <ul>\n                                <li>Contexte utilisateur: {user ? '✅ Chargé' : '❌ Non chargé'}</li>\n                                <li>ID utilisateur: {user?.id || 'Non défini'}</li>\n                                <li>Token localStorage: {localStorage.getItem('token') ? '✅ Présent' : '❌ Absent'}</li>\n                                <li>Données utilisateur: {localStorage.getItem('user') ? '✅ Présentes' : '❌ Absentes'}</li>\n                            </ul>\n                        </div>\n\n                        <div className=\"auth-actions\">\n                            <button\n                                onClick={() => window.location.href = '/login'}\n                                className=\"btn btn-primary\"\n                            >\n                                🔑 Se Connecter\n                            </button>\n                            <button\n                                onClick={() => window.location.reload()}\n                                className=\"btn btn-secondary\"\n                            >\n                                🔄 Actualiser\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"messaging-system\">\n            <div className=\"messaging-header\">\n                <h1>💬 Messagerie</h1>\n                <div className=\"user-info\">\n                    <span className=\"current-user\">👤 {user?.nom || 'Utilisateur'} (ID: {currentUserId})</span>\n                </div>\n                <div className=\"messaging-stats\">\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.total_messages || 0}</span>\n                        <span className=\"stat-label\">Messages</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.messages_non_lus || 0}</span>\n                        <span className=\"stat-label\">Non lus</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{conversations.length}</span>\n                        <span className=\"stat-label\">Conversations</span>\n                    </span>\n                </div>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <span>❌ {error}</span>\n                    <button onClick={() => setError('')}>✕</button>\n                    <div className=\"error-debug\">\n                        <small>Debug: User ID = {getCurrentUserId()}, Context = {user ? 'OK' : 'KO'}</small>\n                    </div>\n                </div>\n            )}\n            \n            <div className=\"messaging-container\">\n                {/* Liste des conversations */}\n                <div className=\"conversations-panel\">\n                    <div className=\"conversations-header\">\n                        <h3>Conversations</h3>\n                        <button \n                            className=\"new-conversation-btn\"\n                            onClick={startNewConversation}\n                            title=\"Nouvelle conversation\"\n                        >\n                            ✏️\n                        </button>\n                    </div>\n                    \n                    <div className=\"conversations-list\">\n                        {loading && conversations.length === 0 ? (\n                            <div className=\"loading\">Chargement...</div>\n                        ) : conversations.length === 0 ? (\n                            <div className=\"no-conversations\">\n                                <p>Aucune conversation</p>\n                                <button onClick={startNewConversation}>\n                                    Démarrer une conversation\n                                </button>\n                            </div>\n                        ) : (\n                            conversations.map(conversation => (\n                                <div\n                                    key={conversation.contact_id}\n                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}\n                                    onClick={() => selectConversation(conversation)}\n                                >\n                                    <div className=\"conversation-avatar\">\n                                        {conversation.contact_nom.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"conversation-info\">\n                                        <div className=\"conversation-name\">\n                                            {conversation.contact_nom}\n                                            <span className=\"conversation-role\">\n                                                {conversation.contact_role}\n                                            </span>\n                                        </div>\n                                        <div className=\"conversation-preview\">\n                                            {conversation.dernier_message || 'Aucun message'}\n                                        </div>\n                                        <div className=\"conversation-meta\">\n                                            <span className=\"conversation-time\">\n                                                {formatDate(conversation.derniere_activite)}\n                                            </span>\n                                            {conversation.messages_non_lus > 0 && (\n                                                <span className=\"unread-badge\">\n                                                    {conversation.messages_non_lus}\n                                                </span>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            ))\n                        )}\n                    </div>\n                </div>\n                \n                {/* Zone de chat */}\n                <div className=\"chat-panel\">\n                    {showNewConversation ? (\n                        <div className=\"new-conversation\">\n                            <div className=\"new-conversation-header\">\n                                <h3>Nouvelle conversation</h3>\n                                <button onClick={() => setShowNewConversation(false)}>✕</button>\n                            </div>\n                            <div className=\"new-conversation-content\">\n                                <select\n                                    value={selectedUser}\n                                    onChange={(e) => setSelectedUser(e.target.value)}\n                                    className=\"user-select\"\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur...</option>\n                                    {authorizedUsers.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} ({user.role})\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n                    ) : selectedConversation ? (\n                        <div className=\"chat-header\">\n                            <div className=\"chat-contact-info\">\n                                <div className=\"chat-avatar\">\n                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}\n                                </div>\n                                <div>\n                                    <div className=\"chat-contact-name\">\n                                        {selectedConversation.contact_nom}\n                                    </div>\n                                    <div className=\"chat-contact-role\">\n                                        {selectedConversation.contact_role}\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    ) : (\n                        <div className=\"no-chat-selected\">\n                            <div className=\"no-chat-content\">\n                                <h3>💬 Messagerie</h3>\n                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>\n                                <button onClick={startNewConversation}>\n                                    Nouvelle conversation\n                                </button>\n                            </div>\n                        </div>\n                    )}\n                    \n                    {/* Messages */}\n                    {(selectedConversation || showNewConversation) && (\n                        <>\n                            <div className=\"messages-container\">\n                                {messages.map(message => {\n                                    const currentUserId = getCurrentUserId();\n                                    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n                                    const messageType = isOwnMessage ? 'sent' : 'received';\n\n                                    return (\n                                        <div\n                                            key={message.id}\n                                            className={`message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`}\n                                            onContextMenu={(e) => handleContextMenu(e, message)}\n                                            data-sender-id={message.expediteur_id}\n                                            data-receiver-id={message.destinataire_id}\n                                        >\n                                            {/* 👤 Affichage du nom de l'expéditeur pour les messages reçus */}\n                                            {!isOwnMessage && (\n                                                <div className=\"message-sender\">\n                                                    {message.expediteur_nom || 'Utilisateur'}\n                                                </div>\n                                            )}\n\n                                            <div className={`message-content ${messageType}-content`}>\n                                                {editingMessage === message.id ? (\n                                                    <div className=\"message-edit\">\n                                                        <textarea\n                                                            value={editContent}\n                                                            onChange={(e) => setEditContent(e.target.value)}\n                                                            className=\"edit-textarea\"\n                                                            autoFocus\n                                                        />\n                                                        <div className=\"edit-actions\">\n                                                            <button onClick={confirmEdit} className=\"confirm-edit\">\n                                                                ✓\n                                                            </button>\n                                                            <button onClick={cancelEditing} className=\"cancel-edit\">\n                                                                ✕\n                                                            </button>\n                                                        </div>\n                                                    </div>\n                                                ) : (\n                                                    <>\n                                                        <div className=\"message-text\">\n                                                            {message.message}\n                                                            {message.modifie === '1' && (\n                                                                <span className=\"message-edited\" title={`Modifié le ${formatDate(message.date_modification)}`}>\n                                                                    (modifié)\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                        <div className={`message-time ${messageType}-time`}>\n                                                            {formatDate(message.date_envoi)}\n                                                            {isOwnMessage && (\n                                                                <span className=\"message-status\">\n                                                                    {message.lu === '1' ? '✓✓' : '✓'}\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                    </>\n                                                )}\n                                            </div>\n\n                                            {/* 🔒 Indicateur de confidentialité (debug) */}\n                                            {process.env.NODE_ENV === 'development' && (\n                                                <div className=\"message-debug\" title={`Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`}>\n                                                    🔒\n                                                </div>\n                                            )}\n                                        </div>\n                                    );\n                                })}\n                                <div ref={messagesEndRef} />\n                            </div>\n                            \n                            {/* Zone de saisie */}\n                            <div className=\"message-input-container\">\n                                <div className=\"message-input-wrapper\">\n                                    <textarea\n                                        value={newMessage}\n                                        onChange={(e) => setNewMessage(e.target.value)}\n                                        placeholder=\"Tapez votre message...\"\n                                        className=\"message-input\"\n                                        rows=\"1\"\n                                        onKeyDown={(e) => {\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                sendMessage();\n                                            }\n                                        }}\n                                    />\n                                    <button\n                                        onClick={sendMessage}\n                                        className=\"send-button\"\n                                        disabled={!newMessage.trim() || loading}\n                                    >\n                                        📤\n                                    </button>\n                                </div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            </div>\n            \n            {/* Menu contextuel */}\n            {showContextMenu && (\n                <div\n                    ref={contextMenuRef}\n                    className=\"context-menu\"\n                    style={{\n                        left: showContextMenu.x,\n                        top: showContextMenu.y\n                    }}\n                >\n                    {showContextMenu.message.can_modify === 1 && (\n                        <button onClick={() => startEditing(showContextMenu.message)}>\n                            ✏️ Modifier\n                        </button>\n                    )}\n                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>\n                        🗑️ Supprimer pour moi\n                    </button>\n                    {showContextMenu.message.can_delete_for_all === 1 && (\n                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>\n                            🗑️ Supprimer pour tous\n                        </button>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default MessagingSystem;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,+BAA+B;AAEtC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC,IAAI;IAAEC,SAAS,EAAEC,WAAW;IAAEC;EAAgB,CAAC,GAAGN,UAAU,CAACC,WAAW,CAAC;EACjF,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtC,MAAMoC,cAAc,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmC,cAAc,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnED,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAElC,IAAI,CAAC;;IAEtC;IACA,IAAIA,IAAI,EAAE;MACN;MACA,MAAMmC,WAAW,GAAG,CAACnC,IAAI,CAACoC,EAAE,EAAEpC,IAAI,CAACqC,OAAO,EAAErC,IAAI,CAACsC,cAAc,EAAEtC,IAAI,CAACuC,EAAE,EAAEvC,IAAI,CAACwC,OAAO,CAAC;MACvF,KAAK,MAAMJ,EAAE,IAAID,WAAW,EAAE;QAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;UAClBH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEE,EAAE,CAAC;UAChD,OAAOM,QAAQ,CAACN,EAAE,CAAC;QACvB;MACJ;IACJ;;IAEA;IACA,IAAI;MACA,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,UAAU,CAAC;MAExD,IAAIA,UAAU,EAAE;QACZ,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACvCV,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEY,QAAQ,CAAC;QAExD,IAAIA,QAAQ,EAAE;UACV;UACA,MAAMX,WAAW,GAAG,CAACW,QAAQ,CAACV,EAAE,EAAEU,QAAQ,CAACT,OAAO,EAAES,QAAQ,CAACR,cAAc,EAAEQ,QAAQ,CAACP,EAAE,EAAEO,QAAQ,CAACN,OAAO,CAAC;UAC3G,KAAK,MAAMJ,EAAE,IAAID,WAAW,EAAE;YAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;cAClBH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEE,EAAE,CAAC;cACjD,OAAOM,QAAQ,CAACN,EAAE,CAAC;YACvB;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACZa,OAAO,CAACgB,IAAI,CAAC,yEAAyE,EAAE7B,KAAK,CAAC;IAClG;;IAEA;IACA,MAAM8B,KAAK,GAAGN,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgB,KAAK,CAAC;IAE5C,IAAIA,KAAK,EAAE;MACP;MACA,IAAIA,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrB,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;QACrC,IAAIF,MAAM,IAAI,CAACX,KAAK,CAACW,MAAM,CAAC,EAAE;UAC1BnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,MAAM,CAAC;UACjD,OAAOV,QAAQ,CAACU,MAAM,CAAC;QAC3B;MACJ;;MAEA;MACA,IAAI;QACA,IAAIF,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;UACrB;UACA,MAAMI,OAAO,GAAGR,IAAI,CAACC,KAAK,CAACQ,IAAI,CAACN,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,MAAMlB,WAAW,GAAG,CAACoB,OAAO,CAACnB,EAAE,EAAEmB,OAAO,CAAClB,OAAO,EAAEkB,OAAO,CAACjB,cAAc,EAAEiB,OAAO,CAACE,GAAG,CAAC;UACtF,KAAK,MAAMrB,EAAE,IAAID,WAAW,EAAE;YAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;cAClBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEE,EAAE,CAAC;cACxC,OAAOM,QAAQ,CAACN,EAAE,CAAC;YACvB;UACJ;QACJ;MACJ,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACZa,OAAO,CAACgB,IAAI,CAAC,wCAAwC,EAAE7B,KAAK,CAAC;MACjE;IACJ;IAEAa,OAAO,CAACgB,IAAI,CAAC,+BAA+B,CAAC;IAC7C,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMN,MAAM,GAAGpB,gBAAgB,CAAC,CAAC;IACjC,OAAOoB,MAAM,IAAIA,MAAM,GAAG,CAAC;EAC/B,CAAC;EAED,MAAMO,YAAY,GAAG,qDAAqD;;EAE1E;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI9B,cAAc,CAAC+B,OAAO,EAAE;MACxB/B,cAAc,CAAC+B,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACjE;EACJ,CAAC;EAEDpE,SAAS,CAAC,MAAM;IACZiE,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACpD,QAAQ,CAAC,CAAC;;EAEd;EACAb,SAAS,CAAC,MAAM;IACZ,MAAMqE,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIlC,cAAc,CAAC8B,OAAO,IAAI,CAAC9B,cAAc,CAAC8B,OAAO,CAACK,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAC1ExC,kBAAkB,CAAC,IAAI,CAAC;MAC5B;IACJ,CAAC;IAEDyC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IACpE,IAAI;MACA,MAAMxB,KAAK,GAAGN,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa;MAE5D,MAAM8B,MAAM,GAAG;QACXF,MAAM;QACNG,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAU1B,KAAK;QACpC;MACJ,CAAC;MAED,IAAIwB,IAAI,KAAKD,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,QAAQ,CAAC,EAAE;QACxEE,MAAM,CAACE,IAAI,GAAG9B,IAAI,CAAC+B,SAAS,CAACJ,IAAI,CAAC;MACtC;MAEA,MAAMK,GAAG,GAAG,GAAGpB,YAAY,WAAWa,QAAQ,EAAE;MAChDvC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;QAAE6C,GAAG;QAAEN,MAAM;QAAED,QAAQ;QAAEtB;MAAM,CAAC,CAAC;MAE7D,MAAM8B,QAAQ,GAAG,MAAMC,KAAK,CAACF,GAAG,EAAEJ,MAAM,CAAC;MACzC1C,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8C,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAEzE,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpCpD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEkD,MAAM,CAAC;MAEzC,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAChE,KAAK,IAAI,YAAY,CAAC;MACjD;MAEA,OAAOgE,MAAM;IACjB,CAAC,CAAC,OAAOhE,KAAK,EAAE;MACZa,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAE;QAClCoD,QAAQ;QACRC,MAAM;QACNrD,KAAK,EAAEA,KAAK,CAACoE,OAAO;QACpBC,KAAK,EAAErE,KAAK,CAACqE;MACjB,CAAC,CAAC;MACF,MAAMrE,KAAK;IACf;EACJ,CAAC;;EAED;EACA,MAAMsE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAC,YAAA;MACAxE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd;MACA,MAAMuE,aAAa,GAAG5D,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAAC4D,aAAa,EAAE;QAChB3D,OAAO,CAACgB,IAAI,CAAC,2DAA2D,CAAC;;QAEzE;QACA,MAAM,IAAI4C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvD,MAAME,WAAW,GAAGhE,gBAAgB,CAAC,CAAC;QACtC,IAAI,CAACgE,WAAW,EAAE;UACd,MAAM,IAAIT,KAAK,CAAC,uDAAuD,CAAC;QAC5E;MACJ;MAEA,MAAMH,MAAM,GAAG,MAAMb,cAAc,CAAC,eAAe,CAAC;MAEpD,IAAI,CAACa,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAChE,KAAK,IAAI,6CAA6C,CAAC;MAClF;;MAEA;MACA,MAAM6E,WAAW,GAAGjE,gBAAgB,CAAC,CAAC;;MAEtC;MACA,MAAMkE,mBAAmB,GAAG,CAACd,MAAM,CAACV,IAAI,IAAI,EAAE,EAAEyB,MAAM,CAACC,YAAY,IAAI;QACnE;QACA,MAAMC,SAAS,GAAG3D,QAAQ,CAAC0D,YAAY,CAACE,UAAU,CAAC;QACnD,OAAOD,SAAS,IAAIA,SAAS,KAAKJ,WAAW,IAAII,SAAS,GAAG,CAAC;MAClE,CAAC,CAAC;MAEFpE,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACjDqE,cAAc,EAAE,EAAAZ,YAAA,GAAAP,MAAM,CAACV,IAAI,cAAAiB,YAAA,uBAAXA,YAAA,CAAaa,MAAM,KAAI,CAAC;QACxCC,eAAe,EAAEP,mBAAmB,CAACM,MAAM;QAC3CnE,OAAO,EAAE4D,WAAW;QACpBS,YAAY,EAAE1G,IAAI,GAAG,YAAY,GAAG;MACxC,CAAC,CAAC;MAEFK,gBAAgB,CAAC6F,mBAAmB,CAAC;IACzC,CAAC,CAAC,OAAO9E,KAAK,EAAE;MACZ,MAAMuF,YAAY,GAAGvF,KAAK,CAACoE,OAAO,IAAI,iBAAiB;MACvDnE,QAAQ,CAAC,2CAA2C,GAAGsF,YAAY,CAAC;MACpE1E,OAAO,CAACb,KAAK,CAAC,mCAAmC,EAAE;QAC/CA,KAAK,EAAEuF,YAAY;QACnBtE,OAAO,EAAEL,gBAAgB,CAAC,CAAC;QAC3B0E,YAAY,EAAE1G,IAAI;QAClB4G,iBAAiB,EAAEhE,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/CgE,kBAAkB,EAAEjE,YAAY,CAACC,OAAO,CAAC,OAAO;MACpD,CAAC,CAAC;IACN,CAAC,SAAS;MACN1B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAM2F,YAAY,GAAG,MAAOT,SAAS,IAAK;IACtC,IAAI;MAAA,IAAAU,aAAA;MACA5F,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd;MACA,MAAMuE,aAAa,GAAG5D,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAAC4D,aAAa,EAAE;QAChB3D,OAAO,CAACgB,IAAI,CAAC,8DAA8D,CAAC;QAC5E,MAAM,IAAIsC,KAAK,CAAC,uDAAuD,CAAC;MAC5E;MAEA,MAAMH,MAAM,GAAG,MAAMb,cAAc,CAAC,uBAAuB8B,SAAS,EAAE,CAAC;MAEvE,IAAI,CAACjB,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAChE,KAAK,IAAI,wCAAwC,CAAC;MAC7E;;MAEA;MACA,MAAM6E,WAAW,GAAGjE,gBAAgB,CAAC,CAAC;;MAEtC;MACA,MAAMgF,cAAc,GAAG,CAAC5B,MAAM,CAACV,IAAI,IAAI,EAAE,EAAEyB,MAAM,CAACX,OAAO,IAAI;QACzD,MAAMyB,YAAY,GAAGvE,QAAQ,CAAC8C,OAAO,CAAC0B,aAAa,CAAC;QACpD,MAAMC,cAAc,GAAGzE,QAAQ,CAAC8C,OAAO,CAAC4B,eAAe,CAAC;;QAExD;QACA,OAAQH,YAAY,KAAKhB,WAAW,IAAIkB,cAAc,KAAKlB,WAAW;MAC1E,CAAC,CAAC,CAACoB,GAAG,CAAC7B,OAAO,IAAI;QACd;QACA,MAAMyB,YAAY,GAAGvE,QAAQ,CAAC8C,OAAO,CAAC0B,aAAa,CAAC;QAEpD,OAAO;UACH,GAAG1B,OAAO;UACV8B,YAAY,EAAEL,YAAY,KAAKhB,WAAW,GAAG,MAAM,GAAG,UAAU;UAChEsB,cAAc,EAAEN,YAAY,KAAKhB;QACrC,CAAC;MACL,CAAC,CAAC;MAEFhE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC1CqE,cAAc,EAAE,EAAAQ,aAAA,GAAA3B,MAAM,CAACV,IAAI,cAAAqC,aAAA,uBAAXA,aAAA,CAAaP,MAAM,KAAI,CAAC;QACxCC,eAAe,EAAEO,cAAc,CAACR,MAAM;QACtCnE,OAAO,EAAE4D,WAAW;QACpBK,UAAU,EAAED,SAAS;QACrBK,YAAY,EAAE1G,IAAI,GAAG,YAAY,GAAG;MACxC,CAAC,CAAC;MAEFS,WAAW,CAACuG,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAO5F,KAAK,EAAE;MACZ,MAAMuF,YAAY,GAAGvF,KAAK,CAACoE,OAAO,IAAI,iBAAiB;MACvDnE,QAAQ,CAAC,sCAAsC,GAAGsF,YAAY,CAAC;MAC/D1E,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAE;QAC1CA,KAAK,EAAEuF,YAAY;QACnBtE,OAAO,EAAEL,gBAAgB,CAAC,CAAC;QAC3BsE,UAAU,EAAED,SAAS;QACrBK,YAAY,EAAE1G,IAAI;QAClB4G,iBAAiB,EAAEhE,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/CgE,kBAAkB,EAAEjE,YAAY,CAACC,OAAO,CAAC,OAAO;MACpD,CAAC,CAAC;IACN,CAAC,SAAS;MACN1B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMqG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,MAAMpC,MAAM,GAAG,MAAMb,cAAc,CAAC,OAAO,CAAC;MAC5C1D,kBAAkB,CAACuE,MAAM,CAACV,IAAI,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACZC,QAAQ,CAAC,0CAA0C,GAAGD,KAAK,CAACoE,OAAO,CAAC;IACxE;EACJ,CAAC;;EAED;EACA,MAAMiC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACA,MAAMrC,MAAM,GAAG,MAAMb,cAAc,CAAC,OAAO,CAAC;MAC5C1C,QAAQ,CAACuD,MAAM,CAACV,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACZa,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;;EAED;EACA,MAAMsG,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAChH,UAAU,CAACiH,IAAI,CAAC,CAAC,EAAE;IAExB,IAAI;MACA,MAAMR,cAAc,GAAG7G,oBAAoB,GAAGA,oBAAoB,CAACgG,UAAU,GAAGtF,YAAY;MAE5F,IAAI,CAACmG,cAAc,EAAE;QACjB9F,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACJ;MAEA,MAAMkD,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;QACjC6C,eAAe,EAAED,cAAc;QAC/B3B,OAAO,EAAE9E,UAAU,CAACiH,IAAI,CAAC;MAC7B,CAAC,CAAC;MAEFhH,aAAa,CAAC,EAAE,CAAC;MACjBI,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA,MAAM2E,iBAAiB,CAAC,CAAC;MACzB,IAAIpF,oBAAoB,EAAE;QACtB,MAAMwG,YAAY,CAACxG,oBAAoB,CAACgG,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACZC,QAAQ,CAAC,oCAAoC,GAAGD,KAAK,CAACoE,OAAO,CAAC;IAClE;EACJ,CAAC;;EAED;EACA,MAAMoC,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,KAAK;IACjD,IAAI;MACA,MAAMvD,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE;QAChCwD,UAAU,EAAEF,SAAS;QACrBrC,OAAO,EAAEsC;MACb,CAAC,CAAC;MAEFvG,iBAAiB,CAAC,IAAI,CAAC;MACvBE,cAAc,CAAC,EAAE,CAAC;;MAElB;MACA,IAAInB,oBAAoB,EAAE;QACtB,MAAMwG,YAAY,CAACxG,oBAAoB,CAACgG,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACZC,QAAQ,CAAC,qCAAqC,GAAGD,KAAK,CAACoE,OAAO,CAAC;IACnE;EACJ,CAAC;;EAED;EACA,MAAMwC,aAAa,GAAG,MAAAA,CAAOH,SAAS,EAAEI,UAAU,GAAG,QAAQ,KAAK;IAC9D,IAAI;MACA,MAAM1D,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACrCwD,UAAU,EAAEF,SAAS;QACrBK,WAAW,EAAED;MACjB,CAAC,CAAC;MAEFtG,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIrB,oBAAoB,EAAE;QACtB,MAAMwG,YAAY,CAACxG,oBAAoB,CAACgG,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAOlF,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAACoE,OAAO,CAAC;IACpE;EACJ,CAAC;;EAED;EACA,MAAM2C,kBAAkB,GAAG,MAAO/B,YAAY,IAAK;IAC/C7F,uBAAuB,CAAC6F,YAAY,CAAC;IACrCrF,sBAAsB,CAAC,KAAK,CAAC;IAC7B,MAAM+F,YAAY,CAACV,YAAY,CAACE,UAAU,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM8B,oBAAoB,GAAGA,CAAA,KAAM;IAC/B7H,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,WAAW,CAAC,EAAE,CAAC;IACfM,sBAAsB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMsH,iBAAiB,GAAGA,CAACC,CAAC,EAAE9C,OAAO,KAAK;IACtC8C,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB5G,kBAAkB,CAAC;MACf6G,CAAC,EAAEF,CAAC,CAACG,OAAO;MACZC,CAAC,EAAEJ,CAAC,CAACK,OAAO;MACZnD,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMoD,YAAY,GAAIpD,OAAO,IAAK;IAC9BjE,iBAAiB,CAACiE,OAAO,CAACpD,EAAE,CAAC;IAC7BX,cAAc,CAAC+D,OAAO,CAACA,OAAO,CAAC;IAC/B7D,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMkH,aAAa,GAAGA,CAAA,KAAM;IACxBtH,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAMqH,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAItH,WAAW,CAACmG,IAAI,CAAC,CAAC,IAAIrG,cAAc,EAAE;MACtC,MAAMsG,WAAW,CAACtG,cAAc,EAAEE,WAAW,CAACmG,IAAI,CAAC,CAAC,CAAC;IACzD;EACJ,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACnF,CAAC,MAAM,IAAIJ,QAAQ,IAAI,CAAC,EAAE;MACtB,OAAON,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEH,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrG,CAAC,MAAM;MACH,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEE,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEL,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrH;EACJ,CAAC;;EAED;EACAhK,SAAS,CAAC,MAAM;IACZ,MAAMqK,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACpC/H,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;MAE5D;MACA,IAAI,CAAClC,IAAI,EAAE;QACPiC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,MAAM,IAAI2D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAC1D;MAEA,MAAM1C,MAAM,GAAGpB,gBAAgB,CAAC,CAAC;MACjCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,MAAM,CAAC;MAEjD,IAAIA,MAAM,EAAE;QACRnB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DwD,iBAAiB,CAAC,CAAC;QACnB8B,mBAAmB,CAAC,CAAC;QACrBC,SAAS,CAAC,CAAC;MACf,CAAC,MAAM;QACHxF,OAAO,CAACgB,IAAI,CAAC,oCAAoC,CAAC;QAClD5B,QAAQ,CAAC,uDAAuD,CAAC;;QAEjE;QACA0E,UAAU,CAAC,MAAM;UACb,MAAMC,WAAW,GAAGhE,gBAAgB,CAAC,CAAC;UACtC,IAAIgE,WAAW,EAAE;YACb/D,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;YACzDb,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YACdqE,iBAAiB,CAAC,CAAC;YACnB8B,mBAAmB,CAAC,CAAC;YACrBC,SAAS,CAAC,CAAC;UACf;QACJ,CAAC,EAAE,IAAI,CAAC;MACZ;IACJ,CAAC;IAEDuC,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAE,CAAChK,IAAI,CAAC,CAAC;;EAEV;EACAL,SAAS,CAAC,MAAM;IACZ,MAAMsK,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BxE,iBAAiB,CAAC,CAAC;MACnB,IAAIpF,oBAAoB,EAAE;QACtBwG,YAAY,CAACxG,oBAAoB,CAACgG,UAAU,CAAC;MACjD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM6D,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAAC3J,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMsF,aAAa,GAAG5D,gBAAgB,CAAC,CAAC;;EAExC;EACA,IAAI,CAAC4D,aAAa,IAAI,CAAC1E,OAAO,IAAIE,KAAK,CAAC+B,QAAQ,CAAC,2BAA2B,CAAC,EAAE;IAC3E,oBACI1D,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC7BlL,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,sBAAsB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACjClL,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/BlL,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,uCAA+B,CAAC,eACpClL,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,6EAAwE,CAAC,eAE5ElL,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,YAAY;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACvBlL,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,sCAA8B,CAAC,eACnClL,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACIlL,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,wBAAsB,EAAC3K,IAAI,GAAG,UAAU,GAAG,cAAmB,CAAC,eACnEP,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,kBAAgB,EAAC,CAAA3K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,EAAE,KAAI,YAAiB,CAAC,eACnD3C,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,sBAAoB,EAAC/H,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,WAAW,GAAG,UAAe,CAAC,eACvFpD,KAAA,CAAA2K,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,0BAAqB,EAAC/H,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG,YAAiB,CAC1F,CACH,CAAC,eAENpD,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBlL,KAAA,CAAA2K,aAAA;MACIQ,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;MAC/CV,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9B,2BAEO,CAAC,eACTlL,KAAA,CAAA2K,aAAA;MACIQ,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACE,MAAM,CAAC,CAAE;MACxCX,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAChC,yBAEO,CACP,CACJ,CACJ,CACJ,CAAC;EAEd;EAEA,oBACIlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BlL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtBlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtBlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAG,EAAC,CAAA3K,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiL,GAAG,KAAI,aAAa,EAAC,QAAM,EAACrF,aAAa,EAAC,GAAO,CACzF,CAAC,eACNnG,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE/I,KAAK,CAACsJ,cAAc,IAAI,CAAQ,CAAC,eAChEzL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CACzC,CAAC,eACPlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE/I,KAAK,CAACuJ,gBAAgB,IAAI,CAAQ,CAAC,eAClE1L,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAa,CACxC,CAAC,eACPlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvK,aAAa,CAACoG,MAAa,CAAC,eAC3D/G,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAmB,CAC9C,CACL,CACJ,CAAC,EAELvJ,KAAK,iBACF3B,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BlL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAE,EAACvJ,KAAY,CAAC,eACtB3B,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAEA,CAAA,KAAMvJ,QAAQ,CAAC,EAAE,CAAE;IAAAiJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAAC,eAC/ClL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBlL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,mBAAiB,EAAC3I,gBAAgB,CAAC,CAAC,EAAC,cAAY,EAAChC,IAAI,GAAG,IAAI,GAAG,IAAY,CAClF,CACJ,CACR,eAEDP,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhClL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChClL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjClL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,eAAiB,CAAC,eACtBlL,KAAA,CAAA2K,aAAA;IACIC,SAAS,EAAC,sBAAsB;IAChCO,OAAO,EAAExC,oBAAqB;IAC9BgD,KAAK,EAAC,uBAAuB;IAAAd,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CAAC,eAENlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BzJ,OAAO,IAAId,aAAa,CAACoG,MAAM,KAAK,CAAC,gBAClC/G,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAkB,CAAC,GAC5CvK,aAAa,CAACoG,MAAM,KAAK,CAAC,gBAC1B/G,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BlL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qBAAsB,CAAC,eAC1BlL,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAExC,oBAAqB;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAE/B,CACP,CAAC,GAENvK,aAAa,CAACiH,GAAG,CAACjB,YAAY,iBAC1B3G,KAAA,CAAA2K,aAAA;IACIiB,GAAG,EAAEjF,YAAY,CAACE,UAAW;IAC7B+D,SAAS,EAAE,qBAAqB,CAAA/J,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEgG,UAAU,MAAKF,YAAY,CAACE,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC/GsE,OAAO,EAAEA,CAAA,KAAMzC,kBAAkB,CAAC/B,YAAY,CAAE;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhDlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BvE,YAAY,CAACkF,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC/C,CAAC,eACN/L,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BvE,YAAY,CAACkF,WAAW,eACzB7L,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BvE,YAAY,CAACqF,YACZ,CACL,CAAC,eACNhM,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCvE,YAAY,CAACsF,eAAe,IAAI,eAChC,CAAC,eACNjM,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BlL,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B5B,UAAU,CAAC3C,YAAY,CAACuF,iBAAiB,CACxC,CAAC,EACNvF,YAAY,CAAC+E,gBAAgB,GAAG,CAAC,iBAC9B1L,KAAA,CAAA2K,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBvE,YAAY,CAAC+E,gBACZ,CAET,CACJ,CACJ,CACR,CAEJ,CACJ,CAAC,eAGN1L,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtB7J,mBAAmB,gBAChBrB,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpClL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAyB,CAAC,eAC9BlL,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAEA,CAAA,KAAM7J,sBAAsB,CAAC,KAAK,CAAE;IAAAuJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC9D,CAAC,eACNlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrClL,KAAA,CAAA2K,aAAA;IACIwB,KAAK,EAAE5K,YAAa;IACpB6K,QAAQ,EAAGvD,CAAC,IAAKrH,eAAe,CAACqH,CAAC,CAACnE,MAAM,CAACyH,KAAK,CAAE;IACjDvB,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBlL,KAAA,CAAA2K,aAAA;IAAQwB,KAAK,EAAC,EAAE;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAsC,CAAC,EACvD/J,eAAe,CAACyG,GAAG,CAACrH,IAAI,iBACrBP,KAAA,CAAA2K,aAAA;IAAQiB,GAAG,EAAErL,IAAI,CAACoC,EAAG;IAACwJ,KAAK,EAAE5L,IAAI,CAACoC,EAAG;IAAAkI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC3K,IAAI,CAACiL,GAAG,EAAC,IAAE,EAACjL,IAAI,CAAC8L,IAAI,EAAC,GACnB,CACX,CACG,CACP,CACJ,CAAC,GACNxL,oBAAoB,gBACpBb,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvBrK,oBAAoB,CAACgL,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACvD,CAAC,eACN/L,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BrK,oBAAoB,CAACgL,WACrB,CAAC,eACN7L,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BrK,oBAAoB,CAACmL,YACrB,CACJ,CACJ,CACJ,CAAC,gBAENhM,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BlL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtBlL,KAAA,CAAA2K,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,iEAA4D,CAAC,eAChElL,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAExC,oBAAqB;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAE/B,CACP,CACJ,CACR,EAGA,CAACrK,oBAAoB,IAAIQ,mBAAmB,kBACzCrB,KAAA,CAAA2K,aAAA,CAAA3K,KAAA,CAAAsM,QAAA,qBACItM,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BnK,QAAQ,CAAC6G,GAAG,CAAC7B,OAAO,IAAI;IACrB,MAAMI,aAAa,GAAG5D,gBAAgB,CAAC,CAAC;IACxC,MAAMgK,YAAY,GAAGtJ,QAAQ,CAAC8C,OAAO,CAAC0B,aAAa,CAAC,KAAKtB,aAAa;IACtE,MAAMqG,WAAW,GAAGD,YAAY,GAAG,MAAM,GAAG,UAAU;IAEtD,oBACIvM,KAAA,CAAA2K,aAAA;MACIiB,GAAG,EAAE7F,OAAO,CAACpD,EAAG;MAChBiI,SAAS,EAAE,WAAW4B,WAAW,IAAID,YAAY,GAAG,aAAa,GAAG,eAAe,EAAG;MACtFE,aAAa,EAAG5D,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAE9C,OAAO,CAAE;MACpD,kBAAgBA,OAAO,CAAC0B,aAAc;MACtC,oBAAkB1B,OAAO,CAAC4B,eAAgB;MAAAkD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAGzC,CAACqB,YAAY,iBACVvM,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1BnF,OAAO,CAAC2G,cAAc,IAAI,aAC1B,CACR,eAED1M,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAE,mBAAmB4B,WAAW,UAAW;MAAA3B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACpDrJ,cAAc,KAAKkE,OAAO,CAACpD,EAAE,gBAC1B3C,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBlL,KAAA,CAAA2K,aAAA;MACIwB,KAAK,EAAEpK,WAAY;MACnBqK,QAAQ,EAAGvD,CAAC,IAAK7G,cAAc,CAAC6G,CAAC,CAACnE,MAAM,CAACyH,KAAK,CAAE;MAChDvB,SAAS,EAAC,eAAe;MACzB+B,SAAS;MAAA9B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACZ,CAAC,eACFlL,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBlL,KAAA,CAAA2K,aAAA;MAAQQ,OAAO,EAAE9B,WAAY;MAACuB,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAE/C,CAAC,eACTlL,KAAA,CAAA2K,aAAA;MAAQQ,OAAO,EAAE/B,aAAc;MAACwB,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAEhD,CACP,CACJ,CAAC,gBAENlL,KAAA,CAAA2K,aAAA,CAAA3K,KAAA,CAAAsM,QAAA,qBACItM,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACxBnF,OAAO,CAACA,OAAO,EACfA,OAAO,CAAC6G,OAAO,KAAK,GAAG,iBACpB5M,KAAA,CAAA2K,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAACe,KAAK,EAAE,cAAcrC,UAAU,CAACvD,OAAO,CAAC8G,iBAAiB,CAAC,EAAG;MAAAhC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEzF,CAET,CAAC,eACNlL,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAE,gBAAgB4B,WAAW,OAAQ;MAAA3B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9C5B,UAAU,CAACvD,OAAO,CAAC+G,UAAU,CAAC,EAC9BP,YAAY,iBACTvM,KAAA,CAAA2K,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC3BnF,OAAO,CAACgH,EAAE,KAAK,GAAG,GAAG,IAAI,GAAG,GAC3B,CAET,CACP,CAEL,CAAC,EAGLC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACnClN,KAAA,CAAA2K,aAAA;MAAKC,SAAS,EAAC,eAAe;MAACe,KAAK,EAAE,eAAe5F,OAAO,CAAC0B,aAAa,mBAAmB1B,OAAO,CAAC4B,eAAe,EAAG;MAAAkD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEnH,CAER,CAAC;EAEd,CAAC,CAAC,eACFlL,KAAA,CAAA2K,aAAA;IAAKwC,GAAG,EAAE9K,cAAe;IAAAwI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC1B,CAAC,eAGNlL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpClL,KAAA,CAAA2K,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClClL,KAAA,CAAA2K,aAAA;IACIwB,KAAK,EAAElL,UAAW;IAClBmL,QAAQ,EAAGvD,CAAC,IAAK3H,aAAa,CAAC2H,CAAC,CAACnE,MAAM,CAACyH,KAAK,CAAE;IAC/CiB,WAAW,EAAC,wBAAwB;IACpCxC,SAAS,EAAC,eAAe;IACzByC,IAAI,EAAC,GAAG;IACRC,SAAS,EAAGzE,CAAC,IAAK;MACd,IAAIA,CAAC,CAAC+C,GAAG,KAAK,OAAO,IAAI,CAAC/C,CAAC,CAAC0E,QAAQ,EAAE;QAClC1E,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBb,WAAW,CAAC,CAAC;MACjB;IACJ,CAAE;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACFlL,KAAA,CAAA2K,aAAA;IACIQ,OAAO,EAAElD,WAAY;IACrB2C,SAAS,EAAC,aAAa;IACvB4C,QAAQ,EAAE,CAACvM,UAAU,CAACiH,IAAI,CAAC,CAAC,IAAIzG,OAAQ;IAAAoJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3C,cAEO,CACP,CACJ,CACP,CAEL,CACJ,CAAC,EAGLjJ,eAAe,iBACZjC,KAAA,CAAA2K,aAAA;IACIwC,GAAG,EAAE7K,cAAe;IACpBsI,SAAS,EAAC,cAAc;IACxB6C,KAAK,EAAE;MACHC,IAAI,EAAEzL,eAAe,CAAC8G,CAAC;MACvB4E,GAAG,EAAE1L,eAAe,CAACgH;IACzB,CAAE;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDjJ,eAAe,CAAC8D,OAAO,CAAC6H,UAAU,KAAK,CAAC,iBACrC5N,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAAClH,eAAe,CAAC8D,OAAO,CAAE;IAAA8E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAEtD,CACX,eACDlL,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAEA,CAAA,KAAM5C,aAAa,CAACtG,eAAe,CAAC8D,OAAO,CAACpD,EAAE,EAAE,QAAQ,CAAE;IAAAkI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uCAEpE,CAAC,EACRjJ,eAAe,CAAC8D,OAAO,CAAC8H,kBAAkB,KAAK,CAAC,iBAC7C7N,KAAA,CAAA2K,aAAA;IAAQQ,OAAO,EAAEA,CAAA,KAAM5C,aAAa,CAACtG,eAAe,CAAC8D,OAAO,CAACpD,EAAE,EAAE,cAAc,CAAE;IAAAkI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wCAE1E,CAEX,CAER,CAAC;AAEd,CAAC;AAED,eAAe5K,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}