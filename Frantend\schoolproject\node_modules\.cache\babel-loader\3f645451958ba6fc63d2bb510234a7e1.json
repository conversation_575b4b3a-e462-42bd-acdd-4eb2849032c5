{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\MessagingSystem.js\";\nimport React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\nconst MessagingSystem = () => {\n  const {\n    user,\n    isLoading: authLoading,\n    isAuthenticated\n  } = useContext(AuthContext);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [authorizedUsers, setAuthorizedUsers] = useState([]);\n  const [showNewConversation, setShowNewConversation] = useState(false);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [showContextMenu, setShowContextMenu] = useState(null);\n  const [stats, setStats] = useState({});\n  const messagesEndRef = useRef(null);\n  const contextMenuRef = useRef(null);\n\n  // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée\n  const getCurrentUserId = () => {\n    // Essayer d'abord depuis le contexte user\n    if (user && user.id) {\n      return parseInt(user.id);\n    }\n\n    // Essayer depuis localStorage comme fallback\n    try {\n      const storedUser = localStorage.getItem('user');\n      if (storedUser) {\n        const userData = JSON.parse(storedUser);\n        if (userData && userData.id) {\n          return parseInt(userData.id);\n        }\n      }\n    } catch (error) {\n      console.warn('Erreur lors de la récupération de l\\'utilisateur depuis localStorage:', error);\n    }\n\n    // Dernière tentative avec le token\n    const token = localStorage.getItem('token');\n    if (token && token.includes('_')) {\n      const userId = token.split('_').pop();\n      if (userId && !isNaN(userId)) {\n        return parseInt(userId);\n      }\n    }\n    return null;\n  };\n\n  // 🔍 Fonction pour vérifier si l'utilisateur est valide\n  const isUserValid = () => {\n    const userId = getCurrentUserId();\n    return userId && userId > 0;\n  };\n  const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n\n  // Scroll automatique vers le bas\n  const scrollToBottom = () => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fermer le menu contextuel en cliquant ailleurs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n        setShowContextMenu(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fonction pour faire des requêtes API\n  const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n    try {\n      const token = localStorage.getItem('token') || 'test_user_1';\n      const config = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      };\n      if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n        config.body = JSON.stringify(data);\n      }\n      const url = `${API_BASE_URL}?action=${endpoint}`;\n      console.log('API Request:', {\n        url,\n        method,\n        endpoint,\n        token\n      });\n      const response = await fetch(url, config);\n      console.log('API Response Status:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('API Response Data:', result);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur API');\n      }\n      return result;\n    } catch (error) {\n      console.error('Erreur API complète:', {\n        endpoint,\n        method,\n        error: error.message,\n        stack: error.stack\n      });\n      throw error;\n    }\n  };\n\n  // Charger les conversations avec confidentialité stricte\n  const loadConversations = async () => {\n    try {\n      var _result$data;\n      setLoading(true);\n      setError(''); // Réinitialiser l'erreur\n\n      // 🔍 Vérification robuste de l'utilisateur\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) {\n        console.warn('� Utilisateur non identifié, tentative de récupération...');\n\n        // Attendre un peu pour que le contexte se charge\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        const retryUserId = getCurrentUserId();\n        if (!retryUserId) {\n          throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n        }\n      }\n      const result = await makeAPIRequest('conversations');\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur lors du chargement des conversations');\n      }\n\n      // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n      const finalUserId = getCurrentUserId();\n\n      // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n      const secureConversations = (result.data || []).filter(conversation => {\n        // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n        const contactId = parseInt(conversation.contact_id);\n        return contactId && contactId !== finalUserId && contactId > 0;\n      });\n      console.log('🔒 Conversations sécurisées chargées:', {\n        total_received: ((_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.length) || 0,\n        secure_filtered: secureConversations.length,\n        user_id: finalUserId,\n        user_context: user ? 'Disponible' : 'Non disponible'\n      });\n      setConversations(secureConversations);\n    } catch (error) {\n      const errorMessage = error.message || 'Erreur inconnue';\n      setError('Impossible de charger les conversations: ' + errorMessage);\n      console.error('🚨 Erreur sécurité conversations:', {\n        error: errorMessage,\n        user_id: getCurrentUserId(),\n        user_context: user,\n        localStorage_user: localStorage.getItem('user'),\n        localStorage_token: localStorage.getItem('token')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les messages d'une conversation avec confidentialité stricte\n  const loadMessages = async contactId => {\n    try {\n      var _result$data2;\n      setLoading(true);\n      setError(''); // Réinitialiser l'erreur\n\n      // � Vérification robuste de l'utilisateur\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) {\n        console.warn('🚨 Utilisateur non identifié lors du chargement des messages');\n        throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n      }\n      const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur lors du chargement des messages');\n      }\n\n      // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n      const finalUserId = getCurrentUserId();\n\n      // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n      const secureMessages = (result.data || []).filter(message => {\n        const expediteurId = parseInt(message.expediteur_id);\n        const destinataireId = parseInt(message.destinataire_id);\n\n        // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n        return expediteurId === finalUserId || destinataireId === finalUserId;\n      }).map(message => {\n        // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n        const expediteurId = parseInt(message.expediteur_id);\n        return {\n          ...message,\n          message_type: expediteurId === finalUserId ? 'sent' : 'received',\n          is_own_message: expediteurId === finalUserId\n        };\n      });\n      console.log('🔒 Messages sécurisés chargés:', {\n        total_received: ((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : _result$data2.length) || 0,\n        secure_filtered: secureMessages.length,\n        user_id: finalUserId,\n        contact_id: contactId,\n        user_context: user ? 'Disponible' : 'Non disponible'\n      });\n      setMessages(secureMessages);\n    } catch (error) {\n      const errorMessage = error.message || 'Erreur inconnue';\n      setError('Impossible de charger les messages: ' + errorMessage);\n      console.error('🚨 Erreur sécurité messages:', {\n        error: errorMessage,\n        user_id: getCurrentUserId(),\n        contact_id: contactId,\n        user_context: user,\n        localStorage_user: localStorage.getItem('user'),\n        localStorage_token: localStorage.getItem('token')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les utilisateurs autorisés\n  const loadAuthorizedUsers = async () => {\n    try {\n      const result = await makeAPIRequest('users');\n      setAuthorizedUsers(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les utilisateurs: ' + error.message);\n    }\n  };\n\n  // Charger les statistiques\n  const loadStats = async () => {\n    try {\n      const result = await makeAPIRequest('stats');\n      setStats(result.data || {});\n    } catch (error) {\n      console.error('Erreur chargement stats:', error);\n    }\n  };\n\n  // Envoyer un message\n  const sendMessage = async () => {\n    if (!newMessage.trim()) return;\n    try {\n      const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n      if (!destinataireId) {\n        setError('Veuillez sélectionner un destinataire');\n        return;\n      }\n      await makeAPIRequest('send', 'POST', {\n        destinataire_id: destinataireId,\n        message: newMessage.trim()\n      });\n      setNewMessage('');\n      setShowNewConversation(false);\n\n      // Recharger les conversations et messages\n      await loadConversations();\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible d\\'envoyer le message: ' + error.message);\n    }\n  };\n\n  // Modifier un message\n  const editMessage = async (messageId, newContent) => {\n    try {\n      await makeAPIRequest('edit', 'PUT', {\n        message_id: messageId,\n        message: newContent\n      });\n      setEditingMessage(null);\n      setEditContent('');\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de modifier le message: ' + error.message);\n    }\n  };\n\n  // Supprimer un message\n  const deleteMessage = async (messageId, deleteType = 'for_me') => {\n    try {\n      await makeAPIRequest('delete', 'DELETE', {\n        message_id: messageId,\n        delete_type: deleteType\n      });\n      setShowContextMenu(null);\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de supprimer le message: ' + error.message);\n    }\n  };\n\n  // Sélectionner une conversation\n  const selectConversation = async conversation => {\n    setSelectedConversation(conversation);\n    setShowNewConversation(false);\n    await loadMessages(conversation.contact_id);\n  };\n\n  // Démarrer une nouvelle conversation\n  const startNewConversation = () => {\n    setSelectedConversation(null);\n    setMessages([]);\n    setShowNewConversation(true);\n  };\n\n  // Gérer le menu contextuel\n  const handleContextMenu = (e, message) => {\n    e.preventDefault();\n    setShowContextMenu({\n      x: e.clientX,\n      y: e.clientY,\n      message: message\n    });\n  };\n\n  // Démarrer l'édition d'un message\n  const startEditing = message => {\n    setEditingMessage(message.id);\n    setEditContent(message.message);\n    setShowContextMenu(null);\n  };\n\n  // Annuler l'édition\n  const cancelEditing = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  // Confirmer l'édition\n  const confirmEdit = async () => {\n    if (editContent.trim() && editingMessage) {\n      await editMessage(editingMessage, editContent.trim());\n    }\n  };\n\n  // Formater la date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffDays <= 7) {\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n\n  // Charger les données au montage du composant\n  useEffect(() => {\n    // Vérifier que l'utilisateur est connecté avant de charger les données\n    if (user && user.id) {\n      loadConversations();\n      loadAuthorizedUsers();\n      loadStats();\n    } else {\n      console.warn('Utilisateur non connecté, chargement des données de test...');\n      // Charger quand même pour les tests\n      loadConversations();\n      loadAuthorizedUsers();\n      loadStats();\n    }\n  }, [user]);\n\n  // Actualiser périodiquement\n  useEffect(() => {\n    const interval = setInterval(() => {\n      loadConversations();\n      if (selectedConversation) {\n        loadMessages(selectedConversation.contact_id);\n      }\n    }, 30000); // Actualiser toutes les 30 secondes\n\n    return () => clearInterval(interval);\n  }, [selectedConversation]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-system\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 434,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-stats\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 438,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 25\n    }\n  }, stats.total_messages || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 25\n    }\n  }, \"Messages\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 25\n    }\n  }, stats.messages_non_lus || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 25\n    }\n  }, \"Non lus\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 25\n    }\n  }, conversations.length), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 25\n    }\n  }, \"Conversations\")))), error && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 21\n    }\n  }, \"\\u274C \", error), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setError(''),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 21\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 25\n    }\n  }, \"Conversations\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"new-conversation-btn\",\n    onClick: startNewConversation,\n    title: \"Nouvelle conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-list\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 21\n    }\n  }, loading && conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 29\n    }\n  }, \"Chargement...\") : conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-conversations\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 33\n    }\n  }, \"Aucune conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 33\n    }\n  }, \"D\\xE9marrer une conversation\")) : conversations.map(conversation => /*#__PURE__*/React.createElement(\"div\", {\n    key: conversation.contact_id,\n    className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.contact_id) === conversation.contact_id ? 'active' : ''}`,\n    onClick: () => selectConversation(conversation),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 37\n    }\n  }, conversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 41\n    }\n  }, conversation.contact_nom, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 45\n    }\n  }, conversation.contact_role)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-preview\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 501,\n      columnNumber: 41\n    }\n  }, conversation.dernier_message || 'Aucun message'), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-meta\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 45\n    }\n  }, formatDate(conversation.derniere_activite)), conversation.messages_non_lus > 0 && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"unread-badge\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 509,\n      columnNumber: 49\n    }\n  }, conversation.messages_non_lus))))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 17\n    }\n  }, showNewConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 525,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setShowNewConversation(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 33\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: selectedUser,\n    onChange: e => setSelectedUser(e.target.value),\n    className: \"user-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur...\"), authorizedUsers.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 41\n    }\n  }, user.nom, \" (\", user.role, \")\"))))) : selectedConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 33\n    }\n  }, selectedConversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_nom), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_role)))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-selected\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 33\n    }\n  }, \"S\\xE9lectionnez une conversation ou d\\xE9marrez-en une nouvelle\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"))), (selectedConversation || showNewConversation) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messages-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 29\n    }\n  }, messages.map(message => {\n    const currentUserId = getCurrentUserId();\n    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n    const messageType = isOwnMessage ? 'sent' : 'received';\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: message.id,\n      className: `message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`,\n      onContextMenu: e => handleContextMenu(e, message),\n      \"data-sender-id\": message.expediteur_id,\n      \"data-receiver-id\": message.destinataire_id,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 41\n      }\n    }, !isOwnMessage && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-sender\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 49\n      }\n    }, message.expediteur_nom || 'Utilisateur'), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-content ${messageType}-content`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 596,\n        columnNumber: 45\n      }\n    }, editingMessage === message.id ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 598,\n        columnNumber: 53\n      }\n    }, /*#__PURE__*/React.createElement(\"textarea\", {\n      value: editContent,\n      onChange: e => setEditContent(e.target.value),\n      className: \"edit-textarea\",\n      autoFocus: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 57\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"edit-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 605,\n        columnNumber: 57\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      onClick: confirmEdit,\n      className: \"confirm-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 61\n      }\n    }, \"\\u2713\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: cancelEditing,\n      className: \"cancel-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 609,\n        columnNumber: 61\n      }\n    }, \"\\u2715\"))) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-text\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 57\n      }\n    }, message.message, message.modifie === '1' && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-edited\",\n      title: `Modifié le ${formatDate(message.date_modification)}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 65\n      }\n    }, \"(modifi\\xE9)\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-time ${messageType}-time`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 57\n      }\n    }, formatDate(message.date_envoi), isOwnMessage && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-status\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 627,\n        columnNumber: 65\n      }\n    }, message.lu === '1' ? '✓✓' : '✓')))), process.env.NODE_ENV === 'development' && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-debug\",\n      title: `Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 49\n      }\n    }, \"\\uD83D\\uDD12\"));\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: messagesEndRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 650,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    value: newMessage,\n    onChange: e => setNewMessage(e.target.value),\n    placeholder: \"Tapez votre message...\",\n    className: \"message-input\",\n    rows: \"1\",\n    onKeyDown: e => {\n      if (e.key === 'Enter' && !e.shiftKey) {\n        e.preventDefault();\n        sendMessage();\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: sendMessage,\n    className: \"send-button\",\n    disabled: !newMessage.trim() || loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 664,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE4\")))))), showContextMenu && /*#__PURE__*/React.createElement(\"div\", {\n    ref: contextMenuRef,\n    className: \"context-menu\",\n    style: {\n      left: showContextMenu.x,\n      top: showContextMenu.y\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 17\n    }\n  }, showContextMenu.message.can_modify === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => startEditing(showContextMenu.message),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 689,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_me'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour moi\"), showContextMenu.message.can_delete_for_all === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_everyone'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 25\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour tous\")));\n};\nexport default MessagingSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "AuthContext", "MessagingSystem", "user", "isLoading", "authLoading", "isAuthenticated", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "authorizedUsers", "setAuthorizedUsers", "showNewConversation", "setShowNewConversation", "selected<PERSON>ser", "setSelectedUser", "loading", "setLoading", "error", "setError", "editingMessage", "setEditingMessage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showContextMenu", "setShowContextMenu", "stats", "setStats", "messagesEndRef", "contextMenuRef", "getCurrentUserId", "id", "parseInt", "storedUser", "localStorage", "getItem", "userData", "JSON", "parse", "console", "warn", "token", "includes", "userId", "split", "pop", "isNaN", "isUserValid", "API_BASE_URL", "scrollToBottom", "current", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "makeAPIRequest", "endpoint", "method", "data", "config", "headers", "body", "stringify", "url", "log", "response", "fetch", "status", "statusText", "result", "json", "success", "Error", "message", "stack", "loadConversations", "_result$data", "currentUserId", "Promise", "resolve", "setTimeout", "retryUserId", "finalUserId", "secureConversations", "filter", "conversation", "contactId", "contact_id", "total_received", "length", "secure_filtered", "user_id", "user_context", "errorMessage", "localStorage_user", "localStorage_token", "loadMessages", "_result$data2", "secureMessages", "expediteurId", "expediteur_id", "destinataireId", "destinataire_id", "map", "message_type", "is_own_message", "loadAuthorizedUsers", "loadStats", "sendMessage", "trim", "editMessage", "messageId", "newContent", "message_id", "deleteMessage", "deleteType", "delete_type", "selectConversation", "startNewConversation", "handleContextMenu", "e", "preventDefault", "x", "clientX", "y", "clientY", "startEditing", "cancelEditing", "confirmEdit", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "interval", "setInterval", "clearInterval", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "total_messages", "messages_non_lus", "onClick", "title", "key", "contact_nom", "char<PERSON>t", "toUpperCase", "contact_role", "dernier_message", "derniere_activite", "value", "onChange", "nom", "role", "Fragment", "isOwnMessage", "messageType", "onContextMenu", "expediteur_nom", "autoFocus", "modifie", "date_modification", "date_envoi", "lu", "process", "env", "NODE_ENV", "ref", "placeholder", "rows", "onKeyDown", "shift<PERSON>ey", "disabled", "style", "left", "top", "can_modify", "can_delete_for_all"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/MessagingSystem.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\n\nconst MessagingSystem = () => {\n    const { user, isLoading: authLoading, isAuthenticated } = useContext(AuthContext);\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [authorizedUsers, setAuthorizedUsers] = useState([]);\n    const [showNewConversation, setShowNewConversation] = useState(false);\n    const [selectedUser, setSelectedUser] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [editingMessage, setEditingMessage] = useState(null);\n    const [editContent, setEditContent] = useState('');\n    const [showContextMenu, setShowContextMenu] = useState(null);\n    const [stats, setStats] = useState({});\n\n    const messagesEndRef = useRef(null);\n    const contextMenuRef = useRef(null);\n\n    // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée\n    const getCurrentUserId = () => {\n        // Essayer d'abord depuis le contexte user\n        if (user && user.id) {\n            return parseInt(user.id);\n        }\n\n        // Essayer depuis localStorage comme fallback\n        try {\n            const storedUser = localStorage.getItem('user');\n            if (storedUser) {\n                const userData = JSON.parse(storedUser);\n                if (userData && userData.id) {\n                    return parseInt(userData.id);\n                }\n            }\n        } catch (error) {\n            console.warn('Erreur lors de la récupération de l\\'utilisateur depuis localStorage:', error);\n        }\n\n        // Dernière tentative avec le token\n        const token = localStorage.getItem('token');\n        if (token && token.includes('_')) {\n            const userId = token.split('_').pop();\n            if (userId && !isNaN(userId)) {\n                return parseInt(userId);\n            }\n        }\n\n        return null;\n    };\n\n    // 🔍 Fonction pour vérifier si l'utilisateur est valide\n    const isUserValid = () => {\n        const userId = getCurrentUserId();\n        return userId && userId > 0;\n    };\n    \n    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n    \n    // Scroll automatique vers le bas\n    const scrollToBottom = () => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n        }\n    };\n    \n    useEffect(() => {\n        scrollToBottom();\n    }, [messages]);\n    \n    // Fermer le menu contextuel en cliquant ailleurs\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n                setShowContextMenu(null);\n            }\n        };\n        \n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    \n    // Fonction pour faire des requêtes API\n    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n        try {\n            const token = localStorage.getItem('token') || 'test_user_1';\n\n            const config = {\n                method,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                }\n            };\n\n            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n                config.body = JSON.stringify(data);\n            }\n\n            const url = `${API_BASE_URL}?action=${endpoint}`;\n            console.log('API Request:', { url, method, endpoint, token });\n\n            const response = await fetch(url, config);\n            console.log('API Response Status:', response.status, response.statusText);\n\n            const result = await response.json();\n            console.log('API Response Data:', result);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur API');\n            }\n\n            return result;\n        } catch (error) {\n            console.error('Erreur API complète:', {\n                endpoint,\n                method,\n                error: error.message,\n                stack: error.stack\n            });\n            throw error;\n        }\n    };\n    \n    // Charger les conversations avec confidentialité stricte\n    const loadConversations = async () => {\n        try {\n            setLoading(true);\n            setError(''); // Réinitialiser l'erreur\n\n            // 🔍 Vérification robuste de l'utilisateur\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) {\n                console.warn('� Utilisateur non identifié, tentative de récupération...');\n\n                // Attendre un peu pour que le contexte se charge\n                await new Promise(resolve => setTimeout(resolve, 1000));\n\n                const retryUserId = getCurrentUserId();\n                if (!retryUserId) {\n                    throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n                }\n            }\n\n            const result = await makeAPIRequest('conversations');\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur lors du chargement des conversations');\n            }\n\n            // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n            const finalUserId = getCurrentUserId();\n\n            // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n            const secureConversations = (result.data || []).filter(conversation => {\n                // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n                const contactId = parseInt(conversation.contact_id);\n                return contactId && contactId !== finalUserId && contactId > 0;\n            });\n\n            console.log('🔒 Conversations sécurisées chargées:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureConversations.length,\n                user_id: finalUserId,\n                user_context: user ? 'Disponible' : 'Non disponible'\n            });\n\n            setConversations(secureConversations);\n        } catch (error) {\n            const errorMessage = error.message || 'Erreur inconnue';\n            setError('Impossible de charger les conversations: ' + errorMessage);\n            console.error('🚨 Erreur sécurité conversations:', {\n                error: errorMessage,\n                user_id: getCurrentUserId(),\n                user_context: user,\n                localStorage_user: localStorage.getItem('user'),\n                localStorage_token: localStorage.getItem('token')\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les messages d'une conversation avec confidentialité stricte\n    const loadMessages = async (contactId) => {\n        try {\n            setLoading(true);\n            setError(''); // Réinitialiser l'erreur\n\n            // � Vérification robuste de l'utilisateur\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) {\n                console.warn('🚨 Utilisateur non identifié lors du chargement des messages');\n                throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n            }\n\n            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur lors du chargement des messages');\n            }\n\n            // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n            const finalUserId = getCurrentUserId();\n\n            // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n            const secureMessages = (result.data || []).filter(message => {\n                const expediteurId = parseInt(message.expediteur_id);\n                const destinataireId = parseInt(message.destinataire_id);\n\n                // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n                return (expediteurId === finalUserId || destinataireId === finalUserId);\n            }).map(message => {\n                // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n                const expediteurId = parseInt(message.expediteur_id);\n\n                return {\n                    ...message,\n                    message_type: expediteurId === finalUserId ? 'sent' : 'received',\n                    is_own_message: expediteurId === finalUserId\n                };\n            });\n\n            console.log('🔒 Messages sécurisés chargés:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureMessages.length,\n                user_id: finalUserId,\n                contact_id: contactId,\n                user_context: user ? 'Disponible' : 'Non disponible'\n            });\n\n            setMessages(secureMessages);\n        } catch (error) {\n            const errorMessage = error.message || 'Erreur inconnue';\n            setError('Impossible de charger les messages: ' + errorMessage);\n            console.error('🚨 Erreur sécurité messages:', {\n                error: errorMessage,\n                user_id: getCurrentUserId(),\n                contact_id: contactId,\n                user_context: user,\n                localStorage_user: localStorage.getItem('user'),\n                localStorage_token: localStorage.getItem('token')\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les utilisateurs autorisés\n    const loadAuthorizedUsers = async () => {\n        try {\n            const result = await makeAPIRequest('users');\n            setAuthorizedUsers(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les utilisateurs: ' + error.message);\n        }\n    };\n    \n    // Charger les statistiques\n    const loadStats = async () => {\n        try {\n            const result = await makeAPIRequest('stats');\n            setStats(result.data || {});\n        } catch (error) {\n            console.error('Erreur chargement stats:', error);\n        }\n    };\n    \n    // Envoyer un message\n    const sendMessage = async () => {\n        if (!newMessage.trim()) return;\n        \n        try {\n            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n            \n            if (!destinataireId) {\n                setError('Veuillez sélectionner un destinataire');\n                return;\n            }\n            \n            await makeAPIRequest('send', 'POST', {\n                destinataire_id: destinataireId,\n                message: newMessage.trim()\n            });\n            \n            setNewMessage('');\n            setShowNewConversation(false);\n            \n            // Recharger les conversations et messages\n            await loadConversations();\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible d\\'envoyer le message: ' + error.message);\n        }\n    };\n    \n    // Modifier un message\n    const editMessage = async (messageId, newContent) => {\n        try {\n            await makeAPIRequest('edit', 'PUT', {\n                message_id: messageId,\n                message: newContent\n            });\n            \n            setEditingMessage(null);\n            setEditContent('');\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de modifier le message: ' + error.message);\n        }\n    };\n    \n    // Supprimer un message\n    const deleteMessage = async (messageId, deleteType = 'for_me') => {\n        try {\n            await makeAPIRequest('delete', 'DELETE', {\n                message_id: messageId,\n                delete_type: deleteType\n            });\n            \n            setShowContextMenu(null);\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de supprimer le message: ' + error.message);\n        }\n    };\n    \n    // Sélectionner une conversation\n    const selectConversation = async (conversation) => {\n        setSelectedConversation(conversation);\n        setShowNewConversation(false);\n        await loadMessages(conversation.contact_id);\n    };\n    \n    // Démarrer une nouvelle conversation\n    const startNewConversation = () => {\n        setSelectedConversation(null);\n        setMessages([]);\n        setShowNewConversation(true);\n    };\n    \n    // Gérer le menu contextuel\n    const handleContextMenu = (e, message) => {\n        e.preventDefault();\n        setShowContextMenu({\n            x: e.clientX,\n            y: e.clientY,\n            message: message\n        });\n    };\n    \n    // Démarrer l'édition d'un message\n    const startEditing = (message) => {\n        setEditingMessage(message.id);\n        setEditContent(message.message);\n        setShowContextMenu(null);\n    };\n    \n    // Annuler l'édition\n    const cancelEditing = () => {\n        setEditingMessage(null);\n        setEditContent('');\n    };\n    \n    // Confirmer l'édition\n    const confirmEdit = async () => {\n        if (editContent.trim() && editingMessage) {\n            await editMessage(editingMessage, editContent.trim());\n        }\n    };\n    \n    // Formater la date\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffTime = Math.abs(now - date);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        \n        if (diffDays === 1) {\n            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });\n        } else if (diffDays <= 7) {\n            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });\n        } else {\n            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });\n        }\n    };\n    \n    // Charger les données au montage du composant\n    useEffect(() => {\n        // Vérifier que l'utilisateur est connecté avant de charger les données\n        if (user && user.id) {\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n        } else {\n            console.warn('Utilisateur non connecté, chargement des données de test...');\n            // Charger quand même pour les tests\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n        }\n    }, [user]);\n    \n    // Actualiser périodiquement\n    useEffect(() => {\n        const interval = setInterval(() => {\n            loadConversations();\n            if (selectedConversation) {\n                loadMessages(selectedConversation.contact_id);\n            }\n        }, 30000); // Actualiser toutes les 30 secondes\n        \n        return () => clearInterval(interval);\n    }, [selectedConversation]);\n    \n    return (\n        <div className=\"messaging-system\">\n            <div className=\"messaging-header\">\n                <h1>💬 Messagerie</h1>\n                <div className=\"messaging-stats\">\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.total_messages || 0}</span>\n                        <span className=\"stat-label\">Messages</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.messages_non_lus || 0}</span>\n                        <span className=\"stat-label\">Non lus</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{conversations.length}</span>\n                        <span className=\"stat-label\">Conversations</span>\n                    </span>\n                </div>\n            </div>\n            \n            {error && (\n                <div className=\"error-message\">\n                    <span>❌ {error}</span>\n                    <button onClick={() => setError('')}>✕</button>\n                </div>\n            )}\n            \n            <div className=\"messaging-container\">\n                {/* Liste des conversations */}\n                <div className=\"conversations-panel\">\n                    <div className=\"conversations-header\">\n                        <h3>Conversations</h3>\n                        <button \n                            className=\"new-conversation-btn\"\n                            onClick={startNewConversation}\n                            title=\"Nouvelle conversation\"\n                        >\n                            ✏️\n                        </button>\n                    </div>\n                    \n                    <div className=\"conversations-list\">\n                        {loading && conversations.length === 0 ? (\n                            <div className=\"loading\">Chargement...</div>\n                        ) : conversations.length === 0 ? (\n                            <div className=\"no-conversations\">\n                                <p>Aucune conversation</p>\n                                <button onClick={startNewConversation}>\n                                    Démarrer une conversation\n                                </button>\n                            </div>\n                        ) : (\n                            conversations.map(conversation => (\n                                <div\n                                    key={conversation.contact_id}\n                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}\n                                    onClick={() => selectConversation(conversation)}\n                                >\n                                    <div className=\"conversation-avatar\">\n                                        {conversation.contact_nom.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"conversation-info\">\n                                        <div className=\"conversation-name\">\n                                            {conversation.contact_nom}\n                                            <span className=\"conversation-role\">\n                                                {conversation.contact_role}\n                                            </span>\n                                        </div>\n                                        <div className=\"conversation-preview\">\n                                            {conversation.dernier_message || 'Aucun message'}\n                                        </div>\n                                        <div className=\"conversation-meta\">\n                                            <span className=\"conversation-time\">\n                                                {formatDate(conversation.derniere_activite)}\n                                            </span>\n                                            {conversation.messages_non_lus > 0 && (\n                                                <span className=\"unread-badge\">\n                                                    {conversation.messages_non_lus}\n                                                </span>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            ))\n                        )}\n                    </div>\n                </div>\n                \n                {/* Zone de chat */}\n                <div className=\"chat-panel\">\n                    {showNewConversation ? (\n                        <div className=\"new-conversation\">\n                            <div className=\"new-conversation-header\">\n                                <h3>Nouvelle conversation</h3>\n                                <button onClick={() => setShowNewConversation(false)}>✕</button>\n                            </div>\n                            <div className=\"new-conversation-content\">\n                                <select\n                                    value={selectedUser}\n                                    onChange={(e) => setSelectedUser(e.target.value)}\n                                    className=\"user-select\"\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur...</option>\n                                    {authorizedUsers.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} ({user.role})\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n                    ) : selectedConversation ? (\n                        <div className=\"chat-header\">\n                            <div className=\"chat-contact-info\">\n                                <div className=\"chat-avatar\">\n                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}\n                                </div>\n                                <div>\n                                    <div className=\"chat-contact-name\">\n                                        {selectedConversation.contact_nom}\n                                    </div>\n                                    <div className=\"chat-contact-role\">\n                                        {selectedConversation.contact_role}\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    ) : (\n                        <div className=\"no-chat-selected\">\n                            <div className=\"no-chat-content\">\n                                <h3>💬 Messagerie</h3>\n                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>\n                                <button onClick={startNewConversation}>\n                                    Nouvelle conversation\n                                </button>\n                            </div>\n                        </div>\n                    )}\n                    \n                    {/* Messages */}\n                    {(selectedConversation || showNewConversation) && (\n                        <>\n                            <div className=\"messages-container\">\n                                {messages.map(message => {\n                                    const currentUserId = getCurrentUserId();\n                                    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n                                    const messageType = isOwnMessage ? 'sent' : 'received';\n\n                                    return (\n                                        <div\n                                            key={message.id}\n                                            className={`message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`}\n                                            onContextMenu={(e) => handleContextMenu(e, message)}\n                                            data-sender-id={message.expediteur_id}\n                                            data-receiver-id={message.destinataire_id}\n                                        >\n                                            {/* 👤 Affichage du nom de l'expéditeur pour les messages reçus */}\n                                            {!isOwnMessage && (\n                                                <div className=\"message-sender\">\n                                                    {message.expediteur_nom || 'Utilisateur'}\n                                                </div>\n                                            )}\n\n                                            <div className={`message-content ${messageType}-content`}>\n                                                {editingMessage === message.id ? (\n                                                    <div className=\"message-edit\">\n                                                        <textarea\n                                                            value={editContent}\n                                                            onChange={(e) => setEditContent(e.target.value)}\n                                                            className=\"edit-textarea\"\n                                                            autoFocus\n                                                        />\n                                                        <div className=\"edit-actions\">\n                                                            <button onClick={confirmEdit} className=\"confirm-edit\">\n                                                                ✓\n                                                            </button>\n                                                            <button onClick={cancelEditing} className=\"cancel-edit\">\n                                                                ✕\n                                                            </button>\n                                                        </div>\n                                                    </div>\n                                                ) : (\n                                                    <>\n                                                        <div className=\"message-text\">\n                                                            {message.message}\n                                                            {message.modifie === '1' && (\n                                                                <span className=\"message-edited\" title={`Modifié le ${formatDate(message.date_modification)}`}>\n                                                                    (modifié)\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                        <div className={`message-time ${messageType}-time`}>\n                                                            {formatDate(message.date_envoi)}\n                                                            {isOwnMessage && (\n                                                                <span className=\"message-status\">\n                                                                    {message.lu === '1' ? '✓✓' : '✓'}\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                    </>\n                                                )}\n                                            </div>\n\n                                            {/* 🔒 Indicateur de confidentialité (debug) */}\n                                            {process.env.NODE_ENV === 'development' && (\n                                                <div className=\"message-debug\" title={`Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`}>\n                                                    🔒\n                                                </div>\n                                            )}\n                                        </div>\n                                    );\n                                })}\n                                <div ref={messagesEndRef} />\n                            </div>\n                            \n                            {/* Zone de saisie */}\n                            <div className=\"message-input-container\">\n                                <div className=\"message-input-wrapper\">\n                                    <textarea\n                                        value={newMessage}\n                                        onChange={(e) => setNewMessage(e.target.value)}\n                                        placeholder=\"Tapez votre message...\"\n                                        className=\"message-input\"\n                                        rows=\"1\"\n                                        onKeyDown={(e) => {\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                sendMessage();\n                                            }\n                                        }}\n                                    />\n                                    <button\n                                        onClick={sendMessage}\n                                        className=\"send-button\"\n                                        disabled={!newMessage.trim() || loading}\n                                    >\n                                        📤\n                                    </button>\n                                </div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            </div>\n            \n            {/* Menu contextuel */}\n            {showContextMenu && (\n                <div\n                    ref={contextMenuRef}\n                    className=\"context-menu\"\n                    style={{\n                        left: showContextMenu.x,\n                        top: showContextMenu.y\n                    }}\n                >\n                    {showContextMenu.message.can_modify === 1 && (\n                        <button onClick={() => startEditing(showContextMenu.message)}>\n                            ✏️ Modifier\n                        </button>\n                    )}\n                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>\n                        🗑️ Supprimer pour moi\n                    </button>\n                    {showContextMenu.message.can_delete_for_all === 1 && (\n                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>\n                            🗑️ Supprimer pour tous\n                        </button>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default MessagingSystem;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,+BAA+B;AAEtC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC,IAAI;IAAEC,SAAS,EAAEC,WAAW;IAAEC;EAAgB,CAAC,GAAGN,UAAU,CAACC,WAAW,CAAC;EACjF,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtC,MAAMoC,cAAc,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmC,cAAc,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B;IACA,IAAIhC,IAAI,IAAIA,IAAI,CAACiC,EAAE,EAAE;MACjB,OAAOC,QAAQ,CAAClC,IAAI,CAACiC,EAAE,CAAC;IAC5B;;IAEA;IACA,IAAI;MACA,MAAME,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAIF,UAAU,EAAE;QACZ,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACvC,IAAIG,QAAQ,IAAIA,QAAQ,CAACL,EAAE,EAAE;UACzB,OAAOC,QAAQ,CAACI,QAAQ,CAACL,EAAE,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC,OAAOb,KAAK,EAAE;MACZqB,OAAO,CAACC,IAAI,CAAC,uEAAuE,EAAEtB,KAAK,CAAC;IAChG;;IAEA;IACA,MAAMuB,KAAK,GAAGP,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIM,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC9B,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MACrC,IAAIF,MAAM,IAAI,CAACG,KAAK,CAACH,MAAM,CAAC,EAAE;QAC1B,OAAOX,QAAQ,CAACW,MAAM,CAAC;MAC3B;IACJ;IAEA,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMJ,MAAM,GAAGb,gBAAgB,CAAC,CAAC;IACjC,OAAOa,MAAM,IAAIA,MAAM,GAAG,CAAC;EAC/B,CAAC;EAED,MAAMK,YAAY,GAAG,qDAAqD;;EAE1E;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIrB,cAAc,CAACsB,OAAO,EAAE;MACxBtB,cAAc,CAACsB,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACjE;EACJ,CAAC;EAED3D,SAAS,CAAC,MAAM;IACZwD,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC3C,QAAQ,CAAC,CAAC;;EAEd;EACAb,SAAS,CAAC,MAAM;IACZ,MAAM4D,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIzB,cAAc,CAACqB,OAAO,IAAI,CAACrB,cAAc,CAACqB,OAAO,CAACK,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAC1E/B,kBAAkB,CAAC,IAAI,CAAC;MAC5B;IACJ,CAAC;IAEDgC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IACpE,IAAI;MACA,MAAMtB,KAAK,GAAGP,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa;MAE5D,MAAM6B,MAAM,GAAG;QACXF,MAAM;QACNG,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUxB,KAAK;QACpC;MACJ,CAAC;MAED,IAAIsB,IAAI,KAAKD,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,QAAQ,CAAC,EAAE;QACxEE,MAAM,CAACE,IAAI,GAAG7B,IAAI,CAAC8B,SAAS,CAACJ,IAAI,CAAC;MACtC;MAEA,MAAMK,GAAG,GAAG,GAAGpB,YAAY,WAAWa,QAAQ,EAAE;MAChDtB,OAAO,CAAC8B,GAAG,CAAC,cAAc,EAAE;QAAED,GAAG;QAAEN,MAAM;QAAED,QAAQ;QAAEpB;MAAM,CAAC,CAAC;MAE7D,MAAM6B,QAAQ,GAAG,MAAMC,KAAK,CAACH,GAAG,EAAEJ,MAAM,CAAC;MACzCzB,OAAO,CAAC8B,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAEzE,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpCpC,OAAO,CAAC8B,GAAG,CAAC,oBAAoB,EAAEK,MAAM,CAAC;MAEzC,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAACxD,KAAK,IAAI,YAAY,CAAC;MACjD;MAEA,OAAOwD,MAAM;IACjB,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACZqB,OAAO,CAACrB,KAAK,CAAC,sBAAsB,EAAE;QAClC2C,QAAQ;QACRC,MAAM;QACN5C,KAAK,EAAEA,KAAK,CAAC4D,OAAO;QACpBC,KAAK,EAAE7D,KAAK,CAAC6D;MACjB,CAAC,CAAC;MACF,MAAM7D,KAAK;IACf;EACJ,CAAC;;EAED;EACA,MAAM8D,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAC,YAAA;MACAhE,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd;MACA,MAAM+D,aAAa,GAAGpD,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACoD,aAAa,EAAE;QAChB3C,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;;QAEzE;QACA,MAAM,IAAI2C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvD,MAAME,WAAW,GAAGxD,gBAAgB,CAAC,CAAC;QACtC,IAAI,CAACwD,WAAW,EAAE;UACd,MAAM,IAAIT,KAAK,CAAC,uDAAuD,CAAC;QAC5E;MACJ;MAEA,MAAMH,MAAM,GAAG,MAAMd,cAAc,CAAC,eAAe,CAAC;MAEpD,IAAI,CAACc,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAACxD,KAAK,IAAI,6CAA6C,CAAC;MAClF;;MAEA;MACA,MAAMqE,WAAW,GAAGzD,gBAAgB,CAAC,CAAC;;MAEtC;MACA,MAAM0D,mBAAmB,GAAG,CAACd,MAAM,CAACX,IAAI,IAAI,EAAE,EAAE0B,MAAM,CAACC,YAAY,IAAI;QACnE;QACA,MAAMC,SAAS,GAAG3D,QAAQ,CAAC0D,YAAY,CAACE,UAAU,CAAC;QACnD,OAAOD,SAAS,IAAIA,SAAS,KAAKJ,WAAW,IAAII,SAAS,GAAG,CAAC;MAClE,CAAC,CAAC;MAEFpD,OAAO,CAAC8B,GAAG,CAAC,uCAAuC,EAAE;QACjDwB,cAAc,EAAE,EAAAZ,YAAA,GAAAP,MAAM,CAACX,IAAI,cAAAkB,YAAA,uBAAXA,YAAA,CAAaa,MAAM,KAAI,CAAC;QACxCC,eAAe,EAAEP,mBAAmB,CAACM,MAAM;QAC3CE,OAAO,EAAET,WAAW;QACpBU,YAAY,EAAEnG,IAAI,GAAG,YAAY,GAAG;MACxC,CAAC,CAAC;MAEFK,gBAAgB,CAACqF,mBAAmB,CAAC;IACzC,CAAC,CAAC,OAAOtE,KAAK,EAAE;MACZ,MAAMgF,YAAY,GAAGhF,KAAK,CAAC4D,OAAO,IAAI,iBAAiB;MACvD3D,QAAQ,CAAC,2CAA2C,GAAG+E,YAAY,CAAC;MACpE3D,OAAO,CAACrB,KAAK,CAAC,mCAAmC,EAAE;QAC/CA,KAAK,EAAEgF,YAAY;QACnBF,OAAO,EAAElE,gBAAgB,CAAC,CAAC;QAC3BmE,YAAY,EAAEnG,IAAI;QAClBqG,iBAAiB,EAAEjE,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/CiE,kBAAkB,EAAElE,YAAY,CAACC,OAAO,CAAC,OAAO;MACpD,CAAC,CAAC;IACN,CAAC,SAAS;MACNlB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMoF,YAAY,GAAG,MAAOV,SAAS,IAAK;IACtC,IAAI;MAAA,IAAAW,aAAA;MACArF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd;MACA,MAAM+D,aAAa,GAAGpD,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACoD,aAAa,EAAE;QAChB3C,OAAO,CAACC,IAAI,CAAC,8DAA8D,CAAC;QAC5E,MAAM,IAAIqC,KAAK,CAAC,uDAAuD,CAAC;MAC5E;MAEA,MAAMH,MAAM,GAAG,MAAMd,cAAc,CAAC,uBAAuB+B,SAAS,EAAE,CAAC;MAEvE,IAAI,CAACjB,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAACxD,KAAK,IAAI,wCAAwC,CAAC;MAC7E;;MAEA;MACA,MAAMqE,WAAW,GAAGzD,gBAAgB,CAAC,CAAC;;MAEtC;MACA,MAAMyE,cAAc,GAAG,CAAC7B,MAAM,CAACX,IAAI,IAAI,EAAE,EAAE0B,MAAM,CAACX,OAAO,IAAI;QACzD,MAAM0B,YAAY,GAAGxE,QAAQ,CAAC8C,OAAO,CAAC2B,aAAa,CAAC;QACpD,MAAMC,cAAc,GAAG1E,QAAQ,CAAC8C,OAAO,CAAC6B,eAAe,CAAC;;QAExD;QACA,OAAQH,YAAY,KAAKjB,WAAW,IAAImB,cAAc,KAAKnB,WAAW;MAC1E,CAAC,CAAC,CAACqB,GAAG,CAAC9B,OAAO,IAAI;QACd;QACA,MAAM0B,YAAY,GAAGxE,QAAQ,CAAC8C,OAAO,CAAC2B,aAAa,CAAC;QAEpD,OAAO;UACH,GAAG3B,OAAO;UACV+B,YAAY,EAAEL,YAAY,KAAKjB,WAAW,GAAG,MAAM,GAAG,UAAU;UAChEuB,cAAc,EAAEN,YAAY,KAAKjB;QACrC,CAAC;MACL,CAAC,CAAC;MAEFhD,OAAO,CAAC8B,GAAG,CAAC,gCAAgC,EAAE;QAC1CwB,cAAc,EAAE,EAAAS,aAAA,GAAA5B,MAAM,CAACX,IAAI,cAAAuC,aAAA,uBAAXA,aAAA,CAAaR,MAAM,KAAI,CAAC;QACxCC,eAAe,EAAEQ,cAAc,CAACT,MAAM;QACtCE,OAAO,EAAET,WAAW;QACpBK,UAAU,EAAED,SAAS;QACrBM,YAAY,EAAEnG,IAAI,GAAG,YAAY,GAAG;MACxC,CAAC,CAAC;MAEFS,WAAW,CAACgG,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOrF,KAAK,EAAE;MACZ,MAAMgF,YAAY,GAAGhF,KAAK,CAAC4D,OAAO,IAAI,iBAAiB;MACvD3D,QAAQ,CAAC,sCAAsC,GAAG+E,YAAY,CAAC;MAC/D3D,OAAO,CAACrB,KAAK,CAAC,8BAA8B,EAAE;QAC1CA,KAAK,EAAEgF,YAAY;QACnBF,OAAO,EAAElE,gBAAgB,CAAC,CAAC;QAC3B8D,UAAU,EAAED,SAAS;QACrBM,YAAY,EAAEnG,IAAI;QAClBqG,iBAAiB,EAAEjE,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/CiE,kBAAkB,EAAElE,YAAY,CAACC,OAAO,CAAC,OAAO;MACpD,CAAC,CAAC;IACN,CAAC,SAAS;MACNlB,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAM8F,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,MAAMrC,MAAM,GAAG,MAAMd,cAAc,CAAC,OAAO,CAAC;MAC5CjD,kBAAkB,CAAC+D,MAAM,CAACX,IAAI,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACZC,QAAQ,CAAC,0CAA0C,GAAGD,KAAK,CAAC4D,OAAO,CAAC;IACxE;EACJ,CAAC;;EAED;EACA,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACA,MAAMtC,MAAM,GAAG,MAAMd,cAAc,CAAC,OAAO,CAAC;MAC5CjC,QAAQ,CAAC+C,MAAM,CAACX,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAO7C,KAAK,EAAE;MACZqB,OAAO,CAACrB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;;EAED;EACA,MAAM+F,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACzG,UAAU,CAAC0G,IAAI,CAAC,CAAC,EAAE;IAExB,IAAI;MACA,MAAMR,cAAc,GAAGtG,oBAAoB,GAAGA,oBAAoB,CAACwF,UAAU,GAAG9E,YAAY;MAE5F,IAAI,CAAC4F,cAAc,EAAE;QACjBvF,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACJ;MAEA,MAAMyC,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;QACjC+C,eAAe,EAAED,cAAc;QAC/B5B,OAAO,EAAEtE,UAAU,CAAC0G,IAAI,CAAC;MAC7B,CAAC,CAAC;MAEFzG,aAAa,CAAC,EAAE,CAAC;MACjBI,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA,MAAMmE,iBAAiB,CAAC,CAAC;MACzB,IAAI5E,oBAAoB,EAAE;QACtB,MAAMiG,YAAY,CAACjG,oBAAoB,CAACwF,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACZC,QAAQ,CAAC,oCAAoC,GAAGD,KAAK,CAAC4D,OAAO,CAAC;IAClE;EACJ,CAAC;;EAED;EACA,MAAMqC,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,KAAK;IACjD,IAAI;MACA,MAAMzD,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE;QAChC0D,UAAU,EAAEF,SAAS;QACrBtC,OAAO,EAAEuC;MACb,CAAC,CAAC;MAEFhG,iBAAiB,CAAC,IAAI,CAAC;MACvBE,cAAc,CAAC,EAAE,CAAC;;MAElB;MACA,IAAInB,oBAAoB,EAAE;QACtB,MAAMiG,YAAY,CAACjG,oBAAoB,CAACwF,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACZC,QAAQ,CAAC,qCAAqC,GAAGD,KAAK,CAAC4D,OAAO,CAAC;IACnE;EACJ,CAAC;;EAED;EACA,MAAMyC,aAAa,GAAG,MAAAA,CAAOH,SAAS,EAAEI,UAAU,GAAG,QAAQ,KAAK;IAC9D,IAAI;MACA,MAAM5D,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACrC0D,UAAU,EAAEF,SAAS;QACrBK,WAAW,EAAED;MACjB,CAAC,CAAC;MAEF/F,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIrB,oBAAoB,EAAE;QACtB,MAAMiG,YAAY,CAACjG,oBAAoB,CAACwF,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO1E,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAAC4D,OAAO,CAAC;IACpE;EACJ,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAG,MAAOhC,YAAY,IAAK;IAC/CrF,uBAAuB,CAACqF,YAAY,CAAC;IACrC7E,sBAAsB,CAAC,KAAK,CAAC;IAC7B,MAAMwF,YAAY,CAACX,YAAY,CAACE,UAAU,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM+B,oBAAoB,GAAGA,CAAA,KAAM;IAC/BtH,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,WAAW,CAAC,EAAE,CAAC;IACfM,sBAAsB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAM+G,iBAAiB,GAAGA,CAACC,CAAC,EAAE/C,OAAO,KAAK;IACtC+C,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBrG,kBAAkB,CAAC;MACfsG,CAAC,EAAEF,CAAC,CAACG,OAAO;MACZC,CAAC,EAAEJ,CAAC,CAACK,OAAO;MACZpD,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMqD,YAAY,GAAIrD,OAAO,IAAK;IAC9BzD,iBAAiB,CAACyD,OAAO,CAAC/C,EAAE,CAAC;IAC7BR,cAAc,CAACuD,OAAO,CAACA,OAAO,CAAC;IAC/BrD,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM2G,aAAa,GAAGA,CAAA,KAAM;IACxB/G,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAM8G,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI/G,WAAW,CAAC4F,IAAI,CAAC,CAAC,IAAI9F,cAAc,EAAE;MACtC,MAAM+F,WAAW,CAAC/F,cAAc,EAAEE,WAAW,CAAC4F,IAAI,CAAC,CAAC,CAAC;IACzD;EACJ,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACnF,CAAC,MAAM,IAAIJ,QAAQ,IAAI,CAAC,EAAE;MACtB,OAAON,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEH,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrG,CAAC,MAAM;MACH,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEE,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEL,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrH;EACJ,CAAC;;EAED;EACAzJ,SAAS,CAAC,MAAM;IACZ;IACA,IAAIK,IAAI,IAAIA,IAAI,CAACiC,EAAE,EAAE;MACjBiD,iBAAiB,CAAC,CAAC;MACnB+B,mBAAmB,CAAC,CAAC;MACrBC,SAAS,CAAC,CAAC;IACf,CAAC,MAAM;MACHzE,OAAO,CAACC,IAAI,CAAC,6DAA6D,CAAC;MAC3E;MACAwC,iBAAiB,CAAC,CAAC;MACnB+B,mBAAmB,CAAC,CAAC;MACrBC,SAAS,CAAC,CAAC;IACf;EACJ,CAAC,EAAE,CAAClH,IAAI,CAAC,CAAC;;EAEV;EACAL,SAAS,CAAC,MAAM;IACZ,MAAM8J,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BxE,iBAAiB,CAAC,CAAC;MACnB,IAAI5E,oBAAoB,EAAE;QACtBiG,YAAY,CAACjG,oBAAoB,CAACwF,UAAU,CAAC;MACjD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM6D,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACnJ,oBAAoB,CAAC,CAAC;EAE1B,oBACIb,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtB1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvI,KAAK,CAACwI,cAAc,IAAI,CAAQ,CAAC,eAChE3K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CACzC,CAAC,eACP1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEvI,KAAK,CAACyI,gBAAgB,IAAI,CAAQ,CAAC,eAClE5K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAa,CACxC,CAAC,eACP1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE/J,aAAa,CAAC4F,MAAa,CAAC,eAC3DvG,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAmB,CAC9C,CACL,CACJ,CAAC,EAEL/I,KAAK,iBACF3B,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAE,EAAC/I,KAAY,CAAC,eACtB3B,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAMjJ,QAAQ,CAAC,EAAE,CAAE;IAAAyI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC7C,CACR,eAED1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhC1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChC1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,eAAiB,CAAC,eACtB1K,KAAA,CAAAmK,aAAA;IACIC,SAAS,EAAC,sBAAsB;IAChCS,OAAO,EAAEzC,oBAAqB;IAC9B0C,KAAK,EAAC,uBAAuB;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CAAC,eAEN1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BjJ,OAAO,IAAId,aAAa,CAAC4F,MAAM,KAAK,CAAC,gBAClCvG,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAkB,CAAC,GAC5C/J,aAAa,CAAC4F,MAAM,KAAK,CAAC,gBAC1BvG,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qBAAsB,CAAC,eAC1B1K,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEzC,oBAAqB;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAE/B,CACP,CAAC,GAEN/J,aAAa,CAAC0G,GAAG,CAAClB,YAAY,iBAC1BnG,KAAA,CAAAmK,aAAA;IACIY,GAAG,EAAE5E,YAAY,CAACE,UAAW;IAC7B+D,SAAS,EAAE,qBAAqB,CAAAvJ,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEwF,UAAU,MAAKF,YAAY,CAACE,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC/GwE,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAChC,YAAY,CAAE;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhD1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BvE,YAAY,CAAC6E,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC/C,CAAC,eACNlL,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BvE,YAAY,CAAC6E,WAAW,eACzBhL,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BvE,YAAY,CAACgF,YACZ,CACL,CAAC,eACNnL,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCvE,YAAY,CAACiF,eAAe,IAAI,eAChC,CAAC,eACNpL,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B1K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B3B,UAAU,CAAC5C,YAAY,CAACkF,iBAAiB,CACxC,CAAC,EACNlF,YAAY,CAACyE,gBAAgB,GAAG,CAAC,iBAC9B5K,KAAA,CAAAmK,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBvE,YAAY,CAACyE,gBACZ,CAET,CACJ,CACJ,CACR,CAEJ,CACJ,CAAC,eAGN5K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtBrJ,mBAAmB,gBAChBrB,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAyB,CAAC,eAC9B1K,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAMvJ,sBAAsB,CAAC,KAAK,CAAE;IAAA+I,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC9D,CAAC,eACN1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrC1K,KAAA,CAAAmK,aAAA;IACImB,KAAK,EAAE/J,YAAa;IACpBgK,QAAQ,EAAGjD,CAAC,IAAK9G,eAAe,CAAC8G,CAAC,CAACrE,MAAM,CAACqH,KAAK,CAAE;IACjDlB,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvB1K,KAAA,CAAAmK,aAAA;IAAQmB,KAAK,EAAC,EAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAsC,CAAC,EACvDvJ,eAAe,CAACkG,GAAG,CAAC9G,IAAI,iBACrBP,KAAA,CAAAmK,aAAA;IAAQY,GAAG,EAAExK,IAAI,CAACiC,EAAG;IAAC8I,KAAK,EAAE/K,IAAI,CAACiC,EAAG;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCnK,IAAI,CAACiL,GAAG,EAAC,IAAE,EAACjL,IAAI,CAACkL,IAAI,EAAC,GACnB,CACX,CACG,CACP,CACJ,CAAC,GACN5K,oBAAoB,gBACpBb,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvB7J,oBAAoB,CAACmK,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACvD,CAAC,eACNlL,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B7J,oBAAoB,CAACmK,WACrB,CAAC,eACNhL,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B7J,oBAAoB,CAACsK,YACrB,CACJ,CACJ,CACJ,CAAC,gBAENnL,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtB1K,KAAA,CAAAmK,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,iEAA4D,CAAC,eAChE1K,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEzC,oBAAqB;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAE/B,CACP,CACJ,CACR,EAGA,CAAC7J,oBAAoB,IAAIQ,mBAAmB,kBACzCrB,KAAA,CAAAmK,aAAA,CAAAnK,KAAA,CAAA0L,QAAA,qBACI1L,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B3J,QAAQ,CAACsG,GAAG,CAAC9B,OAAO,IAAI;IACrB,MAAMI,aAAa,GAAGpD,gBAAgB,CAAC,CAAC;IACxC,MAAMoJ,YAAY,GAAGlJ,QAAQ,CAAC8C,OAAO,CAAC2B,aAAa,CAAC,KAAKvB,aAAa;IACtE,MAAMiG,WAAW,GAAGD,YAAY,GAAG,MAAM,GAAG,UAAU;IAEtD,oBACI3L,KAAA,CAAAmK,aAAA;MACIY,GAAG,EAAExF,OAAO,CAAC/C,EAAG;MAChB4H,SAAS,EAAE,WAAWwB,WAAW,IAAID,YAAY,GAAG,aAAa,GAAG,eAAe,EAAG;MACtFE,aAAa,EAAGvD,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAE/C,OAAO,CAAE;MACpD,kBAAgBA,OAAO,CAAC2B,aAAc;MACtC,oBAAkB3B,OAAO,CAAC6B,eAAgB;MAAAiD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAGzC,CAACiB,YAAY,iBACV3L,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1BnF,OAAO,CAACuG,cAAc,IAAI,aAC1B,CACR,eAED9L,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAE,mBAAmBwB,WAAW,UAAW;MAAAvB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACpD7I,cAAc,KAAK0D,OAAO,CAAC/C,EAAE,gBAC1BxC,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzB1K,KAAA,CAAAmK,aAAA;MACImB,KAAK,EAAEvJ,WAAY;MACnBwJ,QAAQ,EAAGjD,CAAC,IAAKtG,cAAc,CAACsG,CAAC,CAACrE,MAAM,CAACqH,KAAK,CAAE;MAChDlB,SAAS,EAAC,eAAe;MACzB2B,SAAS;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACZ,CAAC,eACF1K,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzB1K,KAAA,CAAAmK,aAAA;MAAQU,OAAO,EAAE/B,WAAY;MAACsB,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAE/C,CAAC,eACT1K,KAAA,CAAAmK,aAAA;MAAQU,OAAO,EAAEhC,aAAc;MAACuB,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAEhD,CACP,CACJ,CAAC,gBAEN1K,KAAA,CAAAmK,aAAA,CAAAnK,KAAA,CAAA0L,QAAA,qBACI1L,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACxBnF,OAAO,CAACA,OAAO,EACfA,OAAO,CAACyG,OAAO,KAAK,GAAG,iBACpBhM,KAAA,CAAAmK,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAACU,KAAK,EAAE,cAAc/B,UAAU,CAACxD,OAAO,CAAC0G,iBAAiB,CAAC,EAAG;MAAA5B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEzF,CAET,CAAC,eACN1K,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAE,gBAAgBwB,WAAW,OAAQ;MAAAvB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9C3B,UAAU,CAACxD,OAAO,CAAC2G,UAAU,CAAC,EAC9BP,YAAY,iBACT3L,KAAA,CAAAmK,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC3BnF,OAAO,CAAC4G,EAAE,KAAK,GAAG,GAAG,IAAI,GAAG,GAC3B,CAET,CACP,CAEL,CAAC,EAGLC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACnCtM,KAAA,CAAAmK,aAAA;MAAKC,SAAS,EAAC,eAAe;MAACU,KAAK,EAAE,eAAevF,OAAO,CAAC2B,aAAa,mBAAmB3B,OAAO,CAAC6B,eAAe,EAAG;MAAAiD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEnH,CAER,CAAC;EAEd,CAAC,CAAC,eACF1K,KAAA,CAAAmK,aAAA;IAAKoC,GAAG,EAAElK,cAAe;IAAAgI,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC1B,CAAC,eAGN1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC1K,KAAA,CAAAmK,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC1K,KAAA,CAAAmK,aAAA;IACImB,KAAK,EAAErK,UAAW;IAClBsK,QAAQ,EAAGjD,CAAC,IAAKpH,aAAa,CAACoH,CAAC,CAACrE,MAAM,CAACqH,KAAK,CAAE;IAC/CkB,WAAW,EAAC,wBAAwB;IACpCpC,SAAS,EAAC,eAAe;IACzBqC,IAAI,EAAC,GAAG;IACRC,SAAS,EAAGpE,CAAC,IAAK;MACd,IAAIA,CAAC,CAACyC,GAAG,KAAK,OAAO,IAAI,CAACzC,CAAC,CAACqE,QAAQ,EAAE;QAClCrE,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBb,WAAW,CAAC,CAAC;MACjB;IACJ,CAAE;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACF1K,KAAA,CAAAmK,aAAA;IACIU,OAAO,EAAEnD,WAAY;IACrB0C,SAAS,EAAC,aAAa;IACvBwC,QAAQ,EAAE,CAAC3L,UAAU,CAAC0G,IAAI,CAAC,CAAC,IAAIlG,OAAQ;IAAA4I,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3C,cAEO,CACP,CACJ,CACP,CAEL,CACJ,CAAC,EAGLzI,eAAe,iBACZjC,KAAA,CAAAmK,aAAA;IACIoC,GAAG,EAAEjK,cAAe;IACpB8H,SAAS,EAAC,cAAc;IACxByC,KAAK,EAAE;MACHC,IAAI,EAAE7K,eAAe,CAACuG,CAAC;MACvBuE,GAAG,EAAE9K,eAAe,CAACyG;IACzB,CAAE;IAAA2B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDzI,eAAe,CAACsD,OAAO,CAACyH,UAAU,KAAK,CAAC,iBACrChN,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAMjC,YAAY,CAAC3G,eAAe,CAACsD,OAAO,CAAE;IAAA8E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAEtD,CACX,eACD1K,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAM7C,aAAa,CAAC/F,eAAe,CAACsD,OAAO,CAAC/C,EAAE,EAAE,QAAQ,CAAE;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uCAEpE,CAAC,EACRzI,eAAe,CAACsD,OAAO,CAAC0H,kBAAkB,KAAK,CAAC,iBAC7CjN,KAAA,CAAAmK,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAM7C,aAAa,CAAC/F,eAAe,CAACsD,OAAO,CAAC/C,EAAE,EAAE,cAAc,CAAE;IAAA6H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wCAE1E,CAEX,CAER,CAAC;AAEd,CAAC;AAED,eAAepK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}