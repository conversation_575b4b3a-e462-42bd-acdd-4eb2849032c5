<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Création API Endpoints Complets</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1400px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #667eea; font-size: 2.8rem; margin-bottom: 10px; }
        .success { background: #d4edda; border-left: 5px solid #28a745; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #f8d7da; border-left: 5px solid #dc3545; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { background: #d1ecf1; border-left: 5px solid #17a2b8; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .endpoint-card { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 5px solid #667eea; }
        .endpoint-title { color: #667eea; font-weight: 600; font-size: 1.2rem; margin-bottom: 10px; }
        .endpoint-method { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 600; margin-right: 10px; }
        .method-get { background: #28a745; color: white; }
        .method-post { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: #212529; }
        .method-delete { background: #dc3545; color: white; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; }
        .btn-success { background: #28a745; }
        pre { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-size: 11px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 CRÉATION API ENDPOINTS</h1>
            <p>API RESTful complète pour le système de messagerie WhatsApp</p>
        </div>
        
        <?php
        $creationSteps = [];
        $errors = [];
        
        try {
            echo '<div class="info">🔧 Création des API endpoints pour toutes les fonctionnalités...</div>';
            
            // CRÉATION DE L'API PRINCIPALE
            $apiCode = '<?php
header("Content-Type: application/json; charset=utf-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");

// Gérer les requêtes OPTIONS (preflight)
if ($_SERVER[\'REQUEST_METHOD\'] === \'OPTIONS\') {
    http_response_code(200);
    exit();
}

require_once \'AuthManager.php\';
require_once \'MessageManager.php\';

/**
 * 🚀 API MESSAGERIE COMPLÈTE
 * Système de messagerie avec fonctionnalités WhatsApp
 */

try {
    // Récupérer la méthode HTTP et l\'endpoint
    $method = $_SERVER[\'REQUEST_METHOD\'];
    $requestUri = $_SERVER[\'REQUEST_URI\'];
    $path = parse_url($requestUri, PHP_URL_PATH);
    $pathParts = explode(\'/\', trim($path, \'/\'));
    
    // Extraire l\'action de l\'URL
    $action = isset($pathParts[count($pathParts) - 1]) ? $pathParts[count($pathParts) - 1] : \'\';
    
    // Si pas d\'action dans l\'URL, la chercher dans les paramètres
    if (empty($action) || $action === \'index.php\') {
        $action = $_GET[\'action\'] ?? $_POST[\'action\'] ?? \'\';
    }
    
    // Authentification
    $authHeader = $_SERVER[\'HTTP_AUTHORIZATION\'] ?? \'\';
    if (empty($authHeader)) {
        $authHeader = $_GET[\'token\'] ?? $_POST[\'token\'] ?? \'test_user_1\';
    }
    
    $token = str_replace(\'Bearer \', \'\', $authHeader);
    
    $authManager = new AuthManager();
    $currentUser = $authManager->authenticateUser($token);
    
    if (!$currentUser) {
        throw new Exception(\'Authentification requise\');
    }
    
    $messageManager = new MessageManager($currentUser);
    
    // Router les requêtes selon l\'action
    switch ($action) {
        
        // 💬 RÉCUPÉRER LES CONVERSATIONS
        case \'conversations\':
        case \'getConversations\':
            if ($method !== \'GET\') {
                throw new Exception(\'Méthode non autorisée pour les conversations\');
            }
            
            $conversations = $messageManager->getConversations();
            
            echo json_encode([
                \'success\' => true,
                \'data\' => $conversations,
                \'count\' => count($conversations),
                \'user\' => [
                    \'id\' => $currentUser[\'id\'],
                    \'nom\' => $currentUser[\'nom\'],
                    \'role\' => $currentUser[\'role\']
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 📨 RÉCUPÉRER LES MESSAGES D\'UNE CONVERSATION
        case \'messages\':
        case \'getMessages\':
            if ($method !== \'GET\') {
                throw new Exception(\'Méthode non autorisée pour les messages\');
            }
            
            $contactId = $_GET[\'contact_id\'] ?? $_GET[\'contactId\'] ?? null;
            if (!$contactId) {
                throw new Exception(\'ID du contact requis\');
            }
            
            $messages = $messageManager->getMessages($contactId);
            
            echo json_encode([
                \'success\' => true,
                \'data\' => $messages,
                \'count\' => count($messages),
                \'contact_id\' => (int)$contactId
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // ✉️ ENVOYER UN MESSAGE
        case \'send\':
        case \'sendMessage\':
            if ($method !== \'POST\') {
                throw new Exception(\'Méthode POST requise pour envoyer un message\');
            }
            
            $input = json_decode(file_get_contents(\'php://input\'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $destinataireId = $input[\'destinataire_id\'] ?? $input[\'destinataireId\'] ?? null;
            $message = $input[\'message\'] ?? null;
            
            if (!$destinataireId || !$message) {
                throw new Exception(\'Destinataire et message requis\');
            }
            
            $messageId = $messageManager->sendMessage($destinataireId, $message);
            
            echo json_encode([
                \'success\' => true,
                \'message\' => \'Message envoyé avec succès\',
                \'message_id\' => $messageId,
                \'data\' => [
                    \'id\' => $messageId,
                    \'expediteur_id\' => $currentUser[\'id\'],
                    \'destinataire_id\' => (int)$destinataireId,
                    \'message\' => $message,
                    \'date_envoi\' => date(\'Y-m-d H:i:s\')
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // ✏️ MODIFIER UN MESSAGE
        case \'edit\':
        case \'editMessage\':
            if ($method !== \'PUT\' && $method !== \'POST\') {
                throw new Exception(\'Méthode PUT ou POST requise pour modifier un message\');
            }
            
            $input = json_decode(file_get_contents(\'php://input\'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $messageId = $input[\'message_id\'] ?? $input[\'messageId\'] ?? null;
            $newContent = $input[\'message\'] ?? $input[\'content\'] ?? null;
            
            if (!$messageId || !$newContent) {
                throw new Exception(\'ID du message et nouveau contenu requis\');
            }
            
            $result = $messageManager->editMessage($messageId, $newContent);
            
            echo json_encode([
                \'success\' => true,
                \'message\' => \'Message modifié avec succès\',
                \'message_id\' => (int)$messageId,
                \'modified_at\' => date(\'Y-m-d H:i:s\')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 🗑️ SUPPRIMER UN MESSAGE
        case \'delete\':
        case \'deleteMessage\':
            if ($method !== \'DELETE\' && $method !== \'POST\') {
                throw new Exception(\'Méthode DELETE ou POST requise pour supprimer un message\');
            }
            
            $input = json_decode(file_get_contents(\'php://input\'), true);
            if (!$input) {
                $input = $_POST;
            }
            
            $messageId = $input[\'message_id\'] ?? $input[\'messageId\'] ?? null;
            $deleteType = $input[\'delete_type\'] ?? $input[\'deleteType\'] ?? \'for_me\';
            
            if (!$messageId) {
                throw new Exception(\'ID du message requis\');
            }
            
            $result = $messageManager->deleteMessage($messageId, $deleteType);
            
            echo json_encode([
                \'success\' => true,
                \'message\' => \'Message supprimé avec succès\',
                \'message_id\' => (int)$messageId,
                \'delete_type\' => $deleteType,
                \'deleted_at\' => date(\'Y-m-d H:i:s\')
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 👥 RÉCUPÉRER LES UTILISATEURS AUTORISÉS
        case \'users\':
        case \'getUsers\':
        case \'authorizedUsers\':
            if ($method !== \'GET\') {
                throw new Exception(\'Méthode GET requise pour les utilisateurs\');
            }
            
            $users = $messageManager->getAuthorizedUsers();
            
            echo json_encode([
                \'success\' => true,
                \'data\' => $users,
                \'count\' => count($users)
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 📊 RÉCUPÉRER LES STATISTIQUES
        case \'stats\':
        case \'getStats\':
        case \'statistics\':
            if ($method !== \'GET\') {
                throw new Exception(\'Méthode GET requise pour les statistiques\');
            }
            
            $stats = $messageManager->getStats();
            
            echo json_encode([
                \'success\' => true,
                \'data\' => $stats,
                \'user\' => [
                    \'id\' => $currentUser[\'id\'],
                    \'nom\' => $currentUser[\'nom\'],
                    \'role\' => $currentUser[\'role\']
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        // 🔍 TESTER L\'API
        case \'test\':
        case \'ping\':
            echo json_encode([
                \'success\' => true,
                \'message\' => \'API Messagerie fonctionnelle\',
                \'timestamp\' => date(\'Y-m-d H:i:s\'),
                \'user\' => [
                    \'id\' => $currentUser[\'id\'],
                    \'nom\' => $currentUser[\'nom\'],
                    \'role\' => $currentUser[\'role\']
                ],
                \'endpoints\' => [
                    \'conversations\' => \'GET /conversations\',
                    \'messages\' => \'GET /messages?contact_id=X\',
                    \'send\' => \'POST /send\',
                    \'edit\' => \'PUT /edit\',
                    \'delete\' => \'DELETE /delete\',
                    \'users\' => \'GET /users\',
                    \'stats\' => \'GET /stats\'
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
            
        default:
            // Action non reconnue - afficher la documentation
            echo json_encode([
                \'success\' => false,
                \'error\' => \'Action non reconnue: \' . $action,
                \'available_actions\' => [
                    \'conversations\' => \'Récupérer les conversations\',
                    \'messages\' => \'Récupérer les messages d\\\'une conversation\',
                    \'send\' => \'Envoyer un message\',
                    \'edit\' => \'Modifier un message\',
                    \'delete\' => \'Supprimer un message\',
                    \'users\' => \'Récupérer les utilisateurs autorisés\',
                    \'stats\' => \'Récupérer les statistiques\',
                    \'test\' => \'Tester l\\\'API\'
                ],
                \'usage\' => [
                    \'method\' => $method,
                    \'action\' => $action,
                    \'user\' => $currentUser[\'nom\'] ?? \'Inconnu\'
                ]
            ], JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        \'success\' => false,
        \'error\' => $e->getMessage(),
        \'timestamp\' => date(\'Y-m-d H:i:s\'),
        \'debug\' => [
            \'method\' => $_SERVER[\'REQUEST_METHOD\'] ?? \'UNKNOWN\',
            \'action\' => $action ?? \'NONE\',
            \'request_uri\' => $_SERVER[\'REQUEST_URI\'] ?? \'UNKNOWN\'
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>';
            
            // Sauvegarder l'API
            try {
                file_put_contents('index.php', $apiCode);
                echo '<div class="success">✅ API index.php créée avec succès</div>';
                $creationSteps[] = 'API RESTful complète avec tous les endpoints';
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur création API: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur création API';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ ERREUR GÉNÉRALE: ' . $e->getMessage() . '</div>';
            $errors[] = 'Erreur générale de création';
        }
        ?>
        
        <div style="margin-top: 40px;">
            <h2>🌐 Endpoints API Créés</h2>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-get">GET</span>
                    /conversations
                </div>
                <p>Récupère la liste des conversations de l'utilisateur avec confidentialité stricte</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-get">GET</span>
                    /messages?contact_id=X
                </div>
                <p>Récupère les messages d'une conversation spécifique</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-post">POST</span>
                    /send
                </div>
                <p>Envoie un nouveau message avec validation des rôles</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-put">PUT</span>
                    /edit
                </div>
                <p>Modifie un message (style WhatsApp) avec sauvegarde de l'original</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-delete">DELETE</span>
                    /delete
                </div>
                <p>Supprime un message pour soi ou pour tous (style WhatsApp)</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-get">GET</span>
                    /users
                </div>
                <p>Récupère les utilisateurs autorisés (Parents, Enseignants, Admins)</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-get">GET</span>
                    /stats
                </div>
                <p>Récupère les statistiques de messagerie de l'utilisateur</p>
            </div>
            
            <div class="endpoint-card">
                <div class="endpoint-title">
                    <span class="endpoint-method method-get">GET</span>
                    /test
                </div>
                <p>Teste la connectivité et l'authentification de l'API</p>
            </div>
            
            <?php if (empty($errors)): ?>
                <div class="success">
                    <h3>✅ API Complète Créée !</h3>
                    <p><strong>Fonctionnalités incluses :</strong></p>
                    <ul>
                        <li>🔒 <strong>Authentification sécurisée</strong> avec gestion des tokens</li>
                        <li>🌐 <strong>CORS configuré</strong> pour les requêtes cross-origin</li>
                        <li>📱 <strong>RESTful</strong> avec méthodes HTTP appropriées</li>
                        <li>🛡️ <strong>Gestion d'erreurs</strong> complète avec codes HTTP</li>
                        <li>📊 <strong>Réponses JSON</strong> structurées et cohérentes</li>
                        <li>🔍 <strong>Debug intégré</strong> pour le développement</li>
                        <li>👥 <strong>Contrôle d'accès</strong> basé sur les rôles</li>
                        <li>📝 <strong>Documentation</strong> automatique des endpoints</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="test_api_complet.php" class="btn btn-success">🧪 TESTER L'API</a>
                    <a href="creation_interface_react.php" class="btn btn-success">➡️ INTERFACE REACT</a>
                </div>
                
            <?php else: ?>
                <div class="error">
                    <h3>❌ Erreurs Détectées</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li>❌ <?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="?" class="btn">🔄 RELANCER CRÉATION</a>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 20px;">
                <a href="creation_message_manager.php" class="btn">⬅️ RETOUR MESSAGEMANAGER</a>
                <a href="diagnostic_table_messages.php" class="btn">🔍 DIAGNOSTIC</a>
            </div>
        </div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>📋 Exemples d'Utilisation</h3>
            <pre>
// Récupérer les conversations
GET /api/messaging/?action=conversations
Authorization: Bearer test_user_1

// Récupérer les messages
GET /api/messaging/?action=messages&contact_id=2
Authorization: Bearer test_user_1

// Envoyer un message
POST /api/messaging/?action=send
Content-Type: application/json
Authorization: Bearer test_user_1
{
    "destinataire_id": 2,
    "message": "Bonjour, comment allez-vous ?"
}

// Modifier un message
PUT /api/messaging/?action=edit
Content-Type: application/json
Authorization: Bearer test_user_1
{
    "message_id": 1,
    "message": "Message modifié"
}

// Supprimer un message
DELETE /api/messaging/?action=delete
Content-Type: application/json
Authorization: Bearer test_user_1
{
    "message_id": 1,
    "delete_type": "for_everyone"
}
            </pre>
        </div>
        
        <div class="info" style="margin-top: 20px;">
            <h3>🔧 Fonctionnalités Techniques</h3>
            <ul>
                <li><strong>Routing intelligent</strong> : Support URL et paramètres</li>
                <li><strong>Authentification flexible</strong> : Headers, GET, POST</li>
                <li><strong>Validation stricte</strong> : Paramètres requis et types</li>
                <li><strong>Gestion d'erreurs</strong> : Codes HTTP et messages détaillés</li>
                <li><strong>CORS complet</strong> : Support preflight et headers</li>
                <li><strong>JSON UTF-8</strong> : Encodage correct des caractères</li>
                <li><strong>Debug intégré</strong> : Informations de débogage</li>
                <li><strong>Documentation auto</strong> : Endpoints disponibles</li>
            </ul>
        </div>
    </div>
</body>
</html>
