import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/ParentEtudiant.css';

const ParentEtudiant = () => {
    const { user } = useContext(AuthContext);
    const [relations, setRelations] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingRelation, setEditingRelation] = useState(null);
    const [parents, setParents] = useState([]);
    const [etudiants, setEtudiants] = useState([]);
    const [formData, setFormData] = useState({
        parent_id: '',
        etudiant_id: '',
        lien_parente: 'Père'
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin';
    const isEnseignant = user?.role === 'Enseignant' || user?.role === 'enseignant';

    useEffect(() => {
        fetchRelations();
        if (isAdmin) {
            fetchParents();
            fetchEtudiants();
        }
    }, [isAdmin]);

    const fetchRelations = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/parent_etudiant/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setRelations(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des relations:', error);
            if (error.response?.status === 403) {
                Swal.fire('Accès refusé', 'Vous n\'avez pas l\'autorisation d\'accéder à cette page', 'error');
            } else {
                Swal.fire('Erreur', 'Impossible de charger les relations parent-étudiant', 'error');
            }
        } finally {
            setLoading(false);
        }
    };

    const fetchParents = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/parents/parent.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setParents(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des parents:', error);
        }
    };

    const fetchEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setEtudiants(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des étudiants:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/parent_etudiant/';
            const method = editingRelation ? 'PUT' : 'POST';
            const data = editingRelation ? { ...formData, id: editingRelation.id } : formData;

            await axios({
                method,
                url,
                data,
                headers: { Authorization: `Bearer ${token}` }
            });

            Swal.fire('Succès', `Relation ${editingRelation ? 'modifiée' : 'créée'} avec succès`, 'success');
            setShowModal(false);
            setEditingRelation(null);
            resetForm();
            fetchRelations();
        } catch (error) {
            console.error('Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (relation) => {
        setEditingRelation(relation);
        setFormData({
            parent_id: relation.parent_id,
            etudiant_id: relation.etudiant_id,
            lien_parente: relation.lien_parente
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Êtes-vous sûr ?',
            text: 'Cette action supprimera définitivement la relation parent-étudiant',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                await axios.delete('http://localhost/Project_PFE/Backend/pages/parent_etudiant/', {
                    headers: { Authorization: `Bearer ${token}` },
                    data: { id }
                });
                Swal.fire('Supprimé !', 'La relation a été supprimée avec succès', 'success');
                fetchRelations();
            } catch (error) {
                console.error('Erreur lors de la suppression:', error);
                Swal.fire('Erreur', error.response?.data?.error || 'Impossible de supprimer la relation', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            parent_id: '',
            etudiant_id: '',
            lien_parente: 'Père'
        });
    };

    const getLienBadge = (lien) => {
        const colors = {
            'Père': '#007bff',
            'Mère': '#e91e63',
            'Tuteur': '#ff9800',
            'Autre': '#6c757d'
        };
        return (
            <span 
                className="badge" 
                style={{ 
                    backgroundColor: colors[lien] || '#6c757d',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '0.8em'
                }}
            >
                {lien}
            </span>
        );
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des relations parent-étudiant...</p>
            </div>
        );
    }

    // Vérifier l'accès
    if (!isAdmin && !isEnseignant) {
        return (
            <div className="access-denied">
                <img src="/access-denied.png" alt="Accès refusé" />
                <h2>Accès refusé</h2>
                <p>Vous n'avez pas l'autorisation d'accéder à cette page.</p>
            </div>
        );
    }

    return (
        <div className="parent-etudiant-container">
            <div className="page-header">
                <div className="header-content">
                    <div className="header-text">
                        <h1>👨‍👩‍👧‍👦 Relations Parent-Étudiant</h1>
                        <p>Gestion des liens familiaux entre parents et étudiants</p>
                    </div>
                    {isAdmin && (
                        <button 
                            className="btn btn-primary add-btn"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/add.png" alt="Ajouter" />
                            Nouvelle relation
                        </button>
                    )}
                </div>
            </div>

            <div className="relations-grid">
                {relations.length === 0 ? (
                    <div className="no-data">
                        <img src="/family.png" alt="Aucune relation" />
                        <p>Aucune relation parent-étudiant trouvée</p>
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👨‍👩‍👧‍👦 Parent</th>
                                    <th>👨‍🎓 Étudiant</th>
                                    <th>🔗 Lien de parenté</th>
                                    <th>📞 Téléphone</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {relations.map((relation) => (
                                    <tr key={relation.id}>
                                        <td>
                                            <div className="parent-info">
                                                <strong>{relation.parent_nom}</strong>
                                                <small>{relation.parent_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div className="student-info">
                                                <strong>{relation.etudiant_nom}</strong>
                                                <small>{relation.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>{getLienBadge(relation.lien_parente)}</td>
                                        <td>{relation.parent_telephone || '-'}</td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button 
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(relation)}
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button 
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(relation.id)}
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Modal pour ajouter/modifier une relation */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingRelation ? 'Modifier la relation' : 'Nouvelle relation parent-étudiant'}</h3>
                            <button 
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingRelation(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>👨‍👩‍👧‍👦 Parent</label>
                                <select
                                    value={formData.parent_id}
                                    onChange={(e) => setFormData({...formData, parent_id: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner un parent</option>
                                    {parents.map((parent) => (
                                        <option key={parent.id} value={parent.id}>
                                            {parent.nom} - {parent.email}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-group">
                                <label>👨‍🎓 Étudiant</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.id} value={etudiant.id}>
                                            {etudiant.nom} - {etudiant.email}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div className="form-group">
                                <label>🔗 Lien de parenté</label>
                                <select
                                    value={formData.lien_parente}
                                    onChange={(e) => setFormData({...formData, lien_parente: e.target.value})}
                                    required
                                >
                                    <option value="Père">Père</option>
                                    <option value="Mère">Mère</option>
                                    <option value="Tuteur">Tuteur</option>
                                    <option value="Autre">Autre</option>
                                </select>
                            </div>
                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingRelation ? 'Modifier' : 'Créer'}
                                </button>
                                <button 
                                    type="button" 
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingRelation(null);
                                        resetForm();
                                    }}
                                >
                                    Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ParentEtudiant;
