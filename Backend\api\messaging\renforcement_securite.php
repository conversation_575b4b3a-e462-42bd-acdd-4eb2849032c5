<?php
/**
 * 🛡️ SCRIPT DE RENFORCEMENT DE LA SÉCURITÉ
 * Ajoute des couches de sécurité supplémentaires pour garantir la confidentialité stricte
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🛡️ Renforcement de la Sécurité - Messagerie</h1>";

try {
    // 1. Vérifier les fichiers critiques
    $criticalFiles = [
        'MessageManager.php' => 'Gestionnaire principal des messages',
        'AuthManager.php' => 'Gestionnaire d\'authentification',
        'index.php' => 'Point d\'entrée API'
    ];
    
    echo "<h2>📁 Vérification des fichiers critiques</h2>";
    
    foreach ($criticalFiles as $file => $description) {
        if (file_exists($file)) {
            echo "<p>✅ $file - $description</p>";
        } else {
            echo "<p>❌ $file - MANQUANT</p>";
        }
    }
    
    // 2. Ajouter des fonctions de sécurité supplémentaires
    $securityFunctions = '
<?php
/**
 * 🛡️ FONCTIONS DE SÉCURITÉ SUPPLÉMENTAIRES
 * Ajout de couches de protection pour la confidentialité stricte
 */

/**
 * Vérifier que l\'utilisateur a accès à un message spécifique
 */
function verifyMessageAccess($messageId, $userId, $pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM messages 
            WHERE id = ? 
            AND (expediteur_id = ? OR destinataire_id = ?)
        ");
        $stmt->execute([$messageId, $userId, $userId]);
        
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        logSecurityViolation("Tentative d\'accès message non autorisé", [
            "message_id" => $messageId,
            "user_id" => $userId,
            "error" => $e->getMessage()
        ]);
        return false;
    }
}

/**
 * Vérifier que l\'utilisateur a accès à une conversation
 */
function verifyConversationAccess($contactId, $userId, $pdo) {
    try {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM messages 
            WHERE (expediteur_id = ? AND destinataire_id = ?) 
            OR (expediteur_id = ? AND destinataire_id = ?)
        ");
        $stmt->execute([$userId, $contactId, $contactId, $userId]);
        
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        logSecurityViolation("Tentative d\'accès conversation non autorisé", [
            "contact_id" => $contactId,
            "user_id" => $userId,
            "error" => $e->getMessage()
        ]);
        return false;
    }
}

/**
 * Logger les violations de sécurité
 */
function logSecurityViolation($message, $data = []) {
    $logEntry = [
        "timestamp" => date("Y-m-d H:i:s"),
        "type" => "SECURITY_VIOLATION",
        "message" => $message,
        "data" => $data,
        "ip" => $_SERVER["REMOTE_ADDR"] ?? "unknown",
        "user_agent" => $_SERVER["HTTP_USER_AGENT"] ?? "unknown"
    ];
    
    error_log("SECURITY_VIOLATION: " . json_encode($logEntry));
}

/**
 * Valider l\'intégrité d\'une requête de message
 */
function validateMessageRequest($data, $userId) {
    $violations = [];
    
    // Vérifier que l\'utilisateur ne tente pas d\'usurper l\'identité
    if (isset($data["expediteur_id"]) && $data["expediteur_id"] != $userId) {
        $violations[] = "Tentative d\'usurpation d\'identité expéditeur";
    }
    
    // Vérifier que le destinataire existe et a accès à la messagerie
    if (isset($data["destinataire_id"])) {
        // Cette vérification devrait être faite avec la base de données
        // Ici on fait juste une validation basique
        if (!is_numeric($data["destinataire_id"]) || $data["destinataire_id"] <= 0) {
            $violations[] = "ID destinataire invalide";
        }
    }
    
    // Vérifier la longueur du message
    if (isset($data["message"])) {
        if (strlen($data["message"]) > 5000) {
            $violations[] = "Message trop long (max 5000 caractères)";
        }
        if (empty(trim($data["message"]))) {
            $violations[] = "Message vide";
        }
    }
    
    return $violations;
}

/**
 * Nettoyer et sécuriser le contenu d\'un message
 */
function sanitizeMessage($message) {
    // Supprimer les balises HTML dangereuses
    $message = strip_tags($message);
    
    // Échapper les caractères spéciaux
    $message = htmlspecialchars($message, ENT_QUOTES, "UTF-8");
    
    // Limiter la longueur
    $message = substr($message, 0, 5000);
    
    return trim($message);
}

/**
 * Générer un token de sécurité pour les requêtes sensibles
 */
function generateSecurityToken($userId, $action) {
    $data = [
        "user_id" => $userId,
        "action" => $action,
        "timestamp" => time(),
        "nonce" => bin2hex(random_bytes(16))
    ];
    
    return base64_encode(json_encode($data));
}

/**
 * Vérifier un token de sécurité
 */
function verifySecurityToken($token, $userId, $action, $maxAge = 3600) {
    try {
        $data = json_decode(base64_decode($token), true);
        
        if (!$data) return false;
        
        // Vérifier l\'utilisateur
        if ($data["user_id"] != $userId) return false;
        
        // Vérifier l\'action
        if ($data["action"] != $action) return false;
        
        // Vérifier l\'âge du token
        if (time() - $data["timestamp"] > $maxAge) return false;
        
        return true;
    } catch (Exception $e) {
        return false;
    }
}
?>';
    
    // Écrire le fichier de sécurité
    file_put_contents('SecurityHelper.php', $securityFunctions);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ Fonctions de sécurité créées</h3>";
    echo "<p>Fichier SecurityHelper.php créé avec les fonctions de sécurité supplémentaires</p>";
    echo "</div>";
    
    // 3. Créer un middleware de sécurité
    $middlewareCode = '
<?php
/**
 * 🛡️ MIDDLEWARE DE SÉCURITÉ
 * Couche de protection supplémentaire pour toutes les requêtes
 */

class SecurityMiddleware {
    private $pdo;
    private $currentUser;
    
    public function __construct($pdo, $currentUser) {
        $this->pdo = $pdo;
        $this->currentUser = $currentUser;
    }
    
    /**
     * Vérifier toutes les requêtes avant traitement
     */
    public function checkRequest($action, $data = []) {
        $violations = [];
        
        // 1. Vérifier l\'authentification
        if (!$this->currentUser || !isset($this->currentUser["id"])) {
            $violations[] = "Utilisateur non authentifié";
        }
        
        // 2. Vérifier le rôle pour la messagerie
        if (!in_array($this->currentUser["role"] ?? "", ["parent", "enseignant", "admin", "responsable"])) {
            $violations[] = "Rôle non autorisé pour la messagerie";
        }
        
        // 3. Vérifications spécifiques par action
        switch ($action) {
            case "conversations":
                // Pas de données supplémentaires requises
                break;
                
            case "messages":
                if (!isset($data["contact_id"]) || !is_numeric($data["contact_id"])) {
                    $violations[] = "ID contact requis et valide";
                }
                break;
                
            case "send":
                if (!isset($data["destinataire_id"]) || !is_numeric($data["destinataire_id"])) {
                    $violations[] = "ID destinataire requis";
                }
                if (!isset($data["message"]) || empty(trim($data["message"]))) {
                    $violations[] = "Message requis";
                }
                break;
                
            case "edit":
                if (!isset($data["message_id"]) || !is_numeric($data["message_id"])) {
                    $violations[] = "ID message requis";
                }
                if (!isset($data["message"]) || empty(trim($data["message"]))) {
                    $violations[] = "Nouveau contenu requis";
                }
                break;
                
            case "delete":
                if (!isset($data["message_id"]) || !is_numeric($data["message_id"])) {
                    $violations[] = "ID message requis";
                }
                break;
        }
        
        // 4. Logger les violations
        if (!empty($violations)) {
            $this->logSecurityViolation("Requête bloquée", [
                "action" => $action,
                "violations" => $violations,
                "user_id" => $this->currentUser["id"] ?? "unknown",
                "data" => $data
            ]);
        }
        
        return $violations;
    }
    
    /**
     * Vérifier l\'accès à un message spécifique
     */
    public function checkMessageAccess($messageId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT expediteur_id, destinataire_id 
                FROM messages 
                WHERE id = ?
            ");
            $stmt->execute([$messageId]);
            $message = $stmt->fetch();
            
            if (!$message) {
                return false;
            }
            
            $userId = $this->currentUser["id"];
            return ($message["expediteur_id"] == $userId || $message["destinataire_id"] == $userId);
            
        } catch (Exception $e) {
            $this->logSecurityViolation("Erreur vérification accès message", [
                "message_id" => $messageId,
                "error" => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * Logger les violations de sécurité
     */
    private function logSecurityViolation($message, $data = []) {
        $logEntry = [
            "timestamp" => date("Y-m-d H:i:s"),
            "type" => "SECURITY_MIDDLEWARE",
            "message" => $message,
            "data" => $data,
            "ip" => $_SERVER["REMOTE_ADDR"] ?? "unknown"
        ];
        
        error_log("SECURITY_MIDDLEWARE: " . json_encode($logEntry));
    }
}
?>';
    
    file_put_contents('SecurityMiddleware.php', $middlewareCode);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>✅ Middleware de sécurité créé</h3>";
    echo "<p>Fichier SecurityMiddleware.php créé pour la protection des requêtes</p>";
    echo "</div>";
    
    // 4. Instructions d\'intégration
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📋 Instructions d\'intégration</h3>";
    echo "<p>Pour intégrer ces améliorations de sécurité :</p>";
    echo "<ol>";
    echo "<li>Inclure SecurityHelper.php dans MessageManager.php</li>";
    echo "<li>Inclure SecurityMiddleware.php dans index.php</li>";
    echo "<li>Ajouter les vérifications dans chaque méthode</li>";
    echo "<li>Tester avec l\'audit de confidentialité</li>";
    echo "</ol>";
    echo "</div>";
    
    // 5. Code d\'exemple d\'intégration
    echo "<h3>💻 Exemple d\'intégration dans MessageManager.php</h3>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;'>";
    echo htmlspecialchars('
// En haut du fichier MessageManager.php
require_once "SecurityHelper.php";

// Dans la méthode getMessages()
public function getMessages($contactId) {
    try {
        $userId = (int)$this->currentUser["id"];
        
        // VÉRIFICATION DE SÉCURITÉ SUPPLÉMENTAIRE
        if (!verifyConversationAccess($contactId, $userId, $this->pdo)) {
            logSecurityViolation("Tentative accès conversation non autorisé", [
                "user_id" => $userId,
                "contact_id" => $contactId
            ]);
            throw new Exception("Accès refusé à cette conversation");
        }
        
        // ... reste du code existant
    }
}
');
    echo "</pre>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='audit_confidentialite.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 Lancer Audit</a>";
    echo "<a href='guide_correction_final.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📋 Guide Principal</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>❌ Erreur</h3>";
    echo "<p>Erreur lors du renforcement: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
