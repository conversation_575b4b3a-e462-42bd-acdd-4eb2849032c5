<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Test Confidentialité Frontend - Messagerie</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1400px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid #667eea; padding-bottom: 20px; }
        .header h1 { color: #667eea; font-size: 3rem; margin-bottom: 10px; }
        .test-section { background: #f8f9fa; padding: 25px; border-radius: 15px; margin: 20px 0; border-left: 5px solid #28a745; }
        .test-fail { border-left-color: #dc3545; background: #fff5f5; }
        .test-warning { border-left-color: #ffc107; background: #fffbf0; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; overflow-x: auto; font-family: 'Courier New', monospace; margin: 15px 0; font-size: 14px; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; }
        .btn:hover { background: #5a67d8; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .result-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .result-card { background: white; padding: 20px; border-radius: 10px; border: 2px solid #e9ecef; }
        .result-card.success { border-color: #28a745; }
        .result-card.fail { border-color: #dc3545; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: 600; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 TEST CONFIDENTIALITÉ FRONTEND</h1>
            <p>Vérification de la sécurité côté client - Messagerie React</p>
        </div>

        <div class="test-section">
            <h2>🧪 Tests de Confidentialité Automatisés</h2>
            <p>Ces tests simulent différents utilisateurs et vérifient que chacun ne voit que ses propres messages.</p>
            
            <button onclick="runAllTests()" class="btn btn-success">🚀 Lancer Tous les Tests</button>
            <button onclick="clearResults()" class="btn">🧹 Effacer Résultats</button>
        </div>

        <div id="test-results"></div>

        <div class="test-section">
            <h2>📋 Scénarios de Test</h2>
            <div class="result-grid">
                <div class="result-card">
                    <h3>🔒 Test 1: Isolation des Messages</h3>
                    <p>Vérifier que l'utilisateur A ne voit pas les messages entre B et C</p>
                    <button onclick="testMessageIsolation()" class="btn">▶️ Tester</button>
                </div>
                
                <div class="result-card">
                    <h3>🛡️ Test 2: Filtrage Frontend</h3>
                    <p>Vérifier que le filtrage côté React fonctionne correctement</p>
                    <button onclick="testFrontendFiltering()" class="btn">▶️ Tester</button>
                </div>
                
                <div class="result-card">
                    <h3>🎯 Test 3: Distinction Sent/Received</h3>
                    <p>Vérifier l'affichage correct des messages envoyés vs reçus</p>
                    <button onclick="testMessageDisplay()" class="btn">▶️ Tester</button>
                </div>
                
                <div class="result-card">
                    <h3>🚫 Test 4: Accès Non Autorisé</h3>
                    <p>Tenter d'accéder aux messages d'autres utilisateurs</p>
                    <button onclick="testUnauthorizedAccess()" class="btn">▶️ Tester</button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Outils de Debug</h2>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button onclick="simulateUser(1001)" class="btn">👤 Simuler Parent 1</button>
                <button onclick="simulateUser(1002)" class="btn">👤 Simuler Parent 2</button>
                <button onclick="simulateUser(1003)" class="btn">👨‍🏫 Simuler Enseignant</button>
                <button onclick="simulateUser(1004)" class="btn">👨‍💼 Simuler Admin</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';
        let testResults = [];

        // Fonction pour faire des requêtes API avec différents utilisateurs
        async function makeAPIRequest(endpoint, userId, method = 'GET', data = null) {
            try {
                const token = `test_user_${userId}`;
                
                const config = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                };

                if (data && (method === 'POST' || method === 'PUT')) {
                    config.body = JSON.stringify(data);
                }

                const url = `${API_BASE_URL}?action=${endpoint}`;
                const response = await fetch(url, config);
                const result = await response.json();

                return {
                    success: result.success,
                    data: result.data || [],
                    error: result.error,
                    userId: userId
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    userId: userId
                };
            }
        }

        // Test 1: Isolation des messages
        async function testMessageIsolation() {
            addTestResult('🔒 Test Isolation des Messages', 'EN COURS', 'info');
            
            try {
                // Récupérer les conversations pour différents utilisateurs
                const user1Conversations = await makeAPIRequest('conversations', 1001);
                const user2Conversations = await makeAPIRequest('conversations', 1002);
                const user3Conversations = await makeAPIRequest('conversations', 1003);

                let violations = [];
                
                // Vérifier que chaque utilisateur a des conversations différentes
                if (user1Conversations.success && user2Conversations.success) {
                    const user1Contacts = user1Conversations.data.map(c => c.contact_id);
                    const user2Contacts = user2Conversations.data.map(c => c.contact_id);
                    
                    // Les utilisateurs ne devraient pas avoir exactement les mêmes contacts
                    if (JSON.stringify(user1Contacts.sort()) === JSON.stringify(user2Contacts.sort())) {
                        violations.push('Utilisateurs 1001 et 1002 ont les mêmes conversations');
                    }
                }

                if (violations.length === 0) {
                    addTestResult('🔒 Test Isolation des Messages', '✅ RÉUSSI - Isolation correcte', 'success');
                } else {
                    addTestResult('🔒 Test Isolation des Messages', `❌ ÉCHEC - ${violations.join(', ')}`, 'fail');
                }
            } catch (error) {
                addTestResult('🔒 Test Isolation des Messages', `❌ ERREUR - ${error.message}`, 'fail');
            }
        }

        // Test 2: Filtrage Frontend
        async function testFrontendFiltering() {
            addTestResult('🛡️ Test Filtrage Frontend', 'EN COURS', 'info');
            
            try {
                // Simuler des messages avec différents expéditeurs/destinataires
                const testMessages = [
                    { id: 1, expediteur_id: 1001, destinataire_id: 1002, message: 'Test 1→2' },
                    { id: 2, expediteur_id: 1002, destinataire_id: 1001, message: 'Test 2→1' },
                    { id: 3, expediteur_id: 1003, destinataire_id: 1004, message: 'Test 3→4' },
                    { id: 4, expediteur_id: 1001, destinataire_id: 1003, message: 'Test 1→3' }
                ];

                // Simuler le filtrage frontend pour l'utilisateur 1001
                const currentUserId = 1001;
                const filteredMessages = testMessages.filter(message => {
                    const expediteurId = parseInt(message.expediteur_id);
                    const destinataireId = parseInt(message.destinataire_id);
                    return (expediteurId === currentUserId || destinataireId === currentUserId);
                });

                // L'utilisateur 1001 devrait voir les messages 1, 2, et 4
                const expectedIds = [1, 2, 4];
                const actualIds = filteredMessages.map(m => m.id).sort();
                
                if (JSON.stringify(expectedIds) === JSON.stringify(actualIds)) {
                    addTestResult('🛡️ Test Filtrage Frontend', '✅ RÉUSSI - Filtrage correct', 'success');
                } else {
                    addTestResult('🛡️ Test Filtrage Frontend', `❌ ÉCHEC - Attendu: ${expectedIds}, Obtenu: ${actualIds}`, 'fail');
                }
            } catch (error) {
                addTestResult('🛡️ Test Filtrage Frontend', `❌ ERREUR - ${error.message}`, 'fail');
            }
        }

        // Test 3: Distinction Sent/Received
        async function testMessageDisplay() {
            addTestResult('🎯 Test Distinction Sent/Received', 'EN COURS', 'info');
            
            try {
                const testMessages = [
                    { id: 1, expediteur_id: 1001, destinataire_id: 1002, message: 'Message envoyé' },
                    { id: 2, expediteur_id: 1002, destinataire_id: 1001, message: 'Message reçu' }
                ];

                const currentUserId = 1001;
                let correctClassification = true;
                let details = [];

                testMessages.forEach(message => {
                    const expediteurId = parseInt(message.expediteur_id);
                    const isOwnMessage = expediteurId === currentUserId;
                    const expectedType = isOwnMessage ? 'sent' : 'received';
                    
                    details.push(`Message ${message.id}: ${expectedType} (expéditeur: ${expediteurId})`);
                    
                    if ((isOwnMessage && expectedType !== 'sent') || (!isOwnMessage && expectedType !== 'received')) {
                        correctClassification = false;
                    }
                });

                if (correctClassification) {
                    addTestResult('🎯 Test Distinction Sent/Received', `✅ RÉUSSI - Classification correcte\n${details.join('\n')}`, 'success');
                } else {
                    addTestResult('🎯 Test Distinction Sent/Received', `❌ ÉCHEC - Classification incorrecte\n${details.join('\n')}`, 'fail');
                }
            } catch (error) {
                addTestResult('🎯 Test Distinction Sent/Received', `❌ ERREUR - ${error.message}`, 'fail');
            }
        }

        // Test 4: Accès non autorisé
        async function testUnauthorizedAccess() {
            addTestResult('🚫 Test Accès Non Autorisé', 'EN COURS', 'info');
            
            try {
                // Tenter d'accéder aux messages avec un token invalide
                const invalidResponse = await makeAPIRequest('conversations', 'invalid_user');
                
                if (!invalidResponse.success) {
                    addTestResult('🚫 Test Accès Non Autorisé', '✅ RÉUSSI - Accès refusé pour token invalide', 'success');
                } else {
                    addTestResult('🚫 Test Accès Non Autorisé', '❌ ÉCHEC - Accès autorisé avec token invalide', 'fail');
                }
            } catch (error) {
                addTestResult('🚫 Test Accès Non Autorisé', `✅ RÉUSSI - Erreur attendue: ${error.message}`, 'success');
            }
        }

        // Simuler un utilisateur spécifique
        async function simulateUser(userId) {
            addTestResult(`👤 Simulation Utilisateur ${userId}`, 'EN COURS', 'info');
            
            try {
                const conversations = await makeAPIRequest('conversations', userId);
                
                if (conversations.success) {
                    const count = conversations.data.length;
                    addTestResult(`👤 Simulation Utilisateur ${userId}`, `✅ ${count} conversations trouvées`, 'success');
                    
                    // Afficher les détails des conversations
                    if (count > 0) {
                        const details = conversations.data.map(c => `Contact: ${c.contact_nom} (ID: ${c.contact_id})`).join('\n');
                        addTestResult(`📋 Détails Utilisateur ${userId}`, details, 'info');
                    }
                } else {
                    addTestResult(`👤 Simulation Utilisateur ${userId}`, `❌ ÉCHEC - ${conversations.error}`, 'fail');
                }
            } catch (error) {
                addTestResult(`👤 Simulation Utilisateur ${userId}`, `❌ ERREUR - ${error.message}`, 'fail');
            }
        }

        // Lancer tous les tests
        async function runAllTests() {
            clearResults();
            addTestResult('🚀 Tests de Confidentialité', 'DÉMARRAGE DE TOUS LES TESTS', 'info');
            
            await testMessageIsolation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testFrontendFiltering();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMessageDisplay();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUnauthorizedAccess();
            
            addTestResult('🏁 Tests Terminés', 'Tous les tests ont été exécutés', 'info');
        }

        // Ajouter un résultat de test
        function addTestResult(title, message, type) {
            const timestamp = new Date().toLocaleTimeString();
            const result = { title, message, type, timestamp };
            testResults.push(result);
            displayResults();
        }

        // Afficher les résultats
        function displayResults() {
            const container = document.getElementById('test-results');
            
            if (testResults.length === 0) {
                container.innerHTML = '<div class="test-section"><p>Aucun test exécuté</p></div>';
                return;
            }

            let html = '<div class="test-section"><h2>📊 Résultats des Tests</h2><table><thead><tr><th>Heure</th><th>Test</th><th>Résultat</th></tr></thead><tbody>';
            
            testResults.forEach(result => {
                const statusClass = result.type === 'success' ? 'status-ok' : result.type === 'fail' ? 'status-fail' : '';
                html += `<tr><td>${result.timestamp}</td><td>${result.title}</td><td class="${statusClass}">${result.message}</td></tr>`;
            });
            
            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // Effacer les résultats
        function clearResults() {
            testResults = [];
            displayResults();
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            displayResults();
        });
    </script>
</body>
</html>
