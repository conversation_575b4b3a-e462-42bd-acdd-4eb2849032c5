<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reconstruction Système Messagerie Complet</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1400px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #667eea; font-size: 2.8rem; margin-bottom: 10px; }
        .header p { color: #666; font-size: 1.2rem; }
        .progress-bar { background: #e9ecef; border-radius: 10px; height: 20px; margin: 20px 0; overflow: hidden; }
        .progress-fill { background: linear-gradient(90deg, #667eea, #764ba2); height: 100%; transition: width 0.5s ease; }
        .step { background: #f8f9fa; padding: 25px; border-radius: 15px; margin: 20px 0; border-left: 5px solid #667eea; }
        .step h3 { color: #667eea; margin-bottom: 15px; }
        .success { background: #d4edda; border-left-color: #28a745; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
        pre { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; overflow-x: auto; font-size: 12px; line-height: 1.4; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; transition: all 0.3s; }
        .btn:hover { background: #5a67d8; transform: translateY(-2px); }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; }
        .feature-icon { font-size: 2rem; margin-bottom: 10px; }
        .feature-title { font-size: 1.2rem; font-weight: 600; margin-bottom: 10px; }
        .feature-desc { opacity: 0.9; font-size: 0.9rem; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 RECONSTRUCTION SYSTÈME MESSAGERIE</h1>
            <p>Création d'un système complet de messagerie avec toutes les fonctionnalités WhatsApp</p>
        </div>
        
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">🔒</div>
                <div class="feature-title">Confidentialité Stricte</div>
                <div class="feature-desc">Messages privés entre expéditeur et destinataire uniquement</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">✏️</div>
                <div class="feature-title">Modification Messages</div>
                <div class="feature-desc">Édition avec sauvegarde de l'original et timestamps</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🗑️</div>
                <div class="feature-title">Suppression WhatsApp</div>
                <div class="feature-desc">Suppression pour soi ou pour tous les participants</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">👥</div>
                <div class="feature-title">Rôles Autorisés</div>
                <div class="feature-desc">Parents, Enseignants, Admins uniquement (pas d'étudiants)</div>
            </div>
        </div>
        
        <?php
        $steps = [];
        $errors = [];
        $totalSteps = 7;
        $currentStep = 0;
        
        try {
            // Configuration de la base de données
            $host = 'localhost';
            $dbname = 'GestionScolaire';
            $username = 'root';
            $password = '';
            
            // Connexion à la base de données
            try {
                $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $currentStep++;
                $steps[] = 'Connexion base de données réussie';
            } catch (Exception $e) {
                $errors[] = 'Connexion BDD échouée: ' . $e->getMessage();
                throw $e;
            }
            
            // ÉTAPE 1: Vérification/Création de la table messages
            echo '<div class="step">';
            echo '<h3>📋 Étape 1: Structure Table Messages</h3>';
            
            try {
                // Vérifier si la table existe
                $stmt = $pdo->query("SHOW TABLES LIKE 'messages'");
                if ($stmt->rowCount() == 0) {
                    echo '<div class="warning">⚠️ Table messages n\'existe pas - Création...</div>';
                    
                    $createTableSQL = "
                    CREATE TABLE messages (
                        id INT(10) NOT NULL AUTO_INCREMENT,
                        expediteur_id INT(10) NULL DEFAULT NULL,
                        destinataire_id INT(10) NULL DEFAULT NULL,
                        message TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
                        date_envoi DATETIME NULL DEFAULT CURRENT_TIMESTAMP,
                        lu TINYINT(1) NULL DEFAULT '0',
                        modifie TINYINT(1) NULL DEFAULT '0',
                        date_modification DATETIME NULL DEFAULT NULL,
                        supprime_par_expediteur TINYINT(1) NULL DEFAULT '0',
                        supprime_par_destinataire TINYINT(1) NULL DEFAULT '0',
                        supprime_expediteur TINYINT(1) NULL DEFAULT '0',
                        supprime_destinataire TINYINT(1) NULL DEFAULT '0',
                        message_original TEXT NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
                        PRIMARY KEY (id) USING BTREE,
                        INDEX expediteur_id (expediteur_id) USING BTREE,
                        INDEX destinataire_id (destinataire_id) USING BTREE,
                        CONSTRAINT messages_ibfk_1 FOREIGN KEY (expediteur_id) REFERENCES utilisateurs (id) ON UPDATE NO ACTION ON DELETE NO ACTION,
                        CONSTRAINT messages_ibfk_2 FOREIGN KEY (destinataire_id) REFERENCES utilisateurs (id) ON UPDATE NO ACTION ON DELETE NO ACTION
                    )
                    COLLATE='utf8mb4_general_ci'
                    ENGINE=InnoDB
                    AUTO_INCREMENT=1
                    ";
                    
                    $pdo->exec($createTableSQL);
                    echo '<div class="success">✅ Table messages créée avec succès</div>';
                } else {
                    echo '<div class="success">✅ Table messages existe déjà</div>';
                }
                
                // Vérifier la structure
                $stmt = $pdo->query("DESCRIBE messages");
                $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $requiredFields = [
                    'id', 'expediteur_id', 'destinataire_id', 'message', 'date_envoi', 'lu',
                    'modifie', 'date_modification', 'supprime_par_expediteur', 'supprime_par_destinataire',
                    'supprime_expediteur', 'supprime_destinataire', 'message_original'
                ];
                
                $existingFields = array_column($structure, 'Field');
                $missingFields = array_diff($requiredFields, $existingFields);
                
                if (empty($missingFields)) {
                    echo '<div class="success">✅ Tous les champs requis sont présents</div>';
                    $currentStep++;
                    $steps[] = 'Structure table messages validée';
                } else {
                    echo '<div class="error">❌ Champs manquants: ' . implode(', ', $missingFields) . '</div>';
                    $errors[] = 'Structure table incomplète';
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur table messages: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur création/vérification table';
            }
            echo '</div>';
            
            // ÉTAPE 2: Création des données de test
            echo '<div class="step">';
            echo '<h3>📝 Étape 2: Données de Test</h3>';
            
            try {
                // Vérifier s'il y a des utilisateurs autorisés
                $stmt = $pdo->query("
                    SELECT u.id, u.nom, r.nom as role
                    FROM utilisateurs u
                    LEFT JOIN roles r ON u.role_id = r.id
                    WHERE r.nom IN ('parent', 'enseignant', 'admin', 'responsable')
                    LIMIT 5
                ");
                $authorizedUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($authorizedUsers) >= 2) {
                    echo '<div class="success">✅ ' . count($authorizedUsers) . ' utilisateurs autorisés trouvés</div>';
                    
                    // Vérifier s'il y a déjà des messages
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM messages");
                    $messageCount = $stmt->fetch()['count'];
                    
                    if ($messageCount < 5) {
                        echo '<div class="info">Création de messages de test...</div>';
                        
                        $testMessages = [
                            [
                                'expediteur' => $authorizedUsers[0]['id'],
                                'destinataire' => $authorizedUsers[1]['id'],
                                'message' => 'Bonjour, j\'espère que vous allez bien. Pouvons-nous discuter du projet ?'
                            ],
                            [
                                'expediteur' => $authorizedUsers[1]['id'],
                                'destinataire' => $authorizedUsers[0]['id'],
                                'message' => 'Bonjour ! Bien sûr, je suis disponible pour en parler. Quand vous convient-il ?'
                            ],
                            [
                                'expediteur' => $authorizedUsers[0]['id'],
                                'destinataire' => $authorizedUsers[1]['id'],
                                'message' => 'Parfait ! Que diriez-vous de demain matin vers 10h ?'
                            ]
                        ];
                        
                        if (count($authorizedUsers) >= 3) {
                            $testMessages[] = [
                                'expediteur' => $authorizedUsers[0]['id'],
                                'destinataire' => $authorizedUsers[2]['id'],
                                'message' => 'Salut ! Comment ça va ? J\'ai une question importante à te poser.'
                            ];
                            $testMessages[] = [
                                'expediteur' => $authorizedUsers[2]['id'],
                                'destinataire' => $authorizedUsers[0]['id'],
                                'message' => 'Salut ! Ça va très bien merci. Dis-moi tout, je t\'écoute.'
                            ];
                        }
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO messages (expediteur_id, destinataire_id, message, date_envoi, lu) 
                            VALUES (?, ?, ?, NOW() - INTERVAL FLOOR(RAND() * 24) HOUR, ?)
                        ");
                        
                        foreach ($testMessages as $index => $msg) {
                            $isRead = $index < 2 ? 1 : 0; // Les 2 premiers sont lus
                            $stmt->execute([
                                $msg['expediteur'],
                                $msg['destinataire'],
                                $msg['message'],
                                $isRead
                            ]);
                        }
                        
                        echo '<div class="success">✅ ' . count($testMessages) . ' messages de test créés</div>';
                    } else {
                        echo '<div class="success">✅ ' . $messageCount . ' messages existants</div>';
                    }
                    
                    $currentStep++;
                    $steps[] = 'Données de test créées/vérifiées';
                    
                } else {
                    echo '<div class="warning">⚠️ Pas assez d\'utilisateurs autorisés pour créer des conversations</div>';
                    $errors[] = 'Utilisateurs insuffisants';
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur données test: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur création données test';
            }
            echo '</div>';
            
            // ÉTAPE 3: Création du nouveau AuthManager
            echo '<div class="step">';
            echo '<h3>🔐 Étape 3: AuthManager Sécurisé</h3>';
            
            $authManagerCode = '<?php
class AuthManager {
    private $pdo;
    
    public function __construct() {
        $host = \'localhost\';
        $dbname = \'GestionScolaire\';
        $username = \'root\';
        $password = \'\';
        
        try {
            $this->pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception("Erreur connexion AuthManager: " . $e->getMessage());
        }
    }
    
    /**
     * Authentifier un utilisateur par token
     */
    public function authenticateUser($token) {
        try {
            // Pour les tests, accepter les tokens de test
            if (strpos($token, \'test_\') === 0) {
                return $this->getTestUser($token);
            }
            
            // Ici vous pouvez implémenter votre logique JWT réelle
            // Pour l\'instant, simulation avec token simple
            if (preg_match(\'/^user_(\d+)$/\', $token, $matches)) {
                $userId = (int)$matches[1];
                return $this->getUserById($userId);
            }
            
            throw new Exception(\'Token invalide\');
            
        } catch (Exception $e) {
            logError(\'Erreur authentification\', [\'token\' => $token, \'error\' => $e->getMessage()]);
            throw new Exception(\'Authentification échouée\');
        }
    }
    
    /**
     * Récupérer un utilisateur par ID
     */
    public function getUserById($userId) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE u.id = ?
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            if (!$user) {
                throw new Exception(\'Utilisateur introuvable\');
            }
            
            // Vérifier que l\'utilisateur a un rôle autorisé
            if (!in_array($user[\'role\'], [\'parent\', \'enseignant\', \'admin\', \'responsable\'])) {
                throw new Exception(\'Rôle non autorisé pour la messagerie\');
            }
            
            return $user;
            
        } catch (Exception $e) {
            logError(\'Erreur getUserById\', [\'user_id\' => $userId, \'error\' => $e->getMessage()]);
            throw $e;
        }
    }
    
    /**
     * Utilisateur de test pour le développement
     */
    private function getTestUser($token) {
        try {
            // Récupérer le premier utilisateur autorisé pour les tests
            $stmt = $this->pdo->query("
                SELECT u.id, u.nom, u.email, r.nom as role
                FROM utilisateurs u
                LEFT JOIN roles r ON u.role_id = r.id
                WHERE r.nom IN (\'parent\', \'enseignant\', \'admin\', \'responsable\')
                LIMIT 1
            ");
            $user = $stmt->fetch();
            
            if (!$user) {
                // Créer un utilisateur de test si aucun n\'existe
                $stmt = $this->pdo->prepare("
                    INSERT INTO utilisateurs (nom, email, mot_de_passe, role_id) 
                    VALUES (?, ?, ?, (SELECT id FROM roles WHERE nom = \'admin\' LIMIT 1))
                ");
                $stmt->execute([\'Test Admin\', \'<EMAIL>\', password_hash(\'test123\', PASSWORD_DEFAULT)]);
                
                $userId = $this->pdo->lastInsertId();
                return $this->getUserById($userId);
            }
            
            return $user;
            
        } catch (Exception $e) {
            logError(\'Erreur getTestUser\', [\'token\' => $token, \'error\' => $e->getMessage()]);
            throw new Exception(\'Impossible de créer utilisateur de test\');
        }
    }
    
    /**
     * Vérifier si un utilisateur peut accéder à la messagerie
     */
    public function canAccessMessaging($user) {
        return in_array($user[\'role\'], [\'parent\', \'enseignant\', \'admin\', \'responsable\']);
    }
    
    /**
     * Vérifier si un utilisateur peut voir un message spécifique
     */
    public function canAccessMessage($user, $message) {
        return ($message[\'expediteur_id\'] == $user[\'id\'] || $message[\'destinataire_id\'] == $user[\'id\']);
    }
}

/**
 * Fonction de logging pour debug
 */
function logError($message, $data = []) {
    $logEntry = [
        \'timestamp\' => date(\'Y-m-d H:i:s\'),
        \'message\' => $message,
        \'data\' => $data
    ];
    
    // Écrire dans un fichier de log (optionnel)
    // file_put_contents(\'messaging_debug.log\', json_encode($logEntry) . "\n", FILE_APPEND);
}
?>';
            
            try {
                file_put_contents('AuthManager.php', $authManagerCode);
                echo '<div class="success">✅ AuthManager.php créé avec succès</div>';
                $currentStep++;
                $steps[] = 'AuthManager sécurisé créé';
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur création AuthManager: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur création AuthManager';
            }
            echo '</div>';
            
            // Affichage de la progression
            $progressPercent = ($currentStep / $totalSteps) * 100;
            echo '<div class="progress-bar">';
            echo '<div class="progress-fill" style="width: ' . $progressPercent . '%"></div>';
            echo '</div>';
            echo '<div class="info">Progression: ' . $currentStep . '/' . $totalSteps . ' étapes (' . round($progressPercent) . '%)</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ ERREUR GÉNÉRALE: ' . $e->getMessage() . '</div>';
            $errors[] = 'Erreur générale de reconstruction';
        }
        ?>
        
        <div style="text-align: center; margin-top: 40px;">
            <h2>📊 État de la Reconstruction</h2>
            
            <?php if (empty($errors) && $currentStep >= 3): ?>
                <div class="success">
                    <h3>✅ Reconstruction en Cours de Réussite !</h3>
                    <p><strong>Étapes complétées :</strong></p>
                    <ul style="text-align: left;">
                        <?php foreach ($steps as $step): ?>
                            <li>✅ <?php echo $step; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <a href="creation_message_manager.php" class="btn btn-success">➡️ CONTINUER - MESSAGEMANAGER</a>
                
            <?php elseif (!empty($errors)): ?>
                <div class="error">
                    <h3>❌ Erreurs Détectées</h3>
                    <ul style="text-align: left;">
                        <?php foreach ($errors as $error): ?>
                            <li>❌ <?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <a href="?" class="btn">🔄 RELANCER RECONSTRUCTION</a>
                
            <?php else: ?>
                <div class="warning">
                    <h3>⚠️ Reconstruction Partielle</h3>
                    <p>Certaines étapes ont été complétées mais d'autres restent à faire.</p>
                </div>
                
                <a href="?" class="btn">🔄 CONTINUER RECONSTRUCTION</a>
            <?php endif; ?>
            
            <a href="diagnostic_table_messages.php" class="btn">🔍 DIAGNOSTIC</a>
        </div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>🎯 Fonctionnalités à Implémenter</h3>
            <div class="feature-grid">
                <div style="background: #f8f9fa; color: #333; padding: 15px; border-radius: 10px;">
                    <strong>✅ Déjà Fait :</strong><br>
                    • Structure table messages<br>
                    • AuthManager sécurisé<br>
                    • Données de test
                </div>
                <div style="background: #f8f9fa; color: #333; padding: 15px; border-radius: 10px;">
                    <strong>🔄 En Cours :</strong><br>
                    • MessageManager complet<br>
                    • API endpoints<br>
                    • Gestion des erreurs
                </div>
                <div style="background: #f8f9fa; color: #333; padding: 15px; border-radius: 10px;">
                    <strong>📋 À Faire :</strong><br>
                    • Interface React moderne<br>
                    • Fonctionnalités WhatsApp<br>
                    • Tests complets
                </div>
            </div>
        </div>
    </div>
</body>
</html>
