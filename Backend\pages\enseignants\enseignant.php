<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de base de données
try {
    $pdo = new PDO("mysql:host=localhost;dbname=GestionScolaire;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur de connexion à la base de données: ' . $e->getMessage()]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet($pdo);
            break;
        case 'POST':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePost($pdo, $input);
            break;
        case 'PUT':
            $input = json_decode(file_get_contents("php://input"), true);
            handlePut($pdo, $input);
            break;
        case 'DELETE':
            $input = json_decode(file_get_contents("php://input"), true);
            handleDelete($pdo, $input);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Méthode non autorisée']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

function handleGet($pdo) {
    try {
        // Récupérer tous les enseignants avec leurs informations utilisateur
        $sql = "
            SELECT 
                e.id,
                e.utilisateur_id,
                e.nom_prenom,
                e.email,
                e.telephone,
                e.specialite,
                e.date_embauche,
                e.salaire,
                e.statut,
                u.nom as utilisateur_nom,
                u.email as utilisateur_email,
                r.nom as role_nom
            FROM enseignants e
            LEFT JOIN utilisateurs u ON e.utilisateur_id = u.id
            LEFT JOIN roles r ON u.role_id = r.id
            ORDER BY e.nom_prenom
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $enseignants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode($enseignants);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des enseignants: ' . $e->getMessage()]);
    }
}

function handlePost($pdo, $input) {
    if (!isset($input['utilisateur_id']) || !isset($input['nom_prenom']) || !isset($input['email'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Données manquantes (utilisateur_id, nom_prenom, email requis)']);
        return;
    }
    
    try {
        // Vérifier que l'utilisateur existe et a le rôle enseignant
        $checkStmt = $pdo->prepare("
            SELECT u.id, r.nom as role_nom 
            FROM utilisateurs u 
            INNER JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ? AND r.nom = 'enseignant'
        ");
        $checkStmt->execute([$input['utilisateur_id']]);
        $user = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            http_response_code(400);
            echo json_encode(['error' => 'L\'utilisateur sélectionné n\'existe pas ou n\'a pas le rôle enseignant']);
            return;
        }
        
        // Vérifier que l'utilisateur n'est pas déjà dans la table enseignants
        $existStmt = $pdo->prepare("SELECT id FROM enseignants WHERE utilisateur_id = ?");
        $existStmt->execute([$input['utilisateur_id']]);
        if ($existStmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Cet utilisateur est déjà enregistré comme enseignant']);
            return;
        }
        
        // Vérifier que l'email n'existe pas déjà
        $emailStmt = $pdo->prepare("SELECT id FROM enseignants WHERE email = ?");
        $emailStmt->execute([$input['email']]);
        if ($emailStmt->fetch()) {
            http_response_code(400);
            echo json_encode(['error' => 'Un enseignant avec cet email existe déjà']);
            return;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO enseignants (utilisateur_id, nom_prenom, email, telephone, specialite, date_embauche, salaire, statut) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $statut = $input['statut'] ?? 'actif';
        $telephone = $input['telephone'] ?? null;
        $specialite = $input['specialite'] ?? null;
        $date_embauche = $input['date_embauche'] ?? null;
        $salaire = $input['salaire'] ?? null;
        
        $stmt->execute([
            $input['utilisateur_id'],
            $input['nom_prenom'],
            $input['email'],
            $telephone,
            $specialite,
            $date_embauche,
            $salaire,
            $statut
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Enseignant créé avec succès',
            'id' => $pdo->lastInsertId()
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la création: ' . $e->getMessage()]);
    }
}

function handlePut($pdo, $input) {
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de l\'enseignant manquant']);
        return;
    }
    
    try {
        // Vérifier que l'enseignant existe
        $checkStmt = $pdo->prepare("SELECT id FROM enseignants WHERE id = ?");
        $checkStmt->execute([$input['id']]);
        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode(['error' => 'Enseignant non trouvé']);
            return;
        }
        
        // Vérifier que l'email n'existe pas déjà pour un autre enseignant
        if (isset($input['email'])) {
            $emailStmt = $pdo->prepare("SELECT id FROM enseignants WHERE email = ? AND id != ?");
            $emailStmt->execute([$input['email'], $input['id']]);
            if ($emailStmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => 'Un autre enseignant avec cet email existe déjà']);
                return;
            }
        }
        
        $stmt = $pdo->prepare("
            UPDATE enseignants 
            SET nom_prenom = ?, 
                email = ?, 
                telephone = ?, 
                specialite = ?, 
                date_embauche = ?, 
                salaire = ?, 
                statut = ?
            WHERE id = ?
        ");
        
        $stmt->execute([
            $input['nom_prenom'] ?? '',
            $input['email'] ?? '',
            $input['telephone'] ?? null,
            $input['specialite'] ?? null,
            $input['date_embauche'] ?? null,
            $input['salaire'] ?? null,
            $input['statut'] ?? 'actif',
            $input['id']
        ]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Enseignant modifié avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'message' => 'Aucune modification nécessaire'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la modification: ' . $e->getMessage()]);
    }
}

function handleDelete($pdo, $input) {
    if (!isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'ID de l\'enseignant manquant']);
        return;
    }
    
    try {
        $stmt = $pdo->prepare("DELETE FROM enseignants WHERE id = ?");
        $stmt->execute([$input['id']]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Enseignant supprimé avec succès'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Enseignant non trouvé'
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la suppression: ' . $e->getMessage()]);
    }
}
?>
