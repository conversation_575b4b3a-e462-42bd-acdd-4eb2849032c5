<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic Table Messages - Système Messagerie Complet</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1400px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #667eea; font-size: 2.5rem; margin-bottom: 10px; }
        .header p { color: #666; font-size: 1.1rem; }
        .diagnostic-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; margin: 30px 0; }
        .diagnostic-card { background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #667eea; }
        .diagnostic-card h3 { color: #667eea; margin-bottom: 15px; font-size: 1.3rem; }
        .success { background: #d4edda; border-left-color: #28a745; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #f8d7da; border-left-color: #dc3545; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { background: #fff3cd; border-left-color: #ffc107; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { background: #d1ecf1; border-left-color: #17a2b8; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
        pre { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; overflow-x: auto; font-size: 13px; line-height: 1.5; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; transition: all 0.3s; }
        .btn:hover { background: #5a67d8; transform: translateY(-2px); }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 30px 0; }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; text-align: center; }
        .stat-number { font-size: 2.5rem; font-weight: 700; margin-bottom: 5px; }
        .stat-label { opacity: 0.9; font-size: 14px; text-transform: uppercase; letter-spacing: 1px; }
        .table-structure { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .field-row { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #dee2e6; }
        .field-name { font-weight: 600; color: #495057; }
        .field-type { color: #6c757d; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 DIAGNOSTIC TABLE MESSAGES</h1>
            <p>Analyse complète de la structure et des données pour le système de messagerie</p>
        </div>
        
        <?php
        $diagnostics = [];
        $errors = [];
        $warnings = [];
        $stats = [];
        
        try {
            // Configuration de la base de données
            $host = 'localhost';
            $dbname = 'GestionScolaire';
            $username = 'root';
            $password = '';
            
            echo '<div class="info">🔧 Diagnostic de la table messages et du système de messagerie...</div>';
            
            // Connexion à la base de données
            try {
                $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                echo '<div class="success">✅ Connexion à la base de données réussie</div>';
                $diagnostics['database_connection'] = true;
            } catch (Exception $e) {
                echo '<div class="error">❌ ERREUR CONNEXION BDD: ' . $e->getMessage() . '</div>';
                $errors[] = 'Connexion base de données échouée';
                exit;
            }
            
            echo '<div class="diagnostic-grid">';
            
            // DIAGNOSTIC 1: Structure de la table messages
            echo '<div class="diagnostic-card">';
            echo '<h3>📋 Structure Table Messages</h3>';
            
            try {
                $stmt = $pdo->query("DESCRIBE messages");
                $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($structure) > 0) {
                    echo '<div class="success">✅ Table messages existe</div>';
                    echo '<div class="table-structure">';
                    
                    $requiredFields = [
                        'id' => 'INT(10) AUTO_INCREMENT',
                        'expediteur_id' => 'INT(10)',
                        'destinataire_id' => 'INT(10)',
                        'message' => 'TEXT',
                        'date_envoi' => 'DATETIME',
                        'lu' => 'TINYINT(1)',
                        'modifie' => 'TINYINT(1)',
                        'date_modification' => 'DATETIME',
                        'supprime_par_expediteur' => 'TINYINT(1)',
                        'supprime_par_destinataire' => 'TINYINT(1)',
                        'supprime_expediteur' => 'TINYINT(1)',
                        'supprime_destinataire' => 'TINYINT(1)',
                        'message_original' => 'TEXT'
                    ];
                    
                    $existingFields = [];
                    foreach ($structure as $field) {
                        $existingFields[$field['Field']] = $field['Type'];
                        echo '<div class="field-row">';
                        echo '<span class="field-name">' . $field['Field'] . '</span>';
                        echo '<span class="field-type">' . $field['Type'] . '</span>';
                        echo '</div>';
                    }
                    echo '</div>';
                    
                    // Vérifier les champs manquants
                    $missingFields = [];
                    foreach ($requiredFields as $fieldName => $fieldType) {
                        if (!isset($existingFields[$fieldName])) {
                            $missingFields[] = $fieldName;
                        }
                    }
                    
                    if (empty($missingFields)) {
                        echo '<div class="success">✅ Tous les champs requis sont présents</div>';
                        $diagnostics['table_structure'] = true;
                    } else {
                        echo '<div class="warning">⚠️ Champs manquants: ' . implode(', ', $missingFields) . '</div>';
                        $warnings[] = 'Champs manquants dans la table messages';
                        $diagnostics['table_structure'] = false;
                    }
                    
                } else {
                    echo '<div class="error">❌ Table messages introuvable</div>';
                    $errors[] = 'Table messages n\'existe pas';
                    $diagnostics['table_structure'] = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur structure: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur lors de la vérification de la structure';
                $diagnostics['table_structure'] = false;
            }
            echo '</div>';
            
            // DIAGNOSTIC 2: Données existantes
            echo '<div class="diagnostic-card">';
            echo '<h3>📊 Données Messages</h3>';
            
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM messages");
                $totalMessages = $stmt->fetch()['total'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM messages WHERE lu = 0");
                $unreadMessages = $stmt->fetch()['total'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM messages WHERE modifie = 1");
                $modifiedMessages = $stmt->fetch()['total'];
                
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM messages WHERE supprime_par_expediteur = 1 OR supprime_par_destinataire = 1");
                $deletedMessages = $stmt->fetch()['total'];
                
                echo '<div class="stats-grid">';
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . $totalMessages . '</div>';
                echo '<div class="stat-label">Messages Total</div>';
                echo '</div>';
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . $unreadMessages . '</div>';
                echo '<div class="stat-label">Non Lus</div>';
                echo '</div>';
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . $modifiedMessages . '</div>';
                echo '<div class="stat-label">Modifiés</div>';
                echo '</div>';
                echo '<div class="stat-card">';
                echo '<div class="stat-number">' . $deletedMessages . '</div>';
                echo '<div class="stat-label">Supprimés</div>';
                echo '</div>';
                echo '</div>';
                
                $stats = [
                    'total' => $totalMessages,
                    'unread' => $unreadMessages,
                    'modified' => $modifiedMessages,
                    'deleted' => $deletedMessages
                ];
                
                if ($totalMessages > 0) {
                    echo '<div class="success">✅ ' . $totalMessages . ' messages trouvés</div>';
                    $diagnostics['has_data'] = true;
                } else {
                    echo '<div class="warning">⚠️ Aucun message dans la base</div>';
                    $warnings[] = 'Aucune donnée de test';
                    $diagnostics['has_data'] = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur données: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur lors de la lecture des données';
                $diagnostics['has_data'] = false;
            }
            echo '</div>';
            
            // DIAGNOSTIC 3: Relations avec utilisateurs
            echo '<div class="diagnostic-card">';
            echo '<h3>🔗 Relations Utilisateurs</h3>';
            
            try {
                $stmt = $pdo->query("
                    SELECT 
                        COUNT(*) as total_relations,
                        COUNT(CASE WHEN u1.id IS NOT NULL THEN 1 END) as valid_expediteurs,
                        COUNT(CASE WHEN u2.id IS NOT NULL THEN 1 END) as valid_destinataires
                    FROM messages m
                    LEFT JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                    LEFT JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                ");
                $relations = $stmt->fetch();
                
                echo '<div class="info">Relations trouvées: ' . $relations['total_relations'] . '</div>';
                echo '<div class="info">Expéditeurs valides: ' . $relations['valid_expediteurs'] . '</div>';
                echo '<div class="info">Destinataires valides: ' . $relations['valid_destinataires'] . '</div>';
                
                if ($relations['valid_expediteurs'] == $relations['total_relations'] && 
                    $relations['valid_destinataires'] == $relations['total_relations']) {
                    echo '<div class="success">✅ Toutes les relations sont valides</div>';
                    $diagnostics['relations_valid'] = true;
                } else {
                    echo '<div class="warning">⚠️ Certaines relations sont invalides</div>';
                    $warnings[] = 'Relations utilisateurs invalides';
                    $diagnostics['relations_valid'] = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur relations: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur lors de la vérification des relations';
                $diagnostics['relations_valid'] = false;
            }
            echo '</div>';
            
            // DIAGNOSTIC 4: Utilisateurs autorisés
            echo '<div class="diagnostic-card">';
            echo '<h3>👥 Utilisateurs Autorisés</h3>';
            
            try {
                $stmt = $pdo->query("
                    SELECT r.nom as role, COUNT(u.id) as count
                    FROM utilisateurs u
                    LEFT JOIN roles r ON u.role_id = r.id
                    WHERE r.nom IN ('parent', 'enseignant', 'admin', 'responsable')
                    GROUP BY r.nom
                    ORDER BY count DESC
                ");
                $authorizedUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $totalAuthorized = 0;
                foreach ($authorizedUsers as $role) {
                    echo '<div class="info">' . ucfirst($role['role']) . ': ' . $role['count'] . ' utilisateurs</div>';
                    $totalAuthorized += $role['count'];
                }
                
                if ($totalAuthorized > 0) {
                    echo '<div class="success">✅ ' . $totalAuthorized . ' utilisateurs autorisés trouvés</div>';
                    $diagnostics['authorized_users'] = true;
                } else {
                    echo '<div class="warning">⚠️ Aucun utilisateur autorisé trouvé</div>';
                    $warnings[] = 'Pas d\'utilisateurs autorisés';
                    $diagnostics['authorized_users'] = false;
                }
                
                // Vérifier les étudiants (qui ne doivent PAS avoir accès)
                $stmt = $pdo->query("
                    SELECT COUNT(u.id) as count
                    FROM utilisateurs u
                    LEFT JOIN roles r ON u.role_id = r.id
                    WHERE r.nom = 'etudiant'
                ");
                $students = $stmt->fetch()['count'];
                echo '<div class="info">🚫 Étudiants (EXCLUS): ' . $students . '</div>';
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur utilisateurs: ' . $e->getMessage() . '</div>';
                $errors[] = 'Erreur lors de la vérification des utilisateurs';
                $diagnostics['authorized_users'] = false;
            }
            echo '</div>';
            
            echo '</div>'; // Fin diagnostic-grid
            
            // DIAGNOSTIC 5: Test requête conversations
            echo '<h2>🔍 Test Requête Conversations</h2>';
            
            try {
                $stmt = $pdo->query("
                    SELECT u.id, u.nom, r.nom as role
                    FROM utilisateurs u
                    LEFT JOIN roles r ON u.role_id = r.id
                    WHERE r.nom IN ('parent', 'enseignant', 'admin', 'responsable')
                    LIMIT 1
                ");
                $testUser = $stmt->fetch();
                
                if ($testUser) {
                    echo '<div class="info">Test avec utilisateur: ' . $testUser['nom'] . ' (ID: ' . $testUser['id'] . ', Rôle: ' . $testUser['role'] . ')</div>';
                    
                    $stmt = $pdo->prepare("
                        SELECT DISTINCT 
                            CASE 
                                WHEN m.expediteur_id = ? THEN m.destinataire_id 
                                ELSE m.expediteur_id 
                            END as contact_id,
                            CASE 
                                WHEN m.expediteur_id = ? THEN COALESCE(u2.nom, 'Utilisateur inconnu')
                                ELSE COALESCE(u1.nom, 'Utilisateur inconnu')
                            END as contact_nom,
                            MAX(m.date_envoi) as derniere_activite,
                            COUNT(*) as total_messages,
                            COUNT(CASE WHEN m.destinataire_id = ? AND m.lu = 0 THEN 1 END) as messages_non_lus
                        FROM messages m
                        LEFT JOIN utilisateurs u1 ON m.expediteur_id = u1.id
                        LEFT JOIN utilisateurs u2 ON m.destinataire_id = u2.id
                        WHERE (m.expediteur_id = ? OR m.destinataire_id = ?)
                        AND (m.supprime_par_expediteur = 0 OR m.expediteur_id != ?)
                        AND (m.supprime_par_destinataire = 0 OR m.destinataire_id != ?)
                        GROUP BY contact_id, contact_nom
                        HAVING contact_id IS NOT NULL AND contact_id != ?
                        ORDER BY derniere_activite DESC
                    ");
                    
                    $userId = $testUser['id'];
                    $stmt->execute([$userId, $userId, $userId, $userId, $userId, $userId, $userId, $userId]);
                    $conversations = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (count($conversations) > 0) {
                        echo '<div class="success">✅ ' . count($conversations) . ' conversations trouvées</div>';
                        echo '<h4>Aperçu des conversations :</h4>';
                        echo '<pre>';
                        foreach (array_slice($conversations, 0, 3) as $conv) {
                            echo "Contact: {$conv['contact_nom']} (ID: {$conv['contact_id']})\n";
                            echo "Messages: {$conv['total_messages']}, Non lus: {$conv['messages_non_lus']}\n";
                            echo "Dernière activité: {$conv['derniere_activite']}\n\n";
                        }
                        echo '</pre>';
                        $diagnostics['conversations_query'] = true;
                    } else {
                        echo '<div class="warning">⚠️ Aucune conversation trouvée pour cet utilisateur</div>';
                        $warnings[] = 'Pas de conversations de test';
                        $diagnostics['conversations_query'] = false;
                    }
                } else {
                    echo '<div class="warning">⚠️ Aucun utilisateur autorisé pour tester</div>';
                    $warnings[] = 'Pas d\'utilisateur de test';
                    $diagnostics['conversations_query'] = false;
                }
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Erreur test conversations: ' . $e->getMessage() . '</div>';
                echo '<pre>' . $e->getTraceAsString() . '</pre>';
                $errors[] = 'Erreur requête conversations';
                $diagnostics['conversations_query'] = false;
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ ERREUR GÉNÉRALE: ' . $e->getMessage() . '</div>';
            $errors[] = 'Erreur générale du diagnostic';
        }
        
        // Résumé du diagnostic
        $totalTests = count($diagnostics);
        $passedTests = array_sum($diagnostics);
        $successRate = $totalTests > 0 ? ($passedTests / $totalTests) * 100 : 0;
        ?>
        
        <div style="margin-top: 40px;">
            <h2>📊 Résumé du Diagnostic</h2>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $passedTests; ?>/<?php echo $totalTests; ?></div>
                    <div class="stat-label">Tests Réussis</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo round($successRate); ?>%</div>
                    <div class="stat-label">Taux de Réussite</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($errors); ?></div>
                    <div class="stat-label">Erreurs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($warnings); ?></div>
                    <div class="stat-label">Avertissements</div>
                </div>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="error">
                    <h3>❌ Erreurs Détectées</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($warnings)): ?>
                <div class="warning">
                    <h3>⚠️ Avertissements</h3>
                    <ul>
                        <?php foreach ($warnings as $warning): ?>
                            <li><?php echo $warning; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <div style="text-align: center; margin-top: 30px;">
                <h3>🔧 Actions Recommandées</h3>
                
                <?php if ($successRate < 80): ?>
                    <a href="reconstruction_systeme_messagerie.php" class="btn btn-danger">🚀 RECONSTRUCTION COMPLÈTE</a>
                    <a href="correction_table_messages.php" class="btn">🔧 CORRIGER TABLE</a>
                <?php else: ?>
                    <a href="creation_donnees_test.php" class="btn btn-success">📝 CRÉER DONNÉES TEST</a>
                    <a href="test_api_messagerie.php" class="btn">🧪 TESTER API</a>
                <?php endif; ?>
                
                <a href="?" class="btn">🔄 RELANCER DIAGNOSTIC</a>
            </div>
        </div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>📋 Prochaines Étapes</h3>
            <ol>
                <li><strong>Corriger les erreurs</strong> identifiées dans le diagnostic</li>
                <li><strong>Créer les données de test</strong> si nécessaire</li>
                <li><strong>Reconstruire le système</strong> avec toutes les fonctionnalités demandées</li>
                <li><strong>Implémenter l'interface</strong> moderne inspirée de WhatsApp</li>
                <li><strong>Tester la sécurité</strong> et la confidentialité</li>
            </ol>
        </div>
    </div>
</body>
</html>
