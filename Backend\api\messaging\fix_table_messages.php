<?php
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 Correction Table Messages</h1>";

try {
    // Connexion à la base de données
    $host = 'localhost';
    $dbname = 'GestionScolaire';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "✅ Connexion à la base de données réussie";
    echo "</div>";
    
    // Vérifier si la table messages existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'messages'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "⚠️ Table 'messages' n'existe pas. Création en cours...";
        echo "</div>";
        
        // Créer la table messages
        $createTableSQL = "
        CREATE TABLE messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            expediteur_id INT NOT NULL,
            destinataire_id INT NOT NULL,
            message TEXT NOT NULL,
            date_envoi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            lu BOOLEAN DEFAULT FALSE,
            modifie BOOLEAN DEFAULT FALSE,
            date_modification TIMESTAMP NULL,
            supprime_par_expediteur BOOLEAN DEFAULT FALSE,
            supprime_par_destinataire BOOLEAN DEFAULT FALSE,
            supprime_expediteur BOOLEAN DEFAULT FALSE,
            supprime_destinataire BOOLEAN DEFAULT FALSE,
            message_original TEXT NULL,
            INDEX idx_expediteur (expediteur_id),
            INDEX idx_destinataire (destinataire_id),
            INDEX idx_date_envoi (date_envoi),
            INDEX idx_conversation (expediteur_id, destinataire_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTableSQL);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ Table 'messages' créée avec succès";
        echo "</div>";
    } else {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "ℹ️ Table 'messages' existe déjà";
        echo "</div>";
    }
    
    // Vérifier la structure de la table
    $stmt = $pdo->query("DESCRIBE messages");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>📋 Structure de la table messages</h2>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>Colonne</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Vérifier les données de test
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM messages");
    $messageCount = $stmt->fetch()['count'];
    
    echo "<h2>📊 Données dans la table</h2>";
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "Nombre de messages: <strong>$messageCount</strong>";
    echo "</div>";
    
    if ($messageCount == 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "⚠️ Aucun message dans la table. Création de données de test...";
        echo "</div>";
        
        // Créer des données de test
        $testMessages = [
            [1, 2, "Bonjour ! Comment allez-vous ?"],
            [2, 1, "Très bien merci ! Et vous ?"],
            [1, 3, "Réunion prévue demain à 14h"],
            [3, 1, "Parfait, je serai là !"],
            [2, 3, "Avez-vous les documents ?"],
            [3, 2, "Oui, je vous les envoie par email"]
        ];
        
        $insertStmt = $pdo->prepare("
            INSERT INTO messages (expediteur_id, destinataire_id, message) 
            VALUES (?, ?, ?)
        ");
        
        foreach ($testMessages as $msg) {
            $insertStmt->execute($msg);
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ " . count($testMessages) . " messages de test créés";
        echo "</div>";
    }
    
    // Vérifier les utilisateurs
    echo "<h2>👥 Vérification des utilisateurs</h2>";
    
    $stmt = $pdo->query("SELECT id, nom, role_id FROM utilisateurs WHERE role_id IN (1, 2, 3, 4) LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "✅ Utilisateurs trouvés: " . count($users);
        echo "</div>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Nom</th><th>Role ID</th></tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['nom'] . "</td>";
            echo "<td>" . $user['role_id'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ Aucun utilisateur trouvé avec les rôles appropriés";
        echo "</div>";
    }
    
    // Test de l'API après correction
    echo "<h2>🧪 Test de l'API après correction</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='?action=test' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🚀 Tester API</a>";
    echo "<a href='?action=conversations' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>💬 Tester Conversations</a>";
    echo "<a href='test_react_simulation.html' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔬 Test React</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "❌ Erreur: " . $e->getMessage();
    echo "</div>";
}
?>
