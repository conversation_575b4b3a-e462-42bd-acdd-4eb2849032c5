{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\DiplomesCRUD.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\nconst DiplomesCRUD = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [diplomes, setDiplomes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingDiplome, setEditingDiplome] = useState(null);\n  const [etudiants, setEtudiants] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [yearFilter, setYearFilter] = useState('all');\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    titre: '',\n    date_obtention: ''\n  });\n\n  // Vérifier si l'utilisateur est Admin\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'responsable';\n  useEffect(() => {\n    fetchDiplomes();\n    if (isAdmin) {\n      fetchEtudiants();\n    }\n  }, [isAdmin]);\n  const fetchDiplomes = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setDiplomes(response.data);\n    } catch (error) {\n      console.error('Erreur lors du chargement des diplômes:', error);\n      Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchEtudiants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setEtudiants(response.data.etudiants);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des étudiants:', error);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des diplômes', 'error');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/diplomes/';\n      const method = editingDiplome ? 'PUT' : 'POST';\n      const data = editingDiplome ? {\n        ...formData,\n        id: editingDiplome.id\n      } : formData;\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      // Proposer de générer le PDF après création/modification\n      const result = await Swal.fire({\n        title: 'Succès!',\n        text: `Diplôme ${editingDiplome ? 'modifié' : 'créé'} avec succès`,\n        icon: 'success',\n        showCancelButton: true,\n        confirmButtonText: '📄 Générer PDF',\n        cancelButtonText: '✅ Continuer',\n        confirmButtonColor: '#3085d6',\n        cancelButtonColor: '#28a745'\n      });\n      if (result.isConfirmed) {\n        const diplomeId = editingDiplome ? editingDiplome.id : response.data.id;\n        generatePDF(diplomeId);\n      }\n      setShowModal(false);\n      setEditingDiplome(null);\n      resetForm();\n      fetchDiplomes();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur:', error);\n      Swal.fire('Erreur', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = diplome => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des diplômes', 'error');\n      return;\n    }\n    setEditingDiplome(diplome);\n    setFormData({\n      etudiant_id: diplome.etudiant_id,\n      titre: diplome.titre,\n      date_obtention: diplome.date_obtention\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des diplômes', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action est irréversible!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        const token = localStorage.getItem('token');\n        await axios.delete('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        Swal.fire('Supprimé!', 'Le diplôme a été supprimé.', 'success');\n        fetchDiplomes();\n      } catch (error) {\n        console.error('Erreur:', error);\n        Swal.fire('Erreur', 'Impossible de supprimer le diplôme', 'error');\n      }\n    }\n  };\n  const generatePDF = diplomeId => {\n    try {\n      console.log('🔄 Génération PDF pour diplôme ID:', diplomeId);\n\n      // Construire l'URL du PDF\n      const pdfUrl = `http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?diplome_id=${diplomeId}`;\n      console.log('📄 URL PDF:', pdfUrl);\n\n      // Afficher un message de chargement\n      Swal.fire({\n        title: 'Génération du PDF...',\n        text: 'Veuillez patienter pendant la génération du diplôme',\n        icon: 'info',\n        allowOutsideClick: false,\n        showConfirmButton: false,\n        timer: 2000,\n        timerProgressBar: true\n      });\n\n      // Ouvrir le PDF dans un nouvel onglet après un court délai\n      setTimeout(() => {\n        const newWindow = window.open(pdfUrl, '_blank');\n        if (!newWindow) {\n          Swal.fire({\n            title: 'Bloqueur de pop-up détecté',\n            text: 'Veuillez autoriser les pop-ups pour ce site et réessayer',\n            icon: 'warning',\n            confirmButtonText: 'Compris'\n          });\n        } else {\n          console.log('✅ PDF ouvert avec succès');\n        }\n      }, 500);\n    } catch (error) {\n      console.error('❌ Erreur lors de la génération PDF:', error);\n      Swal.fire({\n        title: 'Erreur',\n        text: 'Impossible de générer le PDF. Veuillez réessayer.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      titre: '',\n      date_obtention: ''\n    });\n  };\n\n  // Obtenir les années uniques pour le filtre\n  const getUniqueYears = () => {\n    const years = diplomes.map(d => new Date(d.date_obtention).getFullYear());\n    return [...new Set(years)].sort((a, b) => b - a);\n  };\n\n  // Filtrage des données\n  const filteredDiplomes = diplomes.filter(diplome => {\n    var _diplome$etudiant_nom, _diplome$titre;\n    const matchesSearch = ((_diplome$etudiant_nom = diplome.etudiant_nom) === null || _diplome$etudiant_nom === void 0 ? void 0 : _diplome$etudiant_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_diplome$titre = diplome.titre) === null || _diplome$titre === void 0 ? void 0 : _diplome$titre.toLowerCase().includes(searchTerm.toLowerCase()));\n    const diplomeYear = new Date(diplome.date_obtention).getFullYear().toString();\n    const matchesYear = yearFilter === 'all' || diplomeYear === yearFilter;\n    return matchesSearch && matchesYear;\n  });\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }\n    }, \"Chargement des dipl\\xF4mes...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 17\n    }\n  }, \"\\uD83C\\uDF93 Gestion des Dipl\\xF4mes\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 21\n    }\n  }, filteredDiplomes.length, \" dipl\\xF4me(s) trouv\\xE9(s)\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '10px',\n      alignItems: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 33\n    }\n  }), \" Nouveau Dipl\\xF4me\")))), !isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '15px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '8px',\n      marginBottom: '20px',\n      border: '1px solid #bbdefb'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      color: '#1976d2'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 21\n    }\n  }, \"\\u2139\\uFE0F Vous consultez les dipl\\xF4mes en mode lecture seule. Seul l'administrateur peut cr\\xE9er, modifier ou supprimer des dipl\\xF4mes.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filters-section\",\n    style: {\n      display: 'flex',\n      gap: '15px',\n      marginBottom: '20px',\n      padding: '15px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-box\",\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher par nom d'\\xE9tudiant ou titre de dipl\\xF4me...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"year-filter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 305,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: yearFilter,\n    onChange: e => setYearFilter(e.target.value),\n    style: {\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px',\n      minWidth: '120px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 25\n    }\n  }, \"Toutes les ann\\xE9es\"), getUniqueYears().map(year => /*#__PURE__*/React.createElement(\"option\", {\n    key: year,\n    value: year,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 29\n    }\n  }, year))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 13\n    }\n  }, filteredDiplomes.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/result.png\",\n    alt: \"Aucun dipl\\xF4me\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 25\n    }\n  }, \"Aucun dipl\\xF4me trouv\\xE9\"), searchTerm && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setSearchTerm(''),\n    className: \"btn btn-secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 29\n    }\n  }, \"Effacer la recherche\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"diplomes-cards\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 21\n    }\n  }, filteredDiplomes.map(diplome => /*#__PURE__*/React.createElement(\"div\", {\n    key: diplome.id,\n    className: \"diplome-card\",\n    style: {\n      backgroundColor: 'white',\n      borderRadius: '12px',\n      padding: '20px',\n      marginBottom: '15px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      border: '1px solid #e9ecef',\n      transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n    },\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-2px)';\n      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0)';\n      e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '2.5rem',\n      color: '#ffd700',\n      minWidth: '60px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDFC6\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      margin: '0 0 10px 0',\n      color: '#2c3e50',\n      fontSize: '1.3rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 41\n    }\n  }, diplome.titre), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '1.1rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDC64\"), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 53\n    }\n  }, diplome.etudiant_nom), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 53\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 53\n    }\n  }, diplome.etudiant_email))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '1.1rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDCC5\"), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 12px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '20px',\n      fontSize: '0.9em',\n      fontWeight: '500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 49\n    }\n  }, new Date(diplome.date_obtention).toLocaleDateString('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginTop: '15px',\n      display: 'flex',\n      gap: '10px',\n      alignItems: 'center',\n      flexWrap: 'wrap'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => generatePDF(diplome.id),\n    title: \"G\\xE9n\\xE9rer et t\\xE9l\\xE9charger le PDF du dipl\\xF4me\",\n    style: {\n      backgroundColor: '#dc3545',\n      color: 'white',\n      border: 'none',\n      borderRadius: '6px',\n      padding: '8px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n      transition: 'all 0.2s ease',\n      boxShadow: '0 2px 4px rgba(84, 220, 53, 0.2)',\n      minWidth: '100px',\n      justifyContent: 'center'\n    },\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = '#c82333';\n      e.target.style.transform = 'translateY(-1px)';\n      e.target.style.boxShadow = '0 4px 8px rgba(220, 53, 69, 0.3)';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = '#dc3545';\n      e.target.style.transform = 'translateY(0)';\n      e.target.style.boxShadow = '0 2px 4px rgba(220, 53, 69, 0.2)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 45\n    }\n  }, \"\\uD83D\\uDCC4 G\\xE9n\\xE9rer PDF\"), isAdmin && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(diplome),\n    title: \"Modifier\",\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 57\n    }\n  }), \"Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(diplome.id),\n    title: \"Supprimer\",\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 57\n    }\n  }), \"Supprimer\"))))))))), filteredDiplomes.length > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-section\",\n    style: {\n      marginTop: '30px',\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 498,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#28a745',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 25\n    }\n  }, filteredDiplomes.length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 25\n    }\n  }, \"Total dipl\\xF4mes\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#007bff',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 25\n    }\n  }, new Set(filteredDiplomes.map(d => d.etudiant_id)).size), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 508,\n      columnNumber: 25\n    }\n  }, \"\\xC9tudiants dipl\\xF4m\\xE9s\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 510,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#ffc107',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 25\n    }\n  }, getUniqueYears().length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 25\n    }\n  }, \"Ann\\xE9es repr\\xE9sent\\xE9es\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#6f42c1',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 25\n    }\n  }, new Set(filteredDiplomes.map(d => d.titre)).size), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 25\n    }\n  }, \"Types de dipl\\xF4mes\"))), showModal && isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 29\n    }\n  }, editingDiplome ? 'Modifier le diplôme' : 'Nouveau diplôme'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingDiplome(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 531,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => setFormData({\n      ...formData,\n      etudiant_id: e.target.value\n    }),\n    required: true,\n    disabled: editingDiplome,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px',\n      backgroundColor: editingDiplome ? '#e9ecef' : 'white'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 559,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.etudiant_id,\n    value: etudiant.etudiant_id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 41\n    }\n  }, etudiant.nom, \" - \", etudiant.email, etudiant.classe_nom && ` (${etudiant.classe_nom})`))), editingDiplome && /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 37\n    }\n  }, \"L'\\xE9tudiant ne peut pas \\xEAtre modifi\\xE9 apr\\xE8s cr\\xE9ation\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 33\n    }\n  }, \"Titre du dipl\\xF4me *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    value: formData.titre,\n    onChange: e => setFormData({\n      ...formData,\n      titre: e.target.value\n    }),\n    placeholder: \"Ex: Licence en Informatique, Master en Gestion...\",\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 33\n    }\n  }, \"Date d'obtention *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_obtention,\n    onChange: e => setFormData({\n      ...formData,\n      date_obtention: e.target.value\n    }),\n    max: new Date().toISOString().split('T')[0],\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 33\n    }\n  }, editingDiplome ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingDiplome(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default DiplomesCRUD;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "DiplomesCRUD", "user", "diplomes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "showModal", "setShowModal", "editingDiplome", "setEditingDiplome", "etudiants", "setEtudiants", "searchTerm", "setSearchTerm", "yearFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formData", "setFormData", "etudiant_id", "titre", "date_obtention", "isAdmin", "role", "fetchDiplomes", "fetchEtudiants", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "error", "console", "fire", "success", "handleSubmit", "e", "preventDefault", "url", "method", "id", "result", "title", "text", "icon", "showCancelButton", "confirmButtonText", "cancelButtonText", "confirmButtonColor", "cancelButtonColor", "isConfirmed", "diplomeId", "generatePDF", "resetForm", "_error$response", "_error$response$data", "handleEdit", "diplome", "handleDelete", "delete", "log", "pdfUrl", "allowOutsideClick", "showConfirmButton", "timer", "timerP<PERSON>ressBar", "setTimeout", "newWindow", "window", "open", "getUniqueYears", "years", "map", "d", "Date", "getFullYear", "Set", "sort", "a", "b", "filteredDiplomes", "filter", "_diplome$etudiant_nom", "_diplome$titre", "matchesSearch", "etudiant_nom", "toLowerCase", "includes", "diplomeYear", "toString", "matchesYear", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "style", "display", "gap", "alignItems", "onClick", "src", "alt", "padding", "backgroundColor", "borderRadius", "marginBottom", "border", "margin", "color", "flex", "type", "placeholder", "value", "onChange", "target", "width", "min<PERSON><PERSON><PERSON>", "year", "key", "boxShadow", "transition", "onMouseEnter", "currentTarget", "transform", "onMouseLeave", "fontSize", "flexDirection", "etudiant_email", "fontWeight", "toLocaleDateString", "month", "day", "marginTop", "flexWrap", "cursor", "justifyContent", "Fragment", "height", "gridTemplateColumns", "textAlign", "size", "onSubmit", "required", "disabled", "etudiant", "nom", "email", "classe_nom", "max", "toISOString", "split"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/DiplomesCRUD.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\n\nconst DiplomesCRUD = () => {\n    const { user } = useContext(AuthContext);\n    const [diplomes, setDiplomes] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingDiplome, setEditingDiplome] = useState(null);\n    const [etudiants, setEtudiants] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [yearFilter, setYearFilter] = useState('all');\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        titre: '',\n        date_obtention: ''\n    });\n\n    // Vérifier si l'utilisateur est Admin\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';\n\n    useEffect(() => {\n        fetchDiplomes();\n        if (isAdmin) {\n            fetchEtudiants();\n        }\n    }, [isAdmin]);\n\n    const fetchDiplomes = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            setDiplomes(response.data);\n        } catch (error) {\n            console.error('Erreur lors du chargement des diplômes:', error);\n            Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchEtudiants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            \n            if (response.data.success) {\n                setEtudiants(response.data.etudiants);\n            }\n        } catch (error) {\n            console.error('Erreur lors du chargement des étudiants:', error);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des diplômes', 'error');\n            return;\n        }\n\n        try {\n            const token = localStorage.getItem('token');\n            const url = 'http://localhost/Project_PFE/Backend/pages/diplomes/';\n            const method = editingDiplome ? 'PUT' : 'POST';\n            const data = editingDiplome ? { ...formData, id: editingDiplome.id } : formData;\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: { \n                    Authorization: `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            // Proposer de générer le PDF après création/modification\n            const result = await Swal.fire({\n                title: 'Succès!',\n                text: `Diplôme ${editingDiplome ? 'modifié' : 'créé'} avec succès`,\n                icon: 'success',\n                showCancelButton: true,\n                confirmButtonText: '📄 Générer PDF',\n                cancelButtonText: '✅ Continuer',\n                confirmButtonColor: '#3085d6',\n                cancelButtonColor: '#28a745'\n            });\n\n            if (result.isConfirmed) {\n                const diplomeId = editingDiplome ? editingDiplome.id : response.data.id;\n                generatePDF(diplomeId);\n            }\n\n            setShowModal(false);\n            setEditingDiplome(null);\n            resetForm();\n            fetchDiplomes();\n        } catch (error) {\n            console.error('Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (diplome) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des diplômes', 'error');\n            return;\n        }\n\n        setEditingDiplome(diplome);\n        setFormData({\n            etudiant_id: diplome.etudiant_id,\n            titre: diplome.titre,\n            date_obtention: diplome.date_obtention\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des diplômes', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr?',\n            text: 'Cette action est irréversible!',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer!',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                const token = localStorage.getItem('token');\n                await axios.delete('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n                    headers: { \n                        Authorization: `Bearer ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n                Swal.fire('Supprimé!', 'Le diplôme a été supprimé.', 'success');\n                fetchDiplomes();\n            } catch (error) {\n                console.error('Erreur:', error);\n                Swal.fire('Erreur', 'Impossible de supprimer le diplôme', 'error');\n            }\n        }\n    };\n\n    const generatePDF = (diplomeId) => {\n        try {\n            console.log('🔄 Génération PDF pour diplôme ID:', diplomeId);\n\n            // Construire l'URL du PDF\n            const pdfUrl = `http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?diplome_id=${diplomeId}`;\n            console.log('📄 URL PDF:', pdfUrl);\n\n            // Afficher un message de chargement\n            Swal.fire({\n                title: 'Génération du PDF...',\n                text: 'Veuillez patienter pendant la génération du diplôme',\n                icon: 'info',\n                allowOutsideClick: false,\n                showConfirmButton: false,\n                timer: 2000,\n                timerProgressBar: true\n            });\n\n            // Ouvrir le PDF dans un nouvel onglet après un court délai\n            setTimeout(() => {\n                const newWindow = window.open(pdfUrl, '_blank');\n\n                if (!newWindow) {\n                    Swal.fire({\n                        title: 'Bloqueur de pop-up détecté',\n                        text: 'Veuillez autoriser les pop-ups pour ce site et réessayer',\n                        icon: 'warning',\n                        confirmButtonText: 'Compris'\n                    });\n                } else {\n                    console.log('✅ PDF ouvert avec succès');\n                }\n            }, 500);\n\n        } catch (error) {\n            console.error('❌ Erreur lors de la génération PDF:', error);\n            Swal.fire({\n                title: 'Erreur',\n                text: 'Impossible de générer le PDF. Veuillez réessayer.',\n                icon: 'error',\n                confirmButtonText: 'OK'\n            });\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            titre: '',\n            date_obtention: ''\n        });\n    };\n\n    // Obtenir les années uniques pour le filtre\n    const getUniqueYears = () => {\n        const years = diplomes.map(d => new Date(d.date_obtention).getFullYear());\n        return [...new Set(years)].sort((a, b) => b - a);\n    };\n\n    // Filtrage des données\n    const filteredDiplomes = diplomes.filter(diplome => {\n        const matchesSearch = diplome.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                             diplome.titre?.toLowerCase().includes(searchTerm.toLowerCase());\n        \n        const diplomeYear = new Date(diplome.date_obtention).getFullYear().toString();\n        const matchesYear = yearFilter === 'all' || diplomeYear === yearFilter;\n        \n        return matchesSearch && matchesYear;\n    });\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des diplômes...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>🎓 Gestion des Diplômes</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredDiplomes.length} diplôme(s) trouvé(s)\n                    </span>\n                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>\n                       \n                            <button\n                                className=\"btn btn-primary\"\n                                onClick={() => setShowModal(true)}\n                            >\n                                <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouveau Diplôme\n                            </button>\n                        \n                    </div>\n                </div>\n            </div>\n\n            {/* Message d'information pour les non-admins */}\n            {!isAdmin && (\n                <div style={{\n                    padding: '15px',\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: '8px',\n                    marginBottom: '20px',\n                    border: '1px solid #bbdefb'\n                }}>\n                    <p style={{ margin: '0', color: '#1976d2' }}>\n                        ℹ️ Vous consultez les diplômes en mode lecture seule. \n                        Seul l'administrateur peut créer, modifier ou supprimer des diplômes.\n                    </p>\n                </div>\n            )}\n\n            {/* Filtres */}\n            <div className=\"filters-section\" style={{\n                display: 'flex',\n                gap: '15px',\n                marginBottom: '20px',\n                padding: '15px',\n                backgroundColor: '#f8f9fa',\n                borderRadius: '8px'\n            }}>\n                <div className=\"search-box\" style={{ flex: 1 }}>\n                    <input\n                        type=\"text\"\n                        placeholder=\"🔍 Rechercher par nom d'étudiant ou titre de diplôme...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                        style={{\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '6px'\n                        }}\n                    />\n                </div>\n                <div className=\"year-filter\">\n                    <select\n                        value={yearFilter}\n                        onChange={(e) => setYearFilter(e.target.value)}\n                        style={{\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '6px',\n                            minWidth: '120px'\n                        }}\n                    >\n                        <option value=\"all\">Toutes les années</option>\n                        {getUniqueYears().map(year => (\n                            <option key={year} value={year}>{year}</option>\n                        ))}\n                    </select>\n                </div>\n            </div>\n\n            <div className=\"factures-grid\">\n                {filteredDiplomes.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/result.png\" alt=\"Aucun diplôme\" />\n                        <p>Aucun diplôme trouvé</p>\n                        {searchTerm && (\n                            <button \n                                onClick={() => setSearchTerm('')}\n                                className=\"btn btn-secondary\"\n                            >\n                                Effacer la recherche\n                            </button>\n                        )}\n                    </div>\n                ) : (\n                    <div className=\"diplomes-cards\">\n                        {filteredDiplomes.map((diplome) => (\n                            <div key={diplome.id} className=\"diplome-card\" style={{\n                                backgroundColor: 'white',\n                                borderRadius: '12px',\n                                padding: '20px',\n                                marginBottom: '15px',\n                                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                border: '1px solid #e9ecef',\n                                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                                e.currentTarget.style.transform = 'translateY(-2px)';\n                                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n                            }}\n                            onMouseLeave={(e) => {\n                                e.currentTarget.style.transform = 'translateY(0)';\n                                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';\n                            }}>\n                                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>\n                                    <div style={{\n                                        fontSize: '2.5rem',\n                                        color: '#ffd700',\n                                        minWidth: '60px'\n                                    }}>\n                                        🏆\n                                    </div>\n                                    <div style={{ flex: 1 }}>\n                                        <h3 style={{\n                                            margin: '0 0 10px 0',\n                                            color: '#2c3e50',\n                                            fontSize: '1.3rem'\n                                        }}>\n                                            {diplome.titre}\n                                        </h3>\n                                        <div style={{\n                                            display: 'flex',\n                                            flexDirection: 'column',\n                                            gap: '8px'\n                                        }}>\n                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                                                <span style={{ fontSize: '1.1rem' }}>👤</span>\n                                                <div>\n                                                    <strong>{diplome.etudiant_nom}</strong>\n                                                    <br />\n                                                    <small style={{ color: '#6c757d' }}>{diplome.etudiant_email}</small>\n                                                </div>\n                                            </div>\n                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                                                <span style={{ fontSize: '1.1rem' }}>📅</span>\n                                                <span style={{\n                                                    padding: '4px 12px',\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '20px',\n                                                    fontSize: '0.9em',\n                                                    fontWeight: '500'\n                                                }}>\n                                                    {new Date(diplome.date_obtention).toLocaleDateString('fr-FR', {\n                                                        year: 'numeric',\n                                                        month: 'long',\n                                                        day: 'numeric'\n                                                    })}\n                                                </span>\n                                            </div>\n                                        </div>\n                                        \n                                        {/* Actions */}\n                                        <div style={{\n                                            marginTop: '15px',\n                                            display: 'flex',\n                                            gap: '10px',\n                                            alignItems: 'center',\n                                            flexWrap: 'wrap'\n                                        }}>\n                                            {/* Bouton PDF - Toujours visible */}\n                                            <button\n                                                onClick={() => generatePDF(diplome.id)}\n                                                title=\"Générer et télécharger le PDF du diplôme\"\n                                                style={{\n                                                    backgroundColor: '#dc3545',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '6px',\n                                                    padding: '8px 16px',\n                                                    fontSize: '14px',\n                                                    fontWeight: '600',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '6px',\n                                                    transition: 'all 0.2s ease',\n                                                    boxShadow: '0 2px 4px rgba(84, 220, 53, 0.2)',\n                                                    minWidth: '100px',\n                                                    justifyContent: 'center'\n                                                }}\n                                                onMouseEnter={(e) => {\n                                                    e.target.style.backgroundColor = '#c82333';\n                                                    e.target.style.transform = 'translateY(-1px)';\n                                                    e.target.style.boxShadow = '0 4px 8px rgba(220, 53, 69, 0.3)';\n                                                }}\n                                                onMouseLeave={(e) => {\n                                                    e.target.style.backgroundColor = '#dc3545';\n                                                    e.target.style.transform = 'translateY(0)';\n                                                    e.target.style.boxShadow = '0 2px 4px rgba(220, 53, 69, 0.2)';\n                                                }}\n                                            >\n                                                📄 Générer PDF\n                                            </button>\n\n                                            {isAdmin && (\n                                                <>\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(diplome)}\n                                                        title=\"Modifier\"\n                                                        style={{\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            gap: '4px'\n                                                        }}\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" style={{ width: '16px', height: '16px' }} />\n                                                        Modifier\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(diplome.id)}\n                                                        title=\"Supprimer\"\n                                                        style={{\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            gap: '4px'\n                                                        }}\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" style={{ width: '16px', height: '16px' }} />\n                                                        Supprimer\n                                                    </button>\n                                                </>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n                )}\n            </div>\n\n            {/* Statistiques */}\n            {filteredDiplomes.length > 0 && (\n                <div className=\"stats-section\" style={{\n                    marginTop: '30px',\n                    padding: '20px',\n                    backgroundColor: '#f8f9fa',\n                    borderRadius: '8px',\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '15px'\n                }}>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#28a745', margin: '0' }}>\n                            {filteredDiplomes.length}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total diplômes</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#007bff', margin: '0' }}>\n                            {new Set(filteredDiplomes.map(d => d.etudiant_id)).size}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Étudiants diplômés</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#ffc107', margin: '0' }}>\n                            {getUniqueYears().length}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Années représentées</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#6f42c1', margin: '0' }}>\n                            {new Set(filteredDiplomes.map(d => d.titre)).size}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Types de diplômes</p>\n                    </div>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier un diplôme */}\n            {showModal && isAdmin && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingDiplome ? 'Modifier le diplôme' : 'Nouveau diplôme'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingDiplome(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant *</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n                                    required\n                                    disabled={editingDiplome}\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px',\n                                        backgroundColor: editingDiplome ? '#e9ecef' : 'white'\n                                    }}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant</option>\n                                    {etudiants.map((etudiant) => (\n                                        <option key={etudiant.etudiant_id} value={etudiant.etudiant_id}>\n                                            {etudiant.nom} - {etudiant.email}\n                                            {etudiant.classe_nom && ` (${etudiant.classe_nom})`}\n                                        </option>\n                                    ))}\n                                </select>\n                                {editingDiplome && (\n                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                        L'étudiant ne peut pas être modifié après création\n                                    </small>\n                                )}\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Titre du diplôme *</label>\n                                <input\n                                    type=\"text\"\n                                    value={formData.titre}\n                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}\n                                    placeholder=\"Ex: Licence en Informatique, Master en Gestion...\"\n                                    required\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Date d'obtention *</label>\n                                <input\n                                    type=\"date\"\n                                    value={formData.date_obtention}\n                                    onChange={(e) => setFormData({...formData, date_obtention: e.target.value})}\n                                    max={new Date().toISOString().split('T')[0]}\n                                    required\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                />\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingDiplome ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingDiplome(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default DiplomesCRUD;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACrCwB,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,OAAO,IAAI,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,OAAO,IAAI,CAAArB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,aAAa;EAEhG3B,SAAS,CAAC,MAAM;IACZ4B,aAAa,CAAC,CAAC;IACf,IAAIF,OAAO,EAAE;MACTG,cAAc,CAAC,CAAC;IACpB;EACJ,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;EAEb,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,sDAAsD,EAAE;QACrFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MACFtB,WAAW,CAACyB,QAAQ,CAACI,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DlC,IAAI,CAACoC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;IACtE,CAAC,SAAS;MACN9B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMmB,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAM9B,KAAK,CAAC+B,GAAG,CAAC,uEAAuE,EAAE;QACtGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,IAAI,CAACI,OAAO,EAAE;QACvBzB,YAAY,CAACiB,QAAQ,CAACI,IAAI,CAACtB,SAAS,CAAC;MACzC;IACJ,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IACpE;EACJ,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAClB,OAAO,EAAE;MACVtB,IAAI,CAACoC,IAAI,CAAC,QAAQ,EAAE,yDAAyD,EAAE,OAAO,CAAC;MACvF;IACJ;IAEA,IAAI;MACA,MAAMV,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMa,GAAG,GAAG,sDAAsD;MAClE,MAAMC,MAAM,GAAGjC,cAAc,GAAG,KAAK,GAAG,MAAM;MAC9C,MAAMwB,IAAI,GAAGxB,cAAc,GAAG;QAAE,GAAGQ,QAAQ;QAAE0B,EAAE,EAAElC,cAAc,CAACkC;MAAG,CAAC,GAAG1B,QAAQ;MAE/E,MAAMY,QAAQ,GAAG,MAAM9B,KAAK,CAAC;QACzB2C,MAAM;QACND,GAAG;QACHR,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;;MAEF;MACA,MAAMkB,MAAM,GAAG,MAAM5C,IAAI,CAACoC,IAAI,CAAC;QAC3BS,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,WAAWrC,cAAc,GAAG,SAAS,GAAG,MAAM,cAAc;QAClEsC,IAAI,EAAE,SAAS;QACfC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,gBAAgB;QACnCC,gBAAgB,EAAE,aAAa;QAC/BC,kBAAkB,EAAE,SAAS;QAC7BC,iBAAiB,EAAE;MACvB,CAAC,CAAC;MAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;QACpB,MAAMC,SAAS,GAAG7C,cAAc,GAAGA,cAAc,CAACkC,EAAE,GAAGd,QAAQ,CAACI,IAAI,CAACU,EAAE;QACvEY,WAAW,CAACD,SAAS,CAAC;MAC1B;MAEA9C,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvB8C,SAAS,CAAC,CAAC;MACXhC,aAAa,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAuB,eAAA,EAAAC,oBAAA;MACZvB,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BlC,IAAI,CAACoC,IAAI,CAAC,QAAQ,EAAE,EAAAqB,eAAA,GAAAvB,KAAK,CAACL,QAAQ,cAAA4B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBxB,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAMyB,UAAU,GAAIC,OAAO,IAAK;IAC5B,IAAI,CAACtC,OAAO,EAAE;MACVtB,IAAI,CAACoC,IAAI,CAAC,QAAQ,EAAE,mDAAmD,EAAE,OAAO,CAAC;MACjF;IACJ;IAEA1B,iBAAiB,CAACkD,OAAO,CAAC;IAC1B1C,WAAW,CAAC;MACRC,WAAW,EAAEyC,OAAO,CAACzC,WAAW;MAChCC,KAAK,EAAEwC,OAAO,CAACxC,KAAK;MACpBC,cAAc,EAAEuC,OAAO,CAACvC;IAC5B,CAAC,CAAC;IACFb,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMqD,YAAY,GAAG,MAAOlB,EAAE,IAAK;IAC/B,IAAI,CAACrB,OAAO,EAAE;MACVtB,IAAI,CAACoC,IAAI,CAAC,QAAQ,EAAE,oDAAoD,EAAE,OAAO,CAAC;MAClF;IACJ;IAEA,MAAMQ,MAAM,GAAG,MAAM5C,IAAI,CAACoC,IAAI,CAAC;MAC3BS,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBG,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BH,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIN,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA,MAAM3B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAM7B,KAAK,CAAC+D,MAAM,CAAC,sDAAsD,EAAE;UACvE/B,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;YAChC,cAAc,EAAE;UACpB,CAAC;UACDO,IAAI,EAAE;YAAEU;UAAG;QACf,CAAC,CAAC;QACF3C,IAAI,CAACoC,IAAI,CAAC,WAAW,EAAE,4BAA4B,EAAE,SAAS,CAAC;QAC/DZ,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOU,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BlC,IAAI,CAACoC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;MACtE;IACJ;EACJ,CAAC;EAED,MAAMmB,WAAW,GAAID,SAAS,IAAK;IAC/B,IAAI;MACAnB,OAAO,CAAC4B,GAAG,CAAC,oCAAoC,EAAET,SAAS,CAAC;;MAE5D;MACA,MAAMU,MAAM,GAAG,wFAAwFV,SAAS,EAAE;MAClHnB,OAAO,CAAC4B,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACAhE,IAAI,CAACoC,IAAI,CAAC;QACNS,KAAK,EAAE,sBAAsB;QAC7BC,IAAI,EAAE,qDAAqD;QAC3DC,IAAI,EAAE,MAAM;QACZkB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,KAAK,EAAE,IAAI;QACXC,gBAAgB,EAAE;MACtB,CAAC,CAAC;;MAEF;MACAC,UAAU,CAAC,MAAM;QACb,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACR,MAAM,EAAE,QAAQ,CAAC;QAE/C,IAAI,CAACM,SAAS,EAAE;UACZtE,IAAI,CAACoC,IAAI,CAAC;YACNS,KAAK,EAAE,4BAA4B;YACnCC,IAAI,EAAE,0DAA0D;YAChEC,IAAI,EAAE,SAAS;YACfE,iBAAiB,EAAE;UACvB,CAAC,CAAC;QACN,CAAC,MAAM;UACHd,OAAO,CAAC4B,GAAG,CAAC,0BAA0B,CAAC;QAC3C;MACJ,CAAC,EAAE,GAAG,CAAC;IAEX,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DlC,IAAI,CAACoC,IAAI,CAAC;QACNS,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,mDAAmD;QACzDC,IAAI,EAAE,OAAO;QACbE,iBAAiB,EAAE;MACvB,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMO,SAAS,GAAGA,CAAA,KAAM;IACpBtC,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAGvE,QAAQ,CAACwE,GAAG,CAACC,CAAC,IAAI,IAAIC,IAAI,CAACD,CAAC,CAACvD,cAAc,CAAC,CAACyD,WAAW,CAAC,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACL,KAAK,CAAC,CAAC,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGhF,QAAQ,CAACiF,MAAM,CAACxB,OAAO,IAAI;IAAA,IAAAyB,qBAAA,EAAAC,cAAA;IAChD,MAAMC,aAAa,GAAG,EAAAF,qBAAA,GAAAzB,OAAO,CAAC4B,YAAY,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GACvE1B,OAAO,CAACxC,KAAK,cAAAkE,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7E,UAAU,CAAC4E,WAAW,CAAC,CAAC,CAAC;IAEpF,MAAME,WAAW,GAAG,IAAId,IAAI,CAACjB,OAAO,CAACvC,cAAc,CAAC,CAACyD,WAAW,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC;IAC7E,MAAMC,WAAW,GAAG9E,UAAU,KAAK,KAAK,IAAI4E,WAAW,KAAK5E,UAAU;IAEtE,OAAOwE,aAAa,IAAIM,WAAW;EACvC,CAAC,CAAC;EAEF,IAAIxF,OAAO,EAAE;IACT,oBACIX,KAAA,CAAAoG,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9B3G,KAAA,CAAAoG,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/B3G,KAAA,CAAAoG,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,+BAA6B,CAC/B,CAAC;EAEd;EAEA,oBACI3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/B3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAA2B,CAAC,eAChC3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3G,KAAA,CAAAoG,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxBlB,gBAAgB,CAACmB,MAAM,EAAC,6BACvB,CAAC,eACP5G,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAAV,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3D3G,KAAA,CAAAoG,aAAA;IACIC,SAAS,EAAC,iBAAiB;IAC3BY,OAAO,EAAEA,CAAA,KAAMnG,YAAY,CAAC,IAAI,CAAE;IAAAwF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElC3G,KAAA,CAAAoG,aAAA;IAAKc,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,uBACjC,CAEX,CACJ,CACJ,CAAC,EAGL,CAAC/E,OAAO,iBACL5B,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MACRO,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACZ,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3G,KAAA,CAAAoG,aAAA;IAAGS,KAAK,EAAE;MAAEY,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gJAG1C,CACF,CACR,eAGD3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAACQ,KAAK,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXQ,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE;IAClB,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACQ,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAE,CAAE;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3C3G,KAAA,CAAAoG,aAAA;IACIwB,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,yEAAyD;IACrEC,KAAK,EAAE3G,UAAW;IAClB4G,QAAQ,EAAGlF,CAAC,IAAKzB,aAAa,CAACyB,CAAC,CAACmF,MAAM,CAACF,KAAK,CAAE;IAC/CjB,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE;IAClB,CAAE;IAAAhB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB3G,KAAA,CAAAoG,aAAA;IACI0B,KAAK,EAAEzG,UAAW;IAClB0G,QAAQ,EAAGlF,CAAC,IAAKvB,aAAa,CAACuB,CAAC,CAACmF,MAAM,CAACF,KAAK,CAAE;IAC/CjB,KAAK,EAAE;MACHO,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE,KAAK;MACnBY,QAAQ,EAAE;IACd,CAAE;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3G,KAAA,CAAAoG,aAAA;IAAQ0B,KAAK,EAAC,KAAK;IAAAxB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAAyB,CAAC,EAC7C5B,cAAc,CAAC,CAAC,CAACE,GAAG,CAACkD,IAAI,iBACtBnI,KAAA,CAAAoG,aAAA;IAAQgC,GAAG,EAAED,IAAK;IAACL,KAAK,EAAEK,IAAK;IAAA7B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEwB,IAAa,CACjD,CACG,CACP,CACJ,CAAC,eAENnI,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBlB,gBAAgB,CAACmB,MAAM,KAAK,CAAC,gBAC1B5G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB3G,KAAA,CAAAoG,aAAA;IAAKc,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,kBAAe;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC7C3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,4BAAuB,CAAC,EAC1BxF,UAAU,iBACPnB,KAAA,CAAAoG,aAAA;IACIa,OAAO,EAAEA,CAAA,KAAM7F,aAAa,CAAC,EAAE,CAAE;IACjCiF,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,sBAEO,CAEX,CAAC,gBAEN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1BlB,gBAAgB,CAACR,GAAG,CAAEf,OAAO,iBAC1BlE,KAAA,CAAAoG,aAAA;IAAKgC,GAAG,EAAElE,OAAO,CAACjB,EAAG;IAACoD,SAAS,EAAC,cAAc;IAACQ,KAAK,EAAE;MAClDQ,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE,MAAM;MACpBF,OAAO,EAAE,MAAM;MACfG,YAAY,EAAE,MAAM;MACpBc,SAAS,EAAE,4BAA4B;MACvCb,MAAM,EAAE,mBAAmB;MAC3Bc,UAAU,EAAE;IAChB,CAAE;IACFC,YAAY,EAAG1F,CAAC,IAAK;MACjBA,CAAC,CAAC2F,aAAa,CAAC3B,KAAK,CAAC4B,SAAS,GAAG,kBAAkB;MACpD5F,CAAC,CAAC2F,aAAa,CAAC3B,KAAK,CAACwB,SAAS,GAAG,6BAA6B;IACnE,CAAE;IACFK,YAAY,EAAG7F,CAAC,IAAK;MACjBA,CAAC,CAAC2F,aAAa,CAAC3B,KAAK,CAAC4B,SAAS,GAAG,eAAe;MACjD5F,CAAC,CAAC2F,aAAa,CAAC3B,KAAK,CAACwB,SAAS,GAAG,4BAA4B;IAClE,CAAE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3G,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEE,UAAU,EAAE,YAAY;MAAED,GAAG,EAAE;IAAO,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnE3G,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MACR8B,QAAQ,EAAE,QAAQ;MAClBjB,KAAK,EAAE,SAAS;MAChBQ,QAAQ,EAAE;IACd,CAAE;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAEE,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAE,CAAE;IAAArB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpB3G,KAAA,CAAAoG,aAAA;IAAIS,KAAK,EAAE;MACPY,MAAM,EAAE,YAAY;MACpBC,KAAK,EAAE,SAAS;MAChBiB,QAAQ,EAAE;IACd,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGzC,OAAO,CAACxC,KACT,CAAC,eACL1B,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MACRC,OAAO,EAAE,MAAM;MACf8B,aAAa,EAAE,QAAQ;MACvB7B,GAAG,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3G,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEE,UAAU,EAAE,QAAQ;MAAED,GAAG,EAAE;IAAM,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9D3G,KAAA,CAAAoG,aAAA;IAAMS,KAAK,EAAE;MAAE8B,QAAQ,EAAE;IAAS,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAQ,CAAC,eAC9C3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASzC,OAAO,CAAC4B,YAAqB,CAAC,eACvC9F,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAOS,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAU,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEzC,OAAO,CAAC2E,cAAsB,CAClE,CACJ,CAAC,eACN7I,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEE,UAAU,EAAE,QAAQ;MAAED,GAAG,EAAE;IAAM,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9D3G,KAAA,CAAAoG,aAAA;IAAMS,KAAK,EAAE;MAAE8B,QAAQ,EAAE;IAAS,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAQ,CAAC,eAC9C3G,KAAA,CAAAoG,aAAA;IAAMS,KAAK,EAAE;MACTO,OAAO,EAAE,UAAU;MACnBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,MAAM;MACpBqB,QAAQ,EAAE,OAAO;MACjBG,UAAU,EAAE;IAChB,CAAE;IAAAxC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,IAAIxB,IAAI,CAACjB,OAAO,CAACvC,cAAc,CAAC,CAACoH,kBAAkB,CAAC,OAAO,EAAE;IAC1DZ,IAAI,EAAE,SAAS;IACfa,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE;EACT,CAAC,CACC,CACL,CACJ,CAAC,eAGNjJ,KAAA,CAAAoG,aAAA;IAAKS,KAAK,EAAE;MACRqC,SAAS,EAAE,MAAM;MACjBpC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXC,UAAU,EAAE,QAAQ;MACpBmC,QAAQ,EAAE;IACd,CAAE;IAAA7C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEE3G,KAAA,CAAAoG,aAAA;IACIa,OAAO,EAAEA,CAAA,KAAMpD,WAAW,CAACK,OAAO,CAACjB,EAAE,CAAE;IACvCE,KAAK,EAAC,yDAA0C;IAChD0D,KAAK,EAAE;MACHQ,eAAe,EAAE,SAAS;MAC1BK,KAAK,EAAE,OAAO;MACdF,MAAM,EAAE,MAAM;MACdF,YAAY,EAAE,KAAK;MACnBF,OAAO,EAAE,UAAU;MACnBuB,QAAQ,EAAE,MAAM;MAChBG,UAAU,EAAE,KAAK;MACjBM,MAAM,EAAE,SAAS;MACjBtC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,GAAG,EAAE,KAAK;MACVuB,UAAU,EAAE,eAAe;MAC3BD,SAAS,EAAE,kCAAkC;MAC7CH,QAAQ,EAAE,OAAO;MACjBmB,cAAc,EAAE;IACpB,CAAE;IACFd,YAAY,EAAG1F,CAAC,IAAK;MACjBA,CAAC,CAACmF,MAAM,CAACnB,KAAK,CAACQ,eAAe,GAAG,SAAS;MAC1CxE,CAAC,CAACmF,MAAM,CAACnB,KAAK,CAAC4B,SAAS,GAAG,kBAAkB;MAC7C5F,CAAC,CAACmF,MAAM,CAACnB,KAAK,CAACwB,SAAS,GAAG,kCAAkC;IACjE,CAAE;IACFK,YAAY,EAAG7F,CAAC,IAAK;MACjBA,CAAC,CAACmF,MAAM,CAACnB,KAAK,CAACQ,eAAe,GAAG,SAAS;MAC1CxE,CAAC,CAACmF,MAAM,CAACnB,KAAK,CAAC4B,SAAS,GAAG,eAAe;MAC1C5F,CAAC,CAACmF,MAAM,CAACnB,KAAK,CAACwB,SAAS,GAAG,kCAAkC;IACjE,CAAE;IAAA/B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gCAEO,CAAC,EAER/E,OAAO,iBACJ5B,KAAA,CAAAoG,aAAA,CAAApG,KAAA,CAAAsJ,QAAA,qBACItJ,KAAA,CAAAoG,aAAA;IACIC,SAAS,EAAC,wBAAwB;IAClCY,OAAO,EAAEA,CAAA,KAAMhD,UAAU,CAACC,OAAO,CAAE;IACnCf,KAAK,EAAC,UAAU;IAChB0D,KAAK,EAAE;MACHC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,GAAG,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3G,KAAA,CAAAoG,aAAA;IAAKc,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAACN,KAAK,EAAE;MAAEoB,KAAK,EAAE,MAAM;MAAEsB,MAAM,EAAE;IAAO,CAAE;IAAAjD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,YAE5E,CAAC,eACT3G,KAAA,CAAAoG,aAAA;IACIC,SAAS,EAAC,uBAAuB;IACjCY,OAAO,EAAEA,CAAA,KAAM9C,YAAY,CAACD,OAAO,CAACjB,EAAE,CAAE;IACxCE,KAAK,EAAC,WAAW;IACjB0D,KAAK,EAAE;MACHC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,GAAG,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3G,KAAA,CAAAoG,aAAA;IAAKc,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAACN,KAAK,EAAE;MAAEoB,KAAK,EAAE,MAAM;MAAEsB,MAAM,EAAE;IAAO,CAAE;IAAAjD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,aAE/E,CACV,CAEL,CACJ,CACJ,CACJ,CACR,CACA,CAER,CAAC,EAGLlB,gBAAgB,CAACmB,MAAM,GAAG,CAAC,iBACxB5G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAACQ,KAAK,EAAE;MAClCqC,SAAS,EAAE,MAAM;MACjB9B,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBR,OAAO,EAAE,MAAM;MACf0C,mBAAmB,EAAE,sCAAsC;MAC3DzC,GAAG,EAAE;IACT,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACE3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACQ,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtD3G,KAAA,CAAAoG,aAAA;IAAIS,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxClB,gBAAgB,CAACmB,MAClB,CAAC,eACL5G,KAAA,CAAAoG,aAAA;IAAGS,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mBAAiB,CACrE,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACQ,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtD3G,KAAA,CAAAoG,aAAA;IAAIS,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,IAAItB,GAAG,CAACI,gBAAgB,CAACR,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzD,WAAW,CAAC,CAAC,CAACiI,IACnD,CAAC,eACL1J,KAAA,CAAAoG,aAAA;IAAGS,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6BAAqB,CACzE,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACQ,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtD3G,KAAA,CAAAoG,aAAA;IAAIS,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC5B,cAAc,CAAC,CAAC,CAAC6B,MAClB,CAAC,eACL5G,KAAA,CAAAoG,aAAA;IAAGS,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAAsB,CAC1E,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACQ,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAnD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtD3G,KAAA,CAAAoG,aAAA;IAAIS,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,IAAItB,GAAG,CAACI,gBAAgB,CAACR,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxD,KAAK,CAAC,CAAC,CAACgI,IAC7C,CAAC,eACL1J,KAAA,CAAAoG,aAAA;IAAGS,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAAoB,CACxE,CACJ,CACR,EAGA9F,SAAS,IAAIe,OAAO,iBACjB5B,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAK5F,cAAc,GAAG,qBAAqB,GAAG,iBAAsB,CAAC,eACrEf,KAAA,CAAAoG,aAAA;IACIC,SAAS,EAAC,WAAW;IACrBY,OAAO,EAAEA,CAAA,KAAM;MACXnG,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvB8C,SAAS,CAAC,CAAC;IACf,CAAE;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3G,KAAA,CAAAoG,aAAA;IAAKc,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAb,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACN3G,KAAA,CAAAoG,aAAA;IAAMuD,QAAQ,EAAE/G,YAAa;IAAA0D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzB3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAiB,CAAC,eACzB3G,KAAA,CAAAoG,aAAA;IACI0B,KAAK,EAAEvG,QAAQ,CAACE,WAAY;IAC5BsG,QAAQ,EAAGlF,CAAC,IAAKrB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,WAAW,EAAEoB,CAAC,CAACmF,MAAM,CAACF;IAAK,CAAC,CAAE;IACzE8B,QAAQ;IACRC,QAAQ,EAAE9I,cAAe;IACzB8F,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBqB,QAAQ,EAAE,MAAM;MAChBtB,eAAe,EAAEtG,cAAc,GAAG,SAAS,GAAG;IAClD,CAAE;IAAAuF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEF3G,KAAA,CAAAoG,aAAA;IAAQ0B,KAAK,EAAC,EAAE;IAAAxB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjD1F,SAAS,CAACgE,GAAG,CAAE6E,QAAQ,iBACpB9J,KAAA,CAAAoG,aAAA;IAAQgC,GAAG,EAAE0B,QAAQ,CAACrI,WAAY;IAACqG,KAAK,EAAEgC,QAAQ,CAACrI,WAAY;IAAA6E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DmD,QAAQ,CAACC,GAAG,EAAC,KAAG,EAACD,QAAQ,CAACE,KAAK,EAC/BF,QAAQ,CAACG,UAAU,IAAI,KAAKH,QAAQ,CAACG,UAAU,GAC5C,CACX,CACG,CAAC,EACRlJ,cAAc,iBACXf,KAAA,CAAAoG,aAAA;IAAOS,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAEiB,QAAQ,EAAE;IAAQ,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mEAEhD,CAEV,CAAC,eAEN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,uBAAyB,CAAC,eACjC3G,KAAA,CAAAoG,aAAA;IACIwB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAEvG,QAAQ,CAACG,KAAM;IACtBqG,QAAQ,EAAGlF,CAAC,IAAKrB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,KAAK,EAAEmB,CAAC,CAACmF,MAAM,CAACF;IAAK,CAAC,CAAE;IACnED,WAAW,EAAC,mDAAmD;IAC/D+B,QAAQ;IACR/C,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBqB,QAAQ,EAAE;IACd,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAEN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB3G,KAAA,CAAAoG,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,oBAAyB,CAAC,eACjC3G,KAAA,CAAAoG,aAAA;IACIwB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAEvG,QAAQ,CAACI,cAAe;IAC/BoG,QAAQ,EAAGlF,CAAC,IAAKrB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,cAAc,EAAEkB,CAAC,CAACmF,MAAM,CAACF;IAAK,CAAC,CAAE;IAC5EoC,GAAG,EAAE,IAAI/E,IAAI,CAAC,CAAC,CAACgF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;IAC5CR,QAAQ;IACR/C,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBqB,QAAQ,EAAE;IACd,CAAE;IAAArC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAEN3G,KAAA,CAAAoG,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B3G,KAAA,CAAAoG,aAAA;IAAQwB,IAAI,EAAC,QAAQ;IAACvB,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5C5F,cAAc,GAAG,aAAa,GAAG,SAC9B,CAAC,eACTf,KAAA,CAAAoG,aAAA;IACIwB,IAAI,EAAC,QAAQ;IACbvB,SAAS,EAAC,mBAAmB;IAC7BY,OAAO,EAAEA,CAAA,KAAM;MACXnG,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvB8C,SAAS,CAAC,CAAC;IACf,CAAE;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAepG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}