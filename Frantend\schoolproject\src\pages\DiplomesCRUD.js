import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const DiplomesCRUD = () => {
    const { user } = useContext(AuthContext);
    const [diplomes, setDiplomes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingDiplome, setEditingDiplome] = useState(null);
    const [etudiants, setEtudiants] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [yearFilter, setYearFilter] = useState('all');
    const [formData, setFormData] = useState({
        etudiant_id: '',
        titre: '',
        date_obtention: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';

    useEffect(() => {
        fetchDiplomes();
        if (isAdmin) {
            fetchEtudiants();
        }
    }, [isAdmin]);

    const fetchDiplomes = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setDiplomes(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des diplômes:', error);
            Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php', {
                headers: { Authorization: `Bearer ${token}` }
            });
            
            if (response.data.success) {
                setEtudiants(response.data.etudiants);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des étudiants:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des diplômes', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/diplomes/';
            const method = editingDiplome ? 'PUT' : 'POST';
            const data = editingDiplome ? { ...formData, id: editingDiplome.id } : formData;

            const response = await axios({
                method,
                url,
                data,
                headers: { 
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            // Proposer de générer le PDF après création/modification
            const result = await Swal.fire({
                title: 'Succès!',
                text: `Diplôme ${editingDiplome ? 'modifié' : 'créé'} avec succès`,
                icon: 'success',
                showCancelButton: true,
                confirmButtonText: '📄 Générer PDF',
                cancelButtonText: '✅ Continuer',
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#28a745'
            });

            if (result.isConfirmed) {
                const diplomeId = editingDiplome ? editingDiplome.id : response.data.id;
                generatePDF(diplomeId);
            }

            setShowModal(false);
            setEditingDiplome(null);
            resetForm();
            fetchDiplomes();
        } catch (error) {
            console.error('Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (diplome) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des diplômes', 'error');
            return;
        }

        setEditingDiplome(diplome);
        setFormData({
            etudiant_id: diplome.etudiant_id,
            titre: diplome.titre,
            date_obtention: diplome.date_obtention
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des diplômes', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                await axios.delete('http://localhost/Project_PFE/Backend/pages/diplomes/', {
                    headers: { 
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });
                Swal.fire('Supprimé!', 'Le diplôme a été supprimé.', 'success');
                fetchDiplomes();
            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire('Erreur', 'Impossible de supprimer le diplôme', 'error');
            }
        }
    };

    const generatePDF = (diplomeId) => {
        try {
            console.log('🔄 Génération PDF pour diplôme ID:', diplomeId);

            // Construire l'URL du PDF
            const pdfUrl = `http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?diplome_id=${diplomeId}`;
            console.log('📄 URL PDF:', pdfUrl);

            // Afficher un message de chargement
            Swal.fire({
                title: 'Génération du PDF...',
                text: 'Veuillez patienter pendant la génération du diplôme',
                icon: 'info',
                allowOutsideClick: false,
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true
            });

            // Ouvrir le PDF dans un nouvel onglet après un court délai
            setTimeout(() => {
                const newWindow = window.open(pdfUrl, '_blank');

                if (!newWindow) {
                    Swal.fire({
                        title: 'Bloqueur de pop-up détecté',
                        text: 'Veuillez autoriser les pop-ups pour ce site et réessayer',
                        icon: 'warning',
                        confirmButtonText: 'Compris'
                    });
                } else {
                    console.log('✅ PDF ouvert avec succès');
                }
            }, 500);

        } catch (error) {
            console.error('❌ Erreur lors de la génération PDF:', error);
            Swal.fire({
                title: 'Erreur',
                text: 'Impossible de générer le PDF. Veuillez réessayer.',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            titre: '',
            date_obtention: ''
        });
    };

    // Obtenir les années uniques pour le filtre
    const getUniqueYears = () => {
        const years = diplomes.map(d => new Date(d.date_obtention).getFullYear());
        return [...new Set(years)].sort((a, b) => b - a);
    };

    // Filtrage des données
    const filteredDiplomes = diplomes.filter(diplome => {
        const matchesSearch = diplome.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             diplome.titre?.toLowerCase().includes(searchTerm.toLowerCase());
        
        const diplomeYear = new Date(diplome.date_obtention).getFullYear().toString();
        const matchesYear = yearFilter === 'all' || diplomeYear === yearFilter;
        
        return matchesSearch && matchesYear;
    });

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des diplômes...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>🎓 Gestion des Diplômes</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredDiplomes.length} diplôme(s) trouvé(s)
                    </span>
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                       
                            <button
                                className="btn btn-primary"
                                onClick={() => setShowModal(true)}
                            >
                                <img src="/plus.png" alt="Ajouter" /> Nouveau Diplôme
                            </button>
                        
                    </div>
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez les diplômes en mode lecture seule. 
                        Seul l'administrateur peut créer, modifier ou supprimer des diplômes.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par nom d'étudiant ou titre de diplôme..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="year-filter">
                    <select
                        value={yearFilter}
                        onChange={(e) => setYearFilter(e.target.value)}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '120px'
                        }}
                    >
                        <option value="all">Toutes les années</option>
                        {getUniqueYears().map(year => (
                            <option key={year} value={year}>{year}</option>
                        ))}
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredDiplomes.length === 0 ? (
                    <div className="no-data">
                        <img src="/result.png" alt="Aucun diplôme" />
                        <p>Aucun diplôme trouvé</p>
                        {searchTerm && (
                            <button 
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="diplomes-cards">
                        {filteredDiplomes.map((diplome) => (
                            <div key={diplome.id} className="diplome-card" style={{
                                backgroundColor: 'white',
                                borderRadius: '12px',
                                padding: '20px',
                                marginBottom: '15px',
                                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                                border: '1px solid #e9ecef',
                                transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.transform = 'translateY(-2px)';
                                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'translateY(0)';
                                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                            }}>
                                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
                                    <div style={{
                                        fontSize: '2.5rem',
                                        color: '#ffd700',
                                        minWidth: '60px'
                                    }}>
                                        🏆
                                    </div>
                                    <div style={{ flex: 1 }}>
                                        <h3 style={{
                                            margin: '0 0 10px 0',
                                            color: '#2c3e50',
                                            fontSize: '1.3rem'
                                        }}>
                                            {diplome.titre}
                                        </h3>
                                        <div style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '8px'
                                        }}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <span style={{ fontSize: '1.1rem' }}>👤</span>
                                                <div>
                                                    <strong>{diplome.etudiant_nom}</strong>
                                                    <br />
                                                    <small style={{ color: '#6c757d' }}>{diplome.etudiant_email}</small>
                                                </div>
                                            </div>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                                <span style={{ fontSize: '1.1rem' }}>📅</span>
                                                <span style={{
                                                    padding: '4px 12px',
                                                    backgroundColor: '#e3f2fd',
                                                    borderRadius: '20px',
                                                    fontSize: '0.9em',
                                                    fontWeight: '500'
                                                }}>
                                                    {new Date(diplome.date_obtention).toLocaleDateString('fr-FR', {
                                                        year: 'numeric',
                                                        month: 'long',
                                                        day: 'numeric'
                                                    })}
                                                </span>
                                            </div>
                                        </div>
                                        
                                        {/* Actions */}
                                        <div style={{
                                            marginTop: '15px',
                                            display: 'flex',
                                            gap: '10px',
                                            alignItems: 'center',
                                            flexWrap: 'wrap'
                                        }}>
                                            {/* Bouton PDF - Toujours visible */}
                                            <button
                                                onClick={() => generatePDF(diplome.id)}
                                                title="Générer et télécharger le PDF du diplôme"
                                                style={{
                                                    backgroundColor: '#198754',
                                                    color: 'white',
                                                    border: 'none',
                                                    borderRadius: '6px',
                                                    padding: '8px 16px',
                                                    fontSize: '14px',
                                                    fontWeight: '600',
                                                    cursor: 'pointer',
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    gap: '6px',
                                                    transition: 'all 0.2s ease',
                                                    boxShadow: '#198754',
                                                    minWidth: '100px',
                                                    justifyContent: 'center'
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.target.style.backgroundColor = '#c82333';
                                                    e.target.style.transform = 'translateY(-1px)';
                                                    e.target.style.boxShadow = '#198754';
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.target.style.backgroundColor = '#dc3545';
                                                    e.target.style.transform = 'translateY(0)';
                                                    e.target.style.boxShadow = '0 2px 4px rgba(220, 53, 69, 0.2)';
                                                }}
                                            >
                                                📄 Générer PDF
                                            </button>

                                            {isAdmin && (
                                                <>
                                                    <button
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(diplome)}
                                                        title="Modifier"
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '4px'
                                                        }}
                                                    >
                                                        <img src="/edit.png" alt="Modifier" style={{ width: '16px', height: '16px' }} />
                                                        Modifier
                                                    </button>
                                                    <button
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(diplome.id)}
                                                        title="Supprimer"
                                                        style={{
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: '4px'
                                                        }}
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" style={{ width: '16px', height: '16px' }} />
                                                        Supprimer
                                                    </button>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Statistiques */}
            {filteredDiplomes.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredDiplomes.length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total diplômes</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {new Set(filteredDiplomes.map(d => d.etudiant_id)).size}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Étudiants diplômés</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {getUniqueYears().length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Années représentées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#6f42c1', margin: '0' }}>
                            {new Set(filteredDiplomes.map(d => d.titre)).size}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Types de diplômes</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier un diplôme */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingDiplome ? 'Modifier le diplôme' : 'Nouveau diplôme'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingDiplome(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant *</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                    disabled={editingDiplome}
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        backgroundColor: editingDiplome ? '#e9ecef' : 'white'
                                    }}
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.etudiant_id} value={etudiant.etudiant_id}>
                                            {etudiant.nom} - {etudiant.email}
                                            {etudiant.classe_nom && ` (${etudiant.classe_nom})`}
                                        </option>
                                    ))}
                                </select>
                                {editingDiplome && (
                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>
                                        L'étudiant ne peut pas être modifié après création
                                    </small>
                                )}
                            </div>

                            <div className="form-group">
                                <label>Titre du diplôme *</label>
                                <input
                                    type="text"
                                    value={formData.titre}
                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}
                                    placeholder="Ex: Licence en Informatique, Master en Gestion..."
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Date d'obtention *</label>
                                <input
                                    type="date"
                                    value={formData.date_obtention}
                                    onChange={(e) => setFormData({...formData, date_obtention: e.target.value})}
                                    max={new Date().toISOString().split('T')[0]}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingDiplome ? '💾 Modifier' : '➕ Créer'}
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingDiplome(null);
                                        resetForm();
                                    }}
                                >
                                    ❌ Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default DiplomesCRUD;
