import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Navbar from './components/Navbar';
import NavbarTop from './components/NavbarTop';
import ProtectedRoute from './components/ProtectedRoute';
import Logout from './components/Logout';

// Pages existantes
import Roles from './pages/Role';
import Filieres from './pages/Filiere';
import Niveaux from './pages/Niveaux';
import Matiere from './pages/Matiere';
import Classe from './pages/Classe';
import Groupe from './pages/Groupe';
import Cours from './pages/Cours';
import Devoirs from './pages/Devoirs';
import Login from './Auth/Login';
import Register from './Auth/Register';
import Parents from './pages/Parents';
import Etudiants from './pages/Etudiants';
import Enseignants from './pages/Enseignants';

// Nouvelles pages
import Homepage from './pages/Homepage';
import EnseignantDashboard from './dashboards/EnseignantDashboard';
import EtudiantDashboard from './dashboards/EtudiantDashboard';
import ParentDashboard from './dashboards/ParentDashboard';
import ResponsableDashboard from './dashboards/ResponsableDashboard';

// Composants de profil et utilisateurs
import UserProfile from './components/UserProfile';
import UsersList from './pages/UsersList';
import UsersListTest from './pages/UsersListTest';

// Nouvelles pages de gestion
import FacturesCRUD from './pages/FacturesCRUD'; // CRUD complet pour Admin
import DiplomesCRUD from './pages/DiplomesCRUD'; // CRUD complet pour Admin avec PDF
import Absences from './pages/Absences'; // CRUD complet pour Admin et Enseignant
import Retards from './pages/Retards'; // CRUD complet pour Admin et Enseignant
import ReponsesQuizUnified from './pages/ReponsesQuizUnified'; // CRUD complet pour les réponses aux quiz
import EmploisDuTemps from './pages/EmploisDuTemps'; // CRUD complet pour les emplois du temps
import NotesUnified from './pages/NotesUnified'; // CRUD complet pour les notes avec calcul automatique
import ParentEtudiant from './pages/ParentEtudiant'; // Gestion des relations parent-étudiant
import Quiz from './pages/Quiz';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Page d'accueil publique */}
          <Route path="/" element={<Homepage />} />
          <Route path="/login" element={<Login />} />
          <Route path="/registers" element={<Register />} />

          {/* Routes avec navigation */}
          <Route path="/*" element={
            <div>
              <Navbar />
              <NavbarTop />
              <div style={{
                marginLeft: '110px',
                marginTop: '60px',
                padding: '20px'
              }}>
                <Routes>
                  {/* Dashboards spécialisés */}
                  <Route path="/dashboard/enseignant" element={
                    <ProtectedRoute requiredRole="enseignant">
                      <EnseignantDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/dashboard/etudiant" element={
                    <ProtectedRoute requiredRoles={["etudiant", "élève"]}>
                      <EtudiantDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/dashboard/parent" element={
                    <ProtectedRoute requiredRole="parent">
                      <ParentDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/dashboard/responsable" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <ResponsableDashboard />
                    </ProtectedRoute>
                  } />

                  {/* Routes administratives (accès restreint) */}
                  <Route path="/roles" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <Roles />
                    </ProtectedRoute>
                  } />
                  <Route path="/matieres" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Matiere />
                    </ProtectedRoute>
                  } />
                  <Route path="/filieres" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <Filieres />
                    </ProtectedRoute>
                  } />
                  <Route path="/niveaux" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <Niveaux />
                    </ProtectedRoute>
                  } />
                  <Route path="/classes" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Classe />
                    </ProtectedRoute>
                  } />
                  <Route path="/groupes" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Groupe />
                    </ProtectedRoute>
                  } />
                  <Route path="/cours" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Cours />
                    </ProtectedRoute>
                  } />
                  <Route path="/devoirs" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Devoirs />
                    </ProtectedRoute>
                  } />
                  <Route path="/parents" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <Parents/>
                    </ProtectedRoute>
                  } />
                  <Route path="/etudiants" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Etudiants />
                    </ProtectedRoute>
                  } />
                  <Route path="/enseignants" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <Enseignants />
                    </ProtectedRoute>
                  } />
                  <Route path="/parent-etudiant" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <ParentEtudiant />
                    </ProtectedRoute>
                  } />

                  {/* Nouvelles routes de gestion (lecture seule) */}
                  <Route path="/factures" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "parent", "etudiant", "élève"]}>
                      <FacturesCRUD />
                    </ProtectedRoute>
                  } />
                  <Route path="/diplomes" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "parent", "etudiant", "élève"]}>
                      <DiplomesCRUD />
                    </ProtectedRoute>
                  } />
                  <Route path="/absences" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant", "parent", "etudiant", "élève"]}>
                      <Absences />
                    </ProtectedRoute>
                  } />
                  <Route path="/retards" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant", "parent", "etudiant", "élève"]}>
                      <Retards />
                    </ProtectedRoute>
                  } />
                  <Route path="/reponses-quiz" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant", "parent", "etudiant", "élève"]}>
                      <ReponsesQuizUnified />
                    </ProtectedRoute>
                  } />
                  <Route path="/emplois-du-temps" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant", "parent", "etudiant", "élève"]}>
                      <EmploisDuTemps />
                    </ProtectedRoute>
                  } />
                  <Route path="/notes" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant", "parent", "etudiant", "élève"]}>
                      <NotesUnified />
                    </ProtectedRoute>
                  } />
                  
                 
                  <Route path="/parent-etudiant" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <ParentEtudiant />
                    </ProtectedRoute>
                  } />
                  <Route path="/quiz" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin", "enseignant"]}>
                      <Quiz />
                    </ProtectedRoute>
                  } />
                  

                  {/* Routes du NavbarTop */}
                  <Route path="/profil" element={
                    <ProtectedRoute>
                      <UserProfile />
                    </ProtectedRoute>
                  } />
                  <Route path="/profil/:id" element={
                    <ProtectedRoute>
                      <UserProfile />
                    </ProtectedRoute>
                  } />
                  <Route path="/utilisateurs" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <UsersList />
                    </ProtectedRoute>
                  } />
                  <Route path="/utilisateurs-test" element={
                    <ProtectedRoute requiredRoles={["responsable", "admin"]}>
                      <UsersListTest />
                    </ProtectedRoute>
                  } />
                  <Route path="/logout" element={
                    <ProtectedRoute>
                      <Logout />
                    </ProtectedRoute>
                  } />
                </Routes>
              </div>
            </div>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
