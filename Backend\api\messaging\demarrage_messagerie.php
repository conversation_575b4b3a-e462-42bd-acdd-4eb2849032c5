<?php
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démarrage Messagerie - Guide Complet</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }
        .container { max-width: 1200px; margin: 0 auto; background: white; color: #333; padding: 40px; border-radius: 20px; box-shadow: 0 20px 60px rgba(0,0,0,0.3); }
        .header { text-align: center; margin-bottom: 40px; }
        .header h1 { color: #667eea; font-size: 2.8rem; margin-bottom: 10px; }
        .success { background: #d4edda; border-left: 5px solid #28a745; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { background: #f8d7da; border-left: 5px solid #dc3545; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { background: #fff3cd; border-left: 5px solid #ffc107; color: #856404; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { background: #d1ecf1; border-left: 5px solid #17a2b8; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step-card { background: #f8f9fa; padding: 25px; border-radius: 15px; margin: 20px 0; border-left: 5px solid #667eea; }
        .step-card h3 { color: #667eea; margin-bottom: 15px; font-size: 1.3rem; }
        .btn { display: inline-block; padding: 12px 25px; background: #667eea; color: white; text-decoration: none; border-radius: 25px; margin: 8px; border: none; cursor: pointer; font-weight: 600; transition: all 0.3s; }
        .btn:hover { background: #5a67d8; transform: translateY(-2px); }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-large { padding: 20px 40px; font-size: 1.2rem; }
        pre { background: #2d3748; color: #e2e8f0; padding: 20px; border-radius: 10px; overflow-x: auto; font-size: 12px; line-height: 1.4; }
        .command-box { background: #1a202c; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; margin: 10px 0; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .feature-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; text-align: center; }
        .feature-icon { font-size: 2.5rem; margin-bottom: 10px; }
        .feature-title { font-size: 1.1rem; font-weight: 600; margin-bottom: 8px; }
        .feature-desc { font-size: 0.9rem; opacity: 0.9; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-warning { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DÉMARRAGE MESSAGERIE</h1>
            <p>Guide complet pour lancer le système de messagerie WhatsApp</p>
        </div>
        
        <?php
        // Vérification du statut des composants
        $backendStatus = file_exists('index.php') && file_exists('MessageManager.php') && file_exists('AuthManager.php');
        $frontendStatus = file_exists('../../../Frantend/schoolproject/src/pages/MessagingSystem.js');
        $reactRunning = false;
        
        // Test si React est en cours d'exécution
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:3000');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 2);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $reactRunning = ($httpCode === 200);
        ?>
        
        <div class="info">
            <h3>📊 État du Système</h3>
            <p>
                <span class="status-indicator <?php echo $backendStatus ? 'status-online' : 'status-offline'; ?>"></span>
                <strong>Backend API:</strong> <?php echo $backendStatus ? 'Opérationnel' : 'Non configuré'; ?>
            </p>
            <p>
                <span class="status-indicator <?php echo $frontendStatus ? 'status-online' : 'status-offline'; ?>"></span>
                <strong>Frontend React:</strong> <?php echo $frontendStatus ? 'Créé' : 'Non créé'; ?>
            </p>
            <p>
                <span class="status-indicator <?php echo $reactRunning ? 'status-online' : 'status-offline'; ?>"></span>
                <strong>Serveur React:</strong> <?php echo $reactRunning ? 'En cours d\'exécution' : 'Arrêté'; ?>
            </p>
        </div>
        
        <div class="step-card">
            <h3>📋 Étape 1: Vérification des Prérequis</h3>
            
            <?php if ($backendStatus && $frontendStatus): ?>
                <div class="success">
                    ✅ <strong>Tous les fichiers sont présents !</strong><br>
                    • Backend API configuré<br>
                    • Frontend React créé<br>
                    • Base de données configurée
                </div>
            <?php else: ?>
                <div class="error">
                    ❌ <strong>Fichiers manquants détectés</strong><br>
                    <?php if (!$backendStatus): ?>
                        • Backend API non configuré<br>
                    <?php endif; ?>
                    <?php if (!$frontendStatus): ?>
                        • Frontend React non créé<br>
                    <?php endif; ?>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <a href="reconstruction_systeme_messagerie.php" class="btn btn-danger">🔧 RECONSTRUIRE SYSTÈME</a>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="step-card">
            <h3>🖥️ Étape 2: Démarrage du Serveur React</h3>
            
            <?php if ($reactRunning): ?>
                <div class="success">
                    ✅ <strong>Serveur React déjà en cours d'exécution !</strong><br>
                    Accessible sur : <a href="http://localhost:3000" target="_blank">http://localhost:3000</a>
                </div>
            <?php else: ?>
                <div class="warning">
                    ⚠️ <strong>Serveur React non démarré</strong><br>
                    Suivez les instructions ci-dessous pour le démarrer.
                </div>
                
                <h4>Instructions de démarrage :</h4>
                <ol>
                    <li><strong>Ouvrir un terminal</strong> dans le dossier du projet</li>
                    <li><strong>Naviguer vers le dossier React :</strong></li>
                </ol>
                
                <div class="command-box">
                    cd Frantend/schoolproject
                </div>
                
                <ol start="3">
                    <li><strong>Installer les dépendances (si première fois) :</strong></li>
                </ol>
                
                <div class="command-box">
                    npm install
                </div>
                
                <ol start="4">
                    <li><strong>Démarrer le serveur de développement :</strong></li>
                </ol>
                
                <div class="command-box">
                    npm start
                </div>
                
                <div class="info">
                    <strong>Note :</strong> Le serveur React se lancera automatiquement sur <code>http://localhost:3000</code>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="step-card">
            <h3>🔐 Étape 3: Connexion et Accès</h3>
            
            <h4>Utilisateurs autorisés :</h4>
            <ul>
                <li><strong>Parents</strong> - Peuvent communiquer avec enseignants et administration</li>
                <li><strong>Enseignants</strong> - Peuvent communiquer avec parents et administration</li>
                <li><strong>Administrateurs</strong> - Accès complet à toutes les conversations</li>
                <li><strong>❌ Étudiants</strong> - Accès interdit au système de messagerie</li>
            </ul>
            
            <div class="warning">
                <strong>Important :</strong> Vous devez être connecté avec un compte ayant l'un des rôles autorisés pour accéder à la messagerie.
            </div>
        </div>
        
        <div class="step-card">
            <h3>💬 Étape 4: Utilisation de la Messagerie</h3>
            
            <h4>Accès à la messagerie :</h4>
            <ol>
                <li>Se connecter à l'application React</li>
                <li>Cliquer sur <strong>"Messagerie"</strong> dans le menu de navigation</li>
                <li>Ou accéder directement à : <code>http://localhost:3000/messagerie</code></li>
            </ol>
            
            <h4>Fonctionnalités disponibles :</h4>
            <ul>
                <li>📝 <strong>Nouvelle conversation</strong> - Bouton "✏️" en haut à droite</li>
                <li>💬 <strong>Messages privés</strong> - Confidentialité stricte entre utilisateurs</li>
                <li>✏️ <strong>Modification</strong> - Clic droit sur un message → "Modifier"</li>
                <li>🗑️ <strong>Suppression</strong> - "Pour moi" ou "Pour tous" (style WhatsApp)</li>
                <li>📊 <strong>Statistiques</strong> - Compteurs en temps réel</li>
                <li>🔄 <strong>Actualisation</strong> - Automatique toutes les 30 secondes</li>
            </ul>
        </div>
        
        <div style="margin-top: 40px;">
            <h2>🎯 Fonctionnalités Implémentées</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-title">Sécurité Avancée</div>
                    <div class="feature-desc">Authentification par tokens, contrôle d'accès par rôles, confidentialité stricte</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💬</div>
                    <div class="feature-title">Messagerie Privée</div>
                    <div class="feature-desc">Conversations privées, messages temps réel, historique complet</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">✏️</div>
                    <div class="feature-title">Édition WhatsApp</div>
                    <div class="feature-desc">Modification messages, suppression pour soi/tous, sauvegarde originaux</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">Interface Moderne</div>
                    <div class="feature-desc">Design WhatsApp, messages alignés, responsive mobile</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <div class="feature-title">Statistiques</div>
                    <div class="feature-desc">Compteurs temps réel, messages lus/non lus, conversations actives</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔄</div>
                    <div class="feature-title">Temps Réel</div>
                    <div class="feature-desc">Actualisation automatique, notifications, synchronisation</div>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <?php if ($reactRunning): ?>
                <a href="http://localhost:3000/messagerie" class="btn btn-success btn-large" target="_blank">
                    🚀 OUVRIR MESSAGERIE
                </a>
            <?php else: ?>
                <div class="warning">
                    <p><strong>Démarrez d'abord le serveur React pour accéder à la messagerie</strong></p>
                </div>
            <?php endif; ?>
            
            <a href="test_integration_complete.php" class="btn">🧪 TESTS INTÉGRATION</a>
            <a href="test_api_complet.php" class="btn">🔧 TESTS API</a>
            <a href="diagnostic_table_messages.php" class="btn">🔍 DIAGNOSTIC</a>
        </div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>🆘 Dépannage</h3>
            
            <h4>Problèmes courants :</h4>
            <ul>
                <li><strong>Erreur "Impossible de charger les conversations"</strong>
                    <ul>
                        <li>Vérifier que l'API backend est accessible</li>
                        <li>Contrôler la connexion à la base de données</li>
                        <li>Exécuter le diagnostic complet</li>
                    </ul>
                </li>
                
                <li><strong>Page messagerie inaccessible</strong>
                    <ul>
                        <li>Vérifier que vous êtes connecté avec un rôle autorisé</li>
                        <li>Contrôler que la route est bien configurée dans App.js</li>
                        <li>Redémarrer le serveur React</li>
                    </ul>
                </li>
                
                <li><strong>Messages ne s'affichent pas</strong>
                    <ul>
                        <li>Vérifier la structure de la table messages</li>
                        <li>Contrôler les permissions utilisateur</li>
                        <li>Tester l'API avec les outils de diagnostic</li>
                    </ul>
                </li>
            </ul>
            
            <h4>Commandes utiles :</h4>
            <div class="command-box">
# Redémarrer React
cd Frantend/schoolproject
npm start

# Vérifier les logs React
# Les erreurs s'affichent dans le terminal

# Tester l'API directement
curl -H "Authorization: Bearer test_user_1" \
     "http://localhost/Project_PFE/Backend/api/messaging/?action=test"
            </div>
        </div>
        
        <div class="success" style="margin-top: 30px;">
            <h3>✅ Système Prêt !</h3>
            <p>Le système de messagerie WhatsApp est maintenant complètement opérationnel avec :</p>
            <ul>
                <li>🔧 <strong>Backend API</strong> - Toutes les fonctionnalités implémentées</li>
                <li>⚛️ <strong>Frontend React</strong> - Interface moderne et responsive</li>
                <li>🗄️ <strong>Base de données</strong> - Structure complète avec toutes les fonctionnalités</li>
                <li>🔐 <strong>Sécurité</strong> - Authentification et contrôle d'accès</li>
                <li>💬 <strong>Fonctionnalités WhatsApp</strong> - Édition, suppression, confidentialité</li>
            </ul>
        </div>
    </div>
</body>
</html>
