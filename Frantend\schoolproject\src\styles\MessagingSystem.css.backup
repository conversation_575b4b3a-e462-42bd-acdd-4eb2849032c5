/* MessagingSystem.css - Design moderne type WhatsApp */

.messaging-system {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: #f0f2f5;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.messaging-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(102, 126, 234, 0.2);
}

.messaging-header h2 {
    margin: 0 0 10px 0;
    font-size: 2.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.messaging-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.messaging-layout {
    display: flex;
    height: 75vh;
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Sidebar des conversations */
.conversations-sidebar {
    width: 380px;
    border-right: 1px solid #e4e6ea;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    align-items: center;
    gap: 12px;
    background: white;
}

.search-box {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.search-box svg {
    position: absolute;
    left: 15px;
    color: #8e8e93;
    z-index: 1;
    font-size: 14px;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 40px;
    border: 1px solid #e4e6ea;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.search-box input:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.new-conversation-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: #667eea;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 16px;
}

.new-conversation-btn:hover {
    background: #5a6fd8;
    transform: scale(1.05);
}

.new-conversation-btn.active {
    background: #dc3545;
    transform: rotate(45deg);
}

.new-conversation-panel {
    padding: 20px;
    border-bottom: 1px solid #e4e6ea;
    background: white;
    max-height: 300px;
    overflow-y: auto;
}

.new-conversation-panel h4 {
    margin: 0 0 15px 0;
    color: #1c1e21;
    font-size: 16px;
    font-weight: 600;
}

.users-list {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-item {
    padding: 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.user-item:hover {
    background: #f0f2f5;
}

.user-avatar, .conversation-avatar, .contact-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.user-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.user-name {
    font-weight: 600;
    color: #1c1e21;
    font-size: 14px;
}

.user-role {
    font-size: 12px;
    color: #65676b;
    text-transform: capitalize;
    background: #e4e6ea;
    padding: 2px 8px;
    border-radius: 10px;
    align-self: flex-start;
}

.user-email {
    font-size: 11px;
    color: #8e8e93;
}

.no-users {
    text-align: center;
    padding: 40px 20px;
    color: #8e8e93;
    font-style: italic;
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
}

.conversation-item {
    padding: 16px 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f0f2f5;
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
}

.conversation-item:hover {
    background: #f8f9fa;
}

.conversation-item.active {
    background: #667eea;
    color: white;
}

.conversation-item.active .contact-name,
.conversation-item.active .contact-role,
.conversation-item.active .last-activity,
.conversation-item.active .last-message {
    color: white;
}

.conversation-item.active .user-role {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.contact-name {
    font-weight: 600;
    font-size: 15px;
    color: #1c1e21;
}

.last-activity {
    font-size: 11px;
    color: #8e8e93;
}

.conversation-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.contact-role {
    font-size: 12px;
    color: #65676b;
    text-transform: capitalize;
}

.unread-badge {
    background: #42b883;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: 600;
}

.last-message {
    font-size: 13px;
    color: #65676b;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.no-conversations {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8e8e93;
    text-align: center;
    padding: 40px 20px;
}

.no-conversations h4 {
    margin: 15px 0 10px 0;
    color: #65676b;
}

.no-conversations p {
    margin: 0;
    font-size: 14px;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8e8e93;
    gap: 15px;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f0f2f5;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Zone de chat principale */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e4e6ea;
    background: white;
    display: flex;
    align-items: center;
    gap: 15px;
}

.contact-details h3 {
    margin: 0 0 4px 0;
    color: #1c1e21;
    font-size: 1.2rem;
}

.contact-details .contact-role {
    color: #65676b;
    font-size: 13px;
    text-transform: capitalize;
}

.messages-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.loading-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8e8e93;
    gap: 15px;
}

.no-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8e8e93;
    text-align: center;
}

.no-messages h4 {
    margin: 15px 0 10px 0;
    color: #65676b;
}

.no-messages p {
    margin: 0;
    font-size: 14px;
}

.message {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    margin-bottom: 8px;
    animation: fadeInMessage 0.3s ease;
    position: relative;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.message.sent .message-content {
    background: #667eea;
    color: white;
    border-bottom-right-radius: 4px;
}

.message.received .message-content {
    background: white;
    color: #1c1e21;
    border-bottom-left-radius: 4px;
    border: 1px solid #e4e6ea;
}

.message-text {
    margin-bottom: 6px;
    line-height: 1.4;
    font-size: 14px;
}

.message-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    opacity: 0.8;
    flex-wrap: wrap;
}

.message.sent .message-meta {
    justify-content: flex-end;
    color: rgba(255, 255, 255, 0.8);
}

.message.received .message-meta {
    justify-content: flex-start;
    color: #8e8e93;
}

.message-time {
    display: flex;
    align-items: center;
    gap: 3px;
}

.modified-indicator {
    display: flex;
    align-items: center;
    gap: 3px;
    font-style: italic;
    background: rgba(255, 193, 7, 0.2);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
}

.message.sent .modified-indicator {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
}

.read-status {
    display: flex;
    align-items: center;
}

.read-status .sent {
    color: rgba(255, 255, 255, 0.6);
}

.read-status .read {
    color: #42b883;
}

.message-actions {
    position: relative;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message:hover .message-actions {
    opacity: 1;
}

.actions-toggle {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(0,0,0,0.1);
    color: #8e8e93;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 12px;
}

.actions-toggle:hover {
    background: rgba(0,0,0,0.2);
    color: #1c1e21;
}

.actions-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e4e6ea;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    z-index: 1000;
    min-width: 180px;
    overflow: hidden;
    animation: fadeInMenu 0.2s ease;
}

@keyframes fadeInMenu {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.action-btn {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    font-size: 13px;
    color: #1c1e21;
    transition: background 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.action-btn:hover {
    background: #f8f9fa;
}

.action-btn.edit-btn:hover {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.action-btn.delete-btn:hover,
.action-btn.delete-all-btn:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.edit-message {
    width: 100%;
}

.edit-message textarea {
    width: 100%;
    min-height: 60px;
    border: 1px solid #e4e6ea;
    border-radius: 12px;
    padding: 12px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    outline: none;
    background: #f8f9fa;
}

.edit-message textarea:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
}

.save-btn, .cancel-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
}

.save-btn {
    background: #42b883;
    color: white;
}

.save-btn:hover {
    background: #369870;
    transform: scale(1.05);
}

.cancel-btn {
    background: #dc3545;
    color: white;
    font-size: 16px;
}

.cancel-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

.message-input-area {
    padding: 20px;
    border-top: 1px solid #e4e6ea;
    background: white;
}

.input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 12px 16px;
    border: 1px solid #e4e6ea;
    transition: all 0.3s ease;
}

.input-container:focus-within {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-container textarea {
    flex: 1;
    border: none;
    background: none;
    outline: none;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    max-height: 120px;
    min-height: 20px;
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: #667eea;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.send-btn:hover:not(:disabled) {
    background: #5a6fd8;
    transform: scale(1.05);
}

.send-btn:disabled {
    background: #e4e6ea;
    color: #8e8e93;
    cursor: not-allowed;
    opacity: 0.6;
}

.no-chat-selected {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8e8e93;
    text-align: center;
    gap: 20px;
    padding: 40px;
}

.no-chat-selected h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #65676b;
}

.no-chat-selected p {
    margin: 0;
    font-size: 1rem;
}

.start-conversation-btn {
    padding: 12px 24px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.start-conversation-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Animations */
@keyframes fadeInMessage {
    from { 
        opacity: 0; 
        transform: translateY(10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Scrollbar personnalisée */
.conversations-list::-webkit-scrollbar,
.messages-area::-webkit-scrollbar,
.new-conversation-panel::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track,
.messages-area::-webkit-scrollbar-track,
.new-conversation-panel::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.conversations-list::-webkit-scrollbar-thumb,
.messages-area::-webkit-scrollbar-thumb,
.new-conversation-panel::-webkit-scrollbar-thumb {
    background: #e4e6ea;
    border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover,
.messages-area::-webkit-scrollbar-thumb:hover,
.new-conversation-panel::-webkit-scrollbar-thumb:hover {
    background: #d0d2d6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .messaging-layout {
        flex-direction: column;
        height: auto;
    }
    
    .conversations-sidebar {
        width: 100%;
        max-height: 300px;
    }
    
    .chat-area {
        min-height: 400px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .messaging-system {
        padding: 10px;
    }
    
    .messaging-header {
        padding: 20px;
    }
    
    .messaging-header h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .conversations-sidebar {
        width: 100%;
    }
    
    .sidebar-header {
        padding: 15px;
    }
    
    .search-box input {
        font-size: 16px; /* Évite le zoom sur iOS */
    }
    
    .message-content {
        max-width: 90%;
    }
    
    .actions-menu {
        min-width: 150px;
    }
}
