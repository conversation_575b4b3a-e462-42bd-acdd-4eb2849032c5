<?php
/**
 * Configuration pour l'API de Messagerie
 * Système de messagerie sécurisé et moderne
 */

// Configuration CORS
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
header('Content-Type: application/json; charset=utf-8');

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration de la base de données
class DatabaseConfig {
    private static $host = 'localhost';
    private static $dbname = 'GestionScolaire';
    private static $username = 'root';
    private static $password = '';
    private static $pdo = null;
    
    public static function getConnection() {
        if (self::$pdo === null) {
            try {
                self::$pdo = new PDO(
                    "mysql:host=" . self::$host . ";dbname=" . self::$dbname . ";charset=utf8mb4",
                    self::$username,
                    self::$password,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'error' => 'Erreur de connexion à la base de données',
                    'details' => $e->getMessage()
                ]);
                exit();
            }
        }
        return self::$pdo;
    }
}

// Classe d'authentification sécurisée
class AuthManager {
    private static $allowedRoles = ['parent', 'enseignant', 'admin', 'responsable'];
    private static $forbiddenRoles = ['etudiant', 'student', 'élève', 'eleve'];
    
    /**
     * Authentifier l'utilisateur et vérifier ses permissions
     */
    public static function authenticate() {
        $headers = getallheaders();
        $token = null;
        
        // Récupérer le token d'autorisation
        if (isset($headers['Authorization'])) {
            $token = str_replace('Bearer ', '', $headers['Authorization']);
        }
        
        if (!$token) {
            self::sendUnauthorized('Token d\'authentification manquant');
        }
        
        $pdo = DatabaseConfig::getConnection();
        
        try {
            // Méthode 1: Vérifier dans la table user_sessions si elle existe
            $user = self::checkSessionToken($pdo, $token);
            
            if (!$user) {
                // Méthode 2: Token de développement/test
                $user = self::checkDevelopmentToken($pdo, $token);
            }
            
            if (!$user) {
                self::sendUnauthorized('Token invalide ou expiré');
            }
            
            // Vérifier que l'utilisateur a accès à la messagerie
            if (!in_array(strtolower($user['role']), self::$allowedRoles)) {
                self::sendForbidden('Accès refusé. Seuls les parents, enseignants et administrateurs peuvent utiliser la messagerie.');
            }

            // Vérification stricte - exclure explicitement les étudiants
            if (in_array(strtolower($user['role']), self::$forbiddenRoles)) {
                self::sendForbidden('Accès strictement interdit aux étudiants.');
            }
            
            return $user;
            
        } catch (Exception $e) {
            self::sendUnauthorized('Erreur d\'authentification: ' . $e->getMessage());
        }
    }
    
    /**
     * Vérifier le token dans la table user_sessions
     */
    private static function checkSessionToken($pdo, $token) {
        try {
            $stmt = $pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role 
                FROM user_sessions s
                JOIN utilisateurs u ON s.user_id = u.id
                JOIN roles r ON u.role_id = r.id
                WHERE s.token = ? AND s.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            return $stmt->fetch();
        } catch (Exception $e) {
            return null; // Table user_sessions n'existe pas
        }
    }
    
    /**
     * Vérifier les tokens de développement
     */
    private static function checkDevelopmentToken($pdo, $token) {
        // Pour les tokens de 32 caractères (générés par le login)
        if (strlen($token) === 32 && ctype_xdigit($token)) {
            $stmt = $pdo->prepare("
                SELECT u.id, u.nom, u.email, r.nom as role 
                FROM utilisateurs u 
                JOIN roles r ON u.role_id = r.id 
                WHERE r.nom IN ('" . implode("','", self::$allowedRoles) . "') 
                ORDER BY u.id 
                LIMIT 1
            ");
            $stmt->execute();
            return $stmt->fetch();
        }
        
        // Pour les tokens de test avec format spécifique
        foreach (self::$allowedRoles as $role) {
            if (strpos($token, $role) !== false) {
                $stmt = $pdo->prepare("
                    SELECT u.id, u.nom, u.email, r.nom as role 
                    FROM utilisateurs u 
                    JOIN roles r ON u.role_id = r.id 
                    WHERE r.nom = ? 
                    LIMIT 1
                ");
                $stmt->execute([$role]);
                return $stmt->fetch();
            }
        }
        
        return null;
    }
    
    /**
     * Envoyer une réponse d'erreur 401
     */
    private static function sendUnauthorized($message) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'code' => 'UNAUTHORIZED'
        ]);
        exit();
    }
    
    /**
     * Envoyer une réponse d'erreur 403
     */
    private static function sendForbidden($message) {
        http_response_code(403);
        echo json_encode([
            'success' => false,
            'error' => $message,
            'code' => 'FORBIDDEN'
        ]);
        exit();
    }
}

// Classe utilitaire pour les réponses JSON
class ResponseHelper {
    public static function success($data = null, $message = null) {
        $response = ['success' => true];
        if ($message) $response['message'] = $message;
        if ($data) $response['data'] = $data;
        echo json_encode($response);
        exit();
    }
    
    public static function error($message, $code = 400, $details = null) {
        http_response_code($code);
        $response = [
            'success' => false,
            'error' => $message
        ];
        if ($details) $response['details'] = $details;
        echo json_encode($response);
        exit();
    }
    
    public static function notFound($message = 'Ressource non trouvée') {
        self::error($message, 404);
    }
    
    public static function badRequest($message = 'Requête invalide') {
        self::error($message, 400);
    }
}

// Classe de validation des données
class Validator {
    public static function validateMessage($data) {
        $errors = [];
        
        if (!isset($data['destinataire_id']) || !is_numeric($data['destinataire_id'])) {
            $errors[] = 'ID du destinataire requis et doit être numérique';
        }
        
        if (!isset($data['message']) || empty(trim($data['message']))) {
            $errors[] = 'Le message ne peut pas être vide';
        }
        
        if (isset($data['message']) && strlen(trim($data['message'])) > 5000) {
            $errors[] = 'Le message ne peut pas dépasser 5000 caractères';
        }
        
        return $errors;
    }
    
    public static function validateMessageUpdate($data) {
        $errors = [];
        
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            $errors[] = 'ID du message requis et doit être numérique';
        }
        
        if (!isset($data['message']) || empty(trim($data['message']))) {
            $errors[] = 'Le nouveau message ne peut pas être vide';
        }
        
        if (isset($data['message']) && strlen(trim($data['message'])) > 5000) {
            $errors[] = 'Le message ne peut pas dépasser 5000 caractères';
        }
        
        return $errors;
    }
    
    public static function validateMessageDelete($data) {
        $errors = [];
        
        if (!isset($data['id']) || !is_numeric($data['id'])) {
            $errors[] = 'ID du message requis et doit être numérique';
        }
        
        if (isset($data['delete_type']) && !in_array($data['delete_type'], ['self', 'both'])) {
            $errors[] = 'Type de suppression invalide (self ou both)';
        }
        
        return $errors;
    }
}

// Fonction utilitaire pour logger les erreurs
function logError($message, $context = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    error_log('MESSAGING_API: ' . json_encode($logEntry));
}
?>
