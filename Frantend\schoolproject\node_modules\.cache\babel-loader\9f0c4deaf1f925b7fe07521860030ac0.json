{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\MessagingSystem.js\";\nimport React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\nconst MessagingSystem = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [authorizedUsers, setAuthorizedUsers] = useState([]);\n  const [showNewConversation, setShowNewConversation] = useState(false);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [showContextMenu, setShowContextMenu] = useState(null);\n  const [stats, setStats] = useState({});\n  const messagesEndRef = useRef(null);\n  const contextMenuRef = useRef(null);\n  const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n\n  // Scroll automatique vers le bas\n  const scrollToBottom = () => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fermer le menu contextuel en cliquant ailleurs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n        setShowContextMenu(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fonction pour faire des requêtes API\n  const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n    try {\n      const token = localStorage.getItem('token') || 'test_user_1';\n      const config = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      };\n      if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n        config.body = JSON.stringify(data);\n      }\n      const url = `${API_BASE_URL}?action=${endpoint}`;\n      console.log('API Request:', {\n        url,\n        method,\n        endpoint,\n        token\n      });\n      const response = await fetch(url, config);\n      console.log('API Response Status:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('API Response Data:', result);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur API');\n      }\n      return result;\n    } catch (error) {\n      console.error('Erreur API complète:', {\n        endpoint,\n        method,\n        error: error.message,\n        stack: error.stack\n      });\n      throw error;\n    }\n  };\n\n  // Charger les conversations avec confidentialité stricte\n  const loadConversations = async () => {\n    try {\n      var _result$data;\n      setLoading(true);\n      const result = await makeAPIRequest('conversations');\n\n      // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n      const currentUserId = parseInt(user === null || user === void 0 ? void 0 : user.id);\n      if (!currentUserId) {\n        throw new Error('Utilisateur non identifié');\n      }\n\n      // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n      const secureConversations = (result.data || []).filter(conversation => {\n        // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n        const contactId = parseInt(conversation.contact_id);\n        return contactId && contactId !== currentUserId && contactId > 0;\n      });\n      console.log('🔒 Conversations sécurisées chargées:', {\n        total_received: ((_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.length) || 0,\n        secure_filtered: secureConversations.length,\n        user_id: currentUserId\n      });\n      setConversations(secureConversations);\n    } catch (error) {\n      setError('Impossible de charger les conversations: ' + error.message);\n      console.error('🚨 Erreur sécurité conversations:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les messages d'une conversation avec confidentialité stricte\n  const loadMessages = async contactId => {\n    try {\n      var _result$data2;\n      setLoading(true);\n      const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n\n      // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n      const currentUserId = parseInt(user === null || user === void 0 ? void 0 : user.id);\n      if (!currentUserId) {\n        throw new Error('Utilisateur non identifié');\n      }\n\n      // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n      const secureMessages = (result.data || []).filter(message => {\n        const expediteurId = parseInt(message.expediteur_id);\n        const destinataireId = parseInt(message.destinataire_id);\n\n        // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n        return expediteurId === currentUserId || destinataireId === currentUserId;\n      }).map(message => {\n        // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n        const expediteurId = parseInt(message.expediteur_id);\n        const currentUserId = parseInt(user === null || user === void 0 ? void 0 : user.id);\n        return {\n          ...message,\n          message_type: expediteurId === currentUserId ? 'sent' : 'received',\n          is_own_message: expediteurId === currentUserId\n        };\n      });\n      console.log('🔒 Messages sécurisés chargés:', {\n        total_received: ((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : _result$data2.length) || 0,\n        secure_filtered: secureMessages.length,\n        user_id: currentUserId,\n        contact_id: contactId\n      });\n      setMessages(secureMessages);\n    } catch (error) {\n      setError('Impossible de charger les messages: ' + error.message);\n      console.error('🚨 Erreur sécurité messages:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les utilisateurs autorisés\n  const loadAuthorizedUsers = async () => {\n    try {\n      const result = await makeAPIRequest('users');\n      setAuthorizedUsers(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les utilisateurs: ' + error.message);\n    }\n  };\n\n  // Charger les statistiques\n  const loadStats = async () => {\n    try {\n      const result = await makeAPIRequest('stats');\n      setStats(result.data || {});\n    } catch (error) {\n      console.error('Erreur chargement stats:', error);\n    }\n  };\n\n  // Envoyer un message\n  const sendMessage = async () => {\n    if (!newMessage.trim()) return;\n    try {\n      const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n      if (!destinataireId) {\n        setError('Veuillez sélectionner un destinataire');\n        return;\n      }\n      await makeAPIRequest('send', 'POST', {\n        destinataire_id: destinataireId,\n        message: newMessage.trim()\n      });\n      setNewMessage('');\n      setShowNewConversation(false);\n\n      // Recharger les conversations et messages\n      await loadConversations();\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible d\\'envoyer le message: ' + error.message);\n    }\n  };\n\n  // Modifier un message\n  const editMessage = async (messageId, newContent) => {\n    try {\n      await makeAPIRequest('edit', 'PUT', {\n        message_id: messageId,\n        message: newContent\n      });\n      setEditingMessage(null);\n      setEditContent('');\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de modifier le message: ' + error.message);\n    }\n  };\n\n  // Supprimer un message\n  const deleteMessage = async (messageId, deleteType = 'for_me') => {\n    try {\n      await makeAPIRequest('delete', 'DELETE', {\n        message_id: messageId,\n        delete_type: deleteType\n      });\n      setShowContextMenu(null);\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de supprimer le message: ' + error.message);\n    }\n  };\n\n  // Sélectionner une conversation\n  const selectConversation = async conversation => {\n    setSelectedConversation(conversation);\n    setShowNewConversation(false);\n    await loadMessages(conversation.contact_id);\n  };\n\n  // Démarrer une nouvelle conversation\n  const startNewConversation = () => {\n    setSelectedConversation(null);\n    setMessages([]);\n    setShowNewConversation(true);\n  };\n\n  // Gérer le menu contextuel\n  const handleContextMenu = (e, message) => {\n    e.preventDefault();\n    setShowContextMenu({\n      x: e.clientX,\n      y: e.clientY,\n      message: message\n    });\n  };\n\n  // Démarrer l'édition d'un message\n  const startEditing = message => {\n    setEditingMessage(message.id);\n    setEditContent(message.message);\n    setShowContextMenu(null);\n  };\n\n  // Annuler l'édition\n  const cancelEditing = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  // Confirmer l'édition\n  const confirmEdit = async () => {\n    if (editContent.trim() && editingMessage) {\n      await editMessage(editingMessage, editContent.trim());\n    }\n  };\n\n  // Formater la date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffDays <= 7) {\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n\n  // Charger les données au montage du composant\n  useEffect(() => {\n    // Vérifier que l'utilisateur est connecté avant de charger les données\n    if (user && user.id) {\n      loadConversations();\n      loadAuthorizedUsers();\n      loadStats();\n    } else {\n      console.warn('Utilisateur non connecté, chargement des données de test...');\n      // Charger quand même pour les tests\n      loadConversations();\n      loadAuthorizedUsers();\n      loadStats();\n    }\n  }, [user]);\n\n  // Actualiser périodiquement\n  useEffect(() => {\n    const interval = setInterval(() => {\n      loadConversations();\n      if (selectedConversation) {\n        loadMessages(selectedConversation.contact_id);\n      }\n    }, 30000); // Actualiser toutes les 30 secondes\n\n    return () => clearInterval(interval);\n  }, [selectedConversation]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-system\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-stats\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 25\n    }\n  }, stats.total_messages || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 25\n    }\n  }, \"Messages\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 361,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 25\n    }\n  }, stats.messages_non_lus || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 25\n    }\n  }, \"Non lus\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 25\n    }\n  }, conversations.length), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 25\n    }\n  }, \"Conversations\")))), error && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 21\n    }\n  }, \"\\u274C \", error), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setError(''),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 21\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 25\n    }\n  }, \"Conversations\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"new-conversation-btn\",\n    onClick: startNewConversation,\n    title: \"Nouvelle conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-list\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 21\n    }\n  }, loading && conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 29\n    }\n  }, \"Chargement...\") : conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-conversations\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 33\n    }\n  }, \"Aucune conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 33\n    }\n  }, \"D\\xE9marrer une conversation\")) : conversations.map(conversation => /*#__PURE__*/React.createElement(\"div\", {\n    key: conversation.contact_id,\n    className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.contact_id) === conversation.contact_id ? 'active' : ''}`,\n    onClick: () => selectConversation(conversation),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 37\n    }\n  }, conversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 41\n    }\n  }, conversation.contact_nom, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 45\n    }\n  }, conversation.contact_role)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-preview\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 41\n    }\n  }, conversation.dernier_message || 'Aucun message'), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-meta\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 45\n    }\n  }, formatDate(conversation.derniere_activite)), conversation.messages_non_lus > 0 && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"unread-badge\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 49\n    }\n  }, conversation.messages_non_lus))))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 17\n    }\n  }, showNewConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setShowNewConversation(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 33\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: selectedUser,\n    onChange: e => setSelectedUser(e.target.value),\n    className: \"user-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 454,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur...\"), authorizedUsers.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 41\n    }\n  }, user.nom, \" (\", user.role, \")\"))))) : selectedConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 465,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 33\n    }\n  }, selectedConversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_nom), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_role)))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-selected\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 482,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 33\n    }\n  }, \"S\\xE9lectionnez une conversation ou d\\xE9marrez-en une nouvelle\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"))), (selectedConversation || showNewConversation) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messages-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 494,\n      columnNumber: 29\n    }\n  }, messages.map(message => {\n    const currentUserId = parseInt(user === null || user === void 0 ? void 0 : user.id);\n    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n    const messageType = isOwnMessage ? 'sent' : 'received';\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: message.id,\n      className: `message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`,\n      onContextMenu: e => handleContextMenu(e, message),\n      \"data-sender-id\": message.expediteur_id,\n      \"data-receiver-id\": message.destinataire_id,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 41\n      }\n    }, !isOwnMessage && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-sender\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 510,\n        columnNumber: 49\n      }\n    }, message.expediteur_nom || 'Utilisateur'), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-content ${messageType}-content`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 45\n      }\n    }, editingMessage === message.id ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 53\n      }\n    }, /*#__PURE__*/React.createElement(\"textarea\", {\n      value: editContent,\n      onChange: e => setEditContent(e.target.value),\n      className: \"edit-textarea\",\n      autoFocus: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 518,\n        columnNumber: 57\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"edit-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 57\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      onClick: confirmEdit,\n      className: \"confirm-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 525,\n        columnNumber: 61\n      }\n    }, \"\\u2713\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: cancelEditing,\n      className: \"cancel-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 61\n      }\n    }, \"\\u2715\"))) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-text\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 57\n      }\n    }, message.message, message.modifie === '1' && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-edited\",\n      title: `Modifié le ${formatDate(message.date_modification)}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 65\n      }\n    }, \"(modifi\\xE9)\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-time ${messageType}-time`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 57\n      }\n    }, formatDate(message.date_envoi), isOwnMessage && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-status\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 546,\n        columnNumber: 65\n      }\n    }, message.lu === '1' ? '✓✓' : '✓')))), process.env.NODE_ENV === 'development' && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-debug\",\n      title: `Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 49\n      }\n    }, \"\\uD83D\\uDD12\"));\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: messagesEndRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 568,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    value: newMessage,\n    onChange: e => setNewMessage(e.target.value),\n    placeholder: \"Tapez votre message...\",\n    className: \"message-input\",\n    rows: \"1\",\n    onKeyDown: e => {\n      if (e.key === 'Enter' && !e.shiftKey) {\n        e.preventDefault();\n        sendMessage();\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: sendMessage,\n    className: \"send-button\",\n    disabled: !newMessage.trim() || loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 583,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE4\")))))), showContextMenu && /*#__PURE__*/React.createElement(\"div\", {\n    ref: contextMenuRef,\n    className: \"context-menu\",\n    style: {\n      left: showContextMenu.x,\n      top: showContextMenu.y\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 17\n    }\n  }, showContextMenu.message.can_modify === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => startEditing(showContextMenu.message),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_me'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour moi\"), showContextMenu.message.can_delete_for_all === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_everyone'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 25\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour tous\")));\n};\nexport default MessagingSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "AuthContext", "MessagingSystem", "user", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "authorizedUsers", "setAuthorizedUsers", "showNewConversation", "setShowNewConversation", "selected<PERSON>ser", "setSelectedUser", "loading", "setLoading", "error", "setError", "editingMessage", "setEditingMessage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showContextMenu", "setShowContextMenu", "stats", "setStats", "messagesEndRef", "contextMenuRef", "API_BASE_URL", "scrollToBottom", "current", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "makeAPIRequest", "endpoint", "method", "data", "token", "localStorage", "getItem", "config", "headers", "body", "JSON", "stringify", "url", "console", "log", "response", "fetch", "status", "statusText", "result", "json", "success", "Error", "message", "stack", "loadConversations", "_result$data", "currentUserId", "parseInt", "id", "secureConversations", "filter", "conversation", "contactId", "contact_id", "total_received", "length", "secure_filtered", "user_id", "loadMessages", "_result$data2", "secureMessages", "expediteurId", "expediteur_id", "destinataireId", "destinataire_id", "map", "message_type", "is_own_message", "loadAuthorizedUsers", "loadStats", "sendMessage", "trim", "editMessage", "messageId", "newContent", "message_id", "deleteMessage", "deleteType", "delete_type", "selectConversation", "startNewConversation", "handleContextMenu", "e", "preventDefault", "x", "clientX", "y", "clientY", "startEditing", "cancelEditing", "confirmEdit", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "warn", "interval", "setInterval", "clearInterval", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "total_messages", "messages_non_lus", "onClick", "title", "key", "contact_nom", "char<PERSON>t", "toUpperCase", "contact_role", "dernier_message", "derniere_activite", "value", "onChange", "nom", "role", "Fragment", "isOwnMessage", "messageType", "onContextMenu", "expediteur_nom", "autoFocus", "modifie", "date_modification", "date_envoi", "lu", "process", "env", "NODE_ENV", "ref", "placeholder", "rows", "onKeyDown", "shift<PERSON>ey", "disabled", "style", "left", "top", "can_modify", "can_delete_for_all"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/MessagingSystem.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\n\nconst MessagingSystem = () => {\n    const { user } = useContext(AuthContext);\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [authorizedUsers, setAuthorizedUsers] = useState([]);\n    const [showNewConversation, setShowNewConversation] = useState(false);\n    const [selectedUser, setSelectedUser] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [editingMessage, setEditingMessage] = useState(null);\n    const [editContent, setEditContent] = useState('');\n    const [showContextMenu, setShowContextMenu] = useState(null);\n    const [stats, setStats] = useState({});\n    \n    const messagesEndRef = useRef(null);\n    const contextMenuRef = useRef(null);\n    \n    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n    \n    // Scroll automatique vers le bas\n    const scrollToBottom = () => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n        }\n    };\n    \n    useEffect(() => {\n        scrollToBottom();\n    }, [messages]);\n    \n    // Fermer le menu contextuel en cliquant ailleurs\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n                setShowContextMenu(null);\n            }\n        };\n        \n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    \n    // Fonction pour faire des requêtes API\n    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n        try {\n            const token = localStorage.getItem('token') || 'test_user_1';\n\n            const config = {\n                method,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                }\n            };\n\n            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n                config.body = JSON.stringify(data);\n            }\n\n            const url = `${API_BASE_URL}?action=${endpoint}`;\n            console.log('API Request:', { url, method, endpoint, token });\n\n            const response = await fetch(url, config);\n            console.log('API Response Status:', response.status, response.statusText);\n\n            const result = await response.json();\n            console.log('API Response Data:', result);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur API');\n            }\n\n            return result;\n        } catch (error) {\n            console.error('Erreur API complète:', {\n                endpoint,\n                method,\n                error: error.message,\n                stack: error.stack\n            });\n            throw error;\n        }\n    };\n    \n    // Charger les conversations avec confidentialité stricte\n    const loadConversations = async () => {\n        try {\n            setLoading(true);\n            const result = await makeAPIRequest('conversations');\n\n            // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n            const currentUserId = parseInt(user?.id);\n            if (!currentUserId) {\n                throw new Error('Utilisateur non identifié');\n            }\n\n            // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n            const secureConversations = (result.data || []).filter(conversation => {\n                // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n                const contactId = parseInt(conversation.contact_id);\n                return contactId && contactId !== currentUserId && contactId > 0;\n            });\n\n            console.log('🔒 Conversations sécurisées chargées:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureConversations.length,\n                user_id: currentUserId\n            });\n\n            setConversations(secureConversations);\n        } catch (error) {\n            setError('Impossible de charger les conversations: ' + error.message);\n            console.error('🚨 Erreur sécurité conversations:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les messages d'une conversation avec confidentialité stricte\n    const loadMessages = async (contactId) => {\n        try {\n            setLoading(true);\n            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n\n            // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n            const currentUserId = parseInt(user?.id);\n            if (!currentUserId) {\n                throw new Error('Utilisateur non identifié');\n            }\n\n            // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n            const secureMessages = (result.data || []).filter(message => {\n                const expediteurId = parseInt(message.expediteur_id);\n                const destinataireId = parseInt(message.destinataire_id);\n\n                // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n                return (expediteurId === currentUserId || destinataireId === currentUserId);\n            }).map(message => {\n                // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n                const expediteurId = parseInt(message.expediteur_id);\n                const currentUserId = parseInt(user?.id);\n\n                return {\n                    ...message,\n                    message_type: expediteurId === currentUserId ? 'sent' : 'received',\n                    is_own_message: expediteurId === currentUserId\n                };\n            });\n\n            console.log('🔒 Messages sécurisés chargés:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureMessages.length,\n                user_id: currentUserId,\n                contact_id: contactId\n            });\n\n            setMessages(secureMessages);\n        } catch (error) {\n            setError('Impossible de charger les messages: ' + error.message);\n            console.error('🚨 Erreur sécurité messages:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les utilisateurs autorisés\n    const loadAuthorizedUsers = async () => {\n        try {\n            const result = await makeAPIRequest('users');\n            setAuthorizedUsers(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les utilisateurs: ' + error.message);\n        }\n    };\n    \n    // Charger les statistiques\n    const loadStats = async () => {\n        try {\n            const result = await makeAPIRequest('stats');\n            setStats(result.data || {});\n        } catch (error) {\n            console.error('Erreur chargement stats:', error);\n        }\n    };\n    \n    // Envoyer un message\n    const sendMessage = async () => {\n        if (!newMessage.trim()) return;\n        \n        try {\n            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n            \n            if (!destinataireId) {\n                setError('Veuillez sélectionner un destinataire');\n                return;\n            }\n            \n            await makeAPIRequest('send', 'POST', {\n                destinataire_id: destinataireId,\n                message: newMessage.trim()\n            });\n            \n            setNewMessage('');\n            setShowNewConversation(false);\n            \n            // Recharger les conversations et messages\n            await loadConversations();\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible d\\'envoyer le message: ' + error.message);\n        }\n    };\n    \n    // Modifier un message\n    const editMessage = async (messageId, newContent) => {\n        try {\n            await makeAPIRequest('edit', 'PUT', {\n                message_id: messageId,\n                message: newContent\n            });\n            \n            setEditingMessage(null);\n            setEditContent('');\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de modifier le message: ' + error.message);\n        }\n    };\n    \n    // Supprimer un message\n    const deleteMessage = async (messageId, deleteType = 'for_me') => {\n        try {\n            await makeAPIRequest('delete', 'DELETE', {\n                message_id: messageId,\n                delete_type: deleteType\n            });\n            \n            setShowContextMenu(null);\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de supprimer le message: ' + error.message);\n        }\n    };\n    \n    // Sélectionner une conversation\n    const selectConversation = async (conversation) => {\n        setSelectedConversation(conversation);\n        setShowNewConversation(false);\n        await loadMessages(conversation.contact_id);\n    };\n    \n    // Démarrer une nouvelle conversation\n    const startNewConversation = () => {\n        setSelectedConversation(null);\n        setMessages([]);\n        setShowNewConversation(true);\n    };\n    \n    // Gérer le menu contextuel\n    const handleContextMenu = (e, message) => {\n        e.preventDefault();\n        setShowContextMenu({\n            x: e.clientX,\n            y: e.clientY,\n            message: message\n        });\n    };\n    \n    // Démarrer l'édition d'un message\n    const startEditing = (message) => {\n        setEditingMessage(message.id);\n        setEditContent(message.message);\n        setShowContextMenu(null);\n    };\n    \n    // Annuler l'édition\n    const cancelEditing = () => {\n        setEditingMessage(null);\n        setEditContent('');\n    };\n    \n    // Confirmer l'édition\n    const confirmEdit = async () => {\n        if (editContent.trim() && editingMessage) {\n            await editMessage(editingMessage, editContent.trim());\n        }\n    };\n    \n    // Formater la date\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffTime = Math.abs(now - date);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        \n        if (diffDays === 1) {\n            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });\n        } else if (diffDays <= 7) {\n            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });\n        } else {\n            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });\n        }\n    };\n    \n    // Charger les données au montage du composant\n    useEffect(() => {\n        // Vérifier que l'utilisateur est connecté avant de charger les données\n        if (user && user.id) {\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n        } else {\n            console.warn('Utilisateur non connecté, chargement des données de test...');\n            // Charger quand même pour les tests\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n        }\n    }, [user]);\n    \n    // Actualiser périodiquement\n    useEffect(() => {\n        const interval = setInterval(() => {\n            loadConversations();\n            if (selectedConversation) {\n                loadMessages(selectedConversation.contact_id);\n            }\n        }, 30000); // Actualiser toutes les 30 secondes\n        \n        return () => clearInterval(interval);\n    }, [selectedConversation]);\n    \n    return (\n        <div className=\"messaging-system\">\n            <div className=\"messaging-header\">\n                <h1>💬 Messagerie</h1>\n                <div className=\"messaging-stats\">\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.total_messages || 0}</span>\n                        <span className=\"stat-label\">Messages</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.messages_non_lus || 0}</span>\n                        <span className=\"stat-label\">Non lus</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{conversations.length}</span>\n                        <span className=\"stat-label\">Conversations</span>\n                    </span>\n                </div>\n            </div>\n            \n            {error && (\n                <div className=\"error-message\">\n                    <span>❌ {error}</span>\n                    <button onClick={() => setError('')}>✕</button>\n                </div>\n            )}\n            \n            <div className=\"messaging-container\">\n                {/* Liste des conversations */}\n                <div className=\"conversations-panel\">\n                    <div className=\"conversations-header\">\n                        <h3>Conversations</h3>\n                        <button \n                            className=\"new-conversation-btn\"\n                            onClick={startNewConversation}\n                            title=\"Nouvelle conversation\"\n                        >\n                            ✏️\n                        </button>\n                    </div>\n                    \n                    <div className=\"conversations-list\">\n                        {loading && conversations.length === 0 ? (\n                            <div className=\"loading\">Chargement...</div>\n                        ) : conversations.length === 0 ? (\n                            <div className=\"no-conversations\">\n                                <p>Aucune conversation</p>\n                                <button onClick={startNewConversation}>\n                                    Démarrer une conversation\n                                </button>\n                            </div>\n                        ) : (\n                            conversations.map(conversation => (\n                                <div\n                                    key={conversation.contact_id}\n                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}\n                                    onClick={() => selectConversation(conversation)}\n                                >\n                                    <div className=\"conversation-avatar\">\n                                        {conversation.contact_nom.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"conversation-info\">\n                                        <div className=\"conversation-name\">\n                                            {conversation.contact_nom}\n                                            <span className=\"conversation-role\">\n                                                {conversation.contact_role}\n                                            </span>\n                                        </div>\n                                        <div className=\"conversation-preview\">\n                                            {conversation.dernier_message || 'Aucun message'}\n                                        </div>\n                                        <div className=\"conversation-meta\">\n                                            <span className=\"conversation-time\">\n                                                {formatDate(conversation.derniere_activite)}\n                                            </span>\n                                            {conversation.messages_non_lus > 0 && (\n                                                <span className=\"unread-badge\">\n                                                    {conversation.messages_non_lus}\n                                                </span>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            ))\n                        )}\n                    </div>\n                </div>\n                \n                {/* Zone de chat */}\n                <div className=\"chat-panel\">\n                    {showNewConversation ? (\n                        <div className=\"new-conversation\">\n                            <div className=\"new-conversation-header\">\n                                <h3>Nouvelle conversation</h3>\n                                <button onClick={() => setShowNewConversation(false)}>✕</button>\n                            </div>\n                            <div className=\"new-conversation-content\">\n                                <select\n                                    value={selectedUser}\n                                    onChange={(e) => setSelectedUser(e.target.value)}\n                                    className=\"user-select\"\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur...</option>\n                                    {authorizedUsers.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} ({user.role})\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n                    ) : selectedConversation ? (\n                        <div className=\"chat-header\">\n                            <div className=\"chat-contact-info\">\n                                <div className=\"chat-avatar\">\n                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}\n                                </div>\n                                <div>\n                                    <div className=\"chat-contact-name\">\n                                        {selectedConversation.contact_nom}\n                                    </div>\n                                    <div className=\"chat-contact-role\">\n                                        {selectedConversation.contact_role}\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    ) : (\n                        <div className=\"no-chat-selected\">\n                            <div className=\"no-chat-content\">\n                                <h3>💬 Messagerie</h3>\n                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>\n                                <button onClick={startNewConversation}>\n                                    Nouvelle conversation\n                                </button>\n                            </div>\n                        </div>\n                    )}\n                    \n                    {/* Messages */}\n                    {(selectedConversation || showNewConversation) && (\n                        <>\n                            <div className=\"messages-container\">\n                                {messages.map(message => {\n                                    const currentUserId = parseInt(user?.id);\n                                    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n                                    const messageType = isOwnMessage ? 'sent' : 'received';\n\n                                    return (\n                                        <div\n                                            key={message.id}\n                                            className={`message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`}\n                                            onContextMenu={(e) => handleContextMenu(e, message)}\n                                            data-sender-id={message.expediteur_id}\n                                            data-receiver-id={message.destinataire_id}\n                                        >\n                                            {/* 👤 Affichage du nom de l'expéditeur pour les messages reçus */}\n                                            {!isOwnMessage && (\n                                                <div className=\"message-sender\">\n                                                    {message.expediteur_nom || 'Utilisateur'}\n                                                </div>\n                                            )}\n\n                                            <div className={`message-content ${messageType}-content`}>\n                                                {editingMessage === message.id ? (\n                                                    <div className=\"message-edit\">\n                                                        <textarea\n                                                            value={editContent}\n                                                            onChange={(e) => setEditContent(e.target.value)}\n                                                            className=\"edit-textarea\"\n                                                            autoFocus\n                                                        />\n                                                        <div className=\"edit-actions\">\n                                                            <button onClick={confirmEdit} className=\"confirm-edit\">\n                                                                ✓\n                                                            </button>\n                                                            <button onClick={cancelEditing} className=\"cancel-edit\">\n                                                                ✕\n                                                            </button>\n                                                        </div>\n                                                    </div>\n                                                ) : (\n                                                    <>\n                                                        <div className=\"message-text\">\n                                                            {message.message}\n                                                            {message.modifie === '1' && (\n                                                                <span className=\"message-edited\" title={`Modifié le ${formatDate(message.date_modification)}`}>\n                                                                    (modifié)\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                        <div className={`message-time ${messageType}-time`}>\n                                                            {formatDate(message.date_envoi)}\n                                                            {isOwnMessage && (\n                                                                <span className=\"message-status\">\n                                                                    {message.lu === '1' ? '✓✓' : '✓'}\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                    </>\n                                                )}\n                                            </div>\n\n                                            {/* 🔒 Indicateur de confidentialité (debug) */}\n                                            {process.env.NODE_ENV === 'development' && (\n                                                <div className=\"message-debug\" title={`Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`}>\n                                                    🔒\n                                                </div>\n                                            )}\n                                        </div>\n                                    );\n                                })}\n                                <div ref={messagesEndRef} />\n                            </div>\n                            \n                            {/* Zone de saisie */}\n                            <div className=\"message-input-container\">\n                                <div className=\"message-input-wrapper\">\n                                    <textarea\n                                        value={newMessage}\n                                        onChange={(e) => setNewMessage(e.target.value)}\n                                        placeholder=\"Tapez votre message...\"\n                                        className=\"message-input\"\n                                        rows=\"1\"\n                                        onKeyDown={(e) => {\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                sendMessage();\n                                            }\n                                        }}\n                                    />\n                                    <button\n                                        onClick={sendMessage}\n                                        className=\"send-button\"\n                                        disabled={!newMessage.trim() || loading}\n                                    >\n                                        📤\n                                    </button>\n                                </div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            </div>\n            \n            {/* Menu contextuel */}\n            {showContextMenu && (\n                <div\n                    ref={contextMenuRef}\n                    className=\"context-menu\"\n                    style={{\n                        left: showContextMenu.x,\n                        top: showContextMenu.y\n                    }}\n                >\n                    {showContextMenu.message.can_modify === 1 && (\n                        <button onClick={() => startEditing(showContextMenu.message)}>\n                            ✏️ Modifier\n                        </button>\n                    )}\n                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>\n                        🗑️ Supprimer pour moi\n                    </button>\n                    {showContextMenu.message.can_delete_for_all === 1 && (\n                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>\n                            🗑️ Supprimer pour tous\n                        </button>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default MessagingSystem;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,+BAA+B;AAEtC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC;EAAK,CAAC,GAAGH,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACS,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,eAAe,EAAEC,kBAAkB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtC,MAAMiC,cAAc,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMgC,cAAc,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMiC,YAAY,GAAG,qDAAqD;;EAE1E;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIH,cAAc,CAACI,OAAO,EAAE;MACxBJ,cAAc,CAACI,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACjE;EACJ,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACZmC,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;;EAEd;EACAV,SAAS,CAAC,MAAM;IACZ,MAAMuC,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIP,cAAc,CAACG,OAAO,IAAI,CAACH,cAAc,CAACG,OAAO,CAACK,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAC1Eb,kBAAkB,CAAC,IAAI,CAAC;MAC5B;IACJ,CAAC;IAEDc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IACpE,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa;MAE5D,MAAMC,MAAM,GAAG;QACXL,MAAM;QACNM,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUJ,KAAK;QACpC;MACJ,CAAC;MAED,IAAID,IAAI,KAAKD,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,QAAQ,CAAC,EAAE;QACxEK,MAAM,CAACE,IAAI,GAAGC,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;MACtC;MAEA,MAAMS,GAAG,GAAG,GAAGxB,YAAY,WAAWa,QAAQ,EAAE;MAChDY,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;QAAEF,GAAG;QAAEV,MAAM;QAAED,QAAQ;QAAEG;MAAM,CAAC,CAAC;MAE7D,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,EAAEL,MAAM,CAAC;MACzCM,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAEzE,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpCP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,MAAM,CAAC;MAEzC,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAC3C,KAAK,IAAI,YAAY,CAAC;MACjD;MAEA,OAAO2C,MAAM;IACjB,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACZqC,OAAO,CAACrC,KAAK,CAAC,sBAAsB,EAAE;QAClCyB,QAAQ;QACRC,MAAM;QACN1B,KAAK,EAAEA,KAAK,CAAC+C,OAAO;QACpBC,KAAK,EAAEhD,KAAK,CAACgD;MACjB,CAAC,CAAC;MACF,MAAMhD,KAAK;IACf;EACJ,CAAC;;EAED;EACA,MAAMiD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAC,YAAA;MACAnD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4C,MAAM,GAAG,MAAMnB,cAAc,CAAC,eAAe,CAAC;;MAEpD;MACA,MAAM2B,aAAa,GAAGC,QAAQ,CAACrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE,CAAC;MACxC,IAAI,CAACF,aAAa,EAAE;QAChB,MAAM,IAAIL,KAAK,CAAC,2BAA2B,CAAC;MAChD;;MAEA;MACA,MAAMQ,mBAAmB,GAAG,CAACX,MAAM,CAAChB,IAAI,IAAI,EAAE,EAAE4B,MAAM,CAACC,YAAY,IAAI;QACnE;QACA,MAAMC,SAAS,GAAGL,QAAQ,CAACI,YAAY,CAACE,UAAU,CAAC;QACnD,OAAOD,SAAS,IAAIA,SAAS,KAAKN,aAAa,IAAIM,SAAS,GAAG,CAAC;MACpE,CAAC,CAAC;MAEFpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACjDqB,cAAc,EAAE,EAAAT,YAAA,GAAAP,MAAM,CAAChB,IAAI,cAAAuB,YAAA,uBAAXA,YAAA,CAAaU,MAAM,KAAI,CAAC;QACxCC,eAAe,EAAEP,mBAAmB,CAACM,MAAM;QAC3CE,OAAO,EAAEX;MACb,CAAC,CAAC;MAEFlE,gBAAgB,CAACqE,mBAAmB,CAAC;IACzC,CAAC,CAAC,OAAOtD,KAAK,EAAE;MACZC,QAAQ,CAAC,2CAA2C,GAAGD,KAAK,CAAC+C,OAAO,CAAC;MACrEV,OAAO,CAACrC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMgE,YAAY,GAAG,MAAON,SAAS,IAAK;IACtC,IAAI;MAAA,IAAAO,aAAA;MACAjE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4C,MAAM,GAAG,MAAMnB,cAAc,CAAC,uBAAuBiC,SAAS,EAAE,CAAC;;MAEvE;MACA,MAAMN,aAAa,GAAGC,QAAQ,CAACrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE,CAAC;MACxC,IAAI,CAACF,aAAa,EAAE;QAChB,MAAM,IAAIL,KAAK,CAAC,2BAA2B,CAAC;MAChD;;MAEA;MACA,MAAMmB,cAAc,GAAG,CAACtB,MAAM,CAAChB,IAAI,IAAI,EAAE,EAAE4B,MAAM,CAACR,OAAO,IAAI;QACzD,MAAMmB,YAAY,GAAGd,QAAQ,CAACL,OAAO,CAACoB,aAAa,CAAC;QACpD,MAAMC,cAAc,GAAGhB,QAAQ,CAACL,OAAO,CAACsB,eAAe,CAAC;;QAExD;QACA,OAAQH,YAAY,KAAKf,aAAa,IAAIiB,cAAc,KAAKjB,aAAa;MAC9E,CAAC,CAAC,CAACmB,GAAG,CAACvB,OAAO,IAAI;QACd;QACA,MAAMmB,YAAY,GAAGd,QAAQ,CAACL,OAAO,CAACoB,aAAa,CAAC;QACpD,MAAMhB,aAAa,GAAGC,QAAQ,CAACrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE,CAAC;QAExC,OAAO;UACH,GAAGN,OAAO;UACVwB,YAAY,EAAEL,YAAY,KAAKf,aAAa,GAAG,MAAM,GAAG,UAAU;UAClEqB,cAAc,EAAEN,YAAY,KAAKf;QACrC,CAAC;MACL,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC1CqB,cAAc,EAAE,EAAAK,aAAA,GAAArB,MAAM,CAAChB,IAAI,cAAAqC,aAAA,uBAAXA,aAAA,CAAaJ,MAAM,KAAI,CAAC;QACxCC,eAAe,EAAEI,cAAc,CAACL,MAAM;QACtCE,OAAO,EAAEX,aAAa;QACtBO,UAAU,EAAED;MAChB,CAAC,CAAC;MAEFpE,WAAW,CAAC4E,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOjE,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAAC+C,OAAO,CAAC;MAChEV,OAAO,CAACrC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAM0E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,MAAM9B,MAAM,GAAG,MAAMnB,cAAc,CAAC,OAAO,CAAC;MAC5C/B,kBAAkB,CAACkD,MAAM,CAAChB,IAAI,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACZC,QAAQ,CAAC,0CAA0C,GAAGD,KAAK,CAAC+C,OAAO,CAAC;IACxE;EACJ,CAAC;;EAED;EACA,MAAM2B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACA,MAAM/B,MAAM,GAAG,MAAMnB,cAAc,CAAC,OAAO,CAAC;MAC5Cf,QAAQ,CAACkC,MAAM,CAAChB,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACZqC,OAAO,CAACrC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;;EAED;EACA,MAAM2E,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAACrF,UAAU,CAACsF,IAAI,CAAC,CAAC,EAAE;IAExB,IAAI;MACA,MAAMR,cAAc,GAAGlF,oBAAoB,GAAGA,oBAAoB,CAACwE,UAAU,GAAG9D,YAAY;MAE5F,IAAI,CAACwE,cAAc,EAAE;QACjBnE,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACJ;MAEA,MAAMuB,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;QACjC6C,eAAe,EAAED,cAAc;QAC/BrB,OAAO,EAAEzD,UAAU,CAACsF,IAAI,CAAC;MAC7B,CAAC,CAAC;MAEFrF,aAAa,CAAC,EAAE,CAAC;MACjBI,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA,MAAMsD,iBAAiB,CAAC,CAAC;MACzB,IAAI/D,oBAAoB,EAAE;QACtB,MAAM6E,YAAY,CAAC7E,oBAAoB,CAACwE,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACZC,QAAQ,CAAC,oCAAoC,GAAGD,KAAK,CAAC+C,OAAO,CAAC;IAClE;EACJ,CAAC;;EAED;EACA,MAAM8B,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,KAAK;IACjD,IAAI;MACA,MAAMvD,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE;QAChCwD,UAAU,EAAEF,SAAS;QACrB/B,OAAO,EAAEgC;MACb,CAAC,CAAC;MAEF5E,iBAAiB,CAAC,IAAI,CAAC;MACvBE,cAAc,CAAC,EAAE,CAAC;;MAElB;MACA,IAAInB,oBAAoB,EAAE;QACtB,MAAM6E,YAAY,CAAC7E,oBAAoB,CAACwE,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACZC,QAAQ,CAAC,qCAAqC,GAAGD,KAAK,CAAC+C,OAAO,CAAC;IACnE;EACJ,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAG,MAAAA,CAAOH,SAAS,EAAEI,UAAU,GAAG,QAAQ,KAAK;IAC9D,IAAI;MACA,MAAM1D,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACrCwD,UAAU,EAAEF,SAAS;QACrBK,WAAW,EAAED;MACjB,CAAC,CAAC;MAEF3E,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIrB,oBAAoB,EAAE;QACtB,MAAM6E,YAAY,CAAC7E,oBAAoB,CAACwE,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO1D,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAAC+C,OAAO,CAAC;IACpE;EACJ,CAAC;;EAED;EACA,MAAMqC,kBAAkB,GAAG,MAAO5B,YAAY,IAAK;IAC/CrE,uBAAuB,CAACqE,YAAY,CAAC;IACrC7D,sBAAsB,CAAC,KAAK,CAAC;IAC7B,MAAMoE,YAAY,CAACP,YAAY,CAACE,UAAU,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM2B,oBAAoB,GAAGA,CAAA,KAAM;IAC/BlG,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,WAAW,CAAC,EAAE,CAAC;IACfM,sBAAsB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAM2F,iBAAiB,GAAGA,CAACC,CAAC,EAAExC,OAAO,KAAK;IACtCwC,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBjF,kBAAkB,CAAC;MACfkF,CAAC,EAAEF,CAAC,CAACG,OAAO;MACZC,CAAC,EAAEJ,CAAC,CAACK,OAAO;MACZ7C,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM8C,YAAY,GAAI9C,OAAO,IAAK;IAC9B5C,iBAAiB,CAAC4C,OAAO,CAACM,EAAE,CAAC;IAC7BhD,cAAc,CAAC0C,OAAO,CAACA,OAAO,CAAC;IAC/BxC,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMuF,aAAa,GAAGA,CAAA,KAAM;IACxB3F,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAM0F,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI3F,WAAW,CAACwE,IAAI,CAAC,CAAC,IAAI1E,cAAc,EAAE;MACtC,MAAM2E,WAAW,CAAC3E,cAAc,EAAEE,WAAW,CAACwE,IAAI,CAAC,CAAC,CAAC;IACzD;EACJ,CAAC;;EAED;EACA,MAAMoB,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACnF,CAAC,MAAM,IAAIJ,QAAQ,IAAI,CAAC,EAAE;MACtB,OAAON,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEH,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrG,CAAC,MAAM;MACH,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEE,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEL,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrH;EACJ,CAAC;;EAED;EACAlI,SAAS,CAAC,MAAM;IACZ;IACA,IAAIK,IAAI,IAAIA,IAAI,CAACsE,EAAE,EAAE;MACjBJ,iBAAiB,CAAC,CAAC;MACnBwB,mBAAmB,CAAC,CAAC;MACrBC,SAAS,CAAC,CAAC;IACf,CAAC,MAAM;MACHrC,OAAO,CAAC4E,IAAI,CAAC,6DAA6D,CAAC;MAC3E;MACAhE,iBAAiB,CAAC,CAAC;MACnBwB,mBAAmB,CAAC,CAAC;MACrBC,SAAS,CAAC,CAAC;IACf;EACJ,CAAC,EAAE,CAAC3F,IAAI,CAAC,CAAC;;EAEV;EACAL,SAAS,CAAC,MAAM;IACZ,MAAMwI,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BlE,iBAAiB,CAAC,CAAC;MACnB,IAAI/D,oBAAoB,EAAE;QACtB6E,YAAY,CAAC7E,oBAAoB,CAACwE,UAAU,CAAC;MACjD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM0D,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAAChI,oBAAoB,CAAC,CAAC;EAE1B,oBACIV,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtBpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEpH,KAAK,CAACqH,cAAc,IAAI,CAAQ,CAAC,eAChErJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CACzC,CAAC,eACPpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEpH,KAAK,CAACsH,gBAAgB,IAAI,CAAQ,CAAC,eAClEtJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAa,CACxC,CAAC,eACPpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE5I,aAAa,CAAC4E,MAAa,CAAC,eAC3DpF,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAmB,CAC9C,CACL,CACJ,CAAC,EAEL5H,KAAK,iBACFxB,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAE,EAAC5H,KAAY,CAAC,eACtBxB,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAM9H,QAAQ,CAAC,EAAE,CAAE;IAAAsH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC7C,CACR,eAEDpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhCpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChCpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,eAAiB,CAAC,eACtBpJ,KAAA,CAAA6I,aAAA;IACIC,SAAS,EAAC,sBAAsB;IAChCS,OAAO,EAAE1C,oBAAqB;IAC9B2C,KAAK,EAAC,uBAAuB;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CAAC,eAENpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B9H,OAAO,IAAId,aAAa,CAAC4E,MAAM,KAAK,CAAC,gBAClCpF,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAkB,CAAC,GAC5C5I,aAAa,CAAC4E,MAAM,KAAK,CAAC,gBAC1BpF,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qBAAsB,CAAC,eAC1BpJ,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAE1C,oBAAqB;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAE/B,CACP,CAAC,GAEN5I,aAAa,CAACsF,GAAG,CAACd,YAAY,iBAC1BhF,KAAA,CAAA6I,aAAA;IACIY,GAAG,EAAEzE,YAAY,CAACE,UAAW;IAC7B4D,SAAS,EAAE,qBAAqB,CAAApI,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAEwE,UAAU,MAAKF,YAAY,CAACE,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC/GqE,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC5B,YAAY,CAAE;IAAA+D,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhDpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BpE,YAAY,CAAC0E,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC/C,CAAC,eACN5J,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BpE,YAAY,CAAC0E,WAAW,eACzB1J,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BpE,YAAY,CAAC6E,YACZ,CACL,CAAC,eACN7J,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCpE,YAAY,CAAC8E,eAAe,IAAI,eAChC,CAAC,eACN9J,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BpJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B5B,UAAU,CAACxC,YAAY,CAAC+E,iBAAiB,CACxC,CAAC,EACN/E,YAAY,CAACsE,gBAAgB,GAAG,CAAC,iBAC9BtJ,KAAA,CAAA6I,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBpE,YAAY,CAACsE,gBACZ,CAET,CACJ,CACJ,CACR,CAEJ,CACJ,CAAC,eAGNtJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtBlI,mBAAmB,gBAChBlB,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpCpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAyB,CAAC,eAC9BpJ,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAMpI,sBAAsB,CAAC,KAAK,CAAE;IAAA4H,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC9D,CAAC,eACNpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrCpJ,KAAA,CAAA6I,aAAA;IACImB,KAAK,EAAE5I,YAAa;IACpB6I,QAAQ,EAAGlD,CAAC,IAAK1F,eAAe,CAAC0F,CAAC,CAACnE,MAAM,CAACoH,KAAK,CAAE;IACjDlB,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvBpJ,KAAA,CAAA6I,aAAA;IAAQmB,KAAK,EAAC,EAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAsC,CAAC,EACvDpI,eAAe,CAAC8E,GAAG,CAACvF,IAAI,iBACrBP,KAAA,CAAA6I,aAAA;IAAQY,GAAG,EAAElJ,IAAI,CAACsE,EAAG;IAACmF,KAAK,EAAEzJ,IAAI,CAACsE,EAAG;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC7I,IAAI,CAAC2J,GAAG,EAAC,IAAE,EAAC3J,IAAI,CAAC4J,IAAI,EAAC,GACnB,CACX,CACG,CACP,CACJ,CAAC,GACNzJ,oBAAoB,gBACpBV,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9BpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvB1I,oBAAoB,CAACgJ,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACvD,CAAC,eACN5J,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B1I,oBAAoB,CAACgJ,WACrB,CAAC,eACN1J,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7B1I,oBAAoB,CAACmJ,YACrB,CACJ,CACJ,CACJ,CAAC,gBAEN7J,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtBpJ,KAAA,CAAA6I,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,iEAA4D,CAAC,eAChEpJ,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAE1C,oBAAqB;IAAAkC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAE/B,CACP,CACJ,CACR,EAGA,CAAC1I,oBAAoB,IAAIQ,mBAAmB,kBACzClB,KAAA,CAAA6I,aAAA,CAAA7I,KAAA,CAAAoK,QAAA,qBACIpK,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BxI,QAAQ,CAACkF,GAAG,CAACvB,OAAO,IAAI;IACrB,MAAMI,aAAa,GAAGC,QAAQ,CAACrE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,EAAE,CAAC;IACxC,MAAMwF,YAAY,GAAGzF,QAAQ,CAACL,OAAO,CAACoB,aAAa,CAAC,KAAKhB,aAAa;IACtE,MAAM2F,WAAW,GAAGD,YAAY,GAAG,MAAM,GAAG,UAAU;IAEtD,oBACIrK,KAAA,CAAA6I,aAAA;MACIY,GAAG,EAAElF,OAAO,CAACM,EAAG;MAChBiE,SAAS,EAAE,WAAWwB,WAAW,IAAID,YAAY,GAAG,aAAa,GAAG,eAAe,EAAG;MACtFE,aAAa,EAAGxD,CAAC,IAAKD,iBAAiB,CAACC,CAAC,EAAExC,OAAO,CAAE;MACpD,kBAAgBA,OAAO,CAACoB,aAAc;MACtC,oBAAkBpB,OAAO,CAACsB,eAAgB;MAAAkD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAGzC,CAACiB,YAAY,iBACVrK,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1B7E,OAAO,CAACiG,cAAc,IAAI,aAC1B,CACR,eAEDxK,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAE,mBAAmBwB,WAAW,UAAW;MAAAvB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACpD1H,cAAc,KAAK6C,OAAO,CAACM,EAAE,gBAC1B7E,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBpJ,KAAA,CAAA6I,aAAA;MACImB,KAAK,EAAEpI,WAAY;MACnBqI,QAAQ,EAAGlD,CAAC,IAAKlF,cAAc,CAACkF,CAAC,CAACnE,MAAM,CAACoH,KAAK,CAAE;MAChDlB,SAAS,EAAC,eAAe;MACzB2B,SAAS;MAAA1B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACZ,CAAC,eACFpJ,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzBpJ,KAAA,CAAA6I,aAAA;MAAQU,OAAO,EAAEhC,WAAY;MAACuB,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAE/C,CAAC,eACTpJ,KAAA,CAAA6I,aAAA;MAAQU,OAAO,EAAEjC,aAAc;MAACwB,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAEhD,CACP,CACJ,CAAC,gBAENpJ,KAAA,CAAA6I,aAAA,CAAA7I,KAAA,CAAAoK,QAAA,qBACIpK,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACxB7E,OAAO,CAACA,OAAO,EACfA,OAAO,CAACmG,OAAO,KAAK,GAAG,iBACpB1K,KAAA,CAAA6I,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAACU,KAAK,EAAE,cAAchC,UAAU,CAACjD,OAAO,CAACoG,iBAAiB,CAAC,EAAG;MAAA5B,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEzF,CAET,CAAC,eACNpJ,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAE,gBAAgBwB,WAAW,OAAQ;MAAAvB,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9C5B,UAAU,CAACjD,OAAO,CAACqG,UAAU,CAAC,EAC9BP,YAAY,iBACTrK,KAAA,CAAA6I,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC3B7E,OAAO,CAACsG,EAAE,KAAK,GAAG,GAAG,IAAI,GAAG,GAC3B,CAET,CACP,CAEL,CAAC,EAGLC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACnChL,KAAA,CAAA6I,aAAA;MAAKC,SAAS,EAAC,eAAe;MAACU,KAAK,EAAE,eAAejF,OAAO,CAACoB,aAAa,mBAAmBpB,OAAO,CAACsB,eAAe,EAAG;MAAAkD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEnH,CAER,CAAC;EAEd,CAAC,CAAC,eACFpJ,KAAA,CAAA6I,aAAA;IAAKoC,GAAG,EAAE/I,cAAe;IAAA6G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC1B,CAAC,eAGNpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpCpJ,KAAA,CAAA6I,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClCpJ,KAAA,CAAA6I,aAAA;IACImB,KAAK,EAAElJ,UAAW;IAClBmJ,QAAQ,EAAGlD,CAAC,IAAKhG,aAAa,CAACgG,CAAC,CAACnE,MAAM,CAACoH,KAAK,CAAE;IAC/CkB,WAAW,EAAC,wBAAwB;IACpCpC,SAAS,EAAC,eAAe;IACzBqC,IAAI,EAAC,GAAG;IACRC,SAAS,EAAGrE,CAAC,IAAK;MACd,IAAIA,CAAC,CAAC0C,GAAG,KAAK,OAAO,IAAI,CAAC1C,CAAC,CAACsE,QAAQ,EAAE;QAClCtE,CAAC,CAACC,cAAc,CAAC,CAAC;QAClBb,WAAW,CAAC,CAAC;MACjB;IACJ,CAAE;IAAA4C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACFpJ,KAAA,CAAA6I,aAAA;IACIU,OAAO,EAAEpD,WAAY;IACrB2C,SAAS,EAAC,aAAa;IACvBwC,QAAQ,EAAE,CAACxK,UAAU,CAACsF,IAAI,CAAC,CAAC,IAAI9E,OAAQ;IAAAyH,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3C,cAEO,CACP,CACJ,CACP,CAEL,CACJ,CAAC,EAGLtH,eAAe,iBACZ9B,KAAA,CAAA6I,aAAA;IACIoC,GAAG,EAAE9I,cAAe;IACpB2G,SAAS,EAAC,cAAc;IACxByC,KAAK,EAAE;MACHC,IAAI,EAAE1J,eAAe,CAACmF,CAAC;MACvBwE,GAAG,EAAE3J,eAAe,CAACqF;IACzB,CAAE;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDtH,eAAe,CAACyC,OAAO,CAACmH,UAAU,KAAK,CAAC,iBACrC1L,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAMlC,YAAY,CAACvF,eAAe,CAACyC,OAAO,CAAE;IAAAwE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAEtD,CACX,eACDpJ,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAM9C,aAAa,CAAC3E,eAAe,CAACyC,OAAO,CAACM,EAAE,EAAE,QAAQ,CAAE;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uCAEpE,CAAC,EACRtH,eAAe,CAACyC,OAAO,CAACoH,kBAAkB,KAAK,CAAC,iBAC7C3L,KAAA,CAAA6I,aAAA;IAAQU,OAAO,EAAEA,CAAA,KAAM9C,aAAa,CAAC3E,eAAe,CAACyC,OAAO,CAACM,EAAE,EAAE,cAAc,CAAE;IAAAkE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wCAE1E,CAEX,CAER,CAAC;AAEd,CAAC;AAED,eAAe9I,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}