/* ===== MESSAGERIE SYSTÈME - STYLE WHATSAPP/MESSENGER MODERNE ===== */

.messaging-system {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
}

/* ===== CONTENEUR PRINCIPAL ===== */
.messaging-container {
    display: flex;
    height: 100vh;
    background: #f0f2f5;
    overflow: hidden;
}

/* ===== SIDEBAR CONVERSATIONS ===== */
.conversations-sidebar {
    width: 350px;
    background: #ffffff;
    border-right: 1px solid #e4e6ea;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.sidebar-header {
    padding: 20px;
    background: #ffffff;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    margin: 0;
    color: #1c1e21;
    font-size: 1.5rem;
    font-weight: 600;
}

.new-conversation-btn {
    background: #1877f2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.2s ease;
}

.new-conversation-btn:hover {
    background: #166fe5;
    transform: scale(1.05);
}

.conversations-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* ===== ÉLÉMENTS DE CONVERSATION ===== */
.conversation-item {
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 1px solid #f0f2f5;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: background-color 0.2s ease;
    position: relative;
}

.conversation-item:hover {
    background: #f0f2f5;
}

.conversation-item.active {
    background: #e7f3ff;
    border-right: 3px solid #1877f2;
}

.conversation-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-weight: 600;
    color: #1c1e21;
    margin-bottom: 4px;
    font-size: 0.95rem;
}

.conversation-preview {
    color: #65676b;
    font-size: 0.85rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.unread-badge {
    background: #1877f2;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    flex-shrink: 0;
}

.empty-conversations {
    padding: 40px 20px;
    text-align: center;
    color: #65676b;
}

.start-conversation-btn {
    background: #1877f2;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 15px;
    transition: background-color 0.2s ease;
}

.start-conversation-btn:hover {
    background: #166fe5;
}

/* ===== ZONE DE CHAT PRINCIPALE ===== */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    height: 100vh;
}

.chat-header {
    padding: 15px 20px;
    background: #ffffff;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
}

.chat-user-name {
    font-weight: 600;
    color: #1c1e21;
    font-size: 1rem;
}

.no-conversation-selected {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.welcome-message {
    text-align: center;
    color: #65676b;
}

.welcome-message h3 {
    margin: 0 0 10px 0;
    color: #1c1e21;
    font-size: 1.5rem;
}

.welcome-message p {
    margin: 0;
    font-size: 1rem;
}

/* ===== CONTENEUR DE MESSAGES ===== */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* ===== MESSAGES STYLE WHATSAPP ===== */
.message-wrapper {
    display: flex;
    margin-bottom: 8px;
}

.message-wrapper.own-message {
    justify-content: flex-end;
}

.message-wrapper.other-message {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    cursor: pointer;
    transition: all 0.2s ease;
}

.message-bubble.sent {
    background: linear-gradient(135deg, #1877f2 0%, #0084ff 100%);
    color: white;
    border-bottom-right-radius: 4px;
    margin-left: auto;
}

.message-bubble.received {
    background: #ffffff;
    color: #1c1e21;
    border: 1px solid #e4e6ea;
    border-bottom-left-radius: 4px;
    margin-right: auto;
}

.message-bubble:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-content {
    font-size: 0.95rem;
    line-height: 1.4;
    margin-bottom: 4px;
}

.message-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.75rem;
    opacity: 0.8;
    margin-top: 4px;
}

.message-bubble.sent .message-meta {
    color: rgba(255, 255, 255, 0.8);
}

.message-bubble.received .message-meta {
    color: #65676b;
}

.message-time {
    font-size: 0.75rem;
}

.message-edited {
    font-style: italic;
    font-size: 0.7rem;
}

.message-status {
    font-size: 0.8rem;
}

.message-status.read {
    color: #4fc3f7;
}

.message-status.unread {
    color: rgba(255, 255, 255, 0.6);
}

/* ===== ÉDITION DE MESSAGES ===== */
.edit-message-container {
    width: 100%;
}

.edit-message-input {
    width: 100%;
    min-height: 60px;
    padding: 8px 12px;
    border: 2px solid #1877f2;
    border-radius: 12px;
    font-size: 0.95rem;
    font-family: inherit;
    resize: vertical;
    background: rgba(255, 255, 255, 0.95);
    color: #1c1e21;
}

.edit-message-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
}

.save-edit-btn, .cancel-edit-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 16px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.save-edit-btn {
    background: #42b883;
    color: white;
}

.save-edit-btn:hover {
    background: #369870;
}

.cancel-edit-btn {
    background: #f56565;
    color: white;
}

.cancel-edit-btn:hover {
    background: #e53e3e;
}

/* ===== ZONE DE SAISIE ===== */
.message-input-container {
    padding: 15px 20px;
    background: #ffffff;
    border-top: 1px solid #e4e6ea;
}

.message-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: #f0f2f5;
    border-radius: 20px;
    padding: 8px 12px;
}

.message-input {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    font-size: 0.95rem;
    font-family: inherit;
    line-height: 1.4;
    max-height: 120px;
    min-height: 20px;
    outline: none;
    color: #1c1e21;
}

.message-input::placeholder {
    color: #65676b;
}

.send-button {
    background: #1877f2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: #166fe5;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #bcc0c4;
    cursor: not-allowed;
    transform: none;
}

/* ===== MENU CONTEXTUEL ===== */
.context-menu {
    background: #ffffff;
    border: 1px solid #e4e6ea;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    min-width: 180px;
    z-index: 1000;
}

.context-menu-item {
    display: block;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    font-size: 0.9rem;
    color: #1c1e21;
    transition: background-color 0.2s ease;
}

.context-menu-item:hover {
    background: #f0f2f5;
}

.context-menu-item.danger {
    color: #f56565;
}

.context-menu-item.danger:hover {
    background: #fed7d7;
}

/* ===== MODALS ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #1c1e21;
    font-size: 1.25rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #65676b;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f0f2f5;
    color: #1c1e21;
}

.modal-body {
    padding: 24px;
}

.modal-body label {
    display: block;
    margin-bottom: 8px;
    color: #1c1e21;
    font-weight: 500;
}

.user-select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e4e6ea;
    border-radius: 8px;
    font-size: 0.95rem;
    background: #ffffff;
    color: #1c1e21;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e6ea;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #1877f2;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #166fe5;
}

.btn-primary:disabled {
    background: #bcc0c4;
    cursor: not-allowed;
}

.btn-secondary {
    background: #e4e6ea;
    color: #1c1e21;
}

.btn-secondary:hover {
    background: #d8dadf;
}

/* ===== ÉTATS DE CHARGEMENT ET ERREURS ===== */
.loading-container, .auth-error-container, .access-denied-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-container {
    flex-direction: column;
    gap: 20px;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-error-content, .access-denied-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 400px;
}

.auth-error-content h2, .access-denied-content h2 {
    margin: 0 0 16px 0;
    color: #1c1e21;
    font-size: 1.5rem;
}

.auth-error-content p, .access-denied-content p {
    margin: 0 0 24px 0;
    color: #65676b;
    line-height: 1.5;
}

/* ===== ANIMATIONS ET TRANSITIONS ===== */
.message-bubble {
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.conversation-item {
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .messaging-container {
        flex-direction: column;
    }

    .conversations-sidebar {
        width: 100%;
        height: 40vh;
        border-right: none;
        border-bottom: 1px solid #e4e6ea;
    }

    .chat-area {
        height: 60vh;
    }

    .message-bubble {
        max-width: 85%;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }
}

@media (max-width: 480px) {
    .conversations-sidebar {
        height: 35vh;
    }

    .chat-area {
        height: 65vh;
    }

    .sidebar-header, .chat-header {
        padding: 12px 16px;
    }

    .messages-container {
        padding: 12px;
    }

    .message-input-container {
        padding: 12px 16px;
    }
}

/* ===== SCROLLBAR PERSONNALISÉE ===== */
.conversations-list::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.conversations-list::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
    background: #f0f2f5;
}

.conversations-list::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
    background: #bcc0c4;
    border-radius: 3px;
}

.conversations-list::-webkit-scrollbar-thumb:hover,
.messages-container::-webkit-scrollbar-thumb:hover {
    background: #8a8d91;
}

/* ===== MESSAGES D'ERREUR ===== */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 5px solid #dc3545;
}

.error-message button {
    background: none;
    border: none;
    color: #721c24;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== CONTAINER PRINCIPAL ===== */
.messaging-container {
    flex: 1;
    display: flex;
    background: white;
    overflow: hidden;
}

/* ===== PANNEAU CONVERSATIONS ===== */
.conversations-panel {
    width: 350px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.conversations-header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversations-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.new-conversation-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.new-conversation-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

/* ===== LISTE CONVERSATIONS ===== */
.conversations-list {
    flex: 1;
    overflow-y: auto;
}

.conversation-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;
}

.conversation-item:hover {
    background: #f0f4ff;
}

.conversation-item.active {
    background: #667eea;
    color: white;
}

.conversation-item.active .conversation-name,
.conversation-item.active .conversation-preview,
.conversation-item.active .conversation-time {
    color: white;
}

.conversation-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    margin-right: 15px;
    flex-shrink: 0;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversation-role {
    font-size: 0.7rem;
    background: #e9ecef;
    color: #666;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.conversation-item.active .conversation-role {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.conversation-preview {
    color: #666;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
}

.conversation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversation-time {
    font-size: 0.8rem;
    color: #999;
}

.unread-badge {
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    font-weight: 600;
}

/* ===== ÉTATS VIDES ===== */
.no-conversations, .no-chat-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #666;
}

.no-conversations p, .no-chat-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.no-conversations button, .no-chat-content button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.no-conversations button:hover, .no-chat-content button:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* ===== PANNEAU CHAT ===== */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

/* ===== NOUVELLE CONVERSATION ===== */
.new-conversation {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.new-conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.new-conversation-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.new-conversation-header button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.user-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.user-select:focus {
    outline: none;
    border-color: #667eea;
}

/* ===== HEADER CHAT ===== */
.chat-header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.chat-contact-info {
    display: flex;
    align-items: center;
}

.chat-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
    margin-right: 15px;
}

.chat-contact-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.chat-contact-role {
    color: #666;
    font-size: 0.9rem;
    text-transform: capitalize;
}

/* ===== CONTAINER MESSAGES ===== */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    background-image: 
        radial-gradient(circle at 25px 25px, rgba(255,255,255,0.2) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 1px, transparent 0);
    background-size: 100px 100px;
}

/* ===== MESSAGES - STYLE WHATSAPP AMÉLIORÉ ===== */
.message {
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    max-width: 70%;
    position: relative;
    animation: messageSlideIn 0.3s ease-out;
}

/* 📤 MESSAGES ENVOYÉS - Alignés à droite avec style bleu */
.message.sent,
.message.own-message {
    margin-left: auto;
    margin-right: 10px;
    align-items: flex-end;
}

/* 📥 MESSAGES REÇUS - Alignés à gauche avec style gris */
.message.received,
.message.other-message {
    margin-right: auto;
    margin-left: 10px;
    align-items: flex-start;
}

/* 👤 NOM DE L'EXPÉDITEUR pour les messages reçus */
.message-sender {
    font-size: 0.75rem;
    color: #667eea;
    font-weight: 600;
    margin-bottom: 4px;
    margin-left: 12px;
    text-transform: capitalize;
}

/* 💬 CONTENU DU MESSAGE */
.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 100%;
    word-wrap: break-word;
    word-break: break-word;
    transition: all 0.2s ease;
}

/* 📤 STYLE MESSAGES ENVOYÉS - Bleu gradient */
.message.sent .message-content,
.message.own-message .message-content,
.sent-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
    margin-left: auto;
}

/* 📥 STYLE MESSAGES REÇUS - Gris clair */
.message.received .message-content,
.message.other-message .message-content,
.received-content {
    background: #f1f3f4;
    color: #333;
    border-bottom-left-radius: 5px;
    margin-right: auto;
}

/* 🎯 HOVER EFFECTS */
.message-content:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 📱 ANIMATION D'APPARITION */
@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-text {
    line-height: 1.4;
    margin-bottom: 5px;
}

.message-edited {
    font-size: 0.7rem;
    opacity: 0.7;
    font-style: italic;
    margin-left: 8px;
}

/* ⏰ TEMPS DU MESSAGE - Style amélioré */
.message-time {
    font-size: 0.7rem;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* ⏰ TEMPS MESSAGES ENVOYÉS */
.message-time.sent-time,
.message.sent .message-time,
.message.own-message .message-time {
    color: rgba(255, 255, 255, 0.8);
    justify-content: flex-end;
    text-align: right;
    opacity: 0.9;
}

/* ⏰ TEMPS MESSAGES REÇUS */
.message-time.received-time,
.message.received .message-time,
.message.other-message .message-time {
    color: #666;
    justify-content: flex-start;
    text-align: left;
    opacity: 0.8;
}

.message-status {
    margin-left: 8px;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
}

/* 🔒 INDICATEUR DE DEBUG (développement uniquement) */
.message-debug {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: help;
    z-index: 10;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.message-debug:hover {
    opacity: 1;
}

/* ===== ÉDITION MESSAGE ===== */
.message-edit {
    width: 100%;
}

.edit-textarea {
    width: 100%;
    min-height: 60px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: inherit;
    resize: vertical;
    border-radius: 8px;
    padding: 8px;
    font-family: inherit;
    font-size: inherit;
}

.edit-textarea:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
}

.edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
}

.confirm-edit, .cancel-edit {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: inherit;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.confirm-edit:hover, .cancel-edit:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== ZONE SAISIE ===== */
.message-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.message-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.message-input-wrapper:focus-within {
    border-color: #667eea;
}

.message-input {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.4;
    max-height: 120px;
    min-height: 20px;
    padding: 8px 0;
}

.message-input:focus {
    outline: none;
}

.send-button {
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: #5a67d8;
    transform: scale(1.1);
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* ===== MENU CONTEXTUEL ===== */
.context-menu {
    position: fixed;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.context-menu button {
    display: block;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.context-menu button:last-child {
    border-bottom: none;
}

.context-menu button:hover {
    background: #f8f9fa;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .messaging-container {
        flex-direction: column;
    }
    
    .conversations-panel {
        width: 100%;
        height: 40%;
    }
    
    .chat-panel {
        height: 60%;
    }
    
    .messaging-header {
        padding: 15px 20px;
    }
    
    .messaging-header h1 {
        font-size: 1.5rem;
    }
    
    .messaging-stats {
        gap: 15px;
    }
    
    .stat-number {
        font-size: 1.4rem;
    }
    
    .message {
        max-width: 85%;
    }
}

@media (max-width: 480px) {
    .conversations-panel {
        height: 50%;
    }
    
    .chat-panel {
        height: 50%;
    }
    
    .messaging-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .messaging-stats {
        justify-content: center;
    }
}

/* ===== AMÉLIORATIONS AUTHENTIFICATION ===== */

/* 👤 INFORMATIONS UTILISATEUR */
.user-info {
    display: flex;
    align-items: center;
    background: rgba(102, 126, 234, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.current-user {
    font-weight: 500;
    color: #667eea;
}

/* 🔐 ERREUR D'AUTHENTIFICATION */
.auth-error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.auth-error-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    width: 100%;
    border: 2px solid #dc3545;
}

.auth-error-content h2 {
    color: #dc3545;
    margin-bottom: 20px;
    font-size: 2rem;
}

.debug-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    text-align: left;
    border-left: 4px solid #667eea;
}

.debug-info h3 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.debug-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.debug-info li {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.debug-info li:last-child {
    border-bottom: none;
}

.auth-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #f57c00 100%);
    transform: translateY(-2px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1abc9c 100%);
    transform: translateY(-2px);
}

.raw-data {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.raw-data h4 {
    color: #dc3545;
    margin-bottom: 10px;
    font-size: 1rem;
}

.code-block {
    background: #2d3748;
    color: #e2e8f0;
    padding: 15px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
}

/* 🔍 DEBUG DANS LES ERREURS */
.error-debug {
    margin-top: 10px;
    padding: 8px;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    border-left: 3px solid #ffc107;
}

.error-debug small {
    color: #666;
    font-size: 0.75rem;
}

/* 📱 RESPONSIVE POUR AUTHENTIFICATION */
@media (max-width: 768px) {
    .auth-error-container {
        padding: 20px;
    }

    .auth-error-content {
        padding: 30px 20px;
    }

    .auth-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}
