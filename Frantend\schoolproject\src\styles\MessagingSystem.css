/* ===== MESSAGERIE SYSTÈME - STYLE WHATSAPP ===== */

.messaging-system {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== HEADER ===== */
.messaging-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.messaging-header h1 {
    margin: 0;
    color: #667eea;
    font-size: 2rem;
    font-weight: 700;
}

.messaging-stats {
    display: flex;
    gap: 30px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #667eea;
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 2px;
}

/* ===== MESSAGES D'ERREUR ===== */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 5px solid #dc3545;
}

.error-message button {
    background: none;
    border: none;
    color: #721c24;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== CONTAINER PRINCIPAL ===== */
.messaging-container {
    flex: 1;
    display: flex;
    background: white;
    overflow: hidden;
}

/* ===== PANNEAU CONVERSATIONS ===== */
.conversations-panel {
    width: 350px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
}

.conversations-header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversations-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
    font-weight: 600;
}

.new-conversation-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.new-conversation-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

/* ===== LISTE CONVERSATIONS ===== */
.conversations-list {
    flex: 1;
    overflow-y: auto;
}

.conversation-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    position: relative;
}

.conversation-item:hover {
    background: #f0f4ff;
}

.conversation-item.active {
    background: #667eea;
    color: white;
}

.conversation-item.active .conversation-name,
.conversation-item.active .conversation-preview,
.conversation-item.active .conversation-time {
    color: white;
}

.conversation-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
    margin-right: 15px;
    flex-shrink: 0;
}

.conversation-info {
    flex: 1;
    min-width: 0;
}

.conversation-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversation-role {
    font-size: 0.7rem;
    background: #e9ecef;
    color: #666;
    padding: 2px 6px;
    border-radius: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.conversation-item.active .conversation-role {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.conversation-preview {
    color: #666;
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 5px;
}

.conversation-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.conversation-time {
    font-size: 0.8rem;
    color: #999;
}

.unread-badge {
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    font-weight: 600;
}

/* ===== ÉTATS VIDES ===== */
.no-conversations, .no-chat-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    color: #666;
}

.no-conversations p, .no-chat-content p {
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.no-conversations button, .no-chat-content button {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.no-conversations button:hover, .no-chat-content button:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* ===== PANNEAU CHAT ===== */
.chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
}

/* ===== NOUVELLE CONVERSATION ===== */
.new-conversation {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.new-conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.new-conversation-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.new-conversation-header button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.user-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.user-select:focus {
    outline: none;
    border-color: #667eea;
}

/* ===== HEADER CHAT ===== */
.chat-header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.chat-contact-info {
    display: flex;
    align-items: center;
}

.chat-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.1rem;
    margin-right: 15px;
}

.chat-contact-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.chat-contact-role {
    color: #666;
    font-size: 0.9rem;
    text-transform: capitalize;
}

/* ===== CONTAINER MESSAGES ===== */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
    background-image: 
        radial-gradient(circle at 25px 25px, rgba(255,255,255,0.2) 2px, transparent 0),
        radial-gradient(circle at 75px 75px, rgba(255,255,255,0.1) 1px, transparent 0);
    background-size: 100px 100px;
}

/* ===== MESSAGES ===== */
.message {
    margin-bottom: 15px;
    display: flex;
    max-width: 70%;
    position: relative;
}

.message.sent {
    margin-left: auto;
    justify-content: flex-end;
}

.message.received {
    margin-right: auto;
    justify-content: flex-start;
}

.message-content {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 100%;
    word-wrap: break-word;
}

.message.sent .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 5px;
}

.message.received .message-content {
    background: white;
    color: #333;
    border-bottom-left-radius: 5px;
}

.message-text {
    line-height: 1.4;
    margin-bottom: 5px;
}

.message-edited {
    font-size: 0.7rem;
    opacity: 0.7;
    font-style: italic;
    margin-left: 8px;
}

.message-time {
    font-size: 0.7rem;
    opacity: 0.7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
}

.message-status {
    margin-left: 8px;
    font-size: 0.8rem;
}

/* ===== ÉDITION MESSAGE ===== */
.message-edit {
    width: 100%;
}

.edit-textarea {
    width: 100%;
    min-height: 60px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: inherit;
    resize: vertical;
    border-radius: 8px;
    padding: 8px;
    font-family: inherit;
    font-size: inherit;
}

.edit-textarea:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.2);
}

.edit-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
    justify-content: flex-end;
}

.confirm-edit, .cancel-edit {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: inherit;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.confirm-edit:hover, .cancel-edit:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== ZONE SAISIE ===== */
.message-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.message-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
    border: 2px solid transparent;
    transition: border-color 0.3s ease;
}

.message-input-wrapper:focus-within {
    border-color: #667eea;
}

.message-input {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.4;
    max-height: 120px;
    min-height: 20px;
    padding: 8px 0;
}

.message-input:focus {
    outline: none;
}

.send-button {
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: #5a67d8;
    transform: scale(1.1);
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* ===== MENU CONTEXTUEL ===== */
.context-menu {
    position: fixed;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 1px solid #e9ecef;
    z-index: 1000;
    min-width: 150px;
    overflow: hidden;
}

.context-menu button {
    display: block;
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f0f0f0;
}

.context-menu button:last-child {
    border-bottom: none;
}

.context-menu button:hover {
    background: #f8f9fa;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .messaging-container {
        flex-direction: column;
    }
    
    .conversations-panel {
        width: 100%;
        height: 40%;
    }
    
    .chat-panel {
        height: 60%;
    }
    
    .messaging-header {
        padding: 15px 20px;
    }
    
    .messaging-header h1 {
        font-size: 1.5rem;
    }
    
    .messaging-stats {
        gap: 15px;
    }
    
    .stat-number {
        font-size: 1.4rem;
    }
    
    .message {
        max-width: 85%;
    }
}

@media (max-width: 480px) {
    .conversations-panel {
        height: 50%;
    }
    
    .chat-panel {
        height: 50%;
    }
    
    .messaging-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .messaging-stats {
        justify-content: center;
    }
}
