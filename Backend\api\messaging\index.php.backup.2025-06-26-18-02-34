<?php
/**
 * API Principale de Messagerie
 * Point d'entrée unique pour toutes les opérations de messagerie
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/MessageManager.php';

try {
    // Authentifier l'utilisateur
    $user = AuthManager::authenticate();
    
    // Créer le gestionnaire de messages
    $messageManager = new MessageManager($user);
    
    // Récupérer la méthode HTTP et les paramètres
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents("php://input"), true) ?? [];
    
    // Router les requêtes selon la méthode HTTP
    switch ($method) {
        case 'GET':
            handleGetRequest($messageManager);
            break;
            
        case 'POST':
            handlePostRequest($messageManager, $input);
            break;
            
        case 'PUT':
            handlePutRequest($messageManager, $input);
            break;
            
        case 'DELETE':
            handleDeleteRequest($messageManager, $input);
            break;
            
        default:
            ResponseHelper::error('Méthode HTTP non supportée', 405);
    }
    
} catch (Exception $e) {
    logError('Erreur API principale', ['error' => $e->getMessage()]);
    ResponseHelper::error($e->getMessage());
}

/**
 * Gérer les requêtes GET
 */
function handleGetRequest($messageManager) {
    try {
        // Récupérer les conversations
        if (isset($_GET['conversations'])) {
            $conversations = $messageManager->getConversations();
            ResponseHelper::success($conversations, 'Conversations récupérées avec succès');
        }
        
        // Récupérer les messages d'une conversation
        elseif (isset($_GET['contact_id'])) {
            $contactId = (int)$_GET['contact_id'];
            if ($contactId <= 0) {
                ResponseHelper::badRequest('ID de contact invalide');
            }
            
            $messages = $messageManager->getMessages($contactId);
            ResponseHelper::success([
                'messages' => $messages,
                'contact_id' => $contactId
            ], 'Messages récupérés avec succès');
        }
        
        // Récupérer les utilisateurs autorisés
        elseif (isset($_GET['users'])) {
            $users = $messageManager->getAuthorizedUsers();
            ResponseHelper::success([
                'users' => $users
            ], 'Utilisateurs récupérés avec succès');
        }
        
        // Récupérer les statistiques (optionnel)
        elseif (isset($_GET['stats'])) {
            $stats = getMessagingStats($messageManager);
            ResponseHelper::success($stats, 'Statistiques récupérées avec succès');
        }
        
        // Par défaut, retourner les conversations
        else {
            $conversations = $messageManager->getConversations();
            ResponseHelper::success([
                'conversations' => $conversations
            ], 'Conversations récupérées avec succès');
        }
        
    } catch (Exception $e) {
        ResponseHelper::error('Erreur lors de la récupération des données: ' . $e->getMessage());
    }
}

/**
 * Gérer les requêtes POST (création de messages)
 */
function handlePostRequest($messageManager, $input) {
    try {
        if (empty($input)) {
            ResponseHelper::badRequest('Données manquantes pour créer le message');
        }
        
        $message = $messageManager->sendMessage($input);
        
        if (!$message) {
            ResponseHelper::error('Échec de la création du message');
        }
        
        ResponseHelper::success($message, 'Message envoyé avec succès');
        
    } catch (Exception $e) {
        ResponseHelper::error('Erreur lors de l\'envoi du message: ' . $e->getMessage());
    }
}

/**
 * Gérer les requêtes PUT (modification de messages)
 */
function handlePutRequest($messageManager, $input) {
    try {
        if (empty($input)) {
            ResponseHelper::badRequest('Données manquantes pour modifier le message');
        }
        
        $message = $messageManager->updateMessage($input);
        
        if (!$message) {
            ResponseHelper::error('Échec de la modification du message');
        }
        
        ResponseHelper::success($message, 'Message modifié avec succès');
        
    } catch (Exception $e) {
        ResponseHelper::error('Erreur lors de la modification du message: ' . $e->getMessage());
    }
}

/**
 * Gérer les requêtes DELETE (suppression de messages)
 */
function handleDeleteRequest($messageManager, $input) {
    try {
        if (empty($input)) {
            ResponseHelper::badRequest('Données manquantes pour supprimer le message');
        }
        
        $result = $messageManager->deleteMessage($input);
        
        ResponseHelper::success($result, $result['message'] ?? 'Message supprimé avec succès');
        
    } catch (Exception $e) {
        ResponseHelper::error('Erreur lors de la suppression du message: ' . $e->getMessage());
    }
}

/**
 * Récupérer les statistiques de messagerie (optionnel)
 */
function getMessagingStats($messageManager) {
    try {
        $pdo = DatabaseConfig::getConnection();
        $userId = $messageManager->currentUser['id'];
        
        // Nombre total de conversations
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT 
                CASE 
                    WHEN expediteur_id = ? THEN destinataire_id 
                    ELSE expediteur_id 
                END
            ) as total_conversations
            FROM messages 
            WHERE (expediteur_id = ? OR destinataire_id = ?)
            AND (
                (expediteur_id = ? AND supprime_par_expediteur = 0 AND supprime_expediteur = 0) OR
                (destinataire_id = ? AND supprime_par_destinataire = 0 AND supprime_destinataire = 0)
            )
        ");
        $stmt->execute([$userId, $userId, $userId, $userId, $userId]);
        $totalConversations = $stmt->fetchColumn();
        
        // Nombre de messages non lus
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as messages_non_lus
            FROM messages 
            WHERE destinataire_id = ? 
            AND lu = 0 
            AND supprime_par_destinataire = 0 
            AND supprime_destinataire = 0
        ");
        $stmt->execute([$userId]);
        $messagesNonLus = $stmt->fetchColumn();
        
        // Nombre total de messages envoyés
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as messages_envoyes
            FROM messages 
            WHERE expediteur_id = ?
            AND supprime_par_expediteur = 0 
            AND supprime_expediteur = 0
        ");
        $stmt->execute([$userId]);
        $messagesEnvoyes = $stmt->fetchColumn();
        
        return [
            'total_conversations' => (int)$totalConversations,
            'messages_non_lus' => (int)$messagesNonLus,
            'messages_envoyes' => (int)$messagesEnvoyes,
            'derniere_activite' => date('Y-m-d H:i:s')
        ];
        
    } catch (Exception $e) {
        return [
            'total_conversations' => 0,
            'messages_non_lus' => 0,
            'messages_envoyes' => 0,
            'erreur' => 'Impossible de récupérer les statistiques'
        ];
    }
}
?>
