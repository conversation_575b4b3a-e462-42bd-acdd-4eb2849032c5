{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\DiplomesCRUD.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\nconst DiplomesCRUD = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [diplomes, setDiplomes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingDiplome, setEditingDiplome] = useState(null);\n  const [etudiants, setEtudiants] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [yearFilter, setYearFilter] = useState('all');\n\n  // États de pagination\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10); // 10 diplômes par page comme les factures\n\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    titre: '',\n    date_obtention: ''\n  });\n\n  // Vérifier si l'utilisateur est Admin\n  const isAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'Admin' || (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'responsable';\n  useEffect(() => {\n    fetchDiplomes();\n    if (isAdmin) {\n      fetchEtudiants();\n    }\n  }, [isAdmin]);\n  const fetchDiplomes = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setDiplomes(response.data);\n    } catch (error) {\n      console.error('Erreur lors du chargement des diplômes:', error);\n      Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchEtudiants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.data.success) {\n        setEtudiants(response.data.etudiants);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des étudiants:', error);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des diplômes', 'error');\n      return;\n    }\n    try {\n      const token = localStorage.getItem('token');\n      const url = 'http://localhost/Project_PFE/Backend/pages/diplomes/';\n      const method = editingDiplome ? 'PUT' : 'POST';\n      const data = editingDiplome ? {\n        ...formData,\n        id: editingDiplome.id\n      } : formData;\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      // Proposer de générer le PDF après création/modification\n      const result = await Swal.fire({\n        title: 'Succès!',\n        text: `Diplôme ${editingDiplome ? 'modifié' : 'créé'} avec succès`,\n        icon: 'success',\n        showCancelButton: true,\n        confirmButtonText: '📄 Générer PDF',\n        cancelButtonText: '✅ Continuer',\n        confirmButtonColor: '#3085d6',\n        cancelButtonColor: '#28a745'\n      });\n      if (result.isConfirmed) {\n        const diplomeId = editingDiplome ? editingDiplome.id : response.data.id;\n        generatePDF(diplomeId);\n      }\n      setShowModal(false);\n      setEditingDiplome(null);\n      resetForm();\n      fetchDiplomes();\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Erreur:', error);\n      Swal.fire('Erreur', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = diplome => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des diplômes', 'error');\n      return;\n    }\n    setEditingDiplome(diplome);\n    setFormData({\n      etudiant_id: diplome.etudiant_id,\n      titre: diplome.titre,\n      date_obtention: diplome.date_obtention\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    if (!isAdmin) {\n      Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des diplômes', 'error');\n      return;\n    }\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action est irréversible!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        const token = localStorage.getItem('token');\n        await axios.delete('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n          headers: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        Swal.fire('Supprimé!', 'Le diplôme a été supprimé.', 'success');\n        fetchDiplomes();\n      } catch (error) {\n        console.error('Erreur:', error);\n        Swal.fire('Erreur', 'Impossible de supprimer le diplôme', 'error');\n      }\n    }\n  };\n  const generatePDF = diplomeId => {\n    try {\n      console.log('🔄 Génération PDF pour diplôme ID:', diplomeId);\n\n      // Construire l'URL du PDF\n      const pdfUrl = `http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?diplome_id=${diplomeId}`;\n      console.log('📄 URL PDF:', pdfUrl);\n\n      // Afficher un message de chargement\n      Swal.fire({\n        title: 'Génération du PDF...',\n        text: 'Veuillez patienter pendant la génération du diplôme',\n        icon: 'info',\n        allowOutsideClick: false,\n        showConfirmButton: false,\n        timer: 2000,\n        timerProgressBar: true\n      });\n\n      // Ouvrir le PDF dans un nouvel onglet après un court délai\n      setTimeout(() => {\n        const newWindow = window.open(pdfUrl, '_blank');\n        if (!newWindow) {\n          Swal.fire({\n            title: 'Bloqueur de pop-up détecté',\n            text: 'Veuillez autoriser les pop-ups pour ce site et réessayer',\n            icon: 'warning',\n            confirmButtonText: 'Compris'\n          });\n        } else {\n          console.log('✅ PDF ouvert avec succès');\n        }\n      }, 500);\n    } catch (error) {\n      console.error('❌ Erreur lors de la génération PDF:', error);\n      Swal.fire({\n        title: 'Erreur',\n        text: 'Impossible de générer le PDF. Veuillez réessayer.',\n        icon: 'error',\n        confirmButtonText: 'OK'\n      });\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      titre: '',\n      date_obtention: ''\n    });\n  };\n\n  // Obtenir les années uniques pour le filtre\n  const getUniqueYears = () => {\n    const years = diplomes.map(d => new Date(d.date_obtention).getFullYear());\n    return [...new Set(years)].sort((a, b) => b - a);\n  };\n\n  // Filtrage des données\n  const filteredDiplomes = diplomes.filter(diplome => {\n    var _diplome$etudiant_nom, _diplome$titre;\n    const matchesSearch = ((_diplome$etudiant_nom = diplome.etudiant_nom) === null || _diplome$etudiant_nom === void 0 ? void 0 : _diplome$etudiant_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_diplome$titre = diplome.titre) === null || _diplome$titre === void 0 ? void 0 : _diplome$titre.toLowerCase().includes(searchTerm.toLowerCase()));\n    const diplomeYear = new Date(diplome.date_obtention).getFullYear().toString();\n    const matchesYear = yearFilter === 'all' || diplomeYear === yearFilter;\n    return matchesSearch && matchesYear;\n  });\n\n  // Pagination\n  const indexOfLastItem = currentPage * itemsPerPage;\n  const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n  const currentDiplomes = filteredDiplomes.slice(indexOfFirstItem, indexOfLastItem);\n  const totalPages = Math.ceil(filteredDiplomes.length / itemsPerPage);\n  const paginate = pageNumber => setCurrentPage(pageNumber);\n\n  // Reset pagination when filters change\n  React.useEffect(() => {\n    setCurrentPage(1);\n  }, [searchTerm, yearFilter]);\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 17\n      }\n    }, \"Chargement des dipl\\xF4mes...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 17\n    }\n  }, \"\\uD83C\\uDF93 Gestion des Dipl\\xF4mes\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 21\n    }\n  }, filteredDiplomes.length, \" dipl\\xF4me(s) trouv\\xE9(s)\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '10px',\n      alignItems: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary\",\n    onClick: () => setShowModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 33\n    }\n  }), \" Nouveau Dipl\\xF4me\")))), !isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      padding: '15px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '8px',\n      marginBottom: '20px',\n      border: '1px solid #bbdefb'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '0',\n      color: '#1976d2'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 21\n    }\n  }, \"\\u2139\\uFE0F Vous consultez les dipl\\xF4mes en mode lecture seule. Seul l'administrateur peut cr\\xE9er, modifier ou supprimer des dipl\\xF4mes.\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filters-section\",\n    style: {\n      display: 'flex',\n      gap: '15px',\n      marginBottom: '20px',\n      padding: '15px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-box\",\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher par nom d'\\xE9tudiant ou titre de dipl\\xF4me...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 21\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"year-filter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: yearFilter,\n    onChange: e => setYearFilter(e.target.value),\n    style: {\n      padding: '10px',\n      border: '1px solid #ddd',\n      borderRadius: '6px',\n      minWidth: '120px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 25\n    }\n  }, \"Toutes les ann\\xE9es\"), getUniqueYears().map(year => /*#__PURE__*/React.createElement(\"option\", {\n    key: year,\n    value: year,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 29\n    }\n  }, year))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 13\n    }\n  }, filteredDiplomes.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/result.png\",\n    alt: \"Aucun dipl\\xF4me\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 25\n    }\n  }, \"Aucun dipl\\xF4me trouv\\xE9\"), searchTerm && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setSearchTerm(''),\n    className: \"btn btn-secondary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 29\n    }\n  }, \"Effacer la recherche\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"diplomes-cards\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 21\n    }\n  }, filteredDiplomes.map(diplome => /*#__PURE__*/React.createElement(\"div\", {\n    key: diplome.id,\n    className: \"diplome-card\",\n    style: {\n      backgroundColor: 'white',\n      borderRadius: '12px',\n      padding: '20px',\n      marginBottom: '15px',\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      border: '1px solid #e9ecef',\n      transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n    },\n    onMouseEnter: e => {\n      e.currentTarget.style.transform = 'translateY(-2px)';\n      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n    },\n    onMouseLeave: e => {\n      e.currentTarget.style.transform = 'translateY(0)';\n      e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'flex-start',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      fontSize: '2.5rem',\n      color: '#ffd700',\n      minWidth: '60px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 37\n    }\n  }, \"\\uD83C\\uDFC6\"), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      flex: 1\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      margin: '0 0 10px 0',\n      color: '#2c3e50',\n      fontSize: '1.3rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 41\n    }\n  }, diplome.titre), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 392,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '1.1rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDC64\"), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 400,\n      columnNumber: 53\n    }\n  }, diplome.etudiant_nom), /*#__PURE__*/React.createElement(\"br\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 53\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 53\n    }\n  }, diplome.etudiant_email))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '8px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      fontSize: '1.1rem'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 49\n    }\n  }, \"\\uD83D\\uDCC5\"), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 12px',\n      backgroundColor: '#e3f2fd',\n      borderRadius: '20px',\n      fontSize: '0.9em',\n      fontWeight: '500'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 49\n    }\n  }, new Date(diplome.date_obtention).toLocaleDateString('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })))), /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      marginTop: '15px',\n      display: 'flex',\n      gap: '10px',\n      alignItems: 'center',\n      flexWrap: 'wrap'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => generatePDF(diplome.id),\n    title: \"G\\xE9n\\xE9rer et t\\xE9l\\xE9charger le PDF du dipl\\xF4me\",\n    style: {\n      backgroundColor: '#198754',\n      color: 'white',\n      border: 'none',\n      borderRadius: '6px',\n      padding: '8px 16px',\n      fontSize: '14px',\n      fontWeight: '600',\n      cursor: 'pointer',\n      display: 'flex',\n      alignItems: 'center',\n      gap: '6px',\n      transition: 'all 0.2s ease',\n      boxShadow: '#198754',\n      minWidth: '100px',\n      justifyContent: 'center'\n    },\n    onMouseEnter: e => {\n      e.target.style.backgroundColor = '#c82333';\n      e.target.style.transform = 'translateY(-1px)';\n      e.target.style.boxShadow = '#198754';\n    },\n    onMouseLeave: e => {\n      e.target.style.backgroundColor = '#dc3545';\n      e.target.style.transform = 'translateY(0)';\n      e.target.style.boxShadow = '#198754';\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 432,\n      columnNumber: 45\n    }\n  }, \"\\uD83D\\uDCC4 G\\xE9n\\xE9rer PDF\"), isAdmin && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning\",\n    onClick: () => handleEdit(diplome),\n    title: \"Modifier\",\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 57\n    }\n  }), \"Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger\",\n    onClick: () => handleDelete(diplome.id),\n    title: \"Supprimer\",\n    style: {\n      display: 'flex',\n      alignItems: 'center',\n      gap: '4px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    style: {\n      width: '16px',\n      height: '16px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 57\n    }\n  }), \"Supprimer\"))))))))), filteredDiplomes.length > 0 && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stats-section\",\n    style: {\n      marginTop: '30px',\n      padding: '20px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '8px',\n      display: 'grid',\n      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n      gap: '15px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#28a745',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 25\n    }\n  }, filteredDiplomes.length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 25\n    }\n  }, \"Total dipl\\xF4mes\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 522,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#007bff',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 25\n    }\n  }, new Set(filteredDiplomes.map(d => d.etudiant_id)).size), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 526,\n      columnNumber: 25\n    }\n  }, \"\\xC9tudiants dipl\\xF4m\\xE9s\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#ffc107',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 25\n    }\n  }, getUniqueYears().length), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 532,\n      columnNumber: 25\n    }\n  }, \"Ann\\xE9es repr\\xE9sent\\xE9es\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"stat-card\",\n    style: {\n      textAlign: 'center'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      color: '#6f42c1',\n      margin: '0'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 535,\n      columnNumber: 25\n    }\n  }, new Set(filteredDiplomes.map(d => d.titre)).size), /*#__PURE__*/React.createElement(\"p\", {\n    style: {\n      margin: '5px 0 0 0',\n      color: '#6c757d'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 25\n    }\n  }, \"Types de dipl\\xF4mes\"))), showModal && isAdmin && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 548,\n      columnNumber: 29\n    }\n  }, editingDiplome ? 'Modifier le diplôme' : 'Nouveau diplôme'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingDiplome(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 560,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant *\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => setFormData({\n      ...formData,\n      etudiant_id: e.target.value\n    }),\n    required: true,\n    disabled: editingDiplome,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px',\n      backgroundColor: editingDiplome ? '#e9ecef' : 'white'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.etudiant_id,\n    value: etudiant.etudiant_id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 41\n    }\n  }, etudiant.nom, \" - \", etudiant.email, etudiant.classe_nom && ` (${etudiant.classe_nom})`))), editingDiplome && /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.8em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 37\n    }\n  }, \"L'\\xE9tudiant ne peut pas \\xEAtre modifi\\xE9 apr\\xE8s cr\\xE9ation\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 33\n    }\n  }, \"Titre du dipl\\xF4me *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    value: formData.titre,\n    onChange: e => setFormData({\n      ...formData,\n      titre: e.target.value\n    }),\n    placeholder: \"Ex: Licence en Informatique, Master en Gestion...\",\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 594,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 611,\n      columnNumber: 33\n    }\n  }, \"Date d'obtention *\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_obtention,\n    onChange: e => setFormData({\n      ...formData,\n      date_obtention: e.target.value\n    }),\n    max: new Date().toISOString().split('T')[0],\n    required: true,\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 628,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 33\n    }\n  }, editingDiplome ? '💾 Modifier' : '➕ Créer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingDiplome(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 33\n    }\n  }, \"\\u274C Annuler\"))))));\n};\nexport default DiplomesCRUD;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "DiplomesCRUD", "user", "diplomes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "showModal", "setShowModal", "editingDiplome", "setEditingDiplome", "etudiants", "setEtudiants", "searchTerm", "setSearchTerm", "yearFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "etudiant_id", "titre", "date_obtention", "isAdmin", "role", "fetchDiplomes", "fetchEtudiants", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "error", "console", "fire", "success", "handleSubmit", "e", "preventDefault", "url", "method", "id", "result", "title", "text", "icon", "showCancelButton", "confirmButtonText", "cancelButtonText", "confirmButtonColor", "cancelButtonColor", "isConfirmed", "diplomeId", "generatePDF", "resetForm", "_error$response", "_error$response$data", "handleEdit", "diplome", "handleDelete", "delete", "log", "pdfUrl", "allowOutsideClick", "showConfirmButton", "timer", "timerP<PERSON>ressBar", "setTimeout", "newWindow", "window", "open", "getUniqueYears", "years", "map", "d", "Date", "getFullYear", "Set", "sort", "a", "b", "filteredDiplomes", "filter", "_diplome$etudiant_nom", "_diplome$titre", "matchesSearch", "etudiant_nom", "toLowerCase", "includes", "diplomeYear", "toString", "matchesYear", "indexOfLastItem", "indexOfFirstItem", "currentDiplomes", "slice", "totalPages", "Math", "ceil", "length", "paginate", "pageNumber", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gap", "alignItems", "onClick", "src", "alt", "padding", "backgroundColor", "borderRadius", "marginBottom", "border", "margin", "color", "flex", "type", "placeholder", "value", "onChange", "target", "width", "min<PERSON><PERSON><PERSON>", "year", "key", "boxShadow", "transition", "onMouseEnter", "currentTarget", "transform", "onMouseLeave", "fontSize", "flexDirection", "etudiant_email", "fontWeight", "toLocaleDateString", "month", "day", "marginTop", "flexWrap", "cursor", "justifyContent", "Fragment", "height", "gridTemplateColumns", "textAlign", "size", "onSubmit", "required", "disabled", "etudiant", "nom", "email", "classe_nom", "max", "toISOString", "split"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/DiplomesCRUD.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Animations.css';\nimport '../css/Factures.css';\n\nconst DiplomesCRUD = () => {\n    const { user } = useContext(AuthContext);\n    const [diplomes, setDiplomes] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingDiplome, setEditingDiplome] = useState(null);\n    const [etudiants, setEtudiants] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [yearFilter, setYearFilter] = useState('all');\n\n    // États de pagination\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10); // 10 diplômes par page comme les factures\n\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        titre: '',\n        date_obtention: ''\n    });\n\n    // Vérifier si l'utilisateur est Admin\n    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';\n\n    useEffect(() => {\n        fetchDiplomes();\n        if (isAdmin) {\n            fetchEtudiants();\n        }\n    }, [isAdmin]);\n\n    const fetchDiplomes = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            setDiplomes(response.data);\n        } catch (error) {\n            console.error('Erreur lors du chargement des diplômes:', error);\n            Swal.fire('Erreur', 'Impossible de charger les diplômes', 'error');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchEtudiants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/getEtudiants.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            \n            if (response.data.success) {\n                setEtudiants(response.data.etudiants);\n            }\n        } catch (error) {\n            console.error('Erreur lors du chargement des étudiants:', error);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        \n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut créer/modifier des diplômes', 'error');\n            return;\n        }\n\n        try {\n            const token = localStorage.getItem('token');\n            const url = 'http://localhost/Project_PFE/Backend/pages/diplomes/';\n            const method = editingDiplome ? 'PUT' : 'POST';\n            const data = editingDiplome ? { ...formData, id: editingDiplome.id } : formData;\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: { \n                    Authorization: `Bearer ${token}`,\n                    'Content-Type': 'application/json'\n                }\n            });\n\n            // Proposer de générer le PDF après création/modification\n            const result = await Swal.fire({\n                title: 'Succès!',\n                text: `Diplôme ${editingDiplome ? 'modifié' : 'créé'} avec succès`,\n                icon: 'success',\n                showCancelButton: true,\n                confirmButtonText: '📄 Générer PDF',\n                cancelButtonText: '✅ Continuer',\n                confirmButtonColor: '#3085d6',\n                cancelButtonColor: '#28a745'\n            });\n\n            if (result.isConfirmed) {\n                const diplomeId = editingDiplome ? editingDiplome.id : response.data.id;\n                generatePDF(diplomeId);\n            }\n\n            setShowModal(false);\n            setEditingDiplome(null);\n            resetForm();\n            fetchDiplomes();\n        } catch (error) {\n            console.error('Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (diplome) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut modifier des diplômes', 'error');\n            return;\n        }\n\n        setEditingDiplome(diplome);\n        setFormData({\n            etudiant_id: diplome.etudiant_id,\n            titre: diplome.titre,\n            date_obtention: diplome.date_obtention\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        if (!isAdmin) {\n            Swal.fire('Erreur', 'Seul l\\'administrateur peut supprimer des diplômes', 'error');\n            return;\n        }\n\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr?',\n            text: 'Cette action est irréversible!',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer!',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                const token = localStorage.getItem('token');\n                await axios.delete('http://localhost/Project_PFE/Backend/pages/diplomes/', {\n                    headers: { \n                        Authorization: `Bearer ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    data: { id }\n                });\n                Swal.fire('Supprimé!', 'Le diplôme a été supprimé.', 'success');\n                fetchDiplomes();\n            } catch (error) {\n                console.error('Erreur:', error);\n                Swal.fire('Erreur', 'Impossible de supprimer le diplôme', 'error');\n            }\n        }\n    };\n\n    const generatePDF = (diplomeId) => {\n        try {\n            console.log('🔄 Génération PDF pour diplôme ID:', diplomeId);\n\n            // Construire l'URL du PDF\n            const pdfUrl = `http://localhost/Project_PFE/Backend/pages/diplomes/generateSimplePDF.php?diplome_id=${diplomeId}`;\n            console.log('📄 URL PDF:', pdfUrl);\n\n            // Afficher un message de chargement\n            Swal.fire({\n                title: 'Génération du PDF...',\n                text: 'Veuillez patienter pendant la génération du diplôme',\n                icon: 'info',\n                allowOutsideClick: false,\n                showConfirmButton: false,\n                timer: 2000,\n                timerProgressBar: true\n            });\n\n            // Ouvrir le PDF dans un nouvel onglet après un court délai\n            setTimeout(() => {\n                const newWindow = window.open(pdfUrl, '_blank');\n\n                if (!newWindow) {\n                    Swal.fire({\n                        title: 'Bloqueur de pop-up détecté',\n                        text: 'Veuillez autoriser les pop-ups pour ce site et réessayer',\n                        icon: 'warning',\n                        confirmButtonText: 'Compris'\n                    });\n                } else {\n                    console.log('✅ PDF ouvert avec succès');\n                }\n            }, 500);\n\n        } catch (error) {\n            console.error('❌ Erreur lors de la génération PDF:', error);\n            Swal.fire({\n                title: 'Erreur',\n                text: 'Impossible de générer le PDF. Veuillez réessayer.',\n                icon: 'error',\n                confirmButtonText: 'OK'\n            });\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            titre: '',\n            date_obtention: ''\n        });\n    };\n\n    // Obtenir les années uniques pour le filtre\n    const getUniqueYears = () => {\n        const years = diplomes.map(d => new Date(d.date_obtention).getFullYear());\n        return [...new Set(years)].sort((a, b) => b - a);\n    };\n\n    // Filtrage des données\n    const filteredDiplomes = diplomes.filter(diplome => {\n        const matchesSearch = diplome.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                             diplome.titre?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        const diplomeYear = new Date(diplome.date_obtention).getFullYear().toString();\n        const matchesYear = yearFilter === 'all' || diplomeYear === yearFilter;\n\n        return matchesSearch && matchesYear;\n    });\n\n    // Pagination\n    const indexOfLastItem = currentPage * itemsPerPage;\n    const indexOfFirstItem = indexOfLastItem - itemsPerPage;\n    const currentDiplomes = filteredDiplomes.slice(indexOfFirstItem, indexOfLastItem);\n    const totalPages = Math.ceil(filteredDiplomes.length / itemsPerPage);\n\n    const paginate = (pageNumber) => setCurrentPage(pageNumber);\n\n    // Reset pagination when filters change\n    React.useEffect(() => {\n        setCurrentPage(1);\n    }, [searchTerm, yearFilter]);\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des diplômes...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>🎓 Gestion des Diplômes</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredDiplomes.length} diplôme(s) trouvé(s)\n                    </span>\n                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>\n                       \n                            <button\n                                className=\"btn btn-primary\"\n                                onClick={() => setShowModal(true)}\n                            >\n                                <img src=\"/plus.png\" alt=\"Ajouter\" /> Nouveau Diplôme\n                            </button>\n                        \n                    </div>\n                </div>\n            </div>\n\n            {/* Message d'information pour les non-admins */}\n            {!isAdmin && (\n                <div style={{\n                    padding: '15px',\n                    backgroundColor: '#e3f2fd',\n                    borderRadius: '8px',\n                    marginBottom: '20px',\n                    border: '1px solid #bbdefb'\n                }}>\n                    <p style={{ margin: '0', color: '#1976d2' }}>\n                        ℹ️ Vous consultez les diplômes en mode lecture seule. \n                        Seul l'administrateur peut créer, modifier ou supprimer des diplômes.\n                    </p>\n                </div>\n            )}\n\n            {/* Filtres */}\n            <div className=\"filters-section\" style={{\n                display: 'flex',\n                gap: '15px',\n                marginBottom: '20px',\n                padding: '15px',\n                backgroundColor: '#f8f9fa',\n                borderRadius: '8px'\n            }}>\n                <div className=\"search-box\" style={{ flex: 1 }}>\n                    <input\n                        type=\"text\"\n                        placeholder=\"🔍 Rechercher par nom d'étudiant ou titre de diplôme...\"\n                        value={searchTerm}\n                        onChange={(e) => setSearchTerm(e.target.value)}\n                        style={{\n                            width: '100%',\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '6px'\n                        }}\n                    />\n                </div>\n                <div className=\"year-filter\">\n                    <select\n                        value={yearFilter}\n                        onChange={(e) => setYearFilter(e.target.value)}\n                        style={{\n                            padding: '10px',\n                            border: '1px solid #ddd',\n                            borderRadius: '6px',\n                            minWidth: '120px'\n                        }}\n                    >\n                        <option value=\"all\">Toutes les années</option>\n                        {getUniqueYears().map(year => (\n                            <option key={year} value={year}>{year}</option>\n                        ))}\n                    </select>\n                </div>\n            </div>\n\n            <div className=\"factures-grid\">\n                {filteredDiplomes.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/result.png\" alt=\"Aucun diplôme\" />\n                        <p>Aucun diplôme trouvé</p>\n                        {searchTerm && (\n                            <button \n                                onClick={() => setSearchTerm('')}\n                                className=\"btn btn-secondary\"\n                            >\n                                Effacer la recherche\n                            </button>\n                        )}\n                    </div>\n                ) : (\n                    <div className=\"diplomes-cards\">\n                        {filteredDiplomes.map((diplome) => (\n                            <div key={diplome.id} className=\"diplome-card\" style={{\n                                backgroundColor: 'white',\n                                borderRadius: '12px',\n                                padding: '20px',\n                                marginBottom: '15px',\n                                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                border: '1px solid #e9ecef',\n                                transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                                e.currentTarget.style.transform = 'translateY(-2px)';\n                                e.currentTarget.style.boxShadow = '0 4px 20px rgba(0,0,0,0.15)';\n                            }}\n                            onMouseLeave={(e) => {\n                                e.currentTarget.style.transform = 'translateY(0)';\n                                e.currentTarget.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';\n                            }}>\n                                <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>\n                                    <div style={{\n                                        fontSize: '2.5rem',\n                                        color: '#ffd700',\n                                        minWidth: '60px'\n                                    }}>\n                                        🏆\n                                    </div>\n                                    <div style={{ flex: 1 }}>\n                                        <h3 style={{\n                                            margin: '0 0 10px 0',\n                                            color: '#2c3e50',\n                                            fontSize: '1.3rem'\n                                        }}>\n                                            {diplome.titre}\n                                        </h3>\n                                        <div style={{\n                                            display: 'flex',\n                                            flexDirection: 'column',\n                                            gap: '8px'\n                                        }}>\n                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                                                <span style={{ fontSize: '1.1rem' }}>👤</span>\n                                                <div>\n                                                    <strong>{diplome.etudiant_nom}</strong>\n                                                    <br />\n                                                    <small style={{ color: '#6c757d' }}>{diplome.etudiant_email}</small>\n                                                </div>\n                                            </div>\n                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                                                <span style={{ fontSize: '1.1rem' }}>📅</span>\n                                                <span style={{\n                                                    padding: '4px 12px',\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '20px',\n                                                    fontSize: '0.9em',\n                                                    fontWeight: '500'\n                                                }}>\n                                                    {new Date(diplome.date_obtention).toLocaleDateString('fr-FR', {\n                                                        year: 'numeric',\n                                                        month: 'long',\n                                                        day: 'numeric'\n                                                    })}\n                                                </span>\n                                            </div>\n                                        </div>\n                                        \n                                        {/* Actions */}\n                                        <div style={{\n                                            marginTop: '15px',\n                                            display: 'flex',\n                                            gap: '10px',\n                                            alignItems: 'center',\n                                            flexWrap: 'wrap'\n                                        }}>\n                                            {/* Bouton PDF - Toujours visible */}\n                                            <button\n                                                onClick={() => generatePDF(diplome.id)}\n                                                title=\"Générer et télécharger le PDF du diplôme\"\n                                                style={{\n                                                    backgroundColor: '#198754',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '6px',\n                                                    padding: '8px 16px',\n                                                    fontSize: '14px',\n                                                    fontWeight: '600',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '6px',\n                                                    transition: 'all 0.2s ease',\n                                                    boxShadow: '#198754',\n                                                    minWidth: '100px',\n                                                    justifyContent: 'center'\n                                                }}\n                                                onMouseEnter={(e) => {\n                                                    e.target.style.backgroundColor = '#c82333';\n                                                    e.target.style.transform = 'translateY(-1px)';\n                                                    e.target.style.boxShadow = '#198754';\n                                                }}\n                                                onMouseLeave={(e) => {\n                                                    e.target.style.backgroundColor = '#dc3545';\n                                                    e.target.style.transform = 'translateY(0)';\n                                                    e.target.style.boxShadow = '#198754';\n                                                }}\n                                            >\n                                                📄 Générer PDF\n                                            </button>\n\n                                            {isAdmin && (\n                                                <>\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning\"\n                                                        onClick={() => handleEdit(diplome)}\n                                                        title=\"Modifier\"\n                                                        style={{\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            gap: '4px'\n                                                        }}\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" style={{ width: '16px', height: '16px' }} />\n                                                        Modifier\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger\"\n                                                        onClick={() => handleDelete(diplome.id)}\n                                                        title=\"Supprimer\"\n                                                        style={{\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            gap: '4px'\n                                                        }}\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" style={{ width: '16px', height: '16px' }} />\n                                                        Supprimer\n                                                    </button>\n                                                </>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        ))}\n                    </div>\n                )}\n            </div>\n\n            {/* Statistiques */}\n            {filteredDiplomes.length > 0 && (\n                <div className=\"stats-section\" style={{\n                    marginTop: '30px',\n                    padding: '20px',\n                    backgroundColor: '#f8f9fa',\n                    borderRadius: '8px',\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '15px'\n                }}>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#28a745', margin: '0' }}>\n                            {filteredDiplomes.length}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total diplômes</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#007bff', margin: '0' }}>\n                            {new Set(filteredDiplomes.map(d => d.etudiant_id)).size}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Étudiants diplômés</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#ffc107', margin: '0' }}>\n                            {getUniqueYears().length}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Années représentées</p>\n                    </div>\n                    <div className=\"stat-card\" style={{ textAlign: 'center' }}>\n                        <h3 style={{ color: '#6f42c1', margin: '0' }}>\n                            {new Set(filteredDiplomes.map(d => d.titre)).size}\n                        </h3>\n                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Types de diplômes</p>\n                    </div>\n                </div>\n            )}\n\n            {/* Modal pour ajouter/modifier un diplôme */}\n            {showModal && isAdmin && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingDiplome ? 'Modifier le diplôme' : 'Nouveau diplôme'}</h3>\n                            <button\n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingDiplome(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant *</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n                                    required\n                                    disabled={editingDiplome}\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px',\n                                        backgroundColor: editingDiplome ? '#e9ecef' : 'white'\n                                    }}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant</option>\n                                    {etudiants.map((etudiant) => (\n                                        <option key={etudiant.etudiant_id} value={etudiant.etudiant_id}>\n                                            {etudiant.nom} - {etudiant.email}\n                                            {etudiant.classe_nom && ` (${etudiant.classe_nom})`}\n                                        </option>\n                                    ))}\n                                </select>\n                                {editingDiplome && (\n                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>\n                                        L'étudiant ne peut pas être modifié après création\n                                    </small>\n                                )}\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Titre du diplôme *</label>\n                                <input\n                                    type=\"text\"\n                                    value={formData.titre}\n                                    onChange={(e) => setFormData({...formData, titre: e.target.value})}\n                                    placeholder=\"Ex: Licence en Informatique, Master en Gestion...\"\n                                    required\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                />\n                            </div>\n\n                            <div className=\"form-group\">\n                                <label>Date d'obtention *</label>\n                                <input\n                                    type=\"date\"\n                                    value={formData.date_obtention}\n                                    onChange={(e) => setFormData({...formData, date_obtention: e.target.value})}\n                                    max={new Date().toISOString().split('T')[0]}\n                                    required\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px'\n                                    }}\n                                />\n                            </div>\n\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingDiplome ? '💾 Modifier' : '➕ Créer'}\n                                </button>\n                                <button\n                                    type=\"button\"\n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingDiplome(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    ❌ Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default DiplomesCRUD;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,qBAAqB;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EACvB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACK,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACwB,YAAY,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;EAErC,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IACrC2B,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,cAAc,EAAE;EACpB,CAAC,CAAC;;EAEF;EACA,MAAMC,OAAO,GAAG,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,OAAO,IAAI,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,OAAO,IAAI,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwB,IAAI,MAAK,aAAa;EAEhG9B,SAAS,CAAC,MAAM;IACZ+B,aAAa,CAAC,CAAC;IACf,IAAIF,OAAO,EAAE;MACTG,cAAc,CAAC,CAAC;IACpB;EACJ,CAAC,EAAE,CAACH,OAAO,CAAC,CAAC;EAEb,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,sDAAsD,EAAE;QACrFC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MACFzB,WAAW,CAAC4B,QAAQ,CAACI,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/DrC,IAAI,CAACuC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;IACtE,CAAC,SAAS;MACNjC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsB,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,GAAG,CAAC,uEAAuE,EAAE;QACtGC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUN,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACI,IAAI,CAACI,OAAO,EAAE;QACvB5B,YAAY,CAACoB,QAAQ,CAACI,IAAI,CAACzB,SAAS,CAAC;MACzC;IACJ,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IACpE;EACJ,CAAC;EAED,MAAMI,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAAClB,OAAO,EAAE;MACVzB,IAAI,CAACuC,IAAI,CAAC,QAAQ,EAAE,yDAAyD,EAAE,OAAO,CAAC;MACvF;IACJ;IAEA,IAAI;MACA,MAAMV,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMa,GAAG,GAAG,sDAAsD;MAClE,MAAMC,MAAM,GAAGpC,cAAc,GAAG,KAAK,GAAG,MAAM;MAC9C,MAAM2B,IAAI,GAAG3B,cAAc,GAAG;QAAE,GAAGW,QAAQ;QAAE0B,EAAE,EAAErC,cAAc,CAACqC;MAAG,CAAC,GAAG1B,QAAQ;MAE/E,MAAMY,QAAQ,GAAG,MAAMjC,KAAK,CAAC;QACzB8C,MAAM;QACND,GAAG;QACHR,IAAI;QACJF,OAAO,EAAE;UACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;UAChC,cAAc,EAAE;QACpB;MACJ,CAAC,CAAC;;MAEF;MACA,MAAMkB,MAAM,GAAG,MAAM/C,IAAI,CAACuC,IAAI,CAAC;QAC3BS,KAAK,EAAE,SAAS;QAChBC,IAAI,EAAE,WAAWxC,cAAc,GAAG,SAAS,GAAG,MAAM,cAAc;QAClEyC,IAAI,EAAE,SAAS;QACfC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,gBAAgB;QACnCC,gBAAgB,EAAE,aAAa;QAC/BC,kBAAkB,EAAE,SAAS;QAC7BC,iBAAiB,EAAE;MACvB,CAAC,CAAC;MAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;QACpB,MAAMC,SAAS,GAAGhD,cAAc,GAAGA,cAAc,CAACqC,EAAE,GAAGd,QAAQ,CAACI,IAAI,CAACU,EAAE;QACvEY,WAAW,CAACD,SAAS,CAAC;MAC1B;MAEAjD,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvBiD,SAAS,CAAC,CAAC;MACXhC,aAAa,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOU,KAAK,EAAE;MAAA,IAAAuB,eAAA,EAAAC,oBAAA;MACZvB,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrC,IAAI,CAACuC,IAAI,CAAC,QAAQ,EAAE,EAAAqB,eAAA,GAAAvB,KAAK,CAACL,QAAQ,cAAA4B,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBxB,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAMyB,UAAU,GAAIC,OAAO,IAAK;IAC5B,IAAI,CAACtC,OAAO,EAAE;MACVzB,IAAI,CAACuC,IAAI,CAAC,QAAQ,EAAE,mDAAmD,EAAE,OAAO,CAAC;MACjF;IACJ;IAEA7B,iBAAiB,CAACqD,OAAO,CAAC;IAC1B1C,WAAW,CAAC;MACRC,WAAW,EAAEyC,OAAO,CAACzC,WAAW;MAChCC,KAAK,EAAEwC,OAAO,CAACxC,KAAK;MACpBC,cAAc,EAAEuC,OAAO,CAACvC;IAC5B,CAAC,CAAC;IACFhB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMwD,YAAY,GAAG,MAAOlB,EAAE,IAAK;IAC/B,IAAI,CAACrB,OAAO,EAAE;MACVzB,IAAI,CAACuC,IAAI,CAAC,QAAQ,EAAE,oDAAoD,EAAE,OAAO,CAAC;MAClF;IACJ;IAEA,MAAMQ,MAAM,GAAG,MAAM/C,IAAI,CAACuC,IAAI,CAAC;MAC3BS,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBG,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BH,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIN,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA,MAAM3B,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAMhC,KAAK,CAACkE,MAAM,CAAC,sDAAsD,EAAE;UACvE/B,OAAO,EAAE;YACLC,aAAa,EAAE,UAAUN,KAAK,EAAE;YAChC,cAAc,EAAE;UACpB,CAAC;UACDO,IAAI,EAAE;YAAEU;UAAG;QACf,CAAC,CAAC;QACF9C,IAAI,CAACuC,IAAI,CAAC,WAAW,EAAE,4BAA4B,EAAE,SAAS,CAAC;QAC/DZ,aAAa,CAAC,CAAC;MACnB,CAAC,CAAC,OAAOU,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BrC,IAAI,CAACuC,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;MACtE;IACJ;EACJ,CAAC;EAED,MAAMmB,WAAW,GAAID,SAAS,IAAK;IAC/B,IAAI;MACAnB,OAAO,CAAC4B,GAAG,CAAC,oCAAoC,EAAET,SAAS,CAAC;;MAE5D;MACA,MAAMU,MAAM,GAAG,wFAAwFV,SAAS,EAAE;MAClHnB,OAAO,CAAC4B,GAAG,CAAC,aAAa,EAAEC,MAAM,CAAC;;MAElC;MACAnE,IAAI,CAACuC,IAAI,CAAC;QACNS,KAAK,EAAE,sBAAsB;QAC7BC,IAAI,EAAE,qDAAqD;QAC3DC,IAAI,EAAE,MAAM;QACZkB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,KAAK,EAAE,IAAI;QACXC,gBAAgB,EAAE;MACtB,CAAC,CAAC;;MAEF;MACAC,UAAU,CAAC,MAAM;QACb,MAAMC,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACR,MAAM,EAAE,QAAQ,CAAC;QAE/C,IAAI,CAACM,SAAS,EAAE;UACZzE,IAAI,CAACuC,IAAI,CAAC;YACNS,KAAK,EAAE,4BAA4B;YACnCC,IAAI,EAAE,0DAA0D;YAChEC,IAAI,EAAE,SAAS;YACfE,iBAAiB,EAAE;UACvB,CAAC,CAAC;QACN,CAAC,MAAM;UACHd,OAAO,CAAC4B,GAAG,CAAC,0BAA0B,CAAC;QAC3C;MACJ,CAAC,EAAE,GAAG,CAAC;IAEX,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DrC,IAAI,CAACuC,IAAI,CAAC;QACNS,KAAK,EAAE,QAAQ;QACfC,IAAI,EAAE,mDAAmD;QACzDC,IAAI,EAAE,OAAO;QACbE,iBAAiB,EAAE;MACvB,CAAC,CAAC;IACN;EACJ,CAAC;EAED,MAAMO,SAAS,GAAGA,CAAA,KAAM;IACpBtC,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,KAAK,EAAE,EAAE;MACTC,cAAc,EAAE;IACpB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMoD,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,KAAK,GAAG1E,QAAQ,CAAC2E,GAAG,CAACC,CAAC,IAAI,IAAIC,IAAI,CAACD,CAAC,CAACvD,cAAc,CAAC,CAACyD,WAAW,CAAC,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACL,KAAK,CAAC,CAAC,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGnF,QAAQ,CAACoF,MAAM,CAACxB,OAAO,IAAI;IAAA,IAAAyB,qBAAA,EAAAC,cAAA;IAChD,MAAMC,aAAa,GAAG,EAAAF,qBAAA,GAAAzB,OAAO,CAAC4B,YAAY,cAAAH,qBAAA,uBAApBA,qBAAA,CAAsBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAC,OAAAH,cAAA,GACvE1B,OAAO,CAACxC,KAAK,cAAAkE,cAAA,uBAAbA,cAAA,CAAeG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAC;IAEpF,MAAME,WAAW,GAAG,IAAId,IAAI,CAACjB,OAAO,CAACvC,cAAc,CAAC,CAACyD,WAAW,CAAC,CAAC,CAACc,QAAQ,CAAC,CAAC;IAC7E,MAAMC,WAAW,GAAGjF,UAAU,KAAK,KAAK,IAAI+E,WAAW,KAAK/E,UAAU;IAEtE,OAAO2E,aAAa,IAAIM,WAAW;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAGhF,WAAW,GAAGE,YAAY;EAClD,MAAM+E,gBAAgB,GAAGD,eAAe,GAAG9E,YAAY;EACvD,MAAMgF,eAAe,GAAGb,gBAAgB,CAACc,KAAK,CAACF,gBAAgB,EAAED,eAAe,CAAC;EACjF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACjB,gBAAgB,CAACkB,MAAM,GAAGrF,YAAY,CAAC;EAEpE,MAAMsF,QAAQ,GAAIC,UAAU,IAAKxF,cAAc,CAACwF,UAAU,CAAC;;EAE3D;EACAhH,KAAK,CAACE,SAAS,CAAC,MAAM;IAClBsB,cAAc,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,CAACL,UAAU,EAAEE,UAAU,CAAC,CAAC;EAE5B,IAAIV,OAAO,EAAE;IACT,oBACIX,KAAA,CAAAiH,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BxH,KAAA,CAAAiH,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BxH,KAAA,CAAAiH,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,+BAA6B,CAC/B,CAAC;EAEd;EAEA,oBACIxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sCAA2B,CAAC,eAChCxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBxH,KAAA,CAAAiH,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB5B,gBAAgB,CAACkB,MAAM,EAAC,6BACvB,CAAC,eACP9G,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,GAAG,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAS,CAAE;IAAAT,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3DxH,KAAA,CAAAiH,aAAA;IACIC,SAAS,EAAC,iBAAiB;IAC3BW,OAAO,EAAEA,CAAA,KAAM/G,YAAY,CAAC,IAAI,CAAE;IAAAqG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAElCxH,KAAA,CAAAiH,aAAA;IAAKa,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,uBACjC,CAEX,CACJ,CACJ,CAAC,EAGL,CAACzF,OAAO,iBACL/B,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MACRO,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE;IACZ,CAAE;IAAAjB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExH,KAAA,CAAAiH,aAAA;IAAGQ,KAAK,EAAE;MAAEY,MAAM,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gJAG1C,CACF,CACR,eAGDxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAACO,KAAK,EAAE;MACpCC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXQ,YAAY,EAAE,MAAM;MACpBH,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE;IAClB,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAACO,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAE,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3CxH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,yEAAyD;IACrEC,KAAK,EAAEvH,UAAW;IAClBwH,QAAQ,EAAG3F,CAAC,IAAK5B,aAAa,CAAC4B,CAAC,CAAC4F,MAAM,CAACF,KAAK,CAAE;IAC/CjB,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE;IAClB,CAAE;IAAAf,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBxH,KAAA,CAAAiH,aAAA;IACIyB,KAAK,EAAErH,UAAW;IAClBsH,QAAQ,EAAG3F,CAAC,IAAK1B,aAAa,CAAC0B,CAAC,CAAC4F,MAAM,CAACF,KAAK,CAAE;IAC/CjB,KAAK,EAAE;MACHO,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,gBAAgB;MACxBF,YAAY,EAAE,KAAK;MACnBY,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFxH,KAAA,CAAAiH,aAAA;IAAQyB,KAAK,EAAC,KAAK;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAAyB,CAAC,EAC7CtC,cAAc,CAAC,CAAC,CAACE,GAAG,CAAC2D,IAAI,iBACtB/I,KAAA,CAAAiH,aAAA;IAAQ+B,GAAG,EAAED,IAAK;IAACL,KAAK,EAAEK,IAAK;IAAA5B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEuB,IAAa,CACjD,CACG,CACP,CACJ,CAAC,eAEN/I,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB5B,gBAAgB,CAACkB,MAAM,KAAK,CAAC,gBAC1B9G,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBxH,KAAA,CAAAiH,aAAA;IAAKa,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,kBAAe;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC7CxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,4BAAuB,CAAC,EAC1BrG,UAAU,iBACPnB,KAAA,CAAAiH,aAAA;IACIY,OAAO,EAAEA,CAAA,KAAMzG,aAAa,CAAC,EAAE,CAAE;IACjC8F,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,sBAEO,CAEX,CAAC,gBAENxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1B5B,gBAAgB,CAACR,GAAG,CAAEf,OAAO,iBAC1BrE,KAAA,CAAAiH,aAAA;IAAK+B,GAAG,EAAE3E,OAAO,CAACjB,EAAG;IAAC8D,SAAS,EAAC,cAAc;IAACO,KAAK,EAAE;MAClDQ,eAAe,EAAE,OAAO;MACxBC,YAAY,EAAE,MAAM;MACpBF,OAAO,EAAE,MAAM;MACfG,YAAY,EAAE,MAAM;MACpBc,SAAS,EAAE,4BAA4B;MACvCb,MAAM,EAAE,mBAAmB;MAC3Bc,UAAU,EAAE;IAChB,CAAE;IACFC,YAAY,EAAGnG,CAAC,IAAK;MACjBA,CAAC,CAACoG,aAAa,CAAC3B,KAAK,CAAC4B,SAAS,GAAG,kBAAkB;MACpDrG,CAAC,CAACoG,aAAa,CAAC3B,KAAK,CAACwB,SAAS,GAAG,6BAA6B;IACnE,CAAE;IACFK,YAAY,EAAGtG,CAAC,IAAK;MACjBA,CAAC,CAACoG,aAAa,CAAC3B,KAAK,CAAC4B,SAAS,GAAG,eAAe;MACjDrG,CAAC,CAACoG,aAAa,CAAC3B,KAAK,CAACwB,SAAS,GAAG,4BAA4B;IAClE,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExH,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEE,UAAU,EAAE,YAAY;MAAED,GAAG,EAAE;IAAO,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACnExH,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MACR8B,QAAQ,EAAE,QAAQ;MAClBjB,KAAK,EAAE,SAAS;MAChBQ,QAAQ,EAAE;IACd,CAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAEE,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MAAEc,IAAI,EAAE;IAAE,CAAE;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBxH,KAAA,CAAAiH,aAAA;IAAIQ,KAAK,EAAE;MACPY,MAAM,EAAE,YAAY;MACpBC,KAAK,EAAE,SAAS;MAChBiB,QAAQ,EAAE;IACd,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGnD,OAAO,CAACxC,KACT,CAAC,eACL7B,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MACRC,OAAO,EAAE,MAAM;MACf8B,aAAa,EAAE,QAAQ;MACvB7B,GAAG,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExH,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEE,UAAU,EAAE,QAAQ;MAAED,GAAG,EAAE;IAAM,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9DxH,KAAA,CAAAiH,aAAA;IAAMQ,KAAK,EAAE;MAAE8B,QAAQ,EAAE;IAAS,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAQ,CAAC,eAC9CxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASnD,OAAO,CAAC4B,YAAqB,CAAC,eACvCjG,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAK,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAOQ,KAAK,EAAE;MAAEa,KAAK,EAAE;IAAU,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAEnD,OAAO,CAACoF,cAAsB,CAClE,CACJ,CAAC,eACNzJ,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEE,UAAU,EAAE,QAAQ;MAAED,GAAG,EAAE;IAAM,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9DxH,KAAA,CAAAiH,aAAA;IAAMQ,KAAK,EAAE;MAAE8B,QAAQ,EAAE;IAAS,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,cAAQ,CAAC,eAC9CxH,KAAA,CAAAiH,aAAA;IAAMQ,KAAK,EAAE;MACTO,OAAO,EAAE,UAAU;MACnBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,MAAM;MACpBqB,QAAQ,EAAE,OAAO;MACjBG,UAAU,EAAE;IAChB,CAAE;IAAAvC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACG,IAAIlC,IAAI,CAACjB,OAAO,CAACvC,cAAc,CAAC,CAAC6H,kBAAkB,CAAC,OAAO,EAAE;IAC1DZ,IAAI,EAAE,SAAS;IACfa,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE;EACT,CAAC,CACC,CACL,CACJ,CAAC,eAGN7J,KAAA,CAAAiH,aAAA;IAAKQ,KAAK,EAAE;MACRqC,SAAS,EAAE,MAAM;MACjBpC,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,MAAM;MACXC,UAAU,EAAE,QAAQ;MACpBmC,QAAQ,EAAE;IACd,CAAE;IAAA5C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEExH,KAAA,CAAAiH,aAAA;IACIY,OAAO,EAAEA,CAAA,KAAM7D,WAAW,CAACK,OAAO,CAACjB,EAAE,CAAE;IACvCE,KAAK,EAAC,yDAA0C;IAChDmE,KAAK,EAAE;MACHQ,eAAe,EAAE,SAAS;MAC1BK,KAAK,EAAE,OAAO;MACdF,MAAM,EAAE,MAAM;MACdF,YAAY,EAAE,KAAK;MACnBF,OAAO,EAAE,UAAU;MACnBuB,QAAQ,EAAE,MAAM;MAChBG,UAAU,EAAE,KAAK;MACjBM,MAAM,EAAE,SAAS;MACjBtC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,GAAG,EAAE,KAAK;MACVuB,UAAU,EAAE,eAAe;MAC3BD,SAAS,EAAE,SAAS;MACpBH,QAAQ,EAAE,OAAO;MACjBmB,cAAc,EAAE;IACpB,CAAE;IACFd,YAAY,EAAGnG,CAAC,IAAK;MACjBA,CAAC,CAAC4F,MAAM,CAACnB,KAAK,CAACQ,eAAe,GAAG,SAAS;MAC1CjF,CAAC,CAAC4F,MAAM,CAACnB,KAAK,CAAC4B,SAAS,GAAG,kBAAkB;MAC7CrG,CAAC,CAAC4F,MAAM,CAACnB,KAAK,CAACwB,SAAS,GAAG,SAAS;IACxC,CAAE;IACFK,YAAY,EAAGtG,CAAC,IAAK;MACjBA,CAAC,CAAC4F,MAAM,CAACnB,KAAK,CAACQ,eAAe,GAAG,SAAS;MAC1CjF,CAAC,CAAC4F,MAAM,CAACnB,KAAK,CAAC4B,SAAS,GAAG,eAAe;MAC1CrG,CAAC,CAAC4F,MAAM,CAACnB,KAAK,CAACwB,SAAS,GAAG,SAAS;IACxC,CAAE;IAAA9B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gCAEO,CAAC,EAERzF,OAAO,iBACJ/B,KAAA,CAAAiH,aAAA,CAAAjH,KAAA,CAAAkK,QAAA,qBACIlK,KAAA,CAAAiH,aAAA;IACIC,SAAS,EAAC,wBAAwB;IAClCW,OAAO,EAAEA,CAAA,KAAMzD,UAAU,CAACC,OAAO,CAAE;IACnCf,KAAK,EAAC,UAAU;IAChBmE,KAAK,EAAE;MACHC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,GAAG,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFxH,KAAA,CAAAiH,aAAA;IAAKa,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAACN,KAAK,EAAE;MAAEoB,KAAK,EAAE,MAAM;MAAEsB,MAAM,EAAE;IAAO,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,YAE5E,CAAC,eACTxH,KAAA,CAAAiH,aAAA;IACIC,SAAS,EAAC,uBAAuB;IACjCW,OAAO,EAAEA,CAAA,KAAMvD,YAAY,CAACD,OAAO,CAACjB,EAAE,CAAE;IACxCE,KAAK,EAAC,WAAW;IACjBmE,KAAK,EAAE;MACHC,OAAO,EAAE,MAAM;MACfE,UAAU,EAAE,QAAQ;MACpBD,GAAG,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFxH,KAAA,CAAAiH,aAAA;IAAKa,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAACN,KAAK,EAAE;MAAEoB,KAAK,EAAE,MAAM;MAAEsB,MAAM,EAAE;IAAO,CAAE;IAAAhD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,aAE/E,CACV,CAEL,CACJ,CACJ,CACJ,CACR,CACA,CAER,CAAC,EAGL5B,gBAAgB,CAACkB,MAAM,GAAG,CAAC,iBACxB9G,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAACO,KAAK,EAAE;MAClCqC,SAAS,EAAE,MAAM;MACjB9B,OAAO,EAAE,MAAM;MACfC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBR,OAAO,EAAE,MAAM;MACf0C,mBAAmB,EAAE,sCAAsC;MAC3DzC,GAAG,EAAE;IACT,CAAE;IAAAR,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACExH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAlD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDxH,KAAA,CAAAiH,aAAA;IAAIQ,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC5B,gBAAgB,CAACkB,MAClB,CAAC,eACL9G,KAAA,CAAAiH,aAAA;IAAGQ,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mBAAiB,CACrE,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAlD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDxH,KAAA,CAAAiH,aAAA;IAAIQ,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,IAAIhC,GAAG,CAACI,gBAAgB,CAACR,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzD,WAAW,CAAC,CAAC,CAAC0I,IACnD,CAAC,eACLtK,KAAA,CAAAiH,aAAA;IAAGQ,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,6BAAqB,CACzE,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAlD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDxH,KAAA,CAAAiH,aAAA;IAAIQ,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxCtC,cAAc,CAAC,CAAC,CAAC4B,MAClB,CAAC,eACL9G,KAAA,CAAAiH,aAAA;IAAGQ,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAAsB,CAC1E,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,WAAW;IAACO,KAAK,EAAE;MAAE4C,SAAS,EAAE;IAAS,CAAE;IAAAlD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtDxH,KAAA,CAAAiH,aAAA;IAAIQ,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAED,MAAM,EAAE;IAAI,CAAE;IAAAlB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,IAAIhC,GAAG,CAACI,gBAAgB,CAACR,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACxD,KAAK,CAAC,CAAC,CAACyI,IAC7C,CAAC,eACLtK,KAAA,CAAAiH,aAAA;IAAGQ,KAAK,EAAE;MAAEY,MAAM,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAU,CAAE;IAAAnB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAAoB,CACxE,CACJ,CACR,EAGA3G,SAAS,IAAIkB,OAAO,iBACjB/B,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKzG,cAAc,GAAG,qBAAqB,GAAG,iBAAsB,CAAC,eACrEf,KAAA,CAAAiH,aAAA;IACIC,SAAS,EAAC,WAAW;IACrBW,OAAO,EAAEA,CAAA,KAAM;MACX/G,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvBiD,SAAS,CAAC,CAAC;IACf,CAAE;IAAAkD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFxH,KAAA,CAAAiH,aAAA;IAAKa,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAZ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNxH,KAAA,CAAAiH,aAAA;IAAMsD,QAAQ,EAAExH,YAAa;IAAAoE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,eAAiB,CAAC,eACzBxH,KAAA,CAAAiH,aAAA;IACIyB,KAAK,EAAEhH,QAAQ,CAACE,WAAY;IAC5B+G,QAAQ,EAAG3F,CAAC,IAAKrB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,WAAW,EAAEoB,CAAC,CAAC4F,MAAM,CAACF;IAAK,CAAC,CAAE;IACzE8B,QAAQ;IACRC,QAAQ,EAAE1J,cAAe;IACzB0G,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBqB,QAAQ,EAAE,MAAM;MAChBtB,eAAe,EAAElH,cAAc,GAAG,SAAS,GAAG;IAClD,CAAE;IAAAoG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFxH,KAAA,CAAAiH,aAAA;IAAQyB,KAAK,EAAC,EAAE;IAAAvB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjDvG,SAAS,CAACmE,GAAG,CAAEsF,QAAQ,iBACpB1K,KAAA,CAAAiH,aAAA;IAAQ+B,GAAG,EAAE0B,QAAQ,CAAC9I,WAAY;IAAC8G,KAAK,EAAEgC,QAAQ,CAAC9I,WAAY;IAAAuF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC1DkD,QAAQ,CAACC,GAAG,EAAC,KAAG,EAACD,QAAQ,CAACE,KAAK,EAC/BF,QAAQ,CAACG,UAAU,IAAI,KAAKH,QAAQ,CAACG,UAAU,GAC5C,CACX,CACG,CAAC,EACR9J,cAAc,iBACXf,KAAA,CAAAiH,aAAA;IAAOQ,KAAK,EAAE;MAAEa,KAAK,EAAE,SAAS;MAAEiB,QAAQ,EAAE;IAAQ,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mEAEhD,CAEV,CAAC,eAENxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,uBAAyB,CAAC,eACjCxH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAEhH,QAAQ,CAACG,KAAM;IACtB8G,QAAQ,EAAG3F,CAAC,IAAKrB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,KAAK,EAAEmB,CAAC,CAAC4F,MAAM,CAACF;IAAK,CAAC,CAAE;IACnED,WAAW,EAAC,mDAAmD;IAC/D+B,QAAQ;IACR/C,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBqB,QAAQ,EAAE;IACd,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBxH,KAAA,CAAAiH,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,oBAAyB,CAAC,eACjCxH,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAEhH,QAAQ,CAACI,cAAe;IAC/B6G,QAAQ,EAAG3F,CAAC,IAAKrB,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,cAAc,EAAEkB,CAAC,CAAC4F,MAAM,CAACF;IAAK,CAAC,CAAE;IAC5EoC,GAAG,EAAE,IAAIxF,IAAI,CAAC,CAAC,CAACyF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE;IAC5CR,QAAQ;IACR/C,KAAK,EAAE;MACHoB,KAAK,EAAE,MAAM;MACbb,OAAO,EAAE,MAAM;MACfI,MAAM,EAAE,mBAAmB;MAC3BF,YAAY,EAAE,KAAK;MACnBqB,QAAQ,EAAE;IACd,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eAENxH,KAAA,CAAAiH,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BxH,KAAA,CAAAiH,aAAA;IAAQuB,IAAI,EAAC,QAAQ;IAACtB,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5CzG,cAAc,GAAG,aAAa,GAAG,SAC9B,CAAC,eACTf,KAAA,CAAAiH,aAAA;IACIuB,IAAI,EAAC,QAAQ;IACbtB,SAAS,EAAC,mBAAmB;IAC7BW,OAAO,EAAEA,CAAA,KAAM;MACX/G,YAAY,CAAC,KAAK,CAAC;MACnBE,iBAAiB,CAAC,IAAI,CAAC;MACvBiD,SAAS,CAAC,CAAC;IACf,CAAE;IAAAkD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,gBAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAejH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module"}