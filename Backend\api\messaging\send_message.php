<?php
/**
 * API pour envoyer un nouveau message
 * Avec validation de sécurité et vérification des droits
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Vérification de la méthode HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Méthode non autorisée', 405);
    }
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('Données JSON invalides', 400);
    }
    
    // Validation des paramètres requis
    validateRequiredParams($input, ['expediteur_id', 'destinataire_id', 'message']);
    
    $expediteurId = (int)$input['expediteur_id'];
    $destinataireId = (int)$input['destinataire_id'];
    $message = sanitizeInput($input['message']);
    
    // Validation des IDs
    if ($expediteurId <= 0 || $destinataireId <= 0) {
        sendErrorResponse('IDs utilisateur invalides', 400);
    }
    
    if ($expediteurId === $destinataireId) {
        sendErrorResponse('Impossible d\'envoyer un message à soi-même', 400);
    }
    
    // Validation du message
    if (strlen($message) < 1 || strlen($message) > 5000) {
        sendErrorResponse('Le message doit contenir entre 1 et 5000 caractères', 400);
    }
    
    // Vérification des droits d'accès à la messagerie pour les deux utilisateurs
    $expediteur = verifyMessagingAccess($expediteurId);
    $destinataire = verifyMessagingAccess($destinataireId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Insertion du nouveau message
    $stmt = $pdo->prepare("
        INSERT INTO messages (
            expediteur_id, 
            destinataire_id, 
            message, 
            date_envoi, 
            lu,
            modifie,
            supprime_par_expediteur,
            supprime_par_destinataire,
            supprime_expediteur,
            supprime_destinataire
        ) VALUES (?, ?, ?, NOW(), 0, 0, 0, 0, 0, 0)
    ");
    
    $stmt->execute([$expediteurId, $destinataireId, $message]);
    $messageId = $pdo->lastInsertId();
    
    // Récupération du message créé pour la réponse
    $stmt = $pdo->prepare("
        SELECT 
            m.id,
            m.expediteur_id,
            m.destinataire_id,
            m.message,
            m.date_envoi,
            m.lu,
            exp.nom as expediteur_nom,
            dest.nom as destinataire_nom
        FROM messages m
        JOIN utilisateurs exp ON m.expediteur_id = exp.id
        JOIN utilisateurs dest ON m.destinataire_id = dest.id
        WHERE m.id = ?
    ");
    
    $stmt->execute([$messageId]);
    $newMessage = $stmt->fetch();
    
    // Log de l'activité
    logActivity($expediteurId, 'SEND_MESSAGE', "Message envoyé à l'utilisateur ID: $destinataireId");
    
    // Optionnel: Créer une notification pour le destinataire
    try {
        $notificationStmt = $pdo->prepare("
            INSERT INTO notifications (
                utilisateur_id, 
                message, 
                date_envoi, 
                lu
            ) VALUES (?, ?, NOW(), 0)
        ");
        
        $notificationMessage = "Nouveau message de " . $expediteur['role_nom'] . " " . $newMessage['expediteur_nom'];
        $notificationStmt->execute([$destinataireId, $notificationMessage]);
        
    } catch (Exception $e) {
        // Log l'erreur mais ne pas faire échouer l'envoi du message
        error_log("Erreur création notification: " . $e->getMessage());
    }
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'message' => 'Message envoyé avec succès',
        'data' => [
            'id' => (int)$newMessage['id'],
            'expediteur_id' => (int)$newMessage['expediteur_id'],
            'destinataire_id' => (int)$newMessage['destinataire_id'],
            'message' => $newMessage['message'],
            'date_envoi' => $newMessage['date_envoi'],
            'lu' => (int)$newMessage['lu'],
            'expediteur_nom' => $newMessage['expediteur_nom'],
            'destinataire_nom' => $newMessage['destinataire_nom']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Erreur send_message.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors de l\'envoi du message', 500);
}
?>
