{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\Retards.js\";\nimport React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Retards.css';\nconst Retards = () => {\n  const {\n    user\n  } = useContext(AuthContext);\n  const [retards, setRetards] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showModal, setShowModal] = useState(false);\n  const [editingRetard, setEditingRetard] = useState(null);\n  const [etudiants, setEtudiants] = useState([]);\n  const [matieres, setMatieres] = useState([]);\n  const [enseignants, setEnseignants] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const [formData, setFormData] = useState({\n    etudiant_id: '',\n    matiere_id: '',\n    enseignant_id: '',\n    date_retard: '',\n    duree_retard: '',\n    justification: ''\n  });\n  useEffect(() => {\n    fetchRetards();\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'enseignant') {\n      fetchEtudiants();\n      fetchMatieres();\n      if ((user === null || user === void 0 ? void 0 : user.role) === 'admin') {\n        fetchenseignants();\n      }\n    }\n  }, []);\n  const fetchRetards = async () => {\n    try {\n      console.log('🔄 Chargement des retards...');\n\n      // Utiliser la même API que les absences (qui fonctionne)\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/retards/index.php');\n      console.log('🔍 DEBUG RETARDS API Response:', response.data);\n      console.log('🔍 Status:', response.status);\n\n      // Vérifier si la réponse est un tableau (même logique que les absences)\n      if (Array.isArray(response.data)) {\n        setRetards(response.data);\n        console.log('✅ Retards chargés:', response.data.length);\n      } else if (response.data && response.data.error) {\n        console.error('❌ Erreur API retards:', response.data.error);\n        setRetards([]);\n      } else {\n        console.warn('⚠️ Format de réponse inattendu:', response.data);\n        setRetards([]);\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('❌ Erreur lors du chargement des retards:', error);\n      console.error('❌ Détails erreur:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n      setRetards([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchEtudiants = async () => {\n    try {\n      console.log('🔄 Chargement des étudiants...');\n\n      // Utiliser la même API que les absences (qui fonctionne)\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');\n      console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n      if (response.data && response.data.success) {\n        setEtudiants(response.data.etudiants);\n        console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n        if (response.data.etudiants.length > 0) {\n          console.log('📚 Premier étudiant:', response.data.etudiants[0]);\n        }\n      } else {\n        console.error('❌ Erreur API étudiants:', response.data.error);\n        setEtudiants([]);\n      }\n    } catch (error) {\n      var _error$response2;\n      console.error('❌ Erreur lors du chargement des étudiants:', error);\n      console.error('❌ Détails erreur:', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n      setEtudiants([]);\n    }\n  };\n  const fetchMatieres = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      setMatieres(response.data);\n    } catch (error) {\n      console.error('Erreur lors du chargement des matières:', error);\n    }\n  };\n  const fetchenseignants = async () => {\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      console.log('🔍 DEBUG ENSEIGNANTS API Response:', response.data);\n      if (response.data.success) {\n        setEnseignants(response.data.enseignants);\n        console.log('✅ Enseignants chargés:', response.data.enseignants.length);\n      } else {\n        console.error('❌ Erreur API enseignants:', response.data.error);\n        setEnseignants([]);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement des enseignants:', error);\n      setEnseignants([]);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Debug : Vérifier les données avant envoi\n    console.log('🔍 DEBUG SUBMIT RETARDS:');\n    console.log('FormData avant envoi:', formData);\n    console.log('Type de etudiant_id:', typeof formData.etudiant_id);\n    console.log('Valeur de etudiant_id:', formData.etudiant_id);\n    console.log('Étudiants disponibles:', etudiants);\n    console.log('Nombre d\\'étudiants:', etudiants.length);\n\n    // Validation des données avec messages détaillés\n    if (!formData.etudiant_id || formData.etudiant_id === '') {\n      console.error('❌ Erreur: etudiant_id vide ou undefined');\n      console.log('FormData complet:', formData);\n      Swal.fire('Erreur', 'Veuillez sélectionner un étudiant. Liste disponible: ' + etudiants.length + ' étudiant(s)', 'error');\n      return;\n    }\n    if (!formData.date_retard || formData.date_retard === '') {\n      console.error('❌ Erreur: date_retard vide ou undefined');\n      Swal.fire('Erreur', 'Veuillez sélectionner une date', 'error');\n      return;\n    }\n    if (!formData.duree_retard || formData.duree_retard === '') {\n      console.error('❌ Erreur: duree_retard vide ou undefined');\n      Swal.fire('Erreur', 'Veuillez saisir la durée du retard', 'error');\n      return;\n    }\n\n    // S'assurer que les IDs sont des nombres\n    const cleanData = {\n      etudiant_id: parseInt(formData.etudiant_id),\n      matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,\n      enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,\n      date_retard: formData.date_retard,\n      duree_retard: formData.duree_retard,\n      justification: formData.justification || null\n    };\n    console.log('🔍 Données nettoyées:', cleanData);\n    try {\n      // Utiliser la même API que les absences (qui fonctionne)\n      const url = 'http://localhost/Project_PFE/Backend/pages/retards/index.php';\n      const method = editingRetard ? 'PUT' : 'POST';\n      const data = editingRetard ? {\n        ...cleanData,\n        id: editingRetard.id\n      } : cleanData;\n      console.log('🔍 Données finales envoyées:', data);\n      const response = await axios({\n        method,\n        url,\n        data,\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('✅ Réponse serveur:', response.data);\n      Swal.fire('Succès', `Retard ${editingRetard ? 'modifié' : 'enregistré'} avec succès`, 'success');\n      setShowModal(false);\n      setEditingRetard(null);\n      resetForm();\n      fetchRetards();\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('❌ Erreur:', error);\n      Swal.fire('Erreur', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || 'Une erreur est survenue', 'error');\n    }\n  };\n  const handleEdit = retard => {\n    setEditingRetard(retard);\n    setFormData({\n      etudiant_id: retard.etudiant_id,\n      matiere_id: retard.matiere_id || '',\n      enseignant_id: retard.enseignant_id || '',\n      date_retard: retard.date_retard,\n      duree_retard: retard.duree_retard,\n      justification: retard.justification || ''\n    });\n    setShowModal(true);\n  };\n  const handleDelete = async id => {\n    const result = await Swal.fire({\n      title: 'Êtes-vous sûr?',\n      text: 'Cette action est irréversible!',\n      icon: 'warning',\n      showCancelButton: true,\n      confirmButtonColor: '#d33',\n      cancelButtonColor: '#3085d6',\n      confirmButtonText: 'Oui, supprimer!',\n      cancelButtonText: 'Annuler'\n    });\n    if (result.isConfirmed) {\n      try {\n        // Utiliser la même API que les absences (qui fonctionne)\n        await axios.delete('http://localhost/Project_PFE/Backend/pages/retards/index.php', {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          data: {\n            id\n          }\n        });\n        Swal.fire('Supprimé!', 'Le retard a été supprimé.', 'success');\n        fetchRetards();\n      } catch (error) {\n        console.error('❌ Erreur:', error);\n        Swal.fire('Erreur', 'Impossible de supprimer le retard', 'error');\n      }\n    }\n  };\n  const resetForm = () => {\n    setFormData({\n      etudiant_id: '',\n      matiere_id: '',\n      enseignant_id: '',\n      date_retard: '',\n      duree_retard: '',\n      justification: ''\n    });\n  };\n\n  // Fonctions de filtrage et pagination (comme les factures)\n  const filteredRetards = retards.filter(retard => {\n    var _retard$etudiant_nom, _retard$matiere_nom, _retard$enseignant_no;\n    const matchesSearch = ((_retard$etudiant_nom = retard.etudiant_nom) === null || _retard$etudiant_nom === void 0 ? void 0 : _retard$etudiant_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_retard$matiere_nom = retard.matiere_nom) === null || _retard$matiere_nom === void 0 ? void 0 : _retard$matiere_nom.toLowerCase().includes(searchTerm.toLowerCase())) || ((_retard$enseignant_no = retard.enseignant_nom) === null || _retard$enseignant_no === void 0 ? void 0 : _retard$enseignant_no.toLowerCase().includes(searchTerm.toLowerCase()));\n    const matchesStatus = statusFilter === 'all' || statusFilter === 'justified' && retard.justification || statusFilter === 'unjustified' && !retard.justification;\n    return matchesSearch && matchesStatus;\n  });\n\n  // Pagination\n  const totalPages = Math.ceil(filteredRetards.length / itemsPerPage);\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  const currentRetards = filteredRetards.slice(startIndex, startIndex + itemsPerPage);\n  const getJustificationBadge = justification => {\n    if (justification && justification.trim()) {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        className: \"badge badge-success\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 20\n        }\n      }, \"Justifi\\xE9\");\n    }\n    return /*#__PURE__*/React.createElement(\"span\", {\n      className: \"badge badge-danger\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 16\n      }\n    }, \"Non justifi\\xE9\");\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '-';\n    return new Date(dateString).toLocaleDateString('fr-FR');\n  };\n\n  // Vérifier si l'utilisateur est admin ou enseignant\n  const canManageRetards = (user === null || user === void 0 ? void 0 : user.role) === 'admin' || (user === null || user === void 0 ? void 0 : user.role) === 'enseignant';\n  const formatDuree = duree => {\n    // Convertir le format TIME en minutes\n    const [hours, minutes] = duree.split(':');\n    const totalMinutes = parseInt(hours) * 60 + parseInt(minutes);\n    if (totalMinutes < 60) {\n      return `${totalMinutes} min`;\n    } else {\n      const h = Math.floor(totalMinutes / 60);\n      const m = totalMinutes % 60;\n      return m > 0 ? `${h}h ${m}min` : `${h}h`;\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"loading-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"spinner\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 17\n      }\n    }), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 17\n      }\n    }, \"Chargement des retards...\"));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"page-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 17\n    }\n  }, \"\\u23F0 Gestion des Retards\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"header-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 21\n    }\n  }, filteredRetards.length, \" retard(s) trouv\\xE9(s)\", totalPages > 1 && ` • Page ${currentPage}/${totalPages}`), canManageRetards && /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-primary add-button\",\n    onClick: () => setShowModal(true),\n    title: \"Ajouter un nouveau retard - Enregistrer le retard d'un \\xE9tudiant avec date, dur\\xE9e et justification\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/plus.png\",\n    alt: \"Ajouter\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 29\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 29\n    }\n  }, \"Nouveau Retard\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"button-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCC5 Date \\u2022 \\u23F1\\uFE0F Dur\\xE9e \\u2022 \\uD83D\\uDC64 \\xC9tudiant \\u2022 \\uD83D\\uDCAC Justification\"))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filters-section\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-filters\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"search-box\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"input\", {\n    type: \"text\",\n    placeholder: \"\\uD83D\\uDD0D Rechercher par \\xE9tudiant, mati\\xE8re ou enseignant...\",\n    value: searchTerm,\n    onChange: e => setSearchTerm(e.target.value),\n    className: \"search-input\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 25\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"filter-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: statusFilter,\n    onChange: e => setStatusFilter(e.target.value),\n    className: \"filter-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"all\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 29\n    }\n  }, \"\\uD83D\\uDCCA Tous les statuts\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"justified\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 29\n    }\n  }, \"\\u2705 Justifi\\xE9s\"), /*#__PURE__*/React.createElement(\"option\", {\n    value: \"unjustified\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 29\n    }\n  }, \"\\u274C Non justifi\\xE9s\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"results-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"total-count\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 21\n    }\n  }, filteredRetards.length, \" retard(s) trouv\\xE9(s)\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"factures-grid\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 13\n    }\n  }, filteredRetards.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-data\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/clock.png\",\n    alt: \"Aucun retard\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 25\n    }\n  }), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 25\n    }\n  }, \"Aucun retard trouv\\xE9\")) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"table-responsive\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"table\", {\n    className: \"table\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"thead\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"tr\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 376,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDC64 \\xC9tudiant\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDA Mati\\xE8re\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCC5 Date\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 37\n    }\n  }, \"\\u23F1\\uFE0F Dur\\xE9e\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 380,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCDD Statut\"), /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCAC Justification\"), canManageRetards && /*#__PURE__*/React.createElement(\"th\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 58\n    }\n  }, \"\\u2699\\uFE0F Actions\"))), /*#__PURE__*/React.createElement(\"tbody\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 29\n    }\n  }, currentRetards.map(retard => /*#__PURE__*/React.createElement(\"tr\", {\n    key: retard.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"student-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 49\n    }\n  }, retard.etudiant_nom), /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 49\n    }\n  }, retard.etudiant_email))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#e8f5e8',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 45\n    }\n  }, retard.matiere_nom || '-')), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"strong\", {\n    style: {\n      color: '#fd7e14',\n      fontSize: '1em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 45\n    }\n  }, formatDate(retard.date_retard))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      padding: '4px 8px',\n      backgroundColor: '#ffeaa7',\n      borderRadius: '4px',\n      fontSize: '0.9em',\n      fontWeight: 'bold',\n      color: '#d63031'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 45\n    }\n  }, formatDuree(retard.duree_retard))), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 41\n    }\n  }, getJustificationBadge(retard.justification)), /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 41\n    }\n  }, retard.justification ? /*#__PURE__*/React.createElement(\"span\", {\n    className: \"justification-text\",\n    title: retard.justification,\n    style: {\n      display: 'inline-block',\n      maxWidth: '200px',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap',\n      padding: '4px 8px',\n      backgroundColor: '#f8f9fa',\n      borderRadius: '4px',\n      fontSize: '0.9em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 49\n    }\n  }, retard.justification) : /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      color: '#6c757d',\n      fontStyle: 'italic'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 49\n    }\n  }, \"Aucune justification\")), canManageRetards && /*#__PURE__*/React.createElement(\"td\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 45\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"action-buttons\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 49\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-warning edit-button\",\n    onClick: () => handleEdit(retard),\n    title: `Modifier le retard de ${retard.etudiant_nom} du ${formatDate(retard.date_retard)} (${formatDuree(retard.duree_retard)})`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/edit.png\",\n    alt: \"Modifier\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 456,\n      columnNumber: 57\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"btn-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 57\n    }\n  }, \"Modifier\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"btn-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 57\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 61\n    }\n  }, \"\\u270F\\uFE0F \\xC9diter dur\\xE9e/justification\"))), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"btn btn-sm btn-danger delete-button\",\n    onClick: () => handleDelete(retard.id),\n    title: `Supprimer définitivement le retard de ${retard.etudiant_nom} du ${formatDate(retard.date_retard)} (${formatDuree(retard.duree_retard)})`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 53\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/delete.png\",\n    alt: \"Supprimer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 467,\n      columnNumber: 57\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"btn-text\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 57\n    }\n  }, \"Supprimer\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"btn-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 57\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 61\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Suppression d\\xE9finitive\")))))))))), filteredRetards.length > itemsPerPage && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 25\n    }\n  }, \"Affichage de \", startIndex + 1, \" \\xE0 \", Math.min(startIndex + itemsPerPage, filteredRetards.length), \" sur \", filteredRetards.length, \" retards\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"pagination\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 489,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    className: \"pagination-btn\",\n    onClick: () => setCurrentPage(prev => Math.max(prev - 1, 1)),\n    disabled: currentPage === 1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 29\n    }\n  }, \"\\u2190 Pr\\xE9c\\xE9dent\"), [...Array(totalPages)].map((_, index) => {\n    const pageNumber = index + 1;\n    if (pageNumber === 1 || pageNumber === totalPages || pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1) {\n      return /*#__PURE__*/React.createElement(\"button\", {\n        key: pageNumber,\n        className: `pagination-btn ${currentPage === pageNumber ? 'active' : ''}`,\n        onClick: () => setCurrentPage(pageNumber),\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 41\n        }\n      }, pageNumber);\n    } else if (pageNumber === currentPage - 2 || pageNumber === currentPage + 2) {\n      return /*#__PURE__*/React.createElement(\"span\", {\n        key: pageNumber,\n        className: \"pagination-ellipsis\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 44\n        }\n      }, \"...\");\n    }\n    return null;\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"pagination-btn\",\n    onClick: () => setCurrentPage(prev => Math.min(prev + 1, totalPages)),\n    disabled: currentPage === totalPages,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 29\n    }\n  }, \"Suivant \\u2192\")))), showModal && canManageRetards && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-overlay\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 540,\n      columnNumber: 29\n    }\n  }, editingRetard ? 'Modifier le retard' : 'Nouveau retard'), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"close-btn\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingRetard(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 541,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"img\", {\n    src: \"/close.png\",\n    alt: \"Fermer\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 33\n    }\n  }))), /*#__PURE__*/React.createElement(\"form\", {\n    onSubmit: handleSubmit,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 33\n    }\n  }, \"\\xC9tudiant\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.etudiant_id,\n    onChange: e => setFormData({\n      ...formData,\n      etudiant_id: e.target.value\n    }),\n    required: true,\n    disabled: editingRetard,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 555,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un \\xE9tudiant\"), etudiants.map(etudiant => /*#__PURE__*/React.createElement(\"option\", {\n    key: etudiant.etudiant_id || etudiant.id,\n    value: etudiant.etudiant_id || etudiant.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 41\n    }\n  }, etudiant.nom, \" \", etudiant.prenom ? ` ${etudiant.prenom}` : '', \" - \", etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe')))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 570,\n      columnNumber: 33\n    }\n  }, \"Mati\\xE8re (optionnel)\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.matiere_id,\n    onChange: e => setFormData({\n      ...formData,\n      matiere_id: e.target.value\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 571,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 575,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner une mati\\xE8re\"), matieres.map(matiere => /*#__PURE__*/React.createElement(\"option\", {\n    key: matiere.id,\n    value: matiere.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 41\n    }\n  }, matiere.nom)))), (user === null || user === void 0 ? void 0 : user.role) === 'admin' && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 37\n    }\n  }, \"Enseignant (optionnel)\"), /*#__PURE__*/React.createElement(\"select\", {\n    value: formData.enseignant_id,\n    onChange: e => setFormData({\n      ...formData,\n      enseignant_id: e.target.value\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 41\n    }\n  }, \"S\\xE9lectionner un enseignant\"), enseignants.map(enseignant => /*#__PURE__*/React.createElement(\"option\", {\n    key: enseignant.enseignant_id || enseignant.id,\n    value: enseignant.enseignant_id || enseignant.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 592,\n      columnNumber: 45\n    }\n  }, enseignant.nom, \" \", enseignant.prenom ? ` ${enseignant.prenom}` : '', \" - \", enseignant.specialite || 'Spécialité non définie')))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 599,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 33\n    }\n  }, \"Date du retard\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"date\",\n    value: formData.date_retard,\n    onChange: e => setFormData({\n      ...formData,\n      date_retard: e.target.value\n    }),\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 609,\n      columnNumber: 33\n    }\n  }, \"Dur\\xE9e du retard\"), /*#__PURE__*/React.createElement(\"input\", {\n    type: \"time\",\n    value: formData.duree_retard,\n    onChange: e => setFormData({\n      ...formData,\n      duree_retard: e.target.value\n    }),\n    required: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 33\n    }\n  }), /*#__PURE__*/React.createElement(\"small\", {\n    style: {\n      color: '#6c757d',\n      fontSize: '0.85em'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 616,\n      columnNumber: 33\n    }\n  }, \"Format: HH:MM (ex: 00:15 pour 15 minutes)\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"form-group\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 620,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"label\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 621,\n      columnNumber: 33\n    }\n  }, \"Justification (optionnel)\"), /*#__PURE__*/React.createElement(\"textarea\", {\n    value: formData.justification,\n    onChange: e => setFormData({\n      ...formData,\n      justification: e.target.value\n    }),\n    placeholder: \"Motif du retard...\",\n    rows: \"3\",\n    style: {\n      width: '100%',\n      padding: '10px',\n      border: '1px solid #ced4da',\n      borderRadius: '4px',\n      fontSize: '14px',\n      resize: 'vertical'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"modal-actions\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"button\", {\n    type: \"submit\",\n    className: \"btn btn-primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 33\n    }\n  }, editingRetard ? 'Modifier' : 'Enregistrer'), /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    className: \"btn btn-secondary\",\n    onClick: () => {\n      setShowModal(false);\n      setEditingRetard(null);\n      resetForm();\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 33\n    }\n  }, \"Annuler\"))))));\n};\nexport default Retards;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "AuthContext", "axios", "<PERSON><PERSON>", "Retards", "user", "retards", "setRetards", "loading", "setLoading", "showModal", "setShowModal", "editingRetard", "setEditingRetard", "etudiants", "setEtudiants", "matieres", "set<PERSON>ati<PERSON>s", "enseignants", "setEnseignants", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "currentPage", "setCurrentPage", "itemsPerPage", "formData", "setFormData", "etudiant_id", "matiere_id", "enseignant_id", "date_retard", "duree_retard", "justification", "fetchRetards", "role", "fetchEtudiants", "fetchMatieres", "fetchenseignants", "console", "log", "response", "get", "data", "status", "Array", "isArray", "length", "error", "warn", "_error$response", "message", "success", "_error$response2", "token", "localStorage", "getItem", "headers", "Authorization", "handleSubmit", "e", "preventDefault", "fire", "cleanData", "parseInt", "url", "method", "id", "resetForm", "_error$response3", "_error$response3$data", "handleEdit", "retard", "handleDelete", "result", "title", "text", "icon", "showCancelButton", "confirmButtonColor", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "isConfirmed", "delete", "filteredRetards", "filter", "_retard$etudiant_nom", "_retard$matiere_nom", "_retard$enseignant_no", "matchesSearch", "etudiant_nom", "toLowerCase", "includes", "matiere_nom", "enseignant_nom", "matchesStatus", "totalPages", "Math", "ceil", "startIndex", "currentRetards", "slice", "getJustificationBadge", "trim", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatDate", "dateString", "Date", "toLocaleDateString", "canManageRetards", "formatDuree", "duree", "hours", "minutes", "split", "totalMinutes", "h", "floor", "m", "onClick", "src", "alt", "type", "placeholder", "value", "onChange", "target", "map", "key", "etudiant_email", "style", "padding", "backgroundColor", "borderRadius", "fontSize", "color", "fontWeight", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "fontStyle", "min", "prev", "max", "disabled", "_", "index", "pageNumber", "onSubmit", "required", "etudiant", "nom", "prenom", "classe_nom", "groupe_nom", "matiere", "enseignant", "specialite", "rows", "width", "border", "resize"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/Retards.js"], "sourcesContent": ["import React, { useState, useEffect, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport axios from 'axios';\nimport Swal from 'sweetalert2';\nimport '../css/Retards.css';\n\nconst Retards = () => {\n    const { user } = useContext(AuthContext);\n    const [retards, setRetards] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [showModal, setShowModal] = useState(false);\n    const [editingRetard, setEditingRetard] = useState(null);\n    const [etudiants, setEtudiants] = useState([]);\n    const [matieres, setMatieres] = useState([]);\n    const [enseignants, setEnseignants] = useState([]);\n    const [searchTerm, setSearchTerm] = useState('');\n    const [statusFilter, setStatusFilter] = useState('all');\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n    const [formData, setFormData] = useState({\n        etudiant_id: '',\n        matiere_id: '',\n        enseignant_id: '',\n        date_retard: '',\n        duree_retard: '',\n        justification: ''\n    });\n\n    useEffect(() => {\n        fetchRetards();\n        if (user?.role === 'admin' || user?.role === 'enseignant') {\n            fetchEtudiants();\n            fetchMatieres();\n            if (user?.role === 'admin') {\n                fetchenseignants();\n            }\n        }\n    }, []);\n\n    const fetchRetards = async () => {\n        try {\n            console.log('🔄 Chargement des retards...');\n\n            // Utiliser la même API que les absences (qui fonctionne)\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/retards/index.php');\n\n            console.log('🔍 DEBUG RETARDS API Response:', response.data);\n            console.log('🔍 Status:', response.status);\n\n            // Vérifier si la réponse est un tableau (même logique que les absences)\n            if (Array.isArray(response.data)) {\n                setRetards(response.data);\n                console.log('✅ Retards chargés:', response.data.length);\n            } else if (response.data && response.data.error) {\n                console.error('❌ Erreur API retards:', response.data.error);\n                setRetards([]);\n            } else {\n                console.warn('⚠️ Format de réponse inattendu:', response.data);\n                setRetards([]);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des retards:', error);\n            console.error('❌ Détails erreur:', error.response?.data || error.message);\n            setRetards([]);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const fetchEtudiants = async () => {\n        try {\n            console.log('🔄 Chargement des étudiants...');\n\n            // Utiliser la même API que les absences (qui fonctionne)\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php');\n\n            console.log('🔍 DEBUG ETUDIANTS API Response:', response.data);\n\n            if (response.data && response.data.success) {\n                setEtudiants(response.data.etudiants);\n                console.log('✅ Étudiants chargés:', response.data.etudiants.length);\n                if (response.data.etudiants.length > 0) {\n                    console.log('📚 Premier étudiant:', response.data.etudiants[0]);\n                }\n            } else {\n                console.error('❌ Erreur API étudiants:', response.data.error);\n                setEtudiants([]);\n            }\n        } catch (error) {\n            console.error('❌ Erreur lors du chargement des étudiants:', error);\n            console.error('❌ Détails erreur:', error.response?.data || error.message);\n            setEtudiants([]);\n        }\n    };\n\n    const fetchMatieres = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n            setMatieres(response.data);\n        } catch (error) {\n            console.error('Erreur lors du chargement des matières:', error);\n        }\n    };\n\n    const fetchenseignants = async () => {\n        try {\n            const token = localStorage.getItem('token');\n            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php', {\n                headers: { Authorization: `Bearer ${token}` }\n            });\n\n            console.log('🔍 DEBUG ENSEIGNANTS API Response:', response.data);\n\n            if (response.data.success) {\n                setEnseignants(response.data.enseignants);\n                console.log('✅ Enseignants chargés:', response.data.enseignants.length);\n            } else {\n                console.error('❌ Erreur API enseignants:', response.data.error);\n                setEnseignants([]);\n            }\n        } catch (error) {\n            console.error('Erreur lors du chargement des enseignants:', error);\n            setEnseignants([]);\n        }\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n\n        // Debug : Vérifier les données avant envoi\n        console.log('🔍 DEBUG SUBMIT RETARDS:');\n        console.log('FormData avant envoi:', formData);\n        console.log('Type de etudiant_id:', typeof formData.etudiant_id);\n        console.log('Valeur de etudiant_id:', formData.etudiant_id);\n        console.log('Étudiants disponibles:', etudiants);\n        console.log('Nombre d\\'étudiants:', etudiants.length);\n\n        // Validation des données avec messages détaillés\n        if (!formData.etudiant_id || formData.etudiant_id === '') {\n            console.error('❌ Erreur: etudiant_id vide ou undefined');\n            console.log('FormData complet:', formData);\n            Swal.fire('Erreur', 'Veuillez sélectionner un étudiant. Liste disponible: ' + etudiants.length + ' étudiant(s)', 'error');\n            return;\n        }\n\n        if (!formData.date_retard || formData.date_retard === '') {\n            console.error('❌ Erreur: date_retard vide ou undefined');\n            Swal.fire('Erreur', 'Veuillez sélectionner une date', 'error');\n            return;\n        }\n\n        if (!formData.duree_retard || formData.duree_retard === '') {\n            console.error('❌ Erreur: duree_retard vide ou undefined');\n            Swal.fire('Erreur', 'Veuillez saisir la durée du retard', 'error');\n            return;\n        }\n\n        // S'assurer que les IDs sont des nombres\n        const cleanData = {\n            etudiant_id: parseInt(formData.etudiant_id),\n            matiere_id: formData.matiere_id ? parseInt(formData.matiere_id) : null,\n            enseignant_id: formData.enseignant_id ? parseInt(formData.enseignant_id) : null,\n            date_retard: formData.date_retard,\n            duree_retard: formData.duree_retard,\n            justification: formData.justification || null\n        };\n\n        console.log('🔍 Données nettoyées:', cleanData);\n\n        try {\n            // Utiliser la même API que les absences (qui fonctionne)\n            const url = 'http://localhost/Project_PFE/Backend/pages/retards/index.php';\n            const method = editingRetard ? 'PUT' : 'POST';\n            const data = editingRetard ? { ...cleanData, id: editingRetard.id } : cleanData;\n\n            console.log('🔍 Données finales envoyées:', data);\n\n            const response = await axios({\n                method,\n                url,\n                data,\n                headers: { 'Content-Type': 'application/json' }\n            });\n\n            console.log('✅ Réponse serveur:', response.data);\n\n            Swal.fire('Succès', `Retard ${editingRetard ? 'modifié' : 'enregistré'} avec succès`, 'success');\n            setShowModal(false);\n            setEditingRetard(null);\n            resetForm();\n            fetchRetards();\n        } catch (error) {\n            console.error('❌ Erreur:', error);\n            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');\n        }\n    };\n\n    const handleEdit = (retard) => {\n        setEditingRetard(retard);\n        setFormData({\n            etudiant_id: retard.etudiant_id,\n            matiere_id: retard.matiere_id || '',\n            enseignant_id: retard.enseignant_id || '',\n            date_retard: retard.date_retard,\n            duree_retard: retard.duree_retard,\n            justification: retard.justification || ''\n        });\n        setShowModal(true);\n    };\n\n    const handleDelete = async (id) => {\n        const result = await Swal.fire({\n            title: 'Êtes-vous sûr?',\n            text: 'Cette action est irréversible!',\n            icon: 'warning',\n            showCancelButton: true,\n            confirmButtonColor: '#d33',\n            cancelButtonColor: '#3085d6',\n            confirmButtonText: 'Oui, supprimer!',\n            cancelButtonText: 'Annuler'\n        });\n\n        if (result.isConfirmed) {\n            try {\n                // Utiliser la même API que les absences (qui fonctionne)\n                await axios.delete('http://localhost/Project_PFE/Backend/pages/retards/index.php', {\n                    headers: { 'Content-Type': 'application/json' },\n                    data: { id }\n                });\n                Swal.fire('Supprimé!', 'Le retard a été supprimé.', 'success');\n                fetchRetards();\n            } catch (error) {\n                console.error('❌ Erreur:', error);\n                Swal.fire('Erreur', 'Impossible de supprimer le retard', 'error');\n            }\n        }\n    };\n\n    const resetForm = () => {\n        setFormData({\n            etudiant_id: '',\n            matiere_id: '',\n            enseignant_id: '',\n            date_retard: '',\n            duree_retard: '',\n            justification: ''\n        });\n    };\n\n    // Fonctions de filtrage et pagination (comme les factures)\n    const filteredRetards = retards.filter(retard => {\n        const matchesSearch = retard.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            retard.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            retard.enseignant_nom?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        const matchesStatus = statusFilter === 'all' ||\n                            (statusFilter === 'justified' && retard.justification) ||\n                            (statusFilter === 'unjustified' && !retard.justification);\n\n        return matchesSearch && matchesStatus;\n    });\n\n    // Pagination\n    const totalPages = Math.ceil(filteredRetards.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const currentRetards = filteredRetards.slice(startIndex, startIndex + itemsPerPage);\n\n    const getJustificationBadge = (justification) => {\n        if (justification && justification.trim()) {\n            return <span className=\"badge badge-success\">Justifié</span>;\n        }\n        return <span className=\"badge badge-danger\">Non justifié</span>;\n    };\n\n    const formatDate = (dateString) => {\n        if (!dateString) return '-';\n        return new Date(dateString).toLocaleDateString('fr-FR');\n    };\n\n    // Vérifier si l'utilisateur est admin ou enseignant\n    const canManageRetards = user?.role === 'admin' || user?.role === 'enseignant';\n\n    const formatDuree = (duree) => {\n        // Convertir le format TIME en minutes\n        const [hours, minutes] = duree.split(':');\n        const totalMinutes = parseInt(hours) * 60 + parseInt(minutes);\n        \n        if (totalMinutes < 60) {\n            return `${totalMinutes} min`;\n        } else {\n            const h = Math.floor(totalMinutes / 60);\n            const m = totalMinutes % 60;\n            return m > 0 ? `${h}h ${m}min` : `${h}h`;\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"loading-container\">\n                <div className=\"spinner\"></div>\n                <p>Chargement des retards...</p>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"factures-container\">\n            <div className=\"page-header\">\n                <h1>⏰ Gestion des Retards</h1>\n                <div className=\"header-info\">\n                    <span className=\"total-count\">\n                        {filteredRetards.length} retard(s) trouvé(s)\n                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}\n                    </span>\n                    {canManageRetards && (\n                        <button\n                            className=\"btn btn-primary add-button\"\n                            onClick={() => setShowModal(true)}\n                            title=\"Ajouter un nouveau retard - Enregistrer le retard d'un étudiant avec date, durée et justification\"\n                        >\n                            <img src=\"/plus.png\" alt=\"Ajouter\" />\n                            <span>Nouveau Retard</span>\n                            <div className=\"button-info\">\n                                <small>📅 Date • ⏱️ Durée • 👤 Étudiant • 💬 Justification</small>\n                            </div>\n                        </button>\n                    )}\n                </div>\n            </div>\n\n            {/* Filtres et recherche (comme les factures) */}\n            <div className=\"filters-section\">\n                <div className=\"search-filters\">\n                    <div className=\"search-box\">\n                        <input\n                            type=\"text\"\n                            placeholder=\"🔍 Rechercher par étudiant, matière ou enseignant...\"\n                            value={searchTerm}\n                            onChange={(e) => setSearchTerm(e.target.value)}\n                            className=\"search-input\"\n                        />\n                    </div>\n                    <div className=\"filter-group\">\n                        <select\n                            value={statusFilter}\n                            onChange={(e) => setStatusFilter(e.target.value)}\n                            className=\"filter-select\"\n                        >\n                            <option value=\"all\">📊 Tous les statuts</option>\n                            <option value=\"justified\">✅ Justifiés</option>\n                            <option value=\"unjustified\">❌ Non justifiés</option>\n                        </select>\n                    </div>\n                </div>\n                <div className=\"results-info\">\n                    <span className=\"total-count\">\n                        {filteredRetards.length} retard(s) trouvé(s)\n                    </span>\n                </div>\n            </div>\n\n            <div className=\"factures-grid\">\n                {filteredRetards.length === 0 ? (\n                    <div className=\"no-data\">\n                        <img src=\"/clock.png\" alt=\"Aucun retard\" />\n                        <p>Aucun retard trouvé</p>\n                    </div>\n                ) : (\n                    <div className=\"table-responsive\">\n                        <table className=\"table\">\n                            <thead>\n                                <tr>\n                                    <th>👤 Étudiant</th>\n                                    <th>📚 Matière</th>\n                                    <th>📅 Date</th>\n                                    <th>⏱️ Durée</th>\n                                    <th>📝 Statut</th>\n                                    <th>💬 Justification</th>\n                                    {canManageRetards && <th>⚙️ Actions</th>}\n                                </tr>\n                            </thead>\n                            <tbody>\n                                {currentRetards.map((retard) => (\n                                    <tr key={retard.id}>\n                                        <td>\n                                            <div className=\"student-info\">\n                                                <strong>{retard.etudiant_nom}</strong>\n                                                <small>{retard.etudiant_email}</small>\n                                            </div>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#e8f5e8',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em'\n                                            }}>\n                                                {retard.matiere_nom || '-'}\n                                            </span>\n                                        </td>\n                                       \n                                        <td>\n                                            <strong style={{ color: '#fd7e14', fontSize: '1em' }}>\n                                                {formatDate(retard.date_retard)}\n                                            </strong>\n                                        </td>\n                                        <td>\n                                            <span style={{\n                                                padding: '4px 8px',\n                                                backgroundColor: '#ffeaa7',\n                                                borderRadius: '4px',\n                                                fontSize: '0.9em',\n                                                fontWeight: 'bold',\n                                                color: '#d63031'\n                                            }}>\n                                                {formatDuree(retard.duree_retard)}\n                                            </span>\n                                        </td>\n                                        <td>{getJustificationBadge(retard.justification)}</td>\n                                        <td>\n                                            {retard.justification ? (\n                                                <span\n                                                    className=\"justification-text\"\n                                                    title={retard.justification}\n                                                    style={{\n                                                        display: 'inline-block',\n                                                        maxWidth: '200px',\n                                                        overflow: 'hidden',\n                                                        textOverflow: 'ellipsis',\n                                                        whiteSpace: 'nowrap',\n                                                        padding: '4px 8px',\n                                                        backgroundColor: '#f8f9fa',\n                                                        borderRadius: '4px',\n                                                        fontSize: '0.9em'\n                                                    }}\n                                                >\n                                                    {retard.justification}\n                                                </span>\n                                            ) : (\n                                                <span style={{ color: '#6c757d', fontStyle: 'italic' }}>\n                                                    Aucune justification\n                                                </span>\n                                            )}\n                                        </td>\n                                        {canManageRetards && (\n                                            <td>\n                                                <div className=\"action-buttons\">\n                                                    <button\n                                                        className=\"btn btn-sm btn-warning edit-button\"\n                                                        onClick={() => handleEdit(retard)}\n                                                        title={`Modifier le retard de ${retard.etudiant_nom} du ${formatDate(retard.date_retard)} (${formatDuree(retard.duree_retard)})`}\n                                                    >\n                                                        <img src=\"/edit.png\" alt=\"Modifier\" />\n                                                        <span className=\"btn-text\">Modifier</span>\n                                                        <div className=\"btn-info\">\n                                                            <small>✏️ Éditer durée/justification</small>\n                                                        </div>\n                                                    </button>\n                                                    <button\n                                                        className=\"btn btn-sm btn-danger delete-button\"\n                                                        onClick={() => handleDelete(retard.id)}\n                                                        title={`Supprimer définitivement le retard de ${retard.etudiant_nom} du ${formatDate(retard.date_retard)} (${formatDuree(retard.duree_retard)})`}\n                                                    >\n                                                        <img src=\"/delete.png\" alt=\"Supprimer\" />\n                                                        <span className=\"btn-text\">Supprimer</span>\n                                                        <div className=\"btn-info\">\n                                                            <small>🗑️ Suppression définitive</small>\n                                                        </div>\n                                                    </button>\n                                                </div>\n                                            </td>\n                                        )}\n                                    </tr>\n                                ))}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n\n                {/* Pagination (comme les factures) */}\n                {filteredRetards.length > itemsPerPage && (\n                    <div className=\"pagination-container\">\n                        <div className=\"pagination-info\">\n                            Affichage de {startIndex + 1} à {Math.min(startIndex + itemsPerPage, filteredRetards.length)} sur {filteredRetards.length} retards\n                        </div>\n                        <div className=\"pagination\">\n                            <button\n                                className=\"pagination-btn\"\n                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                                disabled={currentPage === 1}\n                            >\n                                ← Précédent\n                            </button>\n\n                            {[...Array(totalPages)].map((_, index) => {\n                                const pageNumber = index + 1;\n                                if (\n                                    pageNumber === 1 ||\n                                    pageNumber === totalPages ||\n                                    (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)\n                                ) {\n                                    return (\n                                        <button\n                                            key={pageNumber}\n                                            className={`pagination-btn ${currentPage === pageNumber ? 'active' : ''}`}\n                                            onClick={() => setCurrentPage(pageNumber)}\n                                        >\n                                            {pageNumber}\n                                        </button>\n                                    );\n                                } else if (\n                                    pageNumber === currentPage - 2 ||\n                                    pageNumber === currentPage + 2\n                                ) {\n                                    return <span key={pageNumber} className=\"pagination-ellipsis\">...</span>;\n                                }\n                                return null;\n                            })}\n\n                            <button\n                                className=\"pagination-btn\"\n                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                                disabled={currentPage === totalPages}\n                            >\n                                Suivant →\n                            </button>\n                        </div>\n                    </div>\n                )}\n            </div>\n\n            {/* Modal pour ajouter/modifier un retard */}\n            {showModal && canManageRetards && (\n                <div className=\"modal-overlay\">\n                    <div className=\"modal-content\">\n                        <div className=\"modal-header\">\n                            <h3>{editingRetard ? 'Modifier le retard' : 'Nouveau retard'}</h3>\n                            <button \n                                className=\"close-btn\"\n                                onClick={() => {\n                                    setShowModal(false);\n                                    setEditingRetard(null);\n                                    resetForm();\n                                }}\n                            >\n                                <img src=\"/close.png\" alt=\"Fermer\" />\n                            </button>\n                        </div>\n                        <form onSubmit={handleSubmit}>\n                            <div className=\"form-group\">\n                                <label>Étudiant</label>\n                                <select\n                                    value={formData.etudiant_id}\n                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}\n                                    required\n                                    disabled={editingRetard}\n                                >\n                                    <option value=\"\">Sélectionner un étudiant</option>\n                                    {etudiants.map((etudiant) => (\n                                        <option key={etudiant.etudiant_id || etudiant.id} value={etudiant.etudiant_id || etudiant.id}>\n                                            {etudiant.nom} {etudiant.prenom ? ` ${etudiant.prenom}` : ''} - {etudiant.classe_nom || etudiant.groupe_nom || 'Sans classe'}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                            <div className=\"form-group\">\n                                <label>Matière (optionnel)</label>\n                                <select\n                                    value={formData.matiere_id}\n                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}\n                                >\n                                    <option value=\"\">Sélectionner une matière</option>\n                                    {matieres.map((matiere) => (\n                                        <option key={matiere.id} value={matiere.id}>\n                                            {matiere.nom}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                            {user?.role === 'admin' && (\n                                <div className=\"form-group\">\n                                    <label>Enseignant (optionnel)</label>\n                                    <select\n                                        value={formData.enseignant_id}\n                                        onChange={(e) => setFormData({...formData, enseignant_id: e.target.value})}\n                                    >\n                                        <option value=\"\">Sélectionner un enseignant</option>\n                                        {enseignants.map((enseignant) => (\n                                            <option key={enseignant.enseignant_id || enseignant.id} value={enseignant.enseignant_id || enseignant.id}>\n                                                {enseignant.nom} {enseignant.prenom ? ` ${enseignant.prenom}` : ''} - {enseignant.specialite || 'Spécialité non définie'}\n                                            </option>\n                                        ))}\n                                    </select>\n                                </div>\n                            )}\n                            <div className=\"form-group\">\n                                <label>Date du retard</label>\n                                <input\n                                    type=\"date\"\n                                    value={formData.date_retard}\n                                    onChange={(e) => setFormData({...formData, date_retard: e.target.value})}\n                                    required\n                                />\n                            </div>\n                            <div className=\"form-group\">\n                                <label>Durée du retard</label>\n                                <input\n                                    type=\"time\"\n                                    value={formData.duree_retard}\n                                    onChange={(e) => setFormData({...formData, duree_retard: e.target.value})}\n                                    required\n                                />\n                                <small style={{color: '#6c757d', fontSize: '0.85em'}}>\n                                    Format: HH:MM (ex: 00:15 pour 15 minutes)\n                                </small>\n                            </div>\n                            <div className=\"form-group\">\n                                <label>Justification (optionnel)</label>\n                                <textarea\n                                    value={formData.justification}\n                                    onChange={(e) => setFormData({...formData, justification: e.target.value})}\n                                    placeholder=\"Motif du retard...\"\n                                    rows=\"3\"\n                                    style={{\n                                        width: '100%',\n                                        padding: '10px',\n                                        border: '1px solid #ced4da',\n                                        borderRadius: '4px',\n                                        fontSize: '14px',\n                                        resize: 'vertical'\n                                    }}\n                                />\n                            </div>\n                            <div className=\"modal-actions\">\n                                <button type=\"submit\" className=\"btn btn-primary\">\n                                    {editingRetard ? 'Modifier' : 'Enregistrer'}\n                                </button>\n                                <button \n                                    type=\"button\" \n                                    className=\"btn btn-secondary\"\n                                    onClick={() => {\n                                        setShowModal(false);\n                                        setEditingRetard(null);\n                                        resetForm();\n                                    }}\n                                >\n                                    Annuler\n                                </button>\n                            </div>\n                        </form>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default Retards;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,aAAa;AAC9B,OAAO,oBAAoB;AAE3B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAClB,MAAM;IAAEC;EAAK,CAAC,GAAGL,UAAU,CAACC,WAAW,CAAC;EACxC,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,YAAY,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACnC,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACrC+B,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACnB,CAAC,CAAC;EAEFnC,SAAS,CAAC,MAAM;IACZoC,YAAY,CAAC,CAAC;IACd,IAAI,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,OAAO,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,YAAY,EAAE;MACvDC,cAAc,CAAC,CAAC;MAChBC,aAAa,CAAC,CAAC;MACf,IAAI,CAAAjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,OAAO,EAAE;QACxBG,gBAAgB,CAAC,CAAC;MACtB;IACJ;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMJ,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACAK,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;;MAE3C;MACA,MAAMC,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,8DAA8D,CAAC;MAEhGH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAC5DJ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEC,QAAQ,CAACG,MAAM,CAAC;;MAE1C;MACA,IAAIC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAACE,IAAI,CAAC,EAAE;QAC9BrC,UAAU,CAACmC,QAAQ,CAACE,IAAI,CAAC;QACzBJ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAACE,IAAI,CAACI,MAAM,CAAC;MAC3D,CAAC,MAAM,IAAIN,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACK,KAAK,EAAE;QAC7CT,OAAO,CAACS,KAAK,CAAC,uBAAuB,EAAEP,QAAQ,CAACE,IAAI,CAACK,KAAK,CAAC;QAC3D1C,UAAU,CAAC,EAAE,CAAC;MAClB,CAAC,MAAM;QACHiC,OAAO,CAACU,IAAI,CAAC,iCAAiC,EAAER,QAAQ,CAACE,IAAI,CAAC;QAC9DrC,UAAU,CAAC,EAAE,CAAC;MAClB;IACJ,CAAC,CAAC,OAAO0C,KAAK,EAAE;MAAA,IAAAE,eAAA;MACZX,OAAO,CAACS,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAChET,OAAO,CAACS,KAAK,CAAC,mBAAmB,EAAE,EAAAE,eAAA,GAAAF,KAAK,CAACP,QAAQ,cAAAS,eAAA,uBAAdA,eAAA,CAAgBP,IAAI,KAAIK,KAAK,CAACG,OAAO,CAAC;MACzE7C,UAAU,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM4B,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACAG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA,MAAMC,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,mEAAmE,CAAC;MAErGH,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAE9D,IAAIF,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACS,OAAO,EAAE;QACxCtC,YAAY,CAAC2B,QAAQ,CAACE,IAAI,CAAC9B,SAAS,CAAC;QACrC0B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC9B,SAAS,CAACkC,MAAM,CAAC;QACnE,IAAIN,QAAQ,CAACE,IAAI,CAAC9B,SAAS,CAACkC,MAAM,GAAG,CAAC,EAAE;UACpCR,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEC,QAAQ,CAACE,IAAI,CAAC9B,SAAS,CAAC,CAAC,CAAC,CAAC;QACnE;MACJ,CAAC,MAAM;QACH0B,OAAO,CAACS,KAAK,CAAC,yBAAyB,EAAEP,QAAQ,CAACE,IAAI,CAACK,KAAK,CAAC;QAC7DlC,YAAY,CAAC,EAAE,CAAC;MACpB;IACJ,CAAC,CAAC,OAAOkC,KAAK,EAAE;MAAA,IAAAK,gBAAA;MACZd,OAAO,CAACS,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClET,OAAO,CAACS,KAAK,CAAC,mBAAmB,EAAE,EAAAK,gBAAA,GAAAL,KAAK,CAACP,QAAQ,cAAAY,gBAAA,uBAAdA,gBAAA,CAAgBV,IAAI,KAAIK,KAAK,CAACG,OAAO,CAAC;MACzErC,YAAY,CAAC,EAAE,CAAC;IACpB;EACJ,CAAC;EAED,MAAMuB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACA,MAAMiB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMf,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,iEAAiE,EAAE;QAChGe,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAChD,CAAC,CAAC;MACFtC,WAAW,CAACyB,QAAQ,CAACE,IAAI,CAAC;IAC9B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZT,OAAO,CAACS,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACnE;EACJ,CAAC;EAED,MAAMV,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACA,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMf,QAAQ,GAAG,MAAMxC,KAAK,CAACyC,GAAG,CAAC,uEAAuE,EAAE;QACtGe,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUJ,KAAK;QAAG;MAChD,CAAC,CAAC;MAEFf,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEhE,IAAIF,QAAQ,CAACE,IAAI,CAACS,OAAO,EAAE;QACvBlC,cAAc,CAACuB,QAAQ,CAACE,IAAI,CAAC1B,WAAW,CAAC;QACzCsB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACE,IAAI,CAAC1B,WAAW,CAAC8B,MAAM,CAAC;MAC3E,CAAC,MAAM;QACHR,OAAO,CAACS,KAAK,CAAC,2BAA2B,EAAEP,QAAQ,CAACE,IAAI,CAACK,KAAK,CAAC;QAC/D9B,cAAc,CAAC,EAAE,CAAC;MACtB;IACJ,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACZT,OAAO,CAACS,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE9B,cAAc,CAAC,EAAE,CAAC;IACtB;EACJ,CAAC;EAED,MAAMyC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;;IAElB;IACAtB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEd,QAAQ,CAAC;IAC9Ca,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,OAAOd,QAAQ,CAACE,WAAW,CAAC;IAChEW,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEd,QAAQ,CAACE,WAAW,CAAC;IAC3DW,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE3B,SAAS,CAAC;IAChD0B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE3B,SAAS,CAACkC,MAAM,CAAC;;IAErD;IACA,IAAI,CAACrB,QAAQ,CAACE,WAAW,IAAIF,QAAQ,CAACE,WAAW,KAAK,EAAE,EAAE;MACtDW,OAAO,CAACS,KAAK,CAAC,yCAAyC,CAAC;MACxDT,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEd,QAAQ,CAAC;MAC1CxB,IAAI,CAAC4D,IAAI,CAAC,QAAQ,EAAE,uDAAuD,GAAGjD,SAAS,CAACkC,MAAM,GAAG,cAAc,EAAE,OAAO,CAAC;MACzH;IACJ;IAEA,IAAI,CAACrB,QAAQ,CAACK,WAAW,IAAIL,QAAQ,CAACK,WAAW,KAAK,EAAE,EAAE;MACtDQ,OAAO,CAACS,KAAK,CAAC,yCAAyC,CAAC;MACxD9C,IAAI,CAAC4D,IAAI,CAAC,QAAQ,EAAE,gCAAgC,EAAE,OAAO,CAAC;MAC9D;IACJ;IAEA,IAAI,CAACpC,QAAQ,CAACM,YAAY,IAAIN,QAAQ,CAACM,YAAY,KAAK,EAAE,EAAE;MACxDO,OAAO,CAACS,KAAK,CAAC,0CAA0C,CAAC;MACzD9C,IAAI,CAAC4D,IAAI,CAAC,QAAQ,EAAE,oCAAoC,EAAE,OAAO,CAAC;MAClE;IACJ;;IAEA;IACA,MAAMC,SAAS,GAAG;MACdnC,WAAW,EAAEoC,QAAQ,CAACtC,QAAQ,CAACE,WAAW,CAAC;MAC3CC,UAAU,EAAEH,QAAQ,CAACG,UAAU,GAAGmC,QAAQ,CAACtC,QAAQ,CAACG,UAAU,CAAC,GAAG,IAAI;MACtEC,aAAa,EAAEJ,QAAQ,CAACI,aAAa,GAAGkC,QAAQ,CAACtC,QAAQ,CAACI,aAAa,CAAC,GAAG,IAAI;MAC/EC,WAAW,EAAEL,QAAQ,CAACK,WAAW;MACjCC,YAAY,EAAEN,QAAQ,CAACM,YAAY;MACnCC,aAAa,EAAEP,QAAQ,CAACO,aAAa,IAAI;IAC7C,CAAC;IAEDM,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuB,SAAS,CAAC;IAE/C,IAAI;MACA;MACA,MAAME,GAAG,GAAG,8DAA8D;MAC1E,MAAMC,MAAM,GAAGvD,aAAa,GAAG,KAAK,GAAG,MAAM;MAC7C,MAAMgC,IAAI,GAAGhC,aAAa,GAAG;QAAE,GAAGoD,SAAS;QAAEI,EAAE,EAAExD,aAAa,CAACwD;MAAG,CAAC,GAAGJ,SAAS;MAE/ExB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEG,IAAI,CAAC;MAEjD,MAAMF,QAAQ,GAAG,MAAMxC,KAAK,CAAC;QACzBiE,MAAM;QACND,GAAG;QACHtB,IAAI;QACJc,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB;MAClD,CAAC,CAAC;MAEFlB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAEhDzC,IAAI,CAAC4D,IAAI,CAAC,QAAQ,EAAE,UAAUnD,aAAa,GAAG,SAAS,GAAG,YAAY,cAAc,EAAE,SAAS,CAAC;MAChGD,YAAY,CAAC,KAAK,CAAC;MACnBE,gBAAgB,CAAC,IAAI,CAAC;MACtBwD,SAAS,CAAC,CAAC;MACXlC,YAAY,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOc,KAAK,EAAE;MAAA,IAAAqB,gBAAA,EAAAC,qBAAA;MACZ/B,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC9C,IAAI,CAAC4D,IAAI,CAAC,QAAQ,EAAE,EAAAO,gBAAA,GAAArB,KAAK,CAACP,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBtB,KAAK,KAAI,yBAAyB,EAAE,OAAO,CAAC;IAC1F;EACJ,CAAC;EAED,MAAMuB,UAAU,GAAIC,MAAM,IAAK;IAC3B5D,gBAAgB,CAAC4D,MAAM,CAAC;IACxB7C,WAAW,CAAC;MACRC,WAAW,EAAE4C,MAAM,CAAC5C,WAAW;MAC/BC,UAAU,EAAE2C,MAAM,CAAC3C,UAAU,IAAI,EAAE;MACnCC,aAAa,EAAE0C,MAAM,CAAC1C,aAAa,IAAI,EAAE;MACzCC,WAAW,EAAEyC,MAAM,CAACzC,WAAW;MAC/BC,YAAY,EAAEwC,MAAM,CAACxC,YAAY;MACjCC,aAAa,EAAEuC,MAAM,CAACvC,aAAa,IAAI;IAC3C,CAAC,CAAC;IACFvB,YAAY,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAM+D,YAAY,GAAG,MAAON,EAAE,IAAK;IAC/B,MAAMO,MAAM,GAAG,MAAMxE,IAAI,CAAC4D,IAAI,CAAC;MAC3Ba,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,IAAI,EAAE,SAAS;MACfC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,MAAM;MAC1BC,iBAAiB,EAAE,SAAS;MAC5BC,iBAAiB,EAAE,iBAAiB;MACpCC,gBAAgB,EAAE;IACtB,CAAC,CAAC;IAEF,IAAIR,MAAM,CAACS,WAAW,EAAE;MACpB,IAAI;QACA;QACA,MAAMlF,KAAK,CAACmF,MAAM,CAAC,8DAA8D,EAAE;UAC/E3B,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/Cd,IAAI,EAAE;YAAEwB;UAAG;QACf,CAAC,CAAC;QACFjE,IAAI,CAAC4D,IAAI,CAAC,WAAW,EAAE,2BAA2B,EAAE,SAAS,CAAC;QAC9D5B,YAAY,CAAC,CAAC;MAClB,CAAC,CAAC,OAAOc,KAAK,EAAE;QACZT,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC9C,IAAI,CAAC4D,IAAI,CAAC,QAAQ,EAAE,mCAAmC,EAAE,OAAO,CAAC;MACrE;IACJ;EACJ,CAAC;EAED,MAAMM,SAAS,GAAGA,CAAA,KAAM;IACpBzC,WAAW,CAAC;MACRC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;IACnB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMoD,eAAe,GAAGhF,OAAO,CAACiF,MAAM,CAACd,MAAM,IAAI;IAAA,IAAAe,oBAAA,EAAAC,mBAAA,EAAAC,qBAAA;IAC7C,MAAMC,aAAa,GAAG,EAAAH,oBAAA,GAAAf,MAAM,CAACmB,YAAY,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1E,UAAU,CAACyE,WAAW,CAAC,CAAC,CAAC,OAAAJ,mBAAA,GACvEhB,MAAM,CAACsB,WAAW,cAAAN,mBAAA,uBAAlBA,mBAAA,CAAoBI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1E,UAAU,CAACyE,WAAW,CAAC,CAAC,CAAC,OAAAH,qBAAA,GACpEjB,MAAM,CAACuB,cAAc,cAAAN,qBAAA,uBAArBA,qBAAA,CAAuBG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC1E,UAAU,CAACyE,WAAW,CAAC,CAAC,CAAC;IAE3F,MAAMI,aAAa,GAAG3E,YAAY,KAAK,KAAK,IACvBA,YAAY,KAAK,WAAW,IAAImD,MAAM,CAACvC,aAAc,IACrDZ,YAAY,KAAK,aAAa,IAAI,CAACmD,MAAM,CAACvC,aAAc;IAE7E,OAAOyD,aAAa,IAAIM,aAAa;EACzC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACd,eAAe,CAACtC,MAAM,GAAGtB,YAAY,CAAC;EACnE,MAAM2E,UAAU,GAAG,CAAC7E,WAAW,GAAG,CAAC,IAAIE,YAAY;EACnD,MAAM4E,cAAc,GAAGhB,eAAe,CAACiB,KAAK,CAACF,UAAU,EAAEA,UAAU,GAAG3E,YAAY,CAAC;EAEnF,MAAM8E,qBAAqB,GAAItE,aAAa,IAAK;IAC7C,IAAIA,aAAa,IAAIA,aAAa,CAACuE,IAAI,CAAC,CAAC,EAAE;MACvC,oBAAO5G,KAAA,CAAA6G,aAAA;QAAMC,SAAS,EAAC,qBAAqB;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,aAAc,CAAC;IAChE;IACA,oBAAOpH,KAAA,CAAA6G,aAAA;MAAMC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,iBAAkB,CAAC;EACnE,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IAC/B,IAAI,CAACA,UAAU,EAAE,OAAO,GAAG;IAC3B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,CAAAjH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,OAAO,IAAI,CAAA/B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,YAAY;EAE9E,MAAMmF,WAAW,GAAIC,KAAK,IAAK;IAC3B;IACA,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;IACzC,MAAMC,YAAY,GAAG3D,QAAQ,CAACwD,KAAK,CAAC,GAAG,EAAE,GAAGxD,QAAQ,CAACyD,OAAO,CAAC;IAE7D,IAAIE,YAAY,GAAG,EAAE,EAAE;MACnB,OAAO,GAAGA,YAAY,MAAM;IAChC,CAAC,MAAM;MACH,MAAMC,CAAC,GAAG1B,IAAI,CAAC2B,KAAK,CAACF,YAAY,GAAG,EAAE,CAAC;MACvC,MAAMG,CAAC,GAAGH,YAAY,GAAG,EAAE;MAC3B,OAAOG,CAAC,GAAG,CAAC,GAAG,GAAGF,CAAC,KAAKE,CAAC,KAAK,GAAG,GAAGF,CAAC,GAAG;IAC5C;EACJ,CAAC;EAED,IAAIrH,OAAO,EAAE;IACT,oBACIX,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC9BpH,KAAA,CAAA6G,aAAA;MAAKC,SAAS,EAAC,SAAS;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CAAM,CAAC,eAC/BpH,KAAA,CAAA6G,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,2BAA4B,CAC9B,CAAC;EAEd;EAEA,oBACIpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC/BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4BAAyB,CAAC,eAC9BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpH,KAAA,CAAA6G,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB3B,eAAe,CAACtC,MAAM,EAAC,yBACxB,EAACkD,UAAU,GAAG,CAAC,IAAI,WAAW1E,WAAW,IAAI0E,UAAU,EACrD,CAAC,EACNoB,gBAAgB,iBACbzH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,4BAA4B;IACtCqB,OAAO,EAAEA,CAAA,KAAMrH,YAAY,CAAC,IAAI,CAAE;IAClCiE,KAAK,EAAC,yGAAmG;IAAAgC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzGpH,KAAA,CAAA6G,aAAA;IAAKuB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,SAAS;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACrCpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,gBAAoB,CAAC,eAC3BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,kHAA0D,CAChE,CACD,CAEX,CACJ,CAAC,eAGNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IACIyB,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,sEAAsD;IAClEC,KAAK,EAAEjH,UAAW;IAClBkH,QAAQ,EAAGzE,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAAC0E,MAAM,CAACF,KAAK,CAAE;IAC/C1B,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAC3B,CACA,CAAC,eACNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpH,KAAA,CAAA6G,aAAA;IACI2B,KAAK,EAAE/G,YAAa;IACpBgH,QAAQ,EAAGzE,CAAC,IAAKtC,eAAe,CAACsC,CAAC,CAAC0E,MAAM,CAACF,KAAK,CAAE;IACjD1B,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEzBpH,KAAA,CAAA6G,aAAA;IAAQ2B,KAAK,EAAC,KAAK;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAA2B,CAAC,eAChDpH,KAAA,CAAA6G,aAAA;IAAQ2B,KAAK,EAAC,WAAW;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,qBAAmB,CAAC,eAC9CpH,KAAA,CAAA6G,aAAA;IAAQ2B,KAAK,EAAC,aAAa;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,yBAAuB,CAC/C,CACP,CACJ,CAAC,eACNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpH,KAAA,CAAA6G,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxB3B,eAAe,CAACtC,MAAM,EAAC,yBACtB,CACL,CACJ,CAAC,eAENnD,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzB3B,eAAe,CAACtC,MAAM,KAAK,CAAC,gBACzBnD,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBpH,KAAA,CAAA6G,aAAA;IAAKuB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,cAAc;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eAC3CpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,wBAAsB,CACxB,CAAC,gBAENpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7BpH,KAAA,CAAA6G,aAAA;IAAOC,SAAS,EAAC,OAAO;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,0BAAe,CAAC,eACpBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAc,CAAC,eACnBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,mBAAW,CAAC,eAChBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAY,CAAC,eACjBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,qBAAa,CAAC,eAClBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,4BAAoB,CAAC,EACxBK,gBAAgB,iBAAIzH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,sBAAc,CACvC,CACD,CAAC,eACRpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKX,cAAc,CAACkC,GAAG,CAAE/D,MAAM,iBACvB5E,KAAA,CAAA6G,aAAA;IAAI+B,GAAG,EAAEhE,MAAM,CAACL,EAAG;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACfpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAASxC,MAAM,CAACmB,YAAqB,CAAC,eACtC/F,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAQxC,MAAM,CAACiE,cAAsB,CACpC,CACL,CAAC,eACL7I,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAMiC,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGxC,MAAM,CAACsB,WAAW,IAAI,GACrB,CACN,CAAC,eAELlG,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAQiC,KAAK,EAAE;MAAEK,KAAK,EAAE,SAAS;MAAED,QAAQ,EAAE;IAAM,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChDC,UAAU,CAACzC,MAAM,CAACzC,WAAW,CAC1B,CACR,CAAC,eACLnC,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAMiC,KAAK,EAAE;MACTC,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,OAAO;MACjBE,UAAU,EAAE,MAAM;MAClBD,KAAK,EAAE;IACX,CAAE;IAAApC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACGM,WAAW,CAAC9C,MAAM,CAACxC,YAAY,CAC9B,CACN,CAAC,eACLpC,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKT,qBAAqB,CAAC/B,MAAM,CAACvC,aAAa,CAAM,CAAC,eACtDrC,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACKxC,MAAM,CAACvC,aAAa,gBACjBrC,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,oBAAoB;IAC9B/B,KAAK,EAAEH,MAAM,CAACvC,aAAc;IAC5ByG,KAAK,EAAE;MACHO,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,UAAU;MACxBC,UAAU,EAAE,QAAQ;MACpBV,OAAO,EAAE,SAAS;MAClBC,eAAe,EAAE,SAAS;MAC1BC,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE;IACd,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAEDxC,MAAM,CAACvC,aACN,CAAC,gBAEPrC,KAAA,CAAA6G,aAAA;IAAMiC,KAAK,EAAE;MAAEK,KAAK,EAAE,SAAS;MAAEO,SAAS,EAAE;IAAS,CAAE;IAAA3C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,sBAElD,CAEV,CAAC,EACJK,gBAAgB,iBACbzH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACIpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,gBAAgB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC3BpH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,oCAAoC;IAC9CqB,OAAO,EAAEA,CAAA,KAAMxD,UAAU,CAACC,MAAM,CAAE;IAClCG,KAAK,EAAE,yBAAyBH,MAAM,CAACmB,YAAY,OAAOsB,UAAU,CAACzC,MAAM,CAACzC,WAAW,CAAC,KAAKuF,WAAW,CAAC9C,MAAM,CAACxC,YAAY,CAAC,GAAI;IAAA2E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjIpH,KAAA,CAAA6G,aAAA;IAAKuB,GAAG,EAAC,WAAW;IAACC,GAAG,EAAC,UAAU;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACtCpH,KAAA,CAAA6G,aAAA;IAAMC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CAAC,eAC1CpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,+CAAoC,CAC1C,CACD,CAAC,eACTpH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,qCAAqC;IAC/CqB,OAAO,EAAEA,CAAA,KAAMtD,YAAY,CAACD,MAAM,CAACL,EAAE,CAAE;IACvCQ,KAAK,EAAE,yCAAyCH,MAAM,CAACmB,YAAY,OAAOsB,UAAU,CAACzC,MAAM,CAACzC,WAAW,CAAC,KAAKuF,WAAW,CAAC9C,MAAM,CAACxC,YAAY,CAAC,GAAI;IAAA2E,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEjJpH,KAAA,CAAA6G,aAAA;IAAKuB,GAAG,EAAC,aAAa;IAACC,GAAG,EAAC,WAAW;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAAC,eACzCpH,KAAA,CAAA6G,aAAA;IAAMC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,WAAe,CAAC,eAC3CpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,UAAU;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,8CAAiC,CACvC,CACD,CACP,CACL,CAER,CACP,CACE,CACJ,CACN,CACR,EAGA3B,eAAe,CAACtC,MAAM,GAAGtB,YAAY,iBAClC7B,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjCpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAChB,EAACZ,UAAU,GAAG,CAAC,EAAC,QAAG,EAACF,IAAI,CAACqD,GAAG,CAACnD,UAAU,GAAG3E,YAAY,EAAE4D,eAAe,CAACtC,MAAM,CAAC,EAAC,OAAK,EAACsC,eAAe,CAACtC,MAAM,EAAC,UACzH,CAAC,eACNnD,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,gBAAgB;IAC1BqB,OAAO,EAAEA,CAAA,KAAMvG,cAAc,CAACgI,IAAI,IAAItD,IAAI,CAACuD,GAAG,CAACD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAE;IAC7DE,QAAQ,EAAEnI,WAAW,KAAK,CAAE;IAAAoF,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/B,wBAEO,CAAC,EAER,CAAC,GAAGnE,KAAK,CAACoD,UAAU,CAAC,CAAC,CAACsC,GAAG,CAAC,CAACoB,CAAC,EAAEC,KAAK,KAAK;IACtC,MAAMC,UAAU,GAAGD,KAAK,GAAG,CAAC;IAC5B,IACIC,UAAU,KAAK,CAAC,IAChBA,UAAU,KAAK5D,UAAU,IACxB4D,UAAU,IAAItI,WAAW,GAAG,CAAC,IAAIsI,UAAU,IAAItI,WAAW,GAAG,CAAE,EAClE;MACE,oBACI3B,KAAA,CAAA6G,aAAA;QACI+B,GAAG,EAAEqB,UAAW;QAChBnD,SAAS,EAAE,kBAAkBnF,WAAW,KAAKsI,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;QAC1E9B,OAAO,EAAEA,CAAA,KAAMvG,cAAc,CAACqI,UAAU,CAAE;QAAAlD,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAEzC6C,UACG,CAAC;IAEjB,CAAC,MAAM,IACHA,UAAU,KAAKtI,WAAW,GAAG,CAAC,IAC9BsI,UAAU,KAAKtI,WAAW,GAAG,CAAC,EAChC;MACE,oBAAO3B,KAAA,CAAA6G,aAAA;QAAM+B,GAAG,EAAEqB,UAAW;QAACnD,SAAS,EAAC,qBAAqB;QAAAC,MAAA;QAAAC,QAAA;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA;MAAA,GAAC,KAAS,CAAC;IAC5E;IACA,OAAO,IAAI;EACf,CAAC,CAAC,eAEFpH,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,gBAAgB;IAC1BqB,OAAO,EAAEA,CAAA,KAAMvG,cAAc,CAACgI,IAAI,IAAItD,IAAI,CAACqD,GAAG,CAACC,IAAI,GAAG,CAAC,EAAEvD,UAAU,CAAC,CAAE;IACtEyD,QAAQ,EAAEnI,WAAW,KAAK0E,UAAW;IAAAU,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxC,gBAEO,CACP,CACJ,CAER,CAAC,EAGLvG,SAAS,IAAI4G,gBAAgB,iBAC1BzH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAKrG,aAAa,GAAG,oBAAoB,GAAG,gBAAqB,CAAC,eAClEf,KAAA,CAAA6G,aAAA;IACIC,SAAS,EAAC,WAAW;IACrBqB,OAAO,EAAEA,CAAA,KAAM;MACXrH,YAAY,CAAC,KAAK,CAAC;MACnBE,gBAAgB,CAAC,IAAI,CAAC;MACtBwD,SAAS,CAAC,CAAC;IACf,CAAE;IAAAuC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEFpH,KAAA,CAAA6G,aAAA;IAAKuB,GAAG,EAAC,YAAY;IAACC,GAAG,EAAC,QAAQ;IAAAtB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAChC,CACP,CAAC,eACNpH,KAAA,CAAA6G,aAAA;IAAMqD,QAAQ,EAAEnG,YAAa;IAAAgD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACzBpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,aAAe,CAAC,eACvBpH,KAAA,CAAA6G,aAAA;IACI2B,KAAK,EAAE1G,QAAQ,CAACE,WAAY;IAC5ByG,QAAQ,EAAGzE,CAAC,IAAKjC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEE,WAAW,EAAEgC,CAAC,CAAC0E,MAAM,CAACF;IAAK,CAAC,CAAE;IACzE2B,QAAQ;IACRL,QAAQ,EAAE/I,aAAc;IAAAgG,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExBpH,KAAA,CAAA6G,aAAA;IAAQ2B,KAAK,EAAC,EAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjDnG,SAAS,CAAC0H,GAAG,CAAEyB,QAAQ,iBACpBpK,KAAA,CAAA6G,aAAA;IAAQ+B,GAAG,EAAEwB,QAAQ,CAACpI,WAAW,IAAIoI,QAAQ,CAAC7F,EAAG;IAACiE,KAAK,EAAE4B,QAAQ,CAACpI,WAAW,IAAIoI,QAAQ,CAAC7F,EAAG;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACxFgD,QAAQ,CAACC,GAAG,EAAC,GAAC,EAACD,QAAQ,CAACE,MAAM,GAAG,IAAIF,QAAQ,CAACE,MAAM,EAAE,GAAG,EAAE,EAAC,KAAG,EAACF,QAAQ,CAACG,UAAU,IAAIH,QAAQ,CAACI,UAAU,IAAI,aAC3G,CACX,CACG,CACP,CAAC,eACNxK,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA0B,CAAC,eAClCpH,KAAA,CAAA6G,aAAA;IACI2B,KAAK,EAAE1G,QAAQ,CAACG,UAAW;IAC3BwG,QAAQ,EAAGzE,CAAC,IAAKjC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEG,UAAU,EAAE+B,CAAC,CAAC0E,MAAM,CAACF;IAAK,CAAC,CAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAExEpH,KAAA,CAAA6G,aAAA;IAAQ2B,KAAK,EAAC,EAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,gCAAgC,CAAC,EACjDjG,QAAQ,CAACwH,GAAG,CAAE8B,OAAO,iBAClBzK,KAAA,CAAA6G,aAAA;IAAQ+B,GAAG,EAAE6B,OAAO,CAAClG,EAAG;IAACiE,KAAK,EAAEiC,OAAO,CAAClG,EAAG;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtCqD,OAAO,CAACJ,GACL,CACX,CACG,CACP,CAAC,EACL,CAAA7J,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+B,IAAI,MAAK,OAAO,iBACnBvC,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,wBAA6B,CAAC,eACrCpH,KAAA,CAAA6G,aAAA;IACI2B,KAAK,EAAE1G,QAAQ,CAACI,aAAc;IAC9BuG,QAAQ,EAAGzE,CAAC,IAAKjC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEI,aAAa,EAAE8B,CAAC,CAAC0E,MAAM,CAACF;IAAK,CAAC,CAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAE3EpH,KAAA,CAAA6G,aAAA;IAAQ2B,KAAK,EAAC,EAAE;IAAAzB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,+BAAkC,CAAC,EACnD/F,WAAW,CAACsH,GAAG,CAAE+B,UAAU,iBACxB1K,KAAA,CAAA6G,aAAA;IAAQ+B,GAAG,EAAE8B,UAAU,CAACxI,aAAa,IAAIwI,UAAU,CAACnG,EAAG;IAACiE,KAAK,EAAEkC,UAAU,CAACxI,aAAa,IAAIwI,UAAU,CAACnG,EAAG;IAAAwC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACpGsD,UAAU,CAACL,GAAG,EAAC,GAAC,EAACK,UAAU,CAACJ,MAAM,GAAG,IAAII,UAAU,CAACJ,MAAM,EAAE,GAAG,EAAE,EAAC,KAAG,EAACI,UAAU,CAACC,UAAU,IAAI,wBAC5F,CACX,CACG,CACP,CACR,eACD3K,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,gBAAqB,CAAC,eAC7BpH,KAAA,CAAA6G,aAAA;IACIyB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAE1G,QAAQ,CAACK,WAAY;IAC5BsG,QAAQ,EAAGzE,CAAC,IAAKjC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEK,WAAW,EAAE6B,CAAC,CAAC0E,MAAM,CAACF;IAAK,CAAC,CAAE;IACzE2B,QAAQ;IAAApD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CACA,CAAC,eACNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,oBAAsB,CAAC,eAC9BpH,KAAA,CAAA6G,aAAA;IACIyB,IAAI,EAAC,MAAM;IACXE,KAAK,EAAE1G,QAAQ,CAACM,YAAa;IAC7BqG,QAAQ,EAAGzE,CAAC,IAAKjC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEM,YAAY,EAAE4B,CAAC,CAAC0E,MAAM,CAACF;IAAK,CAAC,CAAE;IAC1E2B,QAAQ;IAAApD,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACX,CAAC,eACFpH,KAAA,CAAA6G,aAAA;IAAOiC,KAAK,EAAE;MAACK,KAAK,EAAE,SAAS;MAAED,QAAQ,EAAE;IAAQ,CAAE;IAAAnC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,2CAE/C,CACN,CAAC,eACNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvBpH,KAAA,CAAA6G,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,2BAAgC,CAAC,eACxCpH,KAAA,CAAA6G,aAAA;IACI2B,KAAK,EAAE1G,QAAQ,CAACO,aAAc;IAC9BoG,QAAQ,EAAGzE,CAAC,IAAKjC,WAAW,CAAC;MAAC,GAAGD,QAAQ;MAAEO,aAAa,EAAE2B,CAAC,CAAC0E,MAAM,CAACF;IAAK,CAAC,CAAE;IAC3ED,WAAW,EAAC,oBAAoB;IAChCqC,IAAI,EAAC,GAAG;IACR9B,KAAK,EAAE;MACH+B,KAAK,EAAE,MAAM;MACb9B,OAAO,EAAE,MAAM;MACf+B,MAAM,EAAE,mBAAmB;MAC3B7B,YAAY,EAAE,KAAK;MACnBC,QAAQ,EAAE,MAAM;MAChB6B,MAAM,EAAE;IACZ,CAAE;IAAAhE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CACA,CAAC,eACNpH,KAAA,CAAA6G,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1BpH,KAAA,CAAA6G,aAAA;IAAQyB,IAAI,EAAC,QAAQ;IAACxB,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC5CrG,aAAa,GAAG,UAAU,GAAG,aAC1B,CAAC,eACTf,KAAA,CAAA6G,aAAA;IACIyB,IAAI,EAAC,QAAQ;IACbxB,SAAS,EAAC,mBAAmB;IAC7BqB,OAAO,EAAEA,CAAA,KAAM;MACXrH,YAAY,CAAC,KAAK,CAAC;MACnBE,gBAAgB,CAAC,IAAI,CAAC;MACtBwD,SAAS,CAAC,CAAC;IACf,CAAE;IAAAuC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACL,SAEO,CACP,CACH,CACL,CACJ,CAER,CAAC;AAEd,CAAC;AAED,eAAe7G,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module"}