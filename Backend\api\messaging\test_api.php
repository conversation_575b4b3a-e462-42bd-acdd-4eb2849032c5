<?php
/**
 * Script de test pour les APIs de messagerie
 * Teste toutes les fonctionnalités du système de messagerie
 */

require_once 'config.php';

// Configuration de test
$testUserId1 = 1; // Admin
$testUserId2 = 2; // Enseignant
$testMessage = "Message de test depuis l'API";

echo "<h1>🧪 Test des APIs de Messagerie</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { color: green; background: #e8f5e8; padding: 10px; margin: 10px 0; border-radius: 5px; }
    .error { color: red; background: #ffe8e8; padding: 10px; margin: 10px 0; border-radius: 5px; }
    .info { color: blue; background: #e8f0ff; padding: 10px; margin: 10px 0; border-radius: 5px; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>";

// Test 1: Connexion à la base de données
echo "<h2>1. Test de connexion à la base de données</h2>";
try {
    $pdo = getDBConnection();
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur de connexion: " . $e->getMessage() . "</div>";
    exit();
}

// Test 2: Vérification de la structure de la table messages
echo "<h2>2. Vérification de la structure de la table messages</h2>";
try {
    $stmt = $pdo->query("DESCRIBE messages");
    $columns = $stmt->fetchAll();
    echo "<div class='success'>✅ Table messages trouvée avec " . count($columns) . " colonnes</div>";
    echo "<div class='info'><strong>Colonnes:</strong><br>";
    foreach ($columns as $col) {
        echo "- " . $col['Field'] . " (" . $col['Type'] . ")<br>";
    }
    echo "</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur structure table: " . $e->getMessage() . "</div>";
}

// Test 3: Vérification des utilisateurs de test
echo "<h2>3. Vérification des utilisateurs de test</h2>";
try {
    $stmt = $pdo->prepare("
        SELECT u.id, u.nom, u.email, r.nom as role_nom 
        FROM utilisateurs u 
        JOIN roles r ON u.role_id = r.id 
        WHERE u.id IN (?, ?)
    ");
    $stmt->execute([$testUserId1, $testUserId2]);
    $users = $stmt->fetchAll();
    
    if (count($users) >= 2) {
        echo "<div class='success'>✅ Utilisateurs de test trouvés</div>";
        foreach ($users as $user) {
            echo "<div class='info'>👤 ID: {$user['id']} - {$user['nom']} ({$user['email']}) - Rôle: {$user['role_nom']}</div>";
        }
    } else {
        echo "<div class='error'>❌ Utilisateurs de test manquants</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur utilisateurs: " . $e->getMessage() . "</div>";
}

// Test 4: Test d'insertion d'un message
echo "<h2>4. Test d'insertion d'un message</h2>";
try {
    $stmt = $pdo->prepare("
        INSERT INTO messages (
            expediteur_id, destinataire_id, message, date_envoi, lu,
            modifie, supprime_par_expediteur, supprime_par_destinataire,
            supprime_expediteur, supprime_destinataire
        ) VALUES (?, ?, ?, NOW(), 0, 0, 0, 0, 0, 0)
    ");
    
    $stmt->execute([$testUserId1, $testUserId2, $testMessage]);
    $messageId = $pdo->lastInsertId();
    
    echo "<div class='success'>✅ Message inséré avec succès (ID: $messageId)</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur insertion: " . $e->getMessage() . "</div>";
}

// Test 5: Test de récupération des messages
echo "<h2>5. Test de récupération des messages</h2>";
try {
    $stmt = $pdo->prepare("
        SELECT m.*, exp.nom as expediteur_nom, dest.nom as destinataire_nom
        FROM messages m
        JOIN utilisateurs exp ON m.expediteur_id = exp.id
        JOIN utilisateurs dest ON m.destinataire_id = dest.id
        WHERE (m.expediteur_id = ? AND m.destinataire_id = ?)
        OR (m.expediteur_id = ? AND m.destinataire_id = ?)
        ORDER BY m.date_envoi DESC
        LIMIT 5
    ");
    
    $stmt->execute([$testUserId1, $testUserId2, $testUserId2, $testUserId1]);
    $messages = $stmt->fetchAll();
    
    echo "<div class='success'>✅ " . count($messages) . " message(s) récupéré(s)</div>";
    
    if (!empty($messages)) {
        echo "<div class='info'><strong>Messages récents:</strong><br>";
        foreach ($messages as $msg) {
            echo "- ID: {$msg['id']} | De: {$msg['expediteur_nom']} → À: {$msg['destinataire_nom']} | Message: " . substr($msg['message'], 0, 50) . "... | Date: {$msg['date_envoi']}<br>";
        }
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur récupération: " . $e->getMessage() . "</div>";
}

// Test 6: Test de la logique de confidentialité
echo "<h2>6. Test de la logique de confidentialité</h2>";
try {
    // Simuler une requête pour l'utilisateur 1
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total
        FROM messages m
        WHERE (m.expediteur_id = ? OR m.destinataire_id = ?)
        AND (
            (m.expediteur_id = ? AND m.supprime_par_expediteur = 0)
            OR 
            (m.destinataire_id = ? AND m.supprime_par_destinataire = 0)
        )
    ");
    
    $stmt->execute([$testUserId1, $testUserId1, $testUserId1, $testUserId1]);
    $result = $stmt->fetch();
    
    echo "<div class='success'>✅ Logique de confidentialité OK - Utilisateur 1 peut voir {$result['total']} message(s)</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur confidentialité: " . $e->getMessage() . "</div>";
}

// Test 7: Test des rôles autorisés
echo "<h2>7. Test des rôles autorisés</h2>";
try {
    $stmt = $pdo->prepare("
        SELECT u.id, u.nom, r.id as role_id, r.nom as role_nom
        FROM utilisateurs u
        JOIN roles r ON u.role_id = r.id
        WHERE r.id IN (1, 2, 4)
        LIMIT 10
    ");
    
    $stmt->execute();
    $authorizedUsers = $stmt->fetchAll();
    
    echo "<div class='success'>✅ " . count($authorizedUsers) . " utilisateur(s) autorisé(s) pour la messagerie</div>";
    
    if (!empty($authorizedUsers)) {
        echo "<div class='info'><strong>Utilisateurs autorisés:</strong><br>";
        foreach ($authorizedUsers as $user) {
            echo "- ID: {$user['id']} | {$user['nom']} | Rôle: {$user['role_nom']} (ID: {$user['role_id']})<br>";
        }
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur rôles: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Résumé des tests</h2>";
echo "<div class='info'>
    <strong>Tests effectués:</strong><br>
    ✅ Connexion base de données<br>
    ✅ Structure table messages<br>
    ✅ Utilisateurs de test<br>
    ✅ Insertion de message<br>
    ✅ Récupération de messages<br>
    ✅ Logique de confidentialité<br>
    ✅ Vérification des rôles autorisés<br>
    <br>
    <strong>Prochaines étapes:</strong><br>
    1. Tester les APIs individuelles (get_users.php, send_message.php, etc.)<br>
    2. Tester l'interface React<br>
    3. Vérifier la sécurité et les permissions<br>
</div>";
?>
