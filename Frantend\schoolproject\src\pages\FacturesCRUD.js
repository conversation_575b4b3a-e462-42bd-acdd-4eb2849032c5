import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';
import Swal from 'sweetalert2';
import '../css/Animations.css';
import '../css/Factures.css';

const FacturesCRUD = () => {
    const { user } = useContext(AuthContext);
    const [factures, setFactures] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingFacture, setEditingFacture] = useState(null);
    const [etudiants, setEtudiants] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage] = useState(10);
    const [formData, setFormData] = useState({
        etudiant_id: '',
        mois: '',
        montant: '',
        statut: 'Non payé',
        date_paiement: ''
    });

    // Vérifier si l'utilisateur est Admin
    const isAdmin = user?.role === 'Admin' || user?.role === 'admin' || user?.role === 'responsable';

    useEffect(() => {
        fetchFactures();
        if (isAdmin) {
            fetchEtudiants();
        }
    }, [isAdmin]);

    const fetchFactures = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/factures/', {
                headers: { Authorization: `Bearer ${token}` }
            });
            setFactures(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des factures:', error);
            Swal.fire('Erreur', 'Impossible de charger les factures', 'error');
        } finally {
            setLoading(false);
        }
    };

    const fetchEtudiants = async () => {
        try {
            const token = localStorage.getItem('token');
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/etudiants/etudiant.php', {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (response.data.success) {
                setEtudiants(response.data.etudiants);
            } else {
                console.error('Erreur API:', response.data.error);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des étudiants:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut créer/modifier des factures', 'error');
            return;
        }

        try {
            const token = localStorage.getItem('token');
            const url = 'http://localhost/Project_PFE/Backend/pages/factures/';
            const method = editingFacture ? 'PUT' : 'POST';
            const data = editingFacture ? { ...formData, id: editingFacture.id } : formData;

            await axios({
                method,
                url,
                data,
                headers: { 
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            Swal.fire('Succès', `Facture ${editingFacture ? 'modifiée' : 'créée'} avec succès`, 'success');
            setShowModal(false);
            setEditingFacture(null);
            resetForm();
            fetchFactures();
        } catch (error) {
            console.error('Erreur:', error);
            Swal.fire('Erreur', error.response?.data?.error || 'Une erreur est survenue', 'error');
        }
    };

    const handleEdit = (facture) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut modifier des factures', 'error');
            return;
        }

        setEditingFacture(facture);
        setFormData({
            etudiant_id: facture.etudiant_id,
            mois: facture.mois,
            montant: facture.montant,
            statut: facture.statut,
            date_paiement: facture.date_paiement || ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        if (!isAdmin) {
            Swal.fire('Erreur', 'Seul l\'administrateur peut supprimer des factures', 'error');
            return;
        }

        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action est irréversible!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer!',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                const token = localStorage.getItem('token');
                await axios.delete('http://localhost/Project_PFE/Backend/pages/factures/', {
                    headers: { 
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    data: { id }
                });
                Swal.fire('Supprimé!', 'La facture a été supprimée.', 'success');
                fetchFactures();
            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire('Erreur', 'Impossible de supprimer la facture', 'error');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            etudiant_id: '',
            mois: '',
            montant: '',
            statut: 'Non payé',
            date_paiement: ''
        });
    };

    const formatMontant = (montant) => {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'MAD'
        }).format(montant);
    };

    const getStatutBadge = (statut) => {
        const badgeClass = statut === 'Payé' ? 'badge-success' : 'badge-danger';
        return <span className={`badge ${badgeClass}`}>{statut}</span>;
    };

    // Filtrage des données
    const filteredFactures = factures.filter(facture => {
        const matchesSearch = facture.etudiant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             facture.etudiant_email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             facture.mois?.includes(searchTerm);

        const matchesStatus = statusFilter === 'all' || facture.statut === statusFilter;

        return matchesSearch && matchesStatus;
    });

    // Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentFactures = filteredFactures.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(filteredFactures.length / itemsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Reset pagination when filters change
    React.useEffect(() => {
        setCurrentPage(1);
    }, [searchTerm, statusFilter]);

    if (loading) {
        return (
            <div className="loading-container">
                <div className="spinner"></div>
                <p>Chargement des factures...</p>
            </div>
        );
    }

    return (
        <div className="factures-container">
            <div className="page-header">
                <h1>💰 Gestion des Factures</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredFactures.length} facture(s) trouvée(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {isAdmin && (
                        <button 
                            className="btn btn-primary"
                            onClick={() => setShowModal(true)}
                        >
                            <img src="/plus.png" alt="Ajouter" /> Nouvelle Facture
                        </button>
                    )}
                </div>
            </div>

            {/* Message d'information pour les non-admins */}
            {!isAdmin && (
                <div style={{
                    padding: '15px',
                    backgroundColor: '#e3f2fd',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    border: '1px solid #bbdefb'
                }}>
                    <p style={{ margin: '0', color: '#1976d2' }}>
                        ℹ️ Vous consultez vos factures en mode lecture seule. 
                        Seul l'administrateur peut créer, modifier ou supprimer des factures.
                    </p>
                </div>
            )}

            {/* Filtres */}
            <div className="filters-section" style={{
                display: 'flex',
                gap: '15px',
                marginBottom: '20px',
                padding: '15px',
                backgroundColor: '#f8f9fa',
                borderRadius: '8px'
            }}>
                <div className="search-box" style={{ flex: 1 }}>
                    <input
                        type="text"
                        placeholder="🔍 Rechercher par nom, email ou mois..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                            width: '100%',
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px'
                        }}
                    />
                </div>
                <div className="status-filter">
                    <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        style={{
                            padding: '10px',
                            border: '1px solid #ddd',
                            borderRadius: '6px',
                            minWidth: '150px'
                        }}
                    >
                        <option value="all">Tous les statuts</option>
                        <option value="Payé">Payé</option>
                        <option value="Non payé">Non payé</option>
                    </select>
                </div>
            </div>

            <div className="factures-grid">
                {filteredFactures.length === 0 ? (
                    <div className="no-data">
                        <img src="/finance.png" alt="Aucune facture" />
                        <p>Aucune facture trouvée</p>
                        {searchTerm && (
                            <button 
                                onClick={() => setSearchTerm('')}
                                className="btn btn-secondary"
                            >
                                Effacer la recherche
                            </button>
                        )}
                    </div>
                ) : (
                    <div className="table-responsive">
                        <table className="table">
                            <thead>
                                <tr>
                                    <th>👤 Étudiant</th>
                                    <th>📅 Mois</th>
                                    <th>💰 Montant</th>
                                    <th>📊 Statut</th>
                                    <th>💳 Date de paiement</th>
                                    {isAdmin && <th>⚙️ Actions</th>}
                                </tr>
                            </thead>
                            <tbody>
                                {currentFactures.map((facture) => (
                                    <tr key={facture.id}>
                                        <td>
                                            <div className="student-info">
                                                <strong>{facture.etudiant_nom}</strong>
                                                <small>{facture.etudiant_email}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <span style={{ 
                                                padding: '4px 8px', 
                                                backgroundColor: '#e3f2fd', 
                                                borderRadius: '4px',
                                                fontSize: '0.9em'
                                            }}>
                                                {facture.mois}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50', fontSize: '1.1em' }}>
                                                {formatMontant(facture.montant)}
                                            </strong>
                                        </td>
                                        <td>{getStatutBadge(facture.statut)}</td>
                                        <td>
                                            {facture.date_paiement ? (
                                                <span style={{ color: '#28a745' }}>
                                                    ✅ {new Date(facture.date_paiement).toLocaleDateString('fr-FR')}
                                                </span>
                                            ) : (
                                                <span style={{ color: '#6c757d' }}>
                                                    ⏳ En attente
                                                </span>
                                            )}
                                        </td>
                                        {isAdmin && (
                                            <td>
                                                <div className="action-buttons">
                                                    <button 
                                                        className="btn btn-sm btn-warning"
                                                        onClick={() => handleEdit(facture)}
                                                        title="Modifier"
                                                    >
                                                        <img src="/edit.png" alt="Modifier" />
                                                    </button>
                                                    <button 
                                                        className="btn btn-sm btn-danger"
                                                        onClick={() => handleDelete(facture.id)}
                                                        title="Supprimer"
                                                    >
                                                        <img src="/delete.png" alt="Supprimer" />
                                                    </button>
                                                </div>
                                            </td>
                                        )}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginTop: '20px',
                    gap: '10px'
                }}>
                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        style={{
                            opacity: currentPage === 1 ? 0.5 : 1,
                            cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
                        }}
                    >
                        ⬅️ Précédent
                    </button>

                    {/* Numéros de pages */}
                    <div style={{ display: 'flex', gap: '5px' }}>
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                            let pageNumber;
                            if (totalPages <= 5) {
                                pageNumber = i + 1;
                            } else if (currentPage <= 3) {
                                pageNumber = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                                pageNumber = totalPages - 4 + i;
                            } else {
                                pageNumber = currentPage - 2 + i;
                            }

                            return (
                                <button
                                    key={pageNumber}
                                    onClick={() => paginate(pageNumber)}
                                    style={{
                                        padding: '8px 12px',
                                        border: '1px solid #ddd',
                                        borderRadius: '4px',
                                        backgroundColor: currentPage === pageNumber ? '#007bff' : 'white',
                                        color: currentPage === pageNumber ? 'white' : '#007bff',
                                        cursor: 'pointer',
                                        fontWeight: currentPage === pageNumber ? 'bold' : 'normal'
                                    }}
                                >
                                    {pageNumber}
                                </button>
                            );
                        })}
                    </div>

                    <button
                        className="btn btn-secondary"
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        style={{
                            opacity: currentPage === totalPages ? 0.5 : 1,
                            cursor: currentPage === totalPages ? 'not-allowed' : 'pointer'
                        }}
                    >
                        Suivant ➡️
                    </button>
                </div>
            )}

            {/* Informations de pagination */}
            {filteredFactures.length > 0 && (
                <div style={{
                    textAlign: 'center',
                    marginTop: '15px',
                    padding: '10px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '4px',
                    fontSize: '14px',
                    color: '#6c757d'
                }}>
                    Affichage de {indexOfFirstItem + 1} à {Math.min(indexOfLastItem, filteredFactures.length)} sur {filteredFactures.length} facture(s)
                    {totalPages > 1 && ` • Page ${currentPage} sur ${totalPages}`}
                </div>
            )}

            {/* Statistiques */}
            {filteredFactures.length > 0 && (
                <div className="stats-section" style={{
                    marginTop: '30px',
                    padding: '20px',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '15px'
                }}>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#28a745', margin: '0' }}>
                            {filteredFactures.filter(f => f.statut === 'Payé').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Factures payées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#dc3545', margin: '0' }}>
                            {filteredFactures.filter(f => f.statut === 'Non payé').length}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Factures impayées</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#007bff', margin: '0' }}>
                            {formatMontant(
                                filteredFactures
                                    .filter(f => f.statut === 'Payé')
                                    .reduce((sum, f) => sum + parseFloat(f.montant), 0)
                            )}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>Total encaissé</p>
                    </div>
                    <div className="stat-card" style={{ textAlign: 'center' }}>
                        <h3 style={{ color: '#ffc107', margin: '0' }}>
                            {formatMontant(
                                filteredFactures
                                    .filter(f => f.statut === 'Non payé')
                                    .reduce((sum, f) => sum + parseFloat(f.montant), 0)
                            )}
                        </h3>
                        <p style={{ margin: '5px 0 0 0', color: '#6c757d' }}>En attente</p>
                    </div>
                </div>
            )}

            {/* Modal pour ajouter/modifier une facture */}
            {showModal && isAdmin && (
                <div className="modal-overlay">
                    <div className="modal-content">
                        <div className="modal-header">
                            <h3>{editingFacture ? 'Modifier la facture' : 'Nouvelle facture'}</h3>
                            <button
                                className="close-btn"
                                onClick={() => {
                                    setShowModal(false);
                                    setEditingFacture(null);
                                    resetForm();
                                }}
                            >
                                <img src="/close.png" alt="Fermer" />
                            </button>
                        </div>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Étudiant *</label>
                                <select
                                    value={formData.etudiant_id}
                                    onChange={(e) => setFormData({...formData, etudiant_id: e.target.value})}
                                    required
                                    disabled={editingFacture}
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px',
                                        backgroundColor: editingFacture ? '#e9ecef' : 'white'
                                    }}
                                >
                                    <option value="">Sélectionner un étudiant</option>
                                    {etudiants.map((etudiant) => (
                                        <option key={etudiant.etudiant_id} value={etudiant.etudiant_id}>
                                            {etudiant.nom} - {etudiant.email}
                                            {etudiant.classe_nom && ` (${etudiant.classe_nom})`}
                                        </option>
                                    ))}
                                </select>
                                {editingFacture && (
                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>
                                        L'étudiant ne peut pas être modifié après création
                                    </small>
                                )}
                            </div>

                            <div className="form-group">
                                <label>Mois (YYYY-MM) *</label>
                                <input
                                    type="month"
                                    value={formData.mois}
                                    onChange={(e) => setFormData({...formData, mois: e.target.value})}
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Montant (MAD) *</label>
                                <input
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    value={formData.montant}
                                    onChange={(e) => setFormData({...formData, montant: e.target.value})}
                                    placeholder="Ex: 1500.00"
                                    required
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                            </div>

                            <div className="form-group">
                                <label>Statut de paiement</label>
                                <select
                                    value={formData.statut}
                                    onChange={(e) => setFormData({...formData, statut: e.target.value})}
                                    style={{
                                        width: '100%',
                                        padding: '10px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                >
                                    <option value="Non payé">Non payé</option>
                                    <option value="Payé">Payé</option>
                                </select>
                            </div>

                            {formData.statut === 'Payé' && (
                                <div className="form-group">
                                    <label>Date de paiement</label>
                                    <input
                                        type="date"
                                        value={formData.date_paiement}
                                        onChange={(e) => setFormData({...formData, date_paiement: e.target.value})}
                                        max={new Date().toISOString().split('T')[0]}
                                        style={{
                                            width: '100%',
                                            padding: '10px',
                                            border: '1px solid #ced4da',
                                            borderRadius: '4px',
                                            fontSize: '14px'
                                        }}
                                    />
                                    <small style={{ color: '#6c757d', fontSize: '0.8em' }}>
                                        Laissez vide pour utiliser la date actuelle
                                    </small>
                                </div>
                            )}

                            <div className="modal-actions">
                                <button type="submit" className="btn btn-primary">
                                    {editingFacture ? '💾 Modifier' : '➕ Créer'}
                                </button>
                                <button
                                    type="button"
                                    className="btn btn-secondary"
                                    onClick={() => {
                                        setShowModal(false);
                                        setEditingFacture(null);
                                        resetForm();
                                    }}
                                >
                                    ❌ Annuler
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FacturesCRUD;
