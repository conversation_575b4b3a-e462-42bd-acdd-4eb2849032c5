<?php
/**
 * API pour récupérer les messages d'une conversation spécifique
 * Avec confidentialité stricte - seuls les messages entre les deux utilisateurs
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Récupération et validation des paramètres
    $userId = $_GET['user_id'] ?? null;
    $otherUserId = $_GET['other_user_id'] ?? null;
    
    if (!$userId || !is_numeric($userId)) {
        sendErrorResponse('ID utilisateur manquant ou invalide', 400);
    }
    
    if (!$otherUserId || !is_numeric($otherUserId)) {
        sendErrorResponse('ID destinataire manquant ou invalide', 400);
    }
    
    $userId = (int)$userId;
    $otherUserId = (int)$otherUserId;
    
    // Vérification des droits d'accès à la messagerie
    verifyMessagingAccess($userId);
    verifyMessagingAccess($otherUserId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Requête pour récupérer les messages avec confidentialité stricte
    // Seuls les messages entre ces deux utilisateurs spécifiques
    $stmt = $pdo->prepare("
        SELECT 
            m.id,
            m.expediteur_id,
            m.destinataire_id,
            m.message,
            m.date_envoi,
            m.lu,
            m.modifie,
            m.date_modification,
            m.message_original,
            exp.nom as expediteur_nom,
            dest.nom as destinataire_nom
        FROM messages m
        JOIN utilisateurs exp ON m.expediteur_id = exp.id
        JOIN utilisateurs dest ON m.destinataire_id = dest.id
        WHERE (
            (m.expediteur_id = ? AND m.destinataire_id = ?)
            OR 
            (m.expediteur_id = ? AND m.destinataire_id = ?)
        )
        AND (
            (m.expediteur_id = ? AND m.supprime_par_expediteur = 0)
            OR 
            (m.destinataire_id = ? AND m.supprime_par_destinataire = 0)
        )
        ORDER BY m.date_envoi ASC
    ");
    
    $stmt->execute([
        $userId, $otherUserId,      // Messages de userId vers otherUserId
        $otherUserId, $userId,      // Messages de otherUserId vers userId
        $userId, $userId            // Vérification suppression pour userId
    ]);
    
    $messages = $stmt->fetchAll();
    
    // Formatage des données
    $formattedMessages = [];
    foreach ($messages as $msg) {
        $formattedMessages[] = [
            'id' => (int)$msg['id'],
            'expediteur_id' => (int)$msg['expediteur_id'],
            'destinataire_id' => (int)$msg['destinataire_id'],
            'message' => $msg['message'],
            'date_envoi' => $msg['date_envoi'],
            'lu' => (int)$msg['lu'],
            'modifie' => (int)$msg['modifie'],
            'date_modification' => $msg['date_modification'],
            'message_original' => $msg['message_original'],
            'expediteur_nom' => $msg['expediteur_nom'],
            'destinataire_nom' => $msg['destinataire_nom']
        ];
    }
    
    // Log de l'activité
    logActivity($userId, 'GET_MESSAGES', "Récupération messages avec utilisateur ID: $otherUserId");
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'messages' => $formattedMessages,
        'total' => count($formattedMessages),
        'conversation' => [
            'user_id' => $userId,
            'other_user_id' => $otherUserId
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Erreur get_messages.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors de la récupération des messages', 500);
}
?>
