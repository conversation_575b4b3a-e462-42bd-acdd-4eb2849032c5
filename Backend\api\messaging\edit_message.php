<?php
/**
 * API pour modifier un message existant
 * Seul l'expéditeur peut modifier son propre message
 */

require_once 'config.php';

try {
    // Vérification de l'authentification
    verifyAuth();
    
    // Vérification de la méthode HTTP
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Méthode non autorisée', 405);
    }
    
    // Récupération des données JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendErrorResponse('Données JSON invalides', 400);
    }
    
    // Validation des paramètres requis
    validateRequiredParams($input, ['message_id', 'user_id', 'new_message']);
    
    $messageId = (int)$input['message_id'];
    $userId = (int)$input['user_id'];
    $newMessage = sanitizeInput($input['new_message']);
    
    // Validation des données
    if ($messageId <= 0 || $userId <= 0) {
        sendErrorResponse('IDs invalides', 400);
    }
    
    if (strlen($newMessage) < 1 || strlen($newMessage) > 5000) {
        sendErrorResponse('Le message doit contenir entre 1 et 5000 caractères', 400);
    }
    
    // Vérification des droits d'accès à la messagerie
    verifyMessagingAccess($userId);
    
    // Connexion à la base de données
    $pdo = getDBConnection();
    
    // Vérification que le message existe et appartient à l'utilisateur
    $stmt = $pdo->prepare("
        SELECT id, expediteur_id, message, modifie, message_original
        FROM messages 
        WHERE id = ? AND expediteur_id = ?
    ");
    
    $stmt->execute([$messageId, $userId]);
    $message = $stmt->fetch();
    
    if (!$message) {
        sendErrorResponse('Message non trouvé ou vous n\'êtes pas autorisé à le modifier', 404);
    }
    
    // Sauvegarder le message original si c'est la première modification
    $originalMessage = $message['message_original'] ?: $message['message'];
    
    // Mise à jour du message
    $stmt = $pdo->prepare("
        UPDATE messages 
        SET 
            message = ?,
            modifie = 1,
            date_modification = NOW(),
            message_original = ?
        WHERE id = ? AND expediteur_id = ?
    ");
    
    $stmt->execute([$newMessage, $originalMessage, $messageId, $userId]);
    
    if ($stmt->rowCount() === 0) {
        sendErrorResponse('Aucune modification effectuée', 400);
    }
    
    // Récupération du message modifié
    $stmt = $pdo->prepare("
        SELECT 
            m.id,
            m.expediteur_id,
            m.destinataire_id,
            m.message,
            m.date_envoi,
            m.modifie,
            m.date_modification,
            m.message_original,
            exp.nom as expediteur_nom,
            dest.nom as destinataire_nom
        FROM messages m
        JOIN utilisateurs exp ON m.expediteur_id = exp.id
        JOIN utilisateurs dest ON m.destinataire_id = dest.id
        WHERE m.id = ?
    ");
    
    $stmt->execute([$messageId]);
    $updatedMessage = $stmt->fetch();
    
    // Log de l'activité
    logActivity($userId, 'EDIT_MESSAGE', "Message ID: $messageId modifié");
    
    // Réponse de succès
    echo json_encode([
        'success' => true,
        'message' => 'Message modifié avec succès',
        'data' => [
            'id' => (int)$updatedMessage['id'],
            'expediteur_id' => (int)$updatedMessage['expediteur_id'],
            'destinataire_id' => (int)$updatedMessage['destinataire_id'],
            'message' => $updatedMessage['message'],
            'date_envoi' => $updatedMessage['date_envoi'],
            'modifie' => (int)$updatedMessage['modifie'],
            'date_modification' => $updatedMessage['date_modification'],
            'message_original' => $updatedMessage['message_original'],
            'expediteur_nom' => $updatedMessage['expediteur_nom'],
            'destinataire_nom' => $updatedMessage['destinataire_nom']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Erreur edit_message.php: " . $e->getMessage());
    sendErrorResponse('Erreur lors de la modification du message', 500);
}
?>
