import React, { useState, useEffect, useRef, useContext, useCallback } from 'react';
import { AuthContext } from '../context/AuthContext';
import '../styles/MessagingSystem.css';

const MessagingSystem = () => {
    // 🔐 Contexte d'authentification
    const { user, isLoading } = useContext(AuthContext);
    
    // 📊 États principaux
    const [conversations, setConversations] = useState([]);
    const [selectedConversation, setSelectedConversation] = useState(null);
    const [messages, setMessages] = useState([]);
    const [newMessage, setNewMessage] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [users, setUsers] = useState([]);
    
    // 🎨 États UI
    const [showNewConversation, setShowNewConversation] = useState(false);
    const [selectedUser, setSelectedUser] = useState('');
    const [contextMenu, setContextMenu] = useState({ show: false, x: 0, y: 0, messageId: null });
    const [editingMessage, setEditingMessage] = useState(null);
    const [editText, setEditText] = useState('');
    
    // 📱 Références
    const messagesEndRef = useRef(null);
    const contextMenuRef = useRef(null);
    const messageInputRef = useRef(null);
    
    // 🔍 Fonction pour obtenir l'ID utilisateur sécurisé
    const getCurrentUserId = useCallback(() => {
        if (user) {
            // Vérifier différentes propriétés possibles pour l'ID
            const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];
            for (const id of possibleIds) {
                if (id && !isNaN(id)) {
                    return parseInt(id);
                }
            }
        }
        
        // Fallback localStorage
        try {
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
                const userData = JSON.parse(storedUser);
                const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];
                for (const id of possibleIds) {
                    if (id && !isNaN(id)) {
                        return parseInt(id);
                    }
                }
            }
        } catch (error) {
            console.warn('Erreur localStorage:', error);
        }
        
        return null;
    }, [user]);
    
    // 🔐 Vérification des droits d'accès
    const hasMessagingAccess = useCallback(() => {
        if (!user || !user.role_id) return false;
        
        // Seuls les Parents (4), Enseignants (2), et Admin (1) ont accès
        const allowedRoles = [1, 2, 4];
        return allowedRoles.includes(parseInt(user.role_id));
    }, [user]);
    
    // 📡 Charger les utilisateurs autorisés
    const loadAuthorizedUsers = useCallback(async () => {
        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;
            
            const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_users.php?current_user=${currentUserId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            if (data.success) {
                setUsers(data.users);
            } else {
                console.error('Erreur chargement utilisateurs:', data.error);
            }
        } catch (error) {
            console.error('Erreur réseau utilisateurs:', error);
        }
    }, [getCurrentUserId]);
    
    // 📡 Charger les conversations
    const loadConversations = useCallback(async () => {
        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;
            
            const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_conversations.php?user_id=${currentUserId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            if (data.success) {
                setConversations(data.conversations);
            } else {
                setError(data.error || 'Erreur lors du chargement des conversations');
            }
        } catch (error) {
            setError('Erreur de connexion au serveur');
            console.error('Erreur:', error);
        }
    }, [getCurrentUserId]);
    
    // 📡 Charger les messages d'une conversation
    const loadMessages = useCallback(async (otherUserId) => {
        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;
            
            const response = await fetch(`http://localhost/Project_PFE/Backend/api/messaging/get_messages.php?user_id=${currentUserId}&other_user_id=${otherUserId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            if (data.success) {
                setMessages(data.messages);
                // Marquer les messages comme lus
                markMessagesAsRead(otherUserId);
            } else {
                console.error('Erreur chargement messages:', data.error);
            }
        } catch (error) {
            console.error('Erreur réseau messages:', error);
        }
    }, [getCurrentUserId]);
    
    // 📡 Marquer les messages comme lus
    const markMessagesAsRead = useCallback(async (otherUserId) => {
        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;
            
            await fetch('http://localhost/Project_PFE/Backend/api/messaging/mark_read.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: currentUserId,
                    other_user_id: otherUserId
                })
            });
        } catch (error) {
            console.error('Erreur marquage lu:', error);
        }
    }, [getCurrentUserId]);
    
    // 📤 Envoyer un message
    const sendMessage = useCallback(async () => {
        if (!newMessage.trim() || !selectedConversation) return;
        
        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;
            
            const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/send_message.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    expediteur_id: currentUserId,
                    destinataire_id: selectedConversation.other_user_id,
                    message: newMessage.trim()
                })
            });
            
            const data = await response.json();
            if (data.success) {
                setNewMessage('');
                loadMessages(selectedConversation.other_user_id);
                loadConversations();
            } else {
                alert('Erreur lors de l\'envoi: ' + data.error);
            }
        } catch (error) {
            alert('Erreur de connexion lors de l\'envoi');
            console.error('Erreur:', error);
        }
    }, [newMessage, selectedConversation, getCurrentUserId, loadMessages, loadConversations]);
    
    // ✏️ Modifier un message
    const editMessage = (messageId, currentText) => {
        setEditingMessage(messageId);
        setEditText(currentText);
        setContextMenu({ show: false, x: 0, y: 0, messageId: null });
    };

    // 💾 Sauvegarder la modification d'un message
    const saveMessageEdit = useCallback(async (messageId) => {
        if (!editText.trim()) return;

        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;

            const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/edit_message.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message_id: messageId,
                    user_id: currentUserId,
                    new_message: editText.trim()
                })
            });

            const data = await response.json();
            if (data.success) {
                setEditingMessage(null);
                setEditText('');
                loadMessages(selectedConversation.other_user_id);
            } else {
                alert('Erreur lors de la modification: ' + data.error);
            }
        } catch (error) {
            alert('Erreur de connexion lors de la modification');
            console.error('Erreur:', error);
        }
    }, [editText, selectedConversation, getCurrentUserId, loadMessages]);

    // 🗑️ Supprimer un message
    const deleteMessage = useCallback(async (messageId, deleteType) => {
        try {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) return;

            const response = await fetch('http://localhost/Project_PFE/Backend/api/messaging/delete_message.php', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message_id: messageId,
                    user_id: currentUserId,
                    delete_type: deleteType // 'sender_only' ou 'both_sides'
                })
            });

            const data = await response.json();
            if (data.success) {
                loadMessages(selectedConversation.other_user_id);
                loadConversations();
            } else {
                alert('Erreur lors de la suppression: ' + data.error);
            }
        } catch (error) {
            alert('Erreur de connexion lors de la suppression');
            console.error('Erreur:', error);
        }

        setContextMenu({ show: false, x: 0, y: 0, messageId: null });
    }, [selectedConversation, getCurrentUserId, loadMessages, loadConversations]);

    // ⌨️ Gestion de la touche Entrée (corrigée pour éviter la dépréciation)
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };
    
    // 📜 Scroll automatique vers le bas
    const scrollToBottom = () => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
    };
    
    // 🔄 Effets
    useEffect(() => {
        scrollToBottom();
    }, [messages]);
    
    useEffect(() => {
        if (!isLoading && hasMessagingAccess()) {
            loadAuthorizedUsers();
            loadConversations();
        }
    }, [isLoading, hasMessagingAccess, loadAuthorizedUsers, loadConversations]);
    
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {
                setContextMenu({ show: false, x: 0, y: 0, messageId: null });
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    
    // 🚫 Vérifications d'accès
    const currentUserId = getCurrentUserId();
    
    if (isLoading) {
        return (
            <div className="messaging-system">
                <div className="loading-container">
                    <div className="loading-spinner"></div>
                    <p>Chargement du système de messagerie...</p>
                </div>
            </div>
        );
    }
    
    if (!currentUserId) {
        return (
            <div className="messaging-system">
                <div className="auth-error-container">
                    <div className="auth-error-content">
                        <h2>🔐 Authentification Requise</h2>
                        <p>Impossible d'accéder à la messagerie sans identification utilisateur.</p>
                        <button onClick={() => window.location.href = '/login'} className="btn btn-primary">
                            🔑 Se Connecter
                        </button>
                    </div>
                </div>
            </div>
        );
    }
    
    if (!hasMessagingAccess()) {
        return (
            <div className="messaging-system">
                <div className="access-denied-container">
                    <div className="access-denied-content">
                        <h2>🚫 Accès Refusé</h2>
                        <p>Seuls les Parents, Enseignants et Administrateurs peuvent accéder à la messagerie.</p>
                        <p>Votre rôle actuel ne vous permet pas d'utiliser cette fonctionnalité.</p>
                    </div>
                </div>
            </div>
        );
    }
    
    return (
        <div className="messaging-system">
            <div className="messaging-container">
                {/* 📋 Sidebar - Liste des conversations */}
                <div className="conversations-sidebar">
                    <div className="sidebar-header">
                        <h2>💬 Messages</h2>
                        <button 
                            className="new-conversation-btn"
                            onClick={() => setShowNewConversation(true)}
                            title="Nouvelle conversation"
                        >
                            ✏️
                        </button>
                    </div>
                    
                    <div className="conversations-list">
                        {conversations.length === 0 ? (
                            <div className="empty-conversations">
                                <p>Aucune conversation</p>
                                <button 
                                    className="start-conversation-btn"
                                    onClick={() => setShowNewConversation(true)}
                                >
                                    Commencer une conversation
                                </button>
                            </div>
                        ) : (
                            conversations.map(conv => (
                                <div 
                                    key={conv.other_user_id}
                                    className={`conversation-item ${selectedConversation?.other_user_id === conv.other_user_id ? 'active' : ''}`}
                                    onClick={() => {
                                        setSelectedConversation(conv);
                                        loadMessages(conv.other_user_id);
                                    }}
                                >
                                    <div className="conversation-avatar">
                                        {conv.other_user_name.charAt(0).toUpperCase()}
                                    </div>
                                    <div className="conversation-info">
                                        <div className="conversation-name">{conv.other_user_name}</div>
                                        <div className="conversation-preview">
                                            {conv.last_message || 'Aucun message'}
                                        </div>
                                    </div>
                                    {conv.unread_count > 0 && (
                                        <div className="unread-badge">{conv.unread_count}</div>
                                    )}
                                </div>
                            ))
                        )}
                    </div>
                </div>
                
                {/* 💬 Zone de chat principale */}
                <div className="chat-area">
                    {selectedConversation ? (
                        <>
                            {/* En-tête du chat */}
                            <div className="chat-header">
                                <div className="chat-user-info">
                                    <div className="chat-avatar">
                                        {selectedConversation.other_user_name.charAt(0).toUpperCase()}
                                    </div>
                                    <div className="chat-user-name">
                                        {selectedConversation.other_user_name}
                                    </div>
                                </div>
                            </div>
                            
                            {/* Messages avec fonctionnalités complètes */}
                            <div className="messages-container">
                                {messages.map(message => {
                                    const isOwnMessage = message.expediteur_id === currentUserId;
                                    const isEditing = editingMessage === message.id;

                                    return (
                                        <div
                                            key={message.id}
                                            className={`message-wrapper ${isOwnMessage ? 'own-message' : 'other-message'}`}
                                        >
                                            <div
                                                className={`message-bubble ${isOwnMessage ? 'sent' : 'received'}`}
                                                onContextMenu={(e) => {
                                                    if (isOwnMessage) {
                                                        e.preventDefault();
                                                        setContextMenu({
                                                            show: true,
                                                            x: e.clientX,
                                                            y: e.clientY,
                                                            messageId: message.id
                                                        });
                                                    }
                                                }}
                                            >
                                                {isEditing ? (
                                                    <div className="edit-message-container">
                                                        <textarea
                                                            value={editText}
                                                            onChange={(e) => setEditText(e.target.value)}
                                                            className="edit-message-input"
                                                            autoFocus
                                                        />
                                                        <div className="edit-message-actions">
                                                            <button
                                                                className="save-edit-btn"
                                                                onClick={() => saveMessageEdit(message.id)}
                                                            >
                                                                ✅ Sauvegarder
                                                            </button>
                                                            <button
                                                                className="cancel-edit-btn"
                                                                onClick={() => {
                                                                    setEditingMessage(null);
                                                                    setEditText('');
                                                                }}
                                                            >
                                                                ❌ Annuler
                                                            </button>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <div className="message-content">
                                                            {message.message}
                                                        </div>
                                                        <div className="message-meta">
                                                            <span className="message-time">
                                                                {new Date(message.date_envoi).toLocaleTimeString('fr-FR', {
                                                                    hour: '2-digit',
                                                                    minute: '2-digit'
                                                                })}
                                                            </span>
                                                            {message.modifie === 1 && (
                                                                <span className="message-edited">
                                                                    Message modifié le {new Date(message.date_modification).toLocaleDateString('fr-FR')}
                                                                </span>
                                                            )}
                                                            {isOwnMessage && (
                                                                <span className={`message-status ${message.lu === 1 ? 'read' : 'unread'}`}>
                                                                    {message.lu === 1 ? '✓✓' : '✓'}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                    );
                                })}
                                <div ref={messagesEndRef} />
                            </div>
                            
                            {/* Zone de saisie */}
                            <div className="message-input-container">
                                <div className="message-input-wrapper">
                                    <textarea
                                        ref={messageInputRef}
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        onKeyDown={handleKeyDown}
                                        placeholder="Tapez votre message..."
                                        className="message-input"
                                        rows="1"
                                    />
                                    <button 
                                        onClick={sendMessage}
                                        className="send-button"
                                        disabled={!newMessage.trim()}
                                    >
                                        📤
                                    </button>
                                </div>
                            </div>
                        </>
                    ) : (
                        <div className="no-conversation-selected">
                            <div className="welcome-message">
                                <h3>💬 Bienvenue dans la messagerie</h3>
                                <p>Sélectionnez une conversation ou commencez-en une nouvelle</p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* 📱 Menu contextuel pour les messages */}
            {contextMenu.show && (
                <div
                    ref={contextMenuRef}
                    className="context-menu"
                    style={{
                        position: 'fixed',
                        top: contextMenu.y,
                        left: contextMenu.x,
                        zIndex: 1000
                    }}
                >
                    <button
                        className="context-menu-item"
                        onClick={() => {
                            const message = messages.find(m => m.id === contextMenu.messageId);
                            if (message) {
                                editMessage(message.id, message.message);
                            }
                        }}
                    >
                        ✏️ Modifier
                    </button>
                    <button
                        className="context-menu-item"
                        onClick={() => {
                            if (window.confirm('Supprimer ce message pour vous uniquement ?')) {
                                deleteMessage(contextMenu.messageId, 'sender_only');
                            }
                        }}
                    >
                        🗑️ Supprimer pour moi
                    </button>
                    <button
                        className="context-menu-item danger"
                        onClick={() => {
                            if (window.confirm('Supprimer ce message pour tout le monde ? Cette action est irréversible.')) {
                                deleteMessage(contextMenu.messageId, 'both_sides');
                            }
                        }}
                    >
                        🗑️ Supprimer pour tous
                    </button>
                </div>
            )}

            {/* 🆕 Modal nouvelle conversation complète */}
            {showNewConversation && (
                <div className="modal-overlay" onClick={() => setShowNewConversation(false)}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <div className="modal-header">
                            <h3>Nouvelle conversation</h3>
                            <button
                                className="modal-close"
                                onClick={() => setShowNewConversation(false)}
                            >
                                ✕
                            </button>
                        </div>
                        <div className="modal-body">
                            <label>Sélectionner un destinataire :</label>
                            <select
                                value={selectedUser}
                                onChange={(e) => setSelectedUser(e.target.value)}
                                className="user-select"
                            >
                                <option value="">-- Choisir un utilisateur --</option>
                                {users.map(user => (
                                    <option key={user.id} value={user.id}>
                                        {user.nom} ({user.role_nom})
                                    </option>
                                ))}
                            </select>
                        </div>
                        <div className="modal-footer">
                            <button
                                className="btn btn-secondary"
                                onClick={() => {
                                    setShowNewConversation(false);
                                    setSelectedUser('');
                                }}
                            >
                                Annuler
                            </button>
                            <button
                                className="btn btn-primary"
                                onClick={() => {
                                    if (selectedUser) {
                                        const user = users.find(u => u.id == selectedUser);
                                        setSelectedConversation({
                                            other_user_id: user.id,
                                            other_user_name: user.nom
                                        });
                                        setMessages([]);
                                        setShowNewConversation(false);
                                        setSelectedUser('');
                                        // Focus sur l'input de message
                                        setTimeout(() => {
                                            if (messageInputRef.current) {
                                                messageInputRef.current.focus();
                                            }
                                        }, 100);
                                    }
                                }}
                                disabled={!selectedUser}
                            >
                                Commencer
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default MessagingSystem;
