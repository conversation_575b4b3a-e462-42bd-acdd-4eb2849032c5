import React, { useState, useEffect, useRef, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import '../css/MessagingSystem.css';

const MessagingSystem = () => {
    const { user } = useContext(AuthContext);
    const [conversations, setConversations] = useState([]);
    const [selectedConversation, setSelectedConversation] = useState(null);
    const [messages, setMessages] = useState([]);
    const [newMessage, setNewMessage] = useState('');
    const [authorizedUsers, setAuthorizedUsers] = useState([]);
    const [showNewConversation, setShowNewConversation] = useState(false);
    const [selectedUser, setSelectedUser] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [editingMessage, setEditingMessage] = useState(null);
    const [editContent, setEditContent] = useState('');
    const [showContextMenu, setShowContextMenu] = useState(null);
    const [stats, setStats] = useState({});
    
    const messagesEndRef = useRef(null);
    const contextMenuRef = useRef(null);
    
    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';
    
    // Scroll automatique vers le bas
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };
    
    useEffect(() => {
        scrollToBottom();
    }, [messages]);
    
    // Fermer le menu contextuel en cliquant ailleurs
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {
                setShowContextMenu(null);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    
    // Fonction pour faire des requêtes API
    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {
        try {
            const token = localStorage.getItem('token') || 'test_user_1';
            
            const config = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            };
            
            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
                config.body = JSON.stringify(data);
            }
            
            const response = await fetch(`${API_BASE_URL}?action=${endpoint}`, config);
            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.error || 'Erreur API');
            }
            
            return result;
        } catch (error) {
            console.error('Erreur API:', error);
            throw error;
        }
    };
    
    // Charger les conversations
    const loadConversations = async () => {
        try {
            setLoading(true);
            const result = await makeAPIRequest('conversations');
            setConversations(result.data || []);
        } catch (error) {
            setError('Impossible de charger les conversations: ' + error.message);
        } finally {
            setLoading(false);
        }
    };
    
    // Charger les messages d'une conversation
    const loadMessages = async (contactId) => {
        try {
            setLoading(true);
            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);
            setMessages(result.data || []);
        } catch (error) {
            setError('Impossible de charger les messages: ' + error.message);
        } finally {
            setLoading(false);
        }
    };
    
    // Charger les utilisateurs autorisés
    const loadAuthorizedUsers = async () => {
        try {
            const result = await makeAPIRequest('users');
            setAuthorizedUsers(result.data || []);
        } catch (error) {
            setError('Impossible de charger les utilisateurs: ' + error.message);
        }
    };
    
    // Charger les statistiques
    const loadStats = async () => {
        try {
            const result = await makeAPIRequest('stats');
            setStats(result.data || {});
        } catch (error) {
            console.error('Erreur chargement stats:', error);
        }
    };
    
    // Envoyer un message
    const sendMessage = async () => {
        if (!newMessage.trim()) return;
        
        try {
            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;
            
            if (!destinataireId) {
                setError('Veuillez sélectionner un destinataire');
                return;
            }
            
            await makeAPIRequest('send', 'POST', {
                destinataire_id: destinataireId,
                message: newMessage.trim()
            });
            
            setNewMessage('');
            setShowNewConversation(false);
            
            // Recharger les conversations et messages
            await loadConversations();
            if (selectedConversation) {
                await loadMessages(selectedConversation.contact_id);
            }
            
        } catch (error) {
            setError('Impossible d\'envoyer le message: ' + error.message);
        }
    };
    
    // Modifier un message
    const editMessage = async (messageId, newContent) => {
        try {
            await makeAPIRequest('edit', 'PUT', {
                message_id: messageId,
                message: newContent
            });
            
            setEditingMessage(null);
            setEditContent('');
            
            // Recharger les messages
            if (selectedConversation) {
                await loadMessages(selectedConversation.contact_id);
            }
            
        } catch (error) {
            setError('Impossible de modifier le message: ' + error.message);
        }
    };
    
    // Supprimer un message
    const deleteMessage = async (messageId, deleteType = 'for_me') => {
        try {
            await makeAPIRequest('delete', 'DELETE', {
                message_id: messageId,
                delete_type: deleteType
            });
            
            setShowContextMenu(null);
            
            // Recharger les messages
            if (selectedConversation) {
                await loadMessages(selectedConversation.contact_id);
            }
            
        } catch (error) {
            setError('Impossible de supprimer le message: ' + error.message);
        }
    };
    
    // Sélectionner une conversation
    const selectConversation = async (conversation) => {
        setSelectedConversation(conversation);
        setShowNewConversation(false);
        await loadMessages(conversation.contact_id);
    };
    
    // Démarrer une nouvelle conversation
    const startNewConversation = () => {
        setSelectedConversation(null);
        setMessages([]);
        setShowNewConversation(true);
    };
    
    // Gérer le menu contextuel
    const handleContextMenu = (e, message) => {
        e.preventDefault();
        setShowContextMenu({
            x: e.clientX,
            y: e.clientY,
            message: message
        });
    };
    
    // Démarrer l'édition d'un message
    const startEditing = (message) => {
        setEditingMessage(message.id);
        setEditContent(message.message);
        setShowContextMenu(null);
    };
    
    // Annuler l'édition
    const cancelEditing = () => {
        setEditingMessage(null);
        setEditContent('');
    };
    
    // Confirmer l'édition
    const confirmEdit = async () => {
        if (editContent.trim() && editingMessage) {
            await editMessage(editingMessage, editContent.trim());
        }
    };
    
    // Formater la date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays <= 7) {
            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });
        }
    };
    
    // Charger les données au montage du composant
    useEffect(() => {
        loadConversations();
        loadAuthorizedUsers();
        loadStats();
    }, []);
    
    // Actualiser périodiquement
    useEffect(() => {
        const interval = setInterval(() => {
            loadConversations();
            if (selectedConversation) {
                loadMessages(selectedConversation.contact_id);
            }
        }, 30000); // Actualiser toutes les 30 secondes
        
        return () => clearInterval(interval);
    }, [selectedConversation]);
    
    return (
        <div className="messaging-system">
            <div className="messaging-header">
                <h1>💬 Messagerie</h1>
                <div className="messaging-stats">
                    <span className="stat-item">
                        <span className="stat-number">{stats.total_messages || 0}</span>
                        <span className="stat-label">Messages</span>
                    </span>
                    <span className="stat-item">
                        <span className="stat-number">{stats.messages_non_lus || 0}</span>
                        <span className="stat-label">Non lus</span>
                    </span>
                    <span className="stat-item">
                        <span className="stat-number">{conversations.length}</span>
                        <span className="stat-label">Conversations</span>
                    </span>
                </div>
            </div>
            
            {error && (
                <div className="error-message">
                    <span>❌ {error}</span>
                    <button onClick={() => setError('')}>✕</button>
                </div>
            )}
            
            <div className="messaging-container">
                {/* Liste des conversations */}
                <div className="conversations-panel">
                    <div className="conversations-header">
                        <h3>Conversations</h3>
                        <button 
                            className="new-conversation-btn"
                            onClick={startNewConversation}
                            title="Nouvelle conversation"
                        >
                            ✏️
                        </button>
                    </div>
                    
                    <div className="conversations-list">
                        {loading && conversations.length === 0 ? (
                            <div className="loading">Chargement...</div>
                        ) : conversations.length === 0 ? (
                            <div className="no-conversations">
                                <p>Aucune conversation</p>
                                <button onClick={startNewConversation}>
                                    Démarrer une conversation
                                </button>
                            </div>
                        ) : (
                            conversations.map(conversation => (
                                <div
                                    key={conversation.contact_id}
                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}
                                    onClick={() => selectConversation(conversation)}
                                >
                                    <div className="conversation-avatar">
                                        {conversation.contact_nom.charAt(0).toUpperCase()}
                                    </div>
                                    <div className="conversation-info">
                                        <div className="conversation-name">
                                            {conversation.contact_nom}
                                            <span className="conversation-role">
                                                {conversation.contact_role}
                                            </span>
                                        </div>
                                        <div className="conversation-preview">
                                            {conversation.dernier_message || 'Aucun message'}
                                        </div>
                                        <div className="conversation-meta">
                                            <span className="conversation-time">
                                                {formatDate(conversation.derniere_activite)}
                                            </span>
                                            {conversation.messages_non_lus > 0 && (
                                                <span className="unread-badge">
                                                    {conversation.messages_non_lus}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
                
                {/* Zone de chat */}
                <div className="chat-panel">
                    {showNewConversation ? (
                        <div className="new-conversation">
                            <div className="new-conversation-header">
                                <h3>Nouvelle conversation</h3>
                                <button onClick={() => setShowNewConversation(false)}>✕</button>
                            </div>
                            <div className="new-conversation-content">
                                <select
                                    value={selectedUser}
                                    onChange={(e) => setSelectedUser(e.target.value)}
                                    className="user-select"
                                >
                                    <option value="">Sélectionner un utilisateur...</option>
                                    {authorizedUsers.map(user => (
                                        <option key={user.id} value={user.id}>
                                            {user.nom} ({user.role})
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    ) : selectedConversation ? (
                        <div className="chat-header">
                            <div className="chat-contact-info">
                                <div className="chat-avatar">
                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <div className="chat-contact-name">
                                        {selectedConversation.contact_nom}
                                    </div>
                                    <div className="chat-contact-role">
                                        {selectedConversation.contact_role}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="no-chat-selected">
                            <div className="no-chat-content">
                                <h3>💬 Messagerie</h3>
                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>
                                <button onClick={startNewConversation}>
                                    Nouvelle conversation
                                </button>
                            </div>
                        </div>
                    )}
                    
                    {/* Messages */}
                    {(selectedConversation || showNewConversation) && (
                        <>
                            <div className="messages-container">
                                {messages.map(message => (
                                    <div
                                        key={message.id}
                                        className={`message ${message.message_type}`}
                                        onContextMenu={(e) => handleContextMenu(e, message)}
                                    >
                                        <div className="message-content">
                                            {editingMessage === message.id ? (
                                                <div className="message-edit">
                                                    <textarea
                                                        value={editContent}
                                                        onChange={(e) => setEditContent(e.target.value)}
                                                        className="edit-textarea"
                                                        autoFocus
                                                    />
                                                    <div className="edit-actions">
                                                        <button onClick={confirmEdit} className="confirm-edit">
                                                            ✓
                                                        </button>
                                                        <button onClick={cancelEditing} className="cancel-edit">
                                                            ✕
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <>
                                                    <div className="message-text">
                                                        {message.message}
                                                        {message.modifie === '1' && (
                                                            <span className="message-edited" title={`Modifié le ${formatDate(message.date_modification)}`}>
                                                                (modifié)
                                                            </span>
                                                        )}
                                                    </div>
                                                    <div className="message-time">
                                                        {formatDate(message.date_envoi)}
                                                        {message.message_type === 'sent' && (
                                                            <span className="message-status">
                                                                {message.lu === '1' ? '✓✓' : '✓'}
                                                            </span>
                                                        )}
                                                    </div>
                                                </>
                                            )}
                                        </div>
                                    </div>
                                ))}
                                <div ref={messagesEndRef} />
                            </div>
                            
                            {/* Zone de saisie */}
                            <div className="message-input-container">
                                <div className="message-input-wrapper">
                                    <textarea
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        placeholder="Tapez votre message..."
                                        className="message-input"
                                        rows="1"
                                        onKeyPress={(e) => {
                                            if (e.key === 'Enter' && !e.shiftKey) {
                                                e.preventDefault();
                                                sendMessage();
                                            }
                                        }}
                                    />
                                    <button
                                        onClick={sendMessage}
                                        className="send-button"
                                        disabled={!newMessage.trim() || loading}
                                    >
                                        📤
                                    </button>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
            
            {/* Menu contextuel */}
            {showContextMenu && (
                <div
                    ref={contextMenuRef}
                    className="context-menu"
                    style={{
                        left: showContextMenu.x,
                        top: showContextMenu.y
                    }}
                >
                    {showContextMenu.message.can_modify === 1 && (
                        <button onClick={() => startEditing(showContextMenu.message)}>
                            ✏️ Modifier
                        </button>
                    )}
                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>
                        🗑️ Supprimer pour moi
                    </button>
                    {showContextMenu.message.can_delete_for_all === 1 && (
                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>
                            🗑️ Supprimer pour tous
                        </button>
                    )}
                </div>
            )}
        </div>
    );
};

export default MessagingSystem;
