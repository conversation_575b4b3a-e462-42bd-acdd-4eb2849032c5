import React, { useState, useEffect, useRef, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import '../styles/MessagingSystem.css';

const MessagingSystem = () => {
    const { user, isLoading: authLoading, isAuthenticated } = useContext(AuthContext);
    const [conversations, setConversations] = useState([]);
    const [selectedConversation, setSelectedConversation] = useState(null);
    const [messages, setMessages] = useState([]);
    const [newMessage, setNewMessage] = useState('');
    const [authorizedUsers, setAuthorizedUsers] = useState([]);
    const [showNewConversation, setShowNewConversation] = useState(false);
    const [selectedUser, setSelectedUser] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [editingMessage, setEditingMessage] = useState(null);
    const [editContent, setEditContent] = useState('');
    const [showContextMenu, setShowContextMenu] = useState(null);
    const [stats, setStats] = useState({});

    const messagesEndRef = useRef(null);
    const contextMenuRef = useRef(null);

    // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée
    const getCurrentUserId = () => {
        console.log('🔍 Tentative de récupération de l\'ID utilisateur...');
        console.log('👤 Contexte user:', user);

        // Essayer d'abord depuis le contexte user avec différentes propriétés possibles
        if (user) {
            // Vérifier différentes propriétés possibles pour l'ID
            const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];
            for (const id of possibleIds) {
                if (id && !isNaN(id)) {
                    console.log('✅ ID trouvé dans le contexte:', id);
                    return parseInt(id);
                }
            }
        }

        // Essayer depuis localStorage comme fallback
        try {
            const storedUser = localStorage.getItem('user');
            console.log('📦 Données localStorage user:', storedUser);

            if (storedUser) {
                const userData = JSON.parse(storedUser);
                console.log('📊 Données utilisateur parsées:', userData);

                if (userData) {
                    // Vérifier différentes propriétés possibles pour l'ID
                    const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];
                    for (const id of possibleIds) {
                        if (id && !isNaN(id)) {
                            console.log('✅ ID trouvé dans localStorage:', id);
                            return parseInt(id);
                        }
                    }
                }
            }
        } catch (error) {
            console.warn('❌ Erreur lors de la récupération de l\'utilisateur depuis localStorage:', error);
        }

        // Dernière tentative avec le token
        const token = localStorage.getItem('token');
        console.log('🔑 Token localStorage:', token);

        if (token) {
            // Essayer différents formats de token
            if (token.includes('_')) {
                const userId = token.split('_').pop();
                if (userId && !isNaN(userId)) {
                    console.log('✅ ID trouvé dans le token:', userId);
                    return parseInt(userId);
                }
            }

            // Essayer de décoder le token s'il est en base64 ou JWT
            try {
                if (token.includes('.')) {
                    // Format JWT potentiel
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const possibleIds = [payload.id, payload.user_id, payload.utilisateur_id, payload.sub];
                    for (const id of possibleIds) {
                        if (id && !isNaN(id)) {
                            console.log('✅ ID trouvé dans JWT:', id);
                            return parseInt(id);
                        }
                    }
                }
            } catch (error) {
                console.warn('⚠️ Impossible de décoder le token JWT:', error);
            }
        }

        console.warn('❌ Aucun ID utilisateur trouvé');
        return null;
    };

    // 🔍 Fonction pour vérifier si l'utilisateur est valide
    const isUserValid = () => {
        const userId = getCurrentUserId();
        return userId && userId > 0;
    };

    // 🔧 Fonction de diagnostic complète
    const diagnoseUserData = () => {
        console.log('🔧 === DIAGNOSTIC COMPLET DES DONNÉES UTILISATEUR ===');

        // 1. Contexte React
        console.log('1️⃣ Contexte React AuthContext:');
        console.log('   - user object:', user);
        console.log('   - user keys:', user ? Object.keys(user) : 'N/A');
        console.log('   - user values:', user ? Object.values(user) : 'N/A');

        // 2. localStorage
        console.log('2️⃣ localStorage:');
        const storedUser = localStorage.getItem('user');
        const storedToken = localStorage.getItem('token');
        console.log('   - raw user data:', storedUser);
        console.log('   - raw token:', storedToken);

        if (storedUser) {
            try {
                const parsedUser = JSON.parse(storedUser);
                console.log('   - parsed user:', parsedUser);
                console.log('   - parsed user keys:', Object.keys(parsedUser));
                console.log('   - parsed user values:', Object.values(parsedUser));
            } catch (e) {
                console.log('   - parsing error:', e.message);
            }
        }

        // 3. sessionStorage
        console.log('3️⃣ sessionStorage:');
        const sessionUser = sessionStorage.getItem('user');
        const sessionToken = sessionStorage.getItem('token');
        console.log('   - session user:', sessionUser);
        console.log('   - session token:', sessionToken);

        // 4. Toutes les clés de stockage
        console.log('4️⃣ Toutes les clés localStorage:');
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            console.log(`   - ${key}: ${localStorage.getItem(key)}`);
        }

        console.log('🔧 === FIN DU DIAGNOSTIC ===');
    };
    
    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';
    
    // Scroll automatique vers le bas
    const scrollToBottom = () => {
        if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
        }
    };
    
    useEffect(() => {
        scrollToBottom();
    }, [messages]);
    
    // Fermer le menu contextuel en cliquant ailleurs
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {
                setShowContextMenu(null);
            }
        };
        
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    
    // Fonction pour faire des requêtes API
    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {
        try {
            const token = localStorage.getItem('token') || 'test_user_1';

            const config = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            };

            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
                config.body = JSON.stringify(data);
            }

            const url = `${API_BASE_URL}?action=${endpoint}`;
            console.log('API Request:', { url, method, endpoint, token });

            const response = await fetch(url, config);
            console.log('API Response Status:', response.status, response.statusText);

            const result = await response.json();
            console.log('API Response Data:', result);

            if (!result.success) {
                throw new Error(result.error || 'Erreur API');
            }

            return result;
        } catch (error) {
            console.error('Erreur API complète:', {
                endpoint,
                method,
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    };
    
    // Charger les conversations avec confidentialité stricte
    const loadConversations = async () => {
        try {
            setLoading(true);
            setError(''); // Réinitialiser l'erreur

            // 🔍 Vérification robuste de l'utilisateur
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                console.warn('� Utilisateur non identifié, tentative de récupération...');

                // Attendre un peu pour que le contexte se charge
                await new Promise(resolve => setTimeout(resolve, 1000));

                const retryUserId = getCurrentUserId();
                if (!retryUserId) {
                    throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');
                }
            }

            const result = await makeAPIRequest('conversations');

            if (!result.success) {
                throw new Error(result.error || 'Erreur lors du chargement des conversations');
            }

            // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations
            const finalUserId = getCurrentUserId();

            // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité
            const secureConversations = (result.data || []).filter(conversation => {
                // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur
                const contactId = parseInt(conversation.contact_id);
                return contactId && contactId !== finalUserId && contactId > 0;
            });

            console.log('🔒 Conversations sécurisées chargées:', {
                total_received: result.data?.length || 0,
                secure_filtered: secureConversations.length,
                user_id: finalUserId,
                user_context: user ? 'Disponible' : 'Non disponible'
            });

            setConversations(secureConversations);
        } catch (error) {
            const errorMessage = error.message || 'Erreur inconnue';
            setError('Impossible de charger les conversations: ' + errorMessage);
            console.error('🚨 Erreur sécurité conversations:', {
                error: errorMessage,
                user_id: getCurrentUserId(),
                user_context: user,
                localStorage_user: localStorage.getItem('user'),
                localStorage_token: localStorage.getItem('token')
            });
        } finally {
            setLoading(false);
        }
    };
    
    // Charger les messages d'une conversation avec confidentialité stricte
    const loadMessages = async (contactId) => {
        try {
            setLoading(true);
            setError(''); // Réinitialiser l'erreur

            // � Vérification robuste de l'utilisateur
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                console.warn('🚨 Utilisateur non identifié lors du chargement des messages');
                throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');
            }

            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);

            if (!result.success) {
                throw new Error(result.error || 'Erreur lors du chargement des messages');
            }

            // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité
            const finalUserId = getCurrentUserId();

            // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté
            const secureMessages = (result.data || []).filter(message => {
                const expediteurId = parseInt(message.expediteur_id);
                const destinataireId = parseInt(message.destinataire_id);

                // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté
                return (expediteurId === finalUserId || destinataireId === finalUserId);
            }).map(message => {
                // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)
                const expediteurId = parseInt(message.expediteur_id);

                return {
                    ...message,
                    message_type: expediteurId === finalUserId ? 'sent' : 'received',
                    is_own_message: expediteurId === finalUserId
                };
            });

            console.log('🔒 Messages sécurisés chargés:', {
                total_received: result.data?.length || 0,
                secure_filtered: secureMessages.length,
                user_id: finalUserId,
                contact_id: contactId,
                user_context: user ? 'Disponible' : 'Non disponible'
            });

            setMessages(secureMessages);
        } catch (error) {
            const errorMessage = error.message || 'Erreur inconnue';
            setError('Impossible de charger les messages: ' + errorMessage);
            console.error('🚨 Erreur sécurité messages:', {
                error: errorMessage,
                user_id: getCurrentUserId(),
                contact_id: contactId,
                user_context: user,
                localStorage_user: localStorage.getItem('user'),
                localStorage_token: localStorage.getItem('token')
            });
        } finally {
            setLoading(false);
        }
    };
    
    // Charger les utilisateurs autorisés
    const loadAuthorizedUsers = async () => {
        try {
            const result = await makeAPIRequest('users');
            setAuthorizedUsers(result.data || []);
        } catch (error) {
            setError('Impossible de charger les utilisateurs: ' + error.message);
        }
    };
    
    // Charger les statistiques
    const loadStats = async () => {
        try {
            const result = await makeAPIRequest('stats');
            setStats(result.data || {});
        } catch (error) {
            console.error('Erreur chargement stats:', error);
        }
    };
    
    // Envoyer un message
    const sendMessage = async () => {
        if (!newMessage.trim()) return;
        
        try {
            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;
            
            if (!destinataireId) {
                setError('Veuillez sélectionner un destinataire');
                return;
            }
            
            await makeAPIRequest('send', 'POST', {
                destinataire_id: destinataireId,
                message: newMessage.trim()
            });
            
            setNewMessage('');
            setShowNewConversation(false);
            
            // Recharger les conversations et messages
            await loadConversations();
            if (selectedConversation) {
                await loadMessages(selectedConversation.contact_id);
            }
            
        } catch (error) {
            setError('Impossible d\'envoyer le message: ' + error.message);
        }
    };
    
    // Modifier un message
    const editMessage = async (messageId, newContent) => {
        try {
            await makeAPIRequest('edit', 'PUT', {
                message_id: messageId,
                message: newContent
            });
            
            setEditingMessage(null);
            setEditContent('');
            
            // Recharger les messages
            if (selectedConversation) {
                await loadMessages(selectedConversation.contact_id);
            }
            
        } catch (error) {
            setError('Impossible de modifier le message: ' + error.message);
        }
    };
    
    // Supprimer un message
    const deleteMessage = async (messageId, deleteType = 'for_me') => {
        try {
            await makeAPIRequest('delete', 'DELETE', {
                message_id: messageId,
                delete_type: deleteType
            });
            
            setShowContextMenu(null);
            
            // Recharger les messages
            if (selectedConversation) {
                await loadMessages(selectedConversation.contact_id);
            }
            
        } catch (error) {
            setError('Impossible de supprimer le message: ' + error.message);
        }
    };
    
    // Sélectionner une conversation
    const selectConversation = async (conversation) => {
        setSelectedConversation(conversation);
        setShowNewConversation(false);
        await loadMessages(conversation.contact_id);
    };
    
    // Démarrer une nouvelle conversation
    const startNewConversation = () => {
        setSelectedConversation(null);
        setMessages([]);
        setShowNewConversation(true);
    };
    
    // Gérer le menu contextuel
    const handleContextMenu = (e, message) => {
        e.preventDefault();
        setShowContextMenu({
            x: e.clientX,
            y: e.clientY,
            message: message
        });
    };
    
    // Démarrer l'édition d'un message
    const startEditing = (message) => {
        setEditingMessage(message.id);
        setEditContent(message.message);
        setShowContextMenu(null);
    };
    
    // Annuler l'édition
    const cancelEditing = () => {
        setEditingMessage(null);
        setEditContent('');
    };
    
    // Confirmer l'édition
    const confirmEdit = async () => {
        if (editContent.trim() && editingMessage) {
            await editMessage(editingMessage, editContent.trim());
        }
    };
    
    // Formater la date
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays === 1) {
            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays <= 7) {
            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });
        }
    };
    
    // Charger les données au montage du composant
    useEffect(() => {
        const initializeMessaging = async () => {
            console.log('🚀 Initialisation du système de messagerie...');

            // Attendre un peu pour que le contexte d'authentification se charge
            if (!user) {
                console.log('⏳ Attente du chargement du contexte utilisateur...');
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            const userId = getCurrentUserId();
            console.log('👤 ID utilisateur détecté:', userId);

            if (userId) {
                console.log('✅ Utilisateur valide, chargement des données...');
                loadConversations();
                loadAuthorizedUsers();
                loadStats();
            } else {
                console.warn('⚠️ Aucun utilisateur valide trouvé');
                setError('Utilisateur non identifié. Veuillez vous reconnecter.');

                // Essayer de charger quand même après un délai
                setTimeout(() => {
                    const retryUserId = getCurrentUserId();
                    if (retryUserId) {
                        console.log('🔄 Retry réussi, chargement des données...');
                        setError(''); // Effacer l'erreur
                        loadConversations();
                        loadAuthorizedUsers();
                        loadStats();
                    }
                }, 2000);
            }
        };

        initializeMessaging();
    }, [user]);
    
    // Actualiser périodiquement
    useEffect(() => {
        const interval = setInterval(() => {
            loadConversations();
            if (selectedConversation) {
                loadMessages(selectedConversation.contact_id);
            }
        }, 30000); // Actualiser toutes les 30 secondes
        
        return () => clearInterval(interval);
    }, [selectedConversation]);
    
    // Vérification de l'utilisateur avant affichage
    const currentUserId = getCurrentUserId();

    // Affichage d'erreur d'authentification
    if (!currentUserId && !loading) {
        // Exécuter le diagnostic automatiquement
        React.useEffect(() => {
            diagnoseUserData();
        }, []);

        return (
            <div className="messaging-system">
                <div className="auth-error-container">
                    <div className="auth-error-content">
                        <h2>🔐 Problème d'Authentification Détecté</h2>
                        <p>L'utilisateur est connecté mais son ID n'est pas accessible.</p>

                        <div className="debug-info">
                            <h3>🔍 Informations de Debug :</h3>
                            <ul>
                                <li>Contexte utilisateur: {user ? '✅ Chargé' : '❌ Non chargé'}</li>
                                <li>Nom utilisateur: {user?.nom || user?.name || 'Non défini'}</li>
                                <li>Email utilisateur: {user?.email || 'Non défini'}</li>
                                <li>ID utilisateur (user.id): {user?.id || 'Non défini'}</li>
                                <li>ID utilisateur (user.user_id): {user?.user_id || 'Non défini'}</li>
                                <li>ID utilisateur (user.utilisateur_id): {user?.utilisateur_id || 'Non défini'}</li>
                                <li>Token localStorage: {localStorage.getItem('token') ? '✅ Présent' : '❌ Absent'}</li>
                                <li>Données utilisateur: {localStorage.getItem('user') ? '✅ Présentes' : '❌ Absentes'}</li>
                            </ul>

                            {localStorage.getItem('user') && (
                                <div className="raw-data">
                                    <h4>📊 Données Brutes localStorage :</h4>
                                    <pre className="code-block">
                                        {localStorage.getItem('user')}
                                    </pre>
                                </div>
                            )}
                        </div>

                        <div className="auth-actions">
                            <button
                                onClick={diagnoseUserData}
                                className="btn btn-warning"
                            >
                                🔧 Diagnostic Console
                            </button>
                            <button
                                onClick={() => {
                                    // Essayer de corriger automatiquement
                                    const storedUser = localStorage.getItem('user');
                                    if (storedUser) {
                                        try {
                                            const userData = JSON.parse(storedUser);
                                            console.log('🔧 Tentative de correction automatique:', userData);

                                            // Essayer de trouver un ID dans les données
                                            const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID];
                                            const foundId = possibleIds.find(id => id && !isNaN(id));

                                            if (foundId) {
                                                // Standardiser sur 'id'
                                                userData.id = parseInt(foundId);
                                                localStorage.setItem('user', JSON.stringify(userData));
                                                console.log('✅ Correction appliquée, rechargement...');
                                                window.location.reload();
                                            } else {
                                                alert('❌ Aucun ID valide trouvé dans les données utilisateur');
                                            }
                                        } catch (e) {
                                            alert('❌ Erreur lors de la correction: ' + e.message);
                                        }
                                    }
                                }}
                                className="btn btn-success"
                            >
                                🔧 Correction Auto
                            </button>
                            <button
                                onClick={() => window.location.href = '/login'}
                                className="btn btn-primary"
                            >
                                🔑 Se Reconnecter
                            </button>
                            <button
                                onClick={() => window.location.reload()}
                                className="btn btn-secondary"
                            >
                                🔄 Actualiser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="messaging-system">
            <div className="messaging-header">
                <h1>💬 Messagerie</h1>
                <div className="user-info">
                    <span className="current-user">👤 {user?.nom || 'Utilisateur'} (ID: {currentUserId})</span>
                </div>
                <div className="messaging-stats">
                    <span className="stat-item">
                        <span className="stat-number">{stats.total_messages || 0}</span>
                        <span className="stat-label">Messages</span>
                    </span>
                    <span className="stat-item">
                        <span className="stat-number">{stats.messages_non_lus || 0}</span>
                        <span className="stat-label">Non lus</span>
                    </span>
                    <span className="stat-item">
                        <span className="stat-number">{conversations.length}</span>
                        <span className="stat-label">Conversations</span>
                    </span>
                </div>
            </div>

            {error && (
                <div className="error-message">
                    <span>❌ {error}</span>
                    <button onClick={() => setError('')}>✕</button>
                    <div className="error-debug">
                        <small>Debug: User ID = {getCurrentUserId()}, Context = {user ? 'OK' : 'KO'}</small>
                    </div>
                </div>
            )}
            
            <div className="messaging-container">
                {/* Liste des conversations */}
                <div className="conversations-panel">
                    <div className="conversations-header">
                        <h3>Conversations</h3>
                        <button 
                            className="new-conversation-btn"
                            onClick={startNewConversation}
                            title="Nouvelle conversation"
                        >
                            ✏️
                        </button>
                    </div>
                    
                    <div className="conversations-list">
                        {loading && conversations.length === 0 ? (
                            <div className="loading">Chargement...</div>
                        ) : conversations.length === 0 ? (
                            <div className="no-conversations">
                                <p>Aucune conversation</p>
                                <button onClick={startNewConversation}>
                                    Démarrer une conversation
                                </button>
                            </div>
                        ) : (
                            conversations.map(conversation => (
                                <div
                                    key={conversation.contact_id}
                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}
                                    onClick={() => selectConversation(conversation)}
                                >
                                    <div className="conversation-avatar">
                                        {conversation.contact_nom.charAt(0).toUpperCase()}
                                    </div>
                                    <div className="conversation-info">
                                        <div className="conversation-name">
                                            {conversation.contact_nom}
                                            <span className="conversation-role">
                                                {conversation.contact_role}
                                            </span>
                                        </div>
                                        <div className="conversation-preview">
                                            {conversation.dernier_message || 'Aucun message'}
                                        </div>
                                        <div className="conversation-meta">
                                            <span className="conversation-time">
                                                {formatDate(conversation.derniere_activite)}
                                            </span>
                                            {conversation.messages_non_lus > 0 && (
                                                <span className="unread-badge">
                                                    {conversation.messages_non_lus}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
                
                {/* Zone de chat */}
                <div className="chat-panel">
                    {showNewConversation ? (
                        <div className="new-conversation">
                            <div className="new-conversation-header">
                                <h3>Nouvelle conversation</h3>
                                <button onClick={() => setShowNewConversation(false)}>✕</button>
                            </div>
                            <div className="new-conversation-content">
                                <select
                                    value={selectedUser}
                                    onChange={(e) => setSelectedUser(e.target.value)}
                                    className="user-select"
                                >
                                    <option value="">Sélectionner un utilisateur...</option>
                                    {authorizedUsers.map(user => (
                                        <option key={user.id} value={user.id}>
                                            {user.nom} ({user.role})
                                        </option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    ) : selectedConversation ? (
                        <div className="chat-header">
                            <div className="chat-contact-info">
                                <div className="chat-avatar">
                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <div className="chat-contact-name">
                                        {selectedConversation.contact_nom}
                                    </div>
                                    <div className="chat-contact-role">
                                        {selectedConversation.contact_role}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="no-chat-selected">
                            <div className="no-chat-content">
                                <h3>💬 Messagerie</h3>
                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>
                                <button onClick={startNewConversation}>
                                    Nouvelle conversation
                                </button>
                            </div>
                        </div>
                    )}
                    
                    {/* Messages */}
                    {(selectedConversation || showNewConversation) && (
                        <>
                            <div className="messages-container">
                                {messages.map(message => {
                                    const currentUserId = getCurrentUserId();
                                    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;
                                    const messageType = isOwnMessage ? 'sent' : 'received';

                                    return (
                                        <div
                                            key={message.id}
                                            className={`message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`}
                                            onContextMenu={(e) => handleContextMenu(e, message)}
                                            data-sender-id={message.expediteur_id}
                                            data-receiver-id={message.destinataire_id}
                                        >
                                            {/* 👤 Affichage du nom de l'expéditeur pour les messages reçus */}
                                            {!isOwnMessage && (
                                                <div className="message-sender">
                                                    {message.expediteur_nom || 'Utilisateur'}
                                                </div>
                                            )}

                                            <div className={`message-content ${messageType}-content`}>
                                                {editingMessage === message.id ? (
                                                    <div className="message-edit">
                                                        <textarea
                                                            value={editContent}
                                                            onChange={(e) => setEditContent(e.target.value)}
                                                            className="edit-textarea"
                                                            autoFocus
                                                        />
                                                        <div className="edit-actions">
                                                            <button onClick={confirmEdit} className="confirm-edit">
                                                                ✓
                                                            </button>
                                                            <button onClick={cancelEditing} className="cancel-edit">
                                                                ✕
                                                            </button>
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <>
                                                        <div className="message-text">
                                                            {message.message}
                                                            {message.modifie === '1' && (
                                                                <span className="message-edited" title={`Modifié le ${formatDate(message.date_modification)}`}>
                                                                    (modifié)
                                                                </span>
                                                            )}
                                                        </div>
                                                        <div className={`message-time ${messageType}-time`}>
                                                            {formatDate(message.date_envoi)}
                                                            {isOwnMessage && (
                                                                <span className="message-status">
                                                                    {message.lu === '1' ? '✓✓' : '✓'}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </>
                                                )}
                                            </div>

                                            {/* 🔒 Indicateur de confidentialité (debug) */}
                                            {process.env.NODE_ENV === 'development' && (
                                                <div className="message-debug" title={`Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`}>
                                                    🔒
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                                <div ref={messagesEndRef} />
                            </div>
                            
                            {/* Zone de saisie */}
                            <div className="message-input-container">
                                <div className="message-input-wrapper">
                                    <textarea
                                        value={newMessage}
                                        onChange={(e) => setNewMessage(e.target.value)}
                                        placeholder="Tapez votre message..."
                                        className="message-input"
                                        rows="1"
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter' && !e.shiftKey) {
                                                e.preventDefault();
                                                sendMessage();
                                            }
                                        }}
                                    />
                                    <button
                                        onClick={sendMessage}
                                        className="send-button"
                                        disabled={!newMessage.trim() || loading}
                                    >
                                        📤
                                    </button>
                                </div>
                            </div>
                        </>
                    )}
                </div>
            </div>
            
            {/* Menu contextuel */}
            {showContextMenu && (
                <div
                    ref={contextMenuRef}
                    className="context-menu"
                    style={{
                        left: showContextMenu.x,
                        top: showContextMenu.y
                    }}
                >
                    {showContextMenu.message.can_modify === 1 && (
                        <button onClick={() => startEditing(showContextMenu.message)}>
                            ✏️ Modifier
                        </button>
                    )}
                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>
                        🗑️ Supprimer pour moi
                    </button>
                    {showContextMenu.message.can_delete_for_all === 1 && (
                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>
                            🗑️ Supprimer pour tous
                        </button>
                    )}
                </div>
            )}
        </div>
    );
};

export default MessagingSystem;
