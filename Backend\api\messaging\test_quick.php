<?php
header('Content-Type: text/html; charset=utf-8');

echo "<h1>Test API Messagerie</h1>";

// Test 1: API Test endpoint
echo "<h2>Test 1: Endpoint Test</h2>";
$url1 = 'http://localhost/Project_PFE/Backend/api/messaging/?action=test';
$ch1 = curl_init();
curl_setopt($ch1, CURLOPT_URL, $url1);
curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch1, CURLOPT_HTTPHEADER, ['Authorization: Bearer test_user_1']);
$response1 = curl_exec($ch1);
$httpCode1 = curl_getinfo($ch1, CURLINFO_HTTP_CODE);
curl_close($ch1);

echo "<p><strong>URL:</strong> $url1</p>";
echo "<p><strong>Code HTTP:</strong> $httpCode1</p>";
echo "<p><strong>Réponse:</strong></p>";
echo "<pre>" . htmlspecialchars($response1) . "</pre>";

// Test 2: Conversations endpoint
echo "<h2>Test 2: Endpoint Conversations</h2>";
$url2 = 'http://localhost/Project_PFE/Backend/api/messaging/?action=conversations';
$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $url2);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_HTTPHEADER, ['Authorization: Bearer test_user_1']);
$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "<p><strong>URL:</strong> $url2</p>";
echo "<p><strong>Code HTTP:</strong> $httpCode2</p>";
echo "<p><strong>Réponse:</strong></p>";
echo "<pre>" . htmlspecialchars($response2) . "</pre>";

// Test 3: Users endpoint
echo "<h2>Test 3: Endpoint Users</h2>";
$url3 = 'http://localhost/Project_PFE/Backend/api/messaging/?action=users';
$ch3 = curl_init();
curl_setopt($ch3, CURLOPT_URL, $url3);
curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch3, CURLOPT_HTTPHEADER, ['Authorization: Bearer test_user_1']);
$response3 = curl_exec($ch3);
$httpCode3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
curl_close($ch3);

echo "<p><strong>URL:</strong> $url3</p>";
echo "<p><strong>Code HTTP:</strong> $httpCode3</p>";
echo "<p><strong>Réponse:</strong></p>";
echo "<pre>" . htmlspecialchars($response3) . "</pre>";

// Test 4: Debug des paramètres
echo "<h2>Test 4: Debug Paramètres</h2>";
echo "<p><strong>REQUEST_URI:</strong> " . ($_SERVER['REQUEST_URI'] ?? 'Non défini') . "</p>";
echo "<p><strong>GET action:</strong> " . ($_GET['action'] ?? 'Non défini') . "</p>";
echo "<p><strong>POST action:</strong> " . ($_POST['action'] ?? 'Non défini') . "</p>";

?>
