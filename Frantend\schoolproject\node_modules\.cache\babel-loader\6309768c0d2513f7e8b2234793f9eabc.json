{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\Project_PFE\\\\Frantend\\\\schoolproject\\\\src\\\\pages\\\\MessagingSystem.js\";\nimport React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\nconst MessagingSystem = () => {\n  const {\n    user,\n    isLoading: authLoading,\n    isAuthenticated\n  } = useContext(AuthContext);\n  const [conversations, setConversations] = useState([]);\n  const [selectedConversation, setSelectedConversation] = useState(null);\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [authorizedUsers, setAuthorizedUsers] = useState([]);\n  const [showNewConversation, setShowNewConversation] = useState(false);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [editingMessage, setEditingMessage] = useState(null);\n  const [editContent, setEditContent] = useState('');\n  const [showContextMenu, setShowContextMenu] = useState(null);\n  const [stats, setStats] = useState({});\n  const messagesEndRef = useRef(null);\n  const contextMenuRef = useRef(null);\n\n  // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée\n  const getCurrentUserId = () => {\n    console.log('🔍 Tentative de récupération de l\\'ID utilisateur...');\n    console.log('👤 Contexte user:', user);\n\n    // Essayer d'abord depuis le contexte user avec différentes propriétés possibles\n    if (user) {\n      // Vérifier différentes propriétés possibles pour l'ID\n      const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];\n      for (const id of possibleIds) {\n        if (id && !isNaN(id)) {\n          console.log('✅ ID trouvé dans le contexte:', id);\n          return parseInt(id);\n        }\n      }\n    }\n\n    // Essayer depuis localStorage comme fallback\n    try {\n      const storedUser = localStorage.getItem('user');\n      console.log('📦 Données localStorage user:', storedUser);\n      if (storedUser) {\n        const userData = JSON.parse(storedUser);\n        console.log('📊 Données utilisateur parsées:', userData);\n        if (userData) {\n          // Vérifier différentes propriétés possibles pour l'ID\n          const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];\n          for (const id of possibleIds) {\n            if (id && !isNaN(id)) {\n              console.log('✅ ID trouvé dans localStorage:', id);\n              return parseInt(id);\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.warn('❌ Erreur lors de la récupération de l\\'utilisateur depuis localStorage:', error);\n    }\n\n    // Dernière tentative avec le token\n    const token = localStorage.getItem('token');\n    console.log('🔑 Token localStorage:', token);\n    if (token) {\n      // Essayer différents formats de token\n      if (token.includes('_')) {\n        const userId = token.split('_').pop();\n        if (userId && !isNaN(userId)) {\n          console.log('✅ ID trouvé dans le token:', userId);\n          return parseInt(userId);\n        }\n      }\n\n      // Essayer de décoder le token s'il est en base64 ou JWT\n      try {\n        if (token.includes('.')) {\n          // Format JWT potentiel\n          const payload = JSON.parse(atob(token.split('.')[1]));\n          const possibleIds = [payload.id, payload.user_id, payload.utilisateur_id, payload.sub];\n          for (const id of possibleIds) {\n            if (id && !isNaN(id)) {\n              console.log('✅ ID trouvé dans JWT:', id);\n              return parseInt(id);\n            }\n          }\n        }\n      } catch (error) {\n        console.warn('⚠️ Impossible de décoder le token JWT:', error);\n      }\n    }\n    console.warn('❌ Aucun ID utilisateur trouvé');\n    return null;\n  };\n\n  // 🔍 Fonction pour vérifier si l'utilisateur est valide\n  const isUserValid = () => {\n    const userId = getCurrentUserId();\n    return userId && userId > 0;\n  };\n\n  // 🔧 Fonction de diagnostic complète\n  const diagnoseUserData = () => {\n    console.log('🔧 === DIAGNOSTIC COMPLET DES DONNÉES UTILISATEUR ===');\n\n    // 1. Contexte React\n    console.log('1️⃣ Contexte React AuthContext:');\n    console.log('   - user object:', user);\n    console.log('   - user keys:', user ? Object.keys(user) : 'N/A');\n    console.log('   - user values:', user ? Object.values(user) : 'N/A');\n\n    // 2. localStorage\n    console.log('2️⃣ localStorage:');\n    const storedUser = localStorage.getItem('user');\n    const storedToken = localStorage.getItem('token');\n    console.log('   - raw user data:', storedUser);\n    console.log('   - raw token:', storedToken);\n    if (storedUser) {\n      try {\n        const parsedUser = JSON.parse(storedUser);\n        console.log('   - parsed user:', parsedUser);\n        console.log('   - parsed user keys:', Object.keys(parsedUser));\n        console.log('   - parsed user values:', Object.values(parsedUser));\n      } catch (e) {\n        console.log('   - parsing error:', e.message);\n      }\n    }\n\n    // 3. sessionStorage\n    console.log('3️⃣ sessionStorage:');\n    const sessionUser = sessionStorage.getItem('user');\n    const sessionToken = sessionStorage.getItem('token');\n    console.log('   - session user:', sessionUser);\n    console.log('   - session token:', sessionToken);\n\n    // 4. Toutes les clés de stockage\n    console.log('4️⃣ Toutes les clés localStorage:');\n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i);\n      console.log(`   - ${key}: ${localStorage.getItem(key)}`);\n    }\n    console.log('🔧 === FIN DU DIAGNOSTIC ===');\n  };\n  const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n\n  // Scroll automatique vers le bas\n  const scrollToBottom = () => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: \"smooth\"\n      });\n    }\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  // Fermer le menu contextuel en cliquant ailleurs\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n        setShowContextMenu(null);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  // Fonction pour faire des requêtes API\n  const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n    try {\n      const token = localStorage.getItem('token') || 'test_user_1';\n      const config = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      };\n      if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n        config.body = JSON.stringify(data);\n      }\n      const url = `${API_BASE_URL}?action=${endpoint}`;\n      console.log('API Request:', {\n        url,\n        method,\n        endpoint,\n        token\n      });\n      const response = await fetch(url, config);\n      console.log('API Response Status:', response.status, response.statusText);\n      const result = await response.json();\n      console.log('API Response Data:', result);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur API');\n      }\n      return result;\n    } catch (error) {\n      console.error('Erreur API complète:', {\n        endpoint,\n        method,\n        error: error.message,\n        stack: error.stack\n      });\n      throw error;\n    }\n  };\n\n  // Charger les conversations avec confidentialité stricte\n  const loadConversations = async () => {\n    try {\n      var _result$data;\n      setLoading(true);\n      setError(''); // Réinitialiser l'erreur\n\n      // 🔍 Vérification robuste de l'utilisateur\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) {\n        console.warn('� Utilisateur non identifié, tentative de récupération...');\n\n        // Attendre un peu pour que le contexte se charge\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        const retryUserId = getCurrentUserId();\n        if (!retryUserId) {\n          throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n        }\n      }\n      const result = await makeAPIRequest('conversations');\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur lors du chargement des conversations');\n      }\n\n      // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n      const finalUserId = getCurrentUserId();\n\n      // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n      const secureConversations = (result.data || []).filter(conversation => {\n        // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n        const contactId = parseInt(conversation.contact_id);\n        return contactId && contactId !== finalUserId && contactId > 0;\n      });\n      console.log('🔒 Conversations sécurisées chargées:', {\n        total_received: ((_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.length) || 0,\n        secure_filtered: secureConversations.length,\n        user_id: finalUserId,\n        user_context: user ? 'Disponible' : 'Non disponible'\n      });\n      setConversations(secureConversations);\n    } catch (error) {\n      const errorMessage = error.message || 'Erreur inconnue';\n      setError('Impossible de charger les conversations: ' + errorMessage);\n      console.error('🚨 Erreur sécurité conversations:', {\n        error: errorMessage,\n        user_id: getCurrentUserId(),\n        user_context: user,\n        localStorage_user: localStorage.getItem('user'),\n        localStorage_token: localStorage.getItem('token')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les messages d'une conversation avec confidentialité stricte\n  const loadMessages = async contactId => {\n    try {\n      var _result$data2;\n      setLoading(true);\n      setError(''); // Réinitialiser l'erreur\n\n      // � Vérification robuste de l'utilisateur\n      const currentUserId = getCurrentUserId();\n      if (!currentUserId) {\n        console.warn('🚨 Utilisateur non identifié lors du chargement des messages');\n        throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n      }\n      const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n      if (!result.success) {\n        throw new Error(result.error || 'Erreur lors du chargement des messages');\n      }\n\n      // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n      const finalUserId = getCurrentUserId();\n\n      // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n      const secureMessages = (result.data || []).filter(message => {\n        const expediteurId = parseInt(message.expediteur_id);\n        const destinataireId = parseInt(message.destinataire_id);\n\n        // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n        return expediteurId === finalUserId || destinataireId === finalUserId;\n      }).map(message => {\n        // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n        const expediteurId = parseInt(message.expediteur_id);\n        return {\n          ...message,\n          message_type: expediteurId === finalUserId ? 'sent' : 'received',\n          is_own_message: expediteurId === finalUserId\n        };\n      });\n      console.log('🔒 Messages sécurisés chargés:', {\n        total_received: ((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : _result$data2.length) || 0,\n        secure_filtered: secureMessages.length,\n        user_id: finalUserId,\n        contact_id: contactId,\n        user_context: user ? 'Disponible' : 'Non disponible'\n      });\n      setMessages(secureMessages);\n    } catch (error) {\n      const errorMessage = error.message || 'Erreur inconnue';\n      setError('Impossible de charger les messages: ' + errorMessage);\n      console.error('🚨 Erreur sécurité messages:', {\n        error: errorMessage,\n        user_id: getCurrentUserId(),\n        contact_id: contactId,\n        user_context: user,\n        localStorage_user: localStorage.getItem('user'),\n        localStorage_token: localStorage.getItem('token')\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Charger les utilisateurs autorisés\n  const loadAuthorizedUsers = async () => {\n    try {\n      const result = await makeAPIRequest('users');\n      setAuthorizedUsers(result.data || []);\n    } catch (error) {\n      setError('Impossible de charger les utilisateurs: ' + error.message);\n    }\n  };\n\n  // Charger les statistiques\n  const loadStats = async () => {\n    try {\n      const result = await makeAPIRequest('stats');\n      setStats(result.data || {});\n    } catch (error) {\n      console.error('Erreur chargement stats:', error);\n    }\n  };\n\n  // Envoyer un message\n  const sendMessage = async () => {\n    if (!newMessage.trim()) return;\n    try {\n      const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n      if (!destinataireId) {\n        setError('Veuillez sélectionner un destinataire');\n        return;\n      }\n      await makeAPIRequest('send', 'POST', {\n        destinataire_id: destinataireId,\n        message: newMessage.trim()\n      });\n      setNewMessage('');\n      setShowNewConversation(false);\n\n      // Recharger les conversations et messages\n      await loadConversations();\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible d\\'envoyer le message: ' + error.message);\n    }\n  };\n\n  // Modifier un message\n  const editMessage = async (messageId, newContent) => {\n    try {\n      await makeAPIRequest('edit', 'PUT', {\n        message_id: messageId,\n        message: newContent\n      });\n      setEditingMessage(null);\n      setEditContent('');\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de modifier le message: ' + error.message);\n    }\n  };\n\n  // Supprimer un message\n  const deleteMessage = async (messageId, deleteType = 'for_me') => {\n    try {\n      await makeAPIRequest('delete', 'DELETE', {\n        message_id: messageId,\n        delete_type: deleteType\n      });\n      setShowContextMenu(null);\n\n      // Recharger les messages\n      if (selectedConversation) {\n        await loadMessages(selectedConversation.contact_id);\n      }\n    } catch (error) {\n      setError('Impossible de supprimer le message: ' + error.message);\n    }\n  };\n\n  // Sélectionner une conversation\n  const selectConversation = async conversation => {\n    setSelectedConversation(conversation);\n    setShowNewConversation(false);\n    await loadMessages(conversation.contact_id);\n  };\n\n  // Démarrer une nouvelle conversation\n  const startNewConversation = () => {\n    setSelectedConversation(null);\n    setMessages([]);\n    setShowNewConversation(true);\n  };\n\n  // Gérer le menu contextuel\n  const handleContextMenu = (e, message) => {\n    e.preventDefault();\n    setShowContextMenu({\n      x: e.clientX,\n      y: e.clientY,\n      message: message\n    });\n  };\n\n  // Démarrer l'édition d'un message\n  const startEditing = message => {\n    setEditingMessage(message.id);\n    setEditContent(message.message);\n    setShowContextMenu(null);\n  };\n\n  // Annuler l'édition\n  const cancelEditing = () => {\n    setEditingMessage(null);\n    setEditContent('');\n  };\n\n  // Confirmer l'édition\n  const confirmEdit = async () => {\n    if (editContent.trim() && editingMessage) {\n      await editMessage(editingMessage, editContent.trim());\n    }\n  };\n\n  // Formater la date\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) {\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else if (diffDays <= 7) {\n      return date.toLocaleDateString('fr-FR', {\n        weekday: 'short',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } else {\n      return date.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n  };\n\n  // Charger les données au montage du composant\n  useEffect(() => {\n    const initializeMessaging = async () => {\n      console.log('🚀 Initialisation du système de messagerie...');\n\n      // Attendre un peu pour que le contexte d'authentification se charge\n      if (!user) {\n        console.log('⏳ Attente du chargement du contexte utilisateur...');\n        await new Promise(resolve => setTimeout(resolve, 500));\n      }\n      const userId = getCurrentUserId();\n      console.log('👤 ID utilisateur détecté:', userId);\n      if (userId) {\n        console.log('✅ Utilisateur valide, chargement des données...');\n        loadConversations();\n        loadAuthorizedUsers();\n        loadStats();\n      } else {\n        console.warn('⚠️ Aucun utilisateur valide trouvé');\n        setError('Utilisateur non identifié. Veuillez vous reconnecter.');\n\n        // Essayer de charger quand même après un délai\n        setTimeout(() => {\n          const retryUserId = getCurrentUserId();\n          if (retryUserId) {\n            console.log('🔄 Retry réussi, chargement des données...');\n            setError(''); // Effacer l'erreur\n            loadConversations();\n            loadAuthorizedUsers();\n            loadStats();\n          }\n        }, 2000);\n      }\n    };\n    initializeMessaging();\n  }, [user]);\n\n  // Actualiser périodiquement\n  useEffect(() => {\n    const interval = setInterval(() => {\n      loadConversations();\n      if (selectedConversation) {\n        loadMessages(selectedConversation.contact_id);\n      }\n    }, 30000); // Actualiser toutes les 30 secondes\n\n    return () => clearInterval(interval);\n  }, [selectedConversation]);\n\n  // Vérification de l'utilisateur avant affichage\n  const currentUserId = getCurrentUserId();\n\n  // Affichage d'erreur d'authentification\n  if (!currentUserId && !loading) {\n    // Exécuter le diagnostic automatiquement\n    React.useEffect(() => {\n      diagnoseUserData();\n    }, []);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"messaging-system\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 558,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-error-container\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 17\n      }\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-error-content\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 560,\n        columnNumber: 21\n      }\n    }, /*#__PURE__*/React.createElement(\"h2\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 25\n      }\n    }, \"\\uD83D\\uDD10 Probl\\xE8me d'Authentification D\\xE9tect\\xE9\"), /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 25\n      }\n    }, \"L'utilisateur est connect\\xE9 mais son ID n'est pas accessible.\"), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"debug-info\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"h3\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD0D Informations de Debug :\"), /*#__PURE__*/React.createElement(\"ul\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 566,\n        columnNumber: 29\n      }\n    }, /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 33\n      }\n    }, \"Contexte utilisateur: \", user ? '✅ Chargé' : '❌ Non chargé'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 33\n      }\n    }, \"Nom utilisateur: \", (user === null || user === void 0 ? void 0 : user.nom) || (user === null || user === void 0 ? void 0 : user.name) || 'Non défini'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 33\n      }\n    }, \"Email utilisateur: \", (user === null || user === void 0 ? void 0 : user.email) || 'Non défini'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 33\n      }\n    }, \"ID utilisateur (user.id): \", (user === null || user === void 0 ? void 0 : user.id) || 'Non défini'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 33\n      }\n    }, \"ID utilisateur (user.user_id): \", (user === null || user === void 0 ? void 0 : user.user_id) || 'Non défini'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 572,\n        columnNumber: 33\n      }\n    }, \"ID utilisateur (user.utilisateur_id): \", (user === null || user === void 0 ? void 0 : user.utilisateur_id) || 'Non défini'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 33\n      }\n    }, \"Token localStorage: \", localStorage.getItem('token') ? '✅ Présent' : '❌ Absent'), /*#__PURE__*/React.createElement(\"li\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 33\n      }\n    }, \"Donn\\xE9es utilisateur: \", localStorage.getItem('user') ? '✅ Présentes' : '❌ Absentes')), localStorage.getItem('user') && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"raw-data\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 33\n      }\n    }, /*#__PURE__*/React.createElement(\"h4\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 579,\n        columnNumber: 37\n      }\n    }, \"\\uD83D\\uDCCA Donn\\xE9es Brutes localStorage :\"), /*#__PURE__*/React.createElement(\"pre\", {\n      className: \"code-block\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 37\n      }\n    }, localStorage.getItem('user')))), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"auth-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 587,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      onClick: diagnoseUserData,\n      className: \"btn btn-warning\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD27 Diagnostic Console\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: () => {\n        // Essayer de corriger automatiquement\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          try {\n            const userData = JSON.parse(storedUser);\n            console.log('🔧 Tentative de correction automatique:', userData);\n\n            // Essayer de trouver un ID dans les données\n            const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID];\n            const foundId = possibleIds.find(id => id && !isNaN(id));\n            if (foundId) {\n              // Standardiser sur 'id'\n              userData.id = parseInt(foundId);\n              localStorage.setItem('user', JSON.stringify(userData));\n              console.log('✅ Correction appliquée, rechargement...');\n              window.location.reload();\n            } else {\n              alert('❌ Aucun ID valide trouvé dans les données utilisateur');\n            }\n          } catch (e) {\n            alert('❌ Erreur lors de la correction: ' + e.message);\n          }\n        }\n      },\n      className: \"btn btn-success\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD27 Correction Auto\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: () => window.location.href = '/login',\n      className: \"btn btn-primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD11 Se Reconnecter\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: () => window.location.reload(),\n      className: \"btn btn-secondary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 29\n      }\n    }, \"\\uD83D\\uDD04 Actualiser\")))));\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-system\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 646,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"h1\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 17\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"user-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 648,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"current-user\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 649,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDC64 \", (user === null || user === void 0 ? void 0 : user.nom) || 'Utilisateur', \" (ID: \", currentUserId, \")\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-stats\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 653,\n      columnNumber: 25\n    }\n  }, stats.total_messages || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 25\n    }\n  }, \"Messages\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 25\n    }\n  }, stats.messages_non_lus || 0), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 658,\n      columnNumber: 25\n    }\n  }, \"Non lus\")), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-item\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 660,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-number\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 661,\n      columnNumber: 25\n    }\n  }, conversations.length), /*#__PURE__*/React.createElement(\"span\", {\n    className: \"stat-label\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 25\n    }\n  }, \"Conversations\")))), error && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-message\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 21\n    }\n  }, \"\\u274C \", error), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setError(''),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 21\n    }\n  }, \"\\u2715\"), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"error-debug\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"small\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 25\n    }\n  }, \"Debug: User ID = \", getCurrentUserId(), \", Context = \", user ? 'OK' : 'KO'))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messaging-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 677,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 680,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 681,\n      columnNumber: 25\n    }\n  }, \"Conversations\"), /*#__PURE__*/React.createElement(\"button\", {\n    className: \"new-conversation-btn\",\n    onClick: startNewConversation,\n    title: \"Nouvelle conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversations-list\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 691,\n      columnNumber: 21\n    }\n  }, loading && conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"loading\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 693,\n      columnNumber: 29\n    }\n  }, \"Chargement...\") : conversations.length === 0 ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-conversations\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 33\n    }\n  }, \"Aucune conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 697,\n      columnNumber: 33\n    }\n  }, \"D\\xE9marrer une conversation\")) : conversations.map(conversation => /*#__PURE__*/React.createElement(\"div\", {\n    key: conversation.contact_id,\n    className: `conversation-item ${(selectedConversation === null || selectedConversation === void 0 ? void 0 : selectedConversation.contact_id) === conversation.contact_id ? 'active' : ''}`,\n    onClick: () => selectConversation(conversation),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 37\n    }\n  }, conversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 711,\n      columnNumber: 37\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 41\n    }\n  }, conversation.contact_nom, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 714,\n      columnNumber: 45\n    }\n  }, conversation.contact_role)), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-preview\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 41\n    }\n  }, conversation.dernier_message || 'Aucun message'), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"conversation-meta\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 721,\n      columnNumber: 41\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    className: \"conversation-time\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 45\n    }\n  }, formatDate(conversation.derniere_activite)), conversation.messages_non_lus > 0 && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"unread-badge\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 726,\n      columnNumber: 49\n    }\n  }, conversation.messages_non_lus))))))), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-panel\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 739,\n      columnNumber: 17\n    }\n  }, showNewConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 743,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => setShowNewConversation(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 744,\n      columnNumber: 33\n    }\n  }, \"\\u2715\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"new-conversation-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"select\", {\n    value: selectedUser,\n    onChange: e => setSelectedUser(e.target.value),\n    className: \"user-select\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 747,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"option\", {\n    value: \"\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 752,\n      columnNumber: 37\n    }\n  }, \"S\\xE9lectionner un utilisateur...\"), authorizedUsers.map(user => /*#__PURE__*/React.createElement(\"option\", {\n    key: user.id,\n    value: user.id,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 754,\n      columnNumber: 41\n    }\n  }, user.nom, \" (\", user.role, \")\"))))) : selectedConversation ? /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-header\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 762,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-info\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 763,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-avatar\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 764,\n      columnNumber: 33\n    }\n  }, selectedConversation.contact_nom.charAt(0).toUpperCase()), /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 767,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 768,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_nom), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"chat-contact-role\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 771,\n      columnNumber: 37\n    }\n  }, selectedConversation.contact_role)))) : /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-selected\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 778,\n      columnNumber: 25\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"no-chat-content\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 779,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"h3\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 780,\n      columnNumber: 33\n    }\n  }, \"\\uD83D\\uDCAC Messagerie\"), /*#__PURE__*/React.createElement(\"p\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 781,\n      columnNumber: 33\n    }\n  }, \"S\\xE9lectionnez une conversation ou d\\xE9marrez-en une nouvelle\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: startNewConversation,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 782,\n      columnNumber: 33\n    }\n  }, \"Nouvelle conversation\"))), (selectedConversation || showNewConversation) && /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"messages-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 792,\n      columnNumber: 29\n    }\n  }, messages.map(message => {\n    const currentUserId = getCurrentUserId();\n    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n    const messageType = isOwnMessage ? 'sent' : 'received';\n    return /*#__PURE__*/React.createElement(\"div\", {\n      key: message.id,\n      className: `message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`,\n      onContextMenu: e => handleContextMenu(e, message),\n      \"data-sender-id\": message.expediteur_id,\n      \"data-receiver-id\": message.destinataire_id,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 41\n      }\n    }, !isOwnMessage && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-sender\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 808,\n        columnNumber: 49\n      }\n    }, message.expediteur_nom || 'Utilisateur'), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-content ${messageType}-content`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 45\n      }\n    }, editingMessage === message.id ? /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 53\n      }\n    }, /*#__PURE__*/React.createElement(\"textarea\", {\n      value: editContent,\n      onChange: e => setEditContent(e.target.value),\n      className: \"edit-textarea\",\n      autoFocus: true,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 816,\n        columnNumber: 57\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: \"edit-actions\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 822,\n        columnNumber: 57\n      }\n    }, /*#__PURE__*/React.createElement(\"button\", {\n      onClick: confirmEdit,\n      className: \"confirm-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 823,\n        columnNumber: 61\n      }\n    }, \"\\u2713\"), /*#__PURE__*/React.createElement(\"button\", {\n      onClick: cancelEditing,\n      className: \"cancel-edit\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 826,\n        columnNumber: 61\n      }\n    }, \"\\u2715\"))) : /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-text\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 57\n      }\n    }, message.message, message.modifie === '1' && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-edited\",\n      title: `Modifié le ${formatDate(message.date_modification)}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 836,\n        columnNumber: 65\n      }\n    }, \"(modifi\\xE9)\")), /*#__PURE__*/React.createElement(\"div\", {\n      className: `message-time ${messageType}-time`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 841,\n        columnNumber: 57\n      }\n    }, formatDate(message.date_envoi), isOwnMessage && /*#__PURE__*/React.createElement(\"span\", {\n      className: \"message-status\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 844,\n        columnNumber: 65\n      }\n    }, message.lu === '1' ? '✓✓' : '✓')))), process.env.NODE_ENV === 'development' && /*#__PURE__*/React.createElement(\"div\", {\n      className: \"message-debug\",\n      title: `Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 49\n      }\n    }, \"\\uD83D\\uDD12\"));\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    ref: messagesEndRef,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 862,\n      columnNumber: 33\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-container\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 866,\n      columnNumber: 29\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: \"message-input-wrapper\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 867,\n      columnNumber: 33\n    }\n  }, /*#__PURE__*/React.createElement(\"textarea\", {\n    value: newMessage,\n    onChange: e => setNewMessage(e.target.value),\n    placeholder: \"Tapez votre message...\",\n    className: \"message-input\",\n    rows: \"1\",\n    onKeyDown: e => {\n      if (e.key === 'Enter' && !e.shiftKey) {\n        e.preventDefault();\n        sendMessage();\n      }\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 868,\n      columnNumber: 37\n    }\n  }), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: sendMessage,\n    className: \"send-button\",\n    disabled: !newMessage.trim() || loading,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 881,\n      columnNumber: 37\n    }\n  }, \"\\uD83D\\uDCE4\")))))), showContextMenu && /*#__PURE__*/React.createElement(\"div\", {\n    ref: contextMenuRef,\n    className: \"context-menu\",\n    style: {\n      left: showContextMenu.x,\n      top: showContextMenu.y\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 897,\n      columnNumber: 17\n    }\n  }, showContextMenu.message.can_modify === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => startEditing(showContextMenu.message),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 906,\n      columnNumber: 25\n    }\n  }, \"\\u270F\\uFE0F Modifier\"), /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_me'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 910,\n      columnNumber: 21\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour moi\"), showContextMenu.message.can_delete_for_all === 1 && /*#__PURE__*/React.createElement(\"button\", {\n    onClick: () => deleteMessage(showContextMenu.message.id, 'for_everyone'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 914,\n      columnNumber: 25\n    }\n  }, \"\\uD83D\\uDDD1\\uFE0F Supprimer pour tous\")));\n};\nexport default MessagingSystem;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useContext", "AuthContext", "MessagingSystem", "user", "isLoading", "authLoading", "isAuthenticated", "conversations", "setConversations", "selectedConversation", "setSelectedConversation", "messages", "setMessages", "newMessage", "setNewMessage", "authorizedUsers", "setAuthorizedUsers", "showNewConversation", "setShowNewConversation", "selected<PERSON>ser", "setSelectedUser", "loading", "setLoading", "error", "setError", "editingMessage", "setEditingMessage", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showContextMenu", "setShowContextMenu", "stats", "setStats", "messagesEndRef", "contextMenuRef", "getCurrentUserId", "console", "log", "possibleIds", "id", "user_id", "utilisateur_id", "ID", "User_ID", "isNaN", "parseInt", "storedUser", "localStorage", "getItem", "userData", "JSON", "parse", "warn", "token", "includes", "userId", "split", "pop", "payload", "atob", "sub", "isUserValid", "diagnoseUserData", "Object", "keys", "values", "storedToken", "parsedUser", "e", "message", "sessionUser", "sessionStorage", "sessionToken", "i", "length", "key", "API_BASE_URL", "scrollToBottom", "current", "scrollIntoView", "behavior", "handleClickOutside", "event", "contains", "target", "document", "addEventListener", "removeEventListener", "makeAPIRequest", "endpoint", "method", "data", "config", "headers", "body", "stringify", "url", "response", "fetch", "status", "statusText", "result", "json", "success", "Error", "stack", "loadConversations", "_result$data", "currentUserId", "Promise", "resolve", "setTimeout", "retryUserId", "finalUserId", "secureConversations", "filter", "conversation", "contactId", "contact_id", "total_received", "secure_filtered", "user_context", "errorMessage", "localStorage_user", "localStorage_token", "loadMessages", "_result$data2", "secureMessages", "expediteurId", "expediteur_id", "destinataireId", "destinataire_id", "map", "message_type", "is_own_message", "loadAuthorizedUsers", "loadStats", "sendMessage", "trim", "editMessage", "messageId", "newContent", "message_id", "deleteMessage", "deleteType", "delete_type", "selectConversation", "startNewConversation", "handleContextMenu", "preventDefault", "x", "clientX", "y", "clientY", "startEditing", "cancelEditing", "confirmEdit", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "day", "month", "initializeMessaging", "interval", "setInterval", "clearInterval", "createElement", "className", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "nom", "name", "email", "onClick", "foundId", "find", "setItem", "window", "location", "reload", "alert", "href", "total_messages", "messages_non_lus", "title", "contact_nom", "char<PERSON>t", "toUpperCase", "contact_role", "dernier_message", "derniere_activite", "value", "onChange", "role", "Fragment", "isOwnMessage", "messageType", "onContextMenu", "expediteur_nom", "autoFocus", "modifie", "date_modification", "date_envoi", "lu", "process", "env", "NODE_ENV", "ref", "placeholder", "rows", "onKeyDown", "shift<PERSON>ey", "disabled", "style", "left", "top", "can_modify", "can_delete_for_all"], "sources": ["C:/laragon/www/Project_PFE/Frantend/schoolproject/src/pages/MessagingSystem.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useContext } from 'react';\nimport { AuthContext } from '../context/AuthContext';\nimport '../styles/MessagingSystem.css';\n\nconst MessagingSystem = () => {\n    const { user, isLoading: authLoading, isAuthenticated } = useContext(AuthContext);\n    const [conversations, setConversations] = useState([]);\n    const [selectedConversation, setSelectedConversation] = useState(null);\n    const [messages, setMessages] = useState([]);\n    const [newMessage, setNewMessage] = useState('');\n    const [authorizedUsers, setAuthorizedUsers] = useState([]);\n    const [showNewConversation, setShowNewConversation] = useState(false);\n    const [selectedUser, setSelectedUser] = useState('');\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [editingMessage, setEditingMessage] = useState(null);\n    const [editContent, setEditContent] = useState('');\n    const [showContextMenu, setShowContextMenu] = useState(null);\n    const [stats, setStats] = useState({});\n\n    const messagesEndRef = useRef(null);\n    const contextMenuRef = useRef(null);\n\n    // 🔍 Fonction pour obtenir l'ID utilisateur de manière sécurisée\n    const getCurrentUserId = () => {\n        console.log('🔍 Tentative de récupération de l\\'ID utilisateur...');\n        console.log('👤 Contexte user:', user);\n\n        // Essayer d'abord depuis le contexte user avec différentes propriétés possibles\n        if (user) {\n            // Vérifier différentes propriétés possibles pour l'ID\n            const possibleIds = [user.id, user.user_id, user.utilisateur_id, user.ID, user.User_ID];\n            for (const id of possibleIds) {\n                if (id && !isNaN(id)) {\n                    console.log('✅ ID trouvé dans le contexte:', id);\n                    return parseInt(id);\n                }\n            }\n        }\n\n        // Essayer depuis localStorage comme fallback\n        try {\n            const storedUser = localStorage.getItem('user');\n            console.log('📦 Données localStorage user:', storedUser);\n\n            if (storedUser) {\n                const userData = JSON.parse(storedUser);\n                console.log('📊 Données utilisateur parsées:', userData);\n\n                if (userData) {\n                    // Vérifier différentes propriétés possibles pour l'ID\n                    const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID, userData.User_ID];\n                    for (const id of possibleIds) {\n                        if (id && !isNaN(id)) {\n                            console.log('✅ ID trouvé dans localStorage:', id);\n                            return parseInt(id);\n                        }\n                    }\n                }\n            }\n        } catch (error) {\n            console.warn('❌ Erreur lors de la récupération de l\\'utilisateur depuis localStorage:', error);\n        }\n\n        // Dernière tentative avec le token\n        const token = localStorage.getItem('token');\n        console.log('🔑 Token localStorage:', token);\n\n        if (token) {\n            // Essayer différents formats de token\n            if (token.includes('_')) {\n                const userId = token.split('_').pop();\n                if (userId && !isNaN(userId)) {\n                    console.log('✅ ID trouvé dans le token:', userId);\n                    return parseInt(userId);\n                }\n            }\n\n            // Essayer de décoder le token s'il est en base64 ou JWT\n            try {\n                if (token.includes('.')) {\n                    // Format JWT potentiel\n                    const payload = JSON.parse(atob(token.split('.')[1]));\n                    const possibleIds = [payload.id, payload.user_id, payload.utilisateur_id, payload.sub];\n                    for (const id of possibleIds) {\n                        if (id && !isNaN(id)) {\n                            console.log('✅ ID trouvé dans JWT:', id);\n                            return parseInt(id);\n                        }\n                    }\n                }\n            } catch (error) {\n                console.warn('⚠️ Impossible de décoder le token JWT:', error);\n            }\n        }\n\n        console.warn('❌ Aucun ID utilisateur trouvé');\n        return null;\n    };\n\n    // 🔍 Fonction pour vérifier si l'utilisateur est valide\n    const isUserValid = () => {\n        const userId = getCurrentUserId();\n        return userId && userId > 0;\n    };\n\n    // 🔧 Fonction de diagnostic complète\n    const diagnoseUserData = () => {\n        console.log('🔧 === DIAGNOSTIC COMPLET DES DONNÉES UTILISATEUR ===');\n\n        // 1. Contexte React\n        console.log('1️⃣ Contexte React AuthContext:');\n        console.log('   - user object:', user);\n        console.log('   - user keys:', user ? Object.keys(user) : 'N/A');\n        console.log('   - user values:', user ? Object.values(user) : 'N/A');\n\n        // 2. localStorage\n        console.log('2️⃣ localStorage:');\n        const storedUser = localStorage.getItem('user');\n        const storedToken = localStorage.getItem('token');\n        console.log('   - raw user data:', storedUser);\n        console.log('   - raw token:', storedToken);\n\n        if (storedUser) {\n            try {\n                const parsedUser = JSON.parse(storedUser);\n                console.log('   - parsed user:', parsedUser);\n                console.log('   - parsed user keys:', Object.keys(parsedUser));\n                console.log('   - parsed user values:', Object.values(parsedUser));\n            } catch (e) {\n                console.log('   - parsing error:', e.message);\n            }\n        }\n\n        // 3. sessionStorage\n        console.log('3️⃣ sessionStorage:');\n        const sessionUser = sessionStorage.getItem('user');\n        const sessionToken = sessionStorage.getItem('token');\n        console.log('   - session user:', sessionUser);\n        console.log('   - session token:', sessionToken);\n\n        // 4. Toutes les clés de stockage\n        console.log('4️⃣ Toutes les clés localStorage:');\n        for (let i = 0; i < localStorage.length; i++) {\n            const key = localStorage.key(i);\n            console.log(`   - ${key}: ${localStorage.getItem(key)}`);\n        }\n\n        console.log('🔧 === FIN DU DIAGNOSTIC ===');\n    };\n    \n    const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';\n    \n    // Scroll automatique vers le bas\n    const scrollToBottom = () => {\n        if (messagesEndRef.current) {\n            messagesEndRef.current.scrollIntoView({ behavior: \"smooth\" });\n        }\n    };\n    \n    useEffect(() => {\n        scrollToBottom();\n    }, [messages]);\n    \n    // Fermer le menu contextuel en cliquant ailleurs\n    useEffect(() => {\n        const handleClickOutside = (event) => {\n            if (contextMenuRef.current && !contextMenuRef.current.contains(event.target)) {\n                setShowContextMenu(null);\n            }\n        };\n        \n        document.addEventListener('mousedown', handleClickOutside);\n        return () => document.removeEventListener('mousedown', handleClickOutside);\n    }, []);\n    \n    // Fonction pour faire des requêtes API\n    const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {\n        try {\n            const token = localStorage.getItem('token') || 'test_user_1';\n\n            const config = {\n                method,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': `Bearer ${token}`\n                }\n            };\n\n            if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {\n                config.body = JSON.stringify(data);\n            }\n\n            const url = `${API_BASE_URL}?action=${endpoint}`;\n            console.log('API Request:', { url, method, endpoint, token });\n\n            const response = await fetch(url, config);\n            console.log('API Response Status:', response.status, response.statusText);\n\n            const result = await response.json();\n            console.log('API Response Data:', result);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur API');\n            }\n\n            return result;\n        } catch (error) {\n            console.error('Erreur API complète:', {\n                endpoint,\n                method,\n                error: error.message,\n                stack: error.stack\n            });\n            throw error;\n        }\n    };\n    \n    // Charger les conversations avec confidentialité stricte\n    const loadConversations = async () => {\n        try {\n            setLoading(true);\n            setError(''); // Réinitialiser l'erreur\n\n            // 🔍 Vérification robuste de l'utilisateur\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) {\n                console.warn('� Utilisateur non identifié, tentative de récupération...');\n\n                // Attendre un peu pour que le contexte se charge\n                await new Promise(resolve => setTimeout(resolve, 1000));\n\n                const retryUserId = getCurrentUserId();\n                if (!retryUserId) {\n                    throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n                }\n            }\n\n            const result = await makeAPIRequest('conversations');\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur lors du chargement des conversations');\n            }\n\n            // 🛡️ SÉCURITÉ FRONTEND: Vérification supplémentaire des conversations\n            const finalUserId = getCurrentUserId();\n\n            // Les conversations sont déjà filtrées côté backend, mais on ajoute une couche de sécurité\n            const secureConversations = (result.data || []).filter(conversation => {\n                // Vérifier que la conversation a un contact_id valide et différent de l'utilisateur\n                const contactId = parseInt(conversation.contact_id);\n                return contactId && contactId !== finalUserId && contactId > 0;\n            });\n\n            console.log('🔒 Conversations sécurisées chargées:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureConversations.length,\n                user_id: finalUserId,\n                user_context: user ? 'Disponible' : 'Non disponible'\n            });\n\n            setConversations(secureConversations);\n        } catch (error) {\n            const errorMessage = error.message || 'Erreur inconnue';\n            setError('Impossible de charger les conversations: ' + errorMessage);\n            console.error('🚨 Erreur sécurité conversations:', {\n                error: errorMessage,\n                user_id: getCurrentUserId(),\n                user_context: user,\n                localStorage_user: localStorage.getItem('user'),\n                localStorage_token: localStorage.getItem('token')\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les messages d'une conversation avec confidentialité stricte\n    const loadMessages = async (contactId) => {\n        try {\n            setLoading(true);\n            setError(''); // Réinitialiser l'erreur\n\n            // � Vérification robuste de l'utilisateur\n            const currentUserId = getCurrentUserId();\n            if (!currentUserId) {\n                console.warn('🚨 Utilisateur non identifié lors du chargement des messages');\n                throw new Error('Utilisateur non identifié. Veuillez vous reconnecter.');\n            }\n\n            const result = await makeAPIRequest(`messages&contact_id=${contactId}`);\n\n            if (!result.success) {\n                throw new Error(result.error || 'Erreur lors du chargement des messages');\n            }\n\n            // 🛡️ SÉCURITÉ FRONTEND: Double vérification de confidentialité\n            const finalUserId = getCurrentUserId();\n\n            // Filtrer les messages pour s'assurer qu'ils concernent bien l'utilisateur connecté\n            const secureMessages = (result.data || []).filter(message => {\n                const expediteurId = parseInt(message.expediteur_id);\n                const destinataireId = parseInt(message.destinataire_id);\n\n                // 🔒 RÈGLE STRICTE: Le message doit impliquer l'utilisateur connecté\n                return (expediteurId === finalUserId || destinataireId === finalUserId);\n            }).map(message => {\n                // 🎯 DÉTERMINER LE TYPE DE MESSAGE (sent/received)\n                const expediteurId = parseInt(message.expediteur_id);\n\n                return {\n                    ...message,\n                    message_type: expediteurId === finalUserId ? 'sent' : 'received',\n                    is_own_message: expediteurId === finalUserId\n                };\n            });\n\n            console.log('🔒 Messages sécurisés chargés:', {\n                total_received: result.data?.length || 0,\n                secure_filtered: secureMessages.length,\n                user_id: finalUserId,\n                contact_id: contactId,\n                user_context: user ? 'Disponible' : 'Non disponible'\n            });\n\n            setMessages(secureMessages);\n        } catch (error) {\n            const errorMessage = error.message || 'Erreur inconnue';\n            setError('Impossible de charger les messages: ' + errorMessage);\n            console.error('🚨 Erreur sécurité messages:', {\n                error: errorMessage,\n                user_id: getCurrentUserId(),\n                contact_id: contactId,\n                user_context: user,\n                localStorage_user: localStorage.getItem('user'),\n                localStorage_token: localStorage.getItem('token')\n            });\n        } finally {\n            setLoading(false);\n        }\n    };\n    \n    // Charger les utilisateurs autorisés\n    const loadAuthorizedUsers = async () => {\n        try {\n            const result = await makeAPIRequest('users');\n            setAuthorizedUsers(result.data || []);\n        } catch (error) {\n            setError('Impossible de charger les utilisateurs: ' + error.message);\n        }\n    };\n    \n    // Charger les statistiques\n    const loadStats = async () => {\n        try {\n            const result = await makeAPIRequest('stats');\n            setStats(result.data || {});\n        } catch (error) {\n            console.error('Erreur chargement stats:', error);\n        }\n    };\n    \n    // Envoyer un message\n    const sendMessage = async () => {\n        if (!newMessage.trim()) return;\n        \n        try {\n            const destinataireId = selectedConversation ? selectedConversation.contact_id : selectedUser;\n            \n            if (!destinataireId) {\n                setError('Veuillez sélectionner un destinataire');\n                return;\n            }\n            \n            await makeAPIRequest('send', 'POST', {\n                destinataire_id: destinataireId,\n                message: newMessage.trim()\n            });\n            \n            setNewMessage('');\n            setShowNewConversation(false);\n            \n            // Recharger les conversations et messages\n            await loadConversations();\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible d\\'envoyer le message: ' + error.message);\n        }\n    };\n    \n    // Modifier un message\n    const editMessage = async (messageId, newContent) => {\n        try {\n            await makeAPIRequest('edit', 'PUT', {\n                message_id: messageId,\n                message: newContent\n            });\n            \n            setEditingMessage(null);\n            setEditContent('');\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de modifier le message: ' + error.message);\n        }\n    };\n    \n    // Supprimer un message\n    const deleteMessage = async (messageId, deleteType = 'for_me') => {\n        try {\n            await makeAPIRequest('delete', 'DELETE', {\n                message_id: messageId,\n                delete_type: deleteType\n            });\n            \n            setShowContextMenu(null);\n            \n            // Recharger les messages\n            if (selectedConversation) {\n                await loadMessages(selectedConversation.contact_id);\n            }\n            \n        } catch (error) {\n            setError('Impossible de supprimer le message: ' + error.message);\n        }\n    };\n    \n    // Sélectionner une conversation\n    const selectConversation = async (conversation) => {\n        setSelectedConversation(conversation);\n        setShowNewConversation(false);\n        await loadMessages(conversation.contact_id);\n    };\n    \n    // Démarrer une nouvelle conversation\n    const startNewConversation = () => {\n        setSelectedConversation(null);\n        setMessages([]);\n        setShowNewConversation(true);\n    };\n    \n    // Gérer le menu contextuel\n    const handleContextMenu = (e, message) => {\n        e.preventDefault();\n        setShowContextMenu({\n            x: e.clientX,\n            y: e.clientY,\n            message: message\n        });\n    };\n    \n    // Démarrer l'édition d'un message\n    const startEditing = (message) => {\n        setEditingMessage(message.id);\n        setEditContent(message.message);\n        setShowContextMenu(null);\n    };\n    \n    // Annuler l'édition\n    const cancelEditing = () => {\n        setEditingMessage(null);\n        setEditContent('');\n    };\n    \n    // Confirmer l'édition\n    const confirmEdit = async () => {\n        if (editContent.trim() && editingMessage) {\n            await editMessage(editingMessage, editContent.trim());\n        }\n    };\n    \n    // Formater la date\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffTime = Math.abs(now - date);\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n        \n        if (diffDays === 1) {\n            return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });\n        } else if (diffDays <= 7) {\n            return date.toLocaleDateString('fr-FR', { weekday: 'short', hour: '2-digit', minute: '2-digit' });\n        } else {\n            return date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit', hour: '2-digit', minute: '2-digit' });\n        }\n    };\n    \n    // Charger les données au montage du composant\n    useEffect(() => {\n        const initializeMessaging = async () => {\n            console.log('🚀 Initialisation du système de messagerie...');\n\n            // Attendre un peu pour que le contexte d'authentification se charge\n            if (!user) {\n                console.log('⏳ Attente du chargement du contexte utilisateur...');\n                await new Promise(resolve => setTimeout(resolve, 500));\n            }\n\n            const userId = getCurrentUserId();\n            console.log('👤 ID utilisateur détecté:', userId);\n\n            if (userId) {\n                console.log('✅ Utilisateur valide, chargement des données...');\n                loadConversations();\n                loadAuthorizedUsers();\n                loadStats();\n            } else {\n                console.warn('⚠️ Aucun utilisateur valide trouvé');\n                setError('Utilisateur non identifié. Veuillez vous reconnecter.');\n\n                // Essayer de charger quand même après un délai\n                setTimeout(() => {\n                    const retryUserId = getCurrentUserId();\n                    if (retryUserId) {\n                        console.log('🔄 Retry réussi, chargement des données...');\n                        setError(''); // Effacer l'erreur\n                        loadConversations();\n                        loadAuthorizedUsers();\n                        loadStats();\n                    }\n                }, 2000);\n            }\n        };\n\n        initializeMessaging();\n    }, [user]);\n    \n    // Actualiser périodiquement\n    useEffect(() => {\n        const interval = setInterval(() => {\n            loadConversations();\n            if (selectedConversation) {\n                loadMessages(selectedConversation.contact_id);\n            }\n        }, 30000); // Actualiser toutes les 30 secondes\n        \n        return () => clearInterval(interval);\n    }, [selectedConversation]);\n    \n    // Vérification de l'utilisateur avant affichage\n    const currentUserId = getCurrentUserId();\n\n    // Affichage d'erreur d'authentification\n    if (!currentUserId && !loading) {\n        // Exécuter le diagnostic automatiquement\n        React.useEffect(() => {\n            diagnoseUserData();\n        }, []);\n\n        return (\n            <div className=\"messaging-system\">\n                <div className=\"auth-error-container\">\n                    <div className=\"auth-error-content\">\n                        <h2>🔐 Problème d'Authentification Détecté</h2>\n                        <p>L'utilisateur est connecté mais son ID n'est pas accessible.</p>\n\n                        <div className=\"debug-info\">\n                            <h3>🔍 Informations de Debug :</h3>\n                            <ul>\n                                <li>Contexte utilisateur: {user ? '✅ Chargé' : '❌ Non chargé'}</li>\n                                <li>Nom utilisateur: {user?.nom || user?.name || 'Non défini'}</li>\n                                <li>Email utilisateur: {user?.email || 'Non défini'}</li>\n                                <li>ID utilisateur (user.id): {user?.id || 'Non défini'}</li>\n                                <li>ID utilisateur (user.user_id): {user?.user_id || 'Non défini'}</li>\n                                <li>ID utilisateur (user.utilisateur_id): {user?.utilisateur_id || 'Non défini'}</li>\n                                <li>Token localStorage: {localStorage.getItem('token') ? '✅ Présent' : '❌ Absent'}</li>\n                                <li>Données utilisateur: {localStorage.getItem('user') ? '✅ Présentes' : '❌ Absentes'}</li>\n                            </ul>\n\n                            {localStorage.getItem('user') && (\n                                <div className=\"raw-data\">\n                                    <h4>📊 Données Brutes localStorage :</h4>\n                                    <pre className=\"code-block\">\n                                        {localStorage.getItem('user')}\n                                    </pre>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"auth-actions\">\n                            <button\n                                onClick={diagnoseUserData}\n                                className=\"btn btn-warning\"\n                            >\n                                🔧 Diagnostic Console\n                            </button>\n                            <button\n                                onClick={() => {\n                                    // Essayer de corriger automatiquement\n                                    const storedUser = localStorage.getItem('user');\n                                    if (storedUser) {\n                                        try {\n                                            const userData = JSON.parse(storedUser);\n                                            console.log('🔧 Tentative de correction automatique:', userData);\n\n                                            // Essayer de trouver un ID dans les données\n                                            const possibleIds = [userData.id, userData.user_id, userData.utilisateur_id, userData.ID];\n                                            const foundId = possibleIds.find(id => id && !isNaN(id));\n\n                                            if (foundId) {\n                                                // Standardiser sur 'id'\n                                                userData.id = parseInt(foundId);\n                                                localStorage.setItem('user', JSON.stringify(userData));\n                                                console.log('✅ Correction appliquée, rechargement...');\n                                                window.location.reload();\n                                            } else {\n                                                alert('❌ Aucun ID valide trouvé dans les données utilisateur');\n                                            }\n                                        } catch (e) {\n                                            alert('❌ Erreur lors de la correction: ' + e.message);\n                                        }\n                                    }\n                                }}\n                                className=\"btn btn-success\"\n                            >\n                                🔧 Correction Auto\n                            </button>\n                            <button\n                                onClick={() => window.location.href = '/login'}\n                                className=\"btn btn-primary\"\n                            >\n                                🔑 Se Reconnecter\n                            </button>\n                            <button\n                                onClick={() => window.location.reload()}\n                                className=\"btn btn-secondary\"\n                            >\n                                🔄 Actualiser\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"messaging-system\">\n            <div className=\"messaging-header\">\n                <h1>💬 Messagerie</h1>\n                <div className=\"user-info\">\n                    <span className=\"current-user\">👤 {user?.nom || 'Utilisateur'} (ID: {currentUserId})</span>\n                </div>\n                <div className=\"messaging-stats\">\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.total_messages || 0}</span>\n                        <span className=\"stat-label\">Messages</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{stats.messages_non_lus || 0}</span>\n                        <span className=\"stat-label\">Non lus</span>\n                    </span>\n                    <span className=\"stat-item\">\n                        <span className=\"stat-number\">{conversations.length}</span>\n                        <span className=\"stat-label\">Conversations</span>\n                    </span>\n                </div>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <span>❌ {error}</span>\n                    <button onClick={() => setError('')}>✕</button>\n                    <div className=\"error-debug\">\n                        <small>Debug: User ID = {getCurrentUserId()}, Context = {user ? 'OK' : 'KO'}</small>\n                    </div>\n                </div>\n            )}\n            \n            <div className=\"messaging-container\">\n                {/* Liste des conversations */}\n                <div className=\"conversations-panel\">\n                    <div className=\"conversations-header\">\n                        <h3>Conversations</h3>\n                        <button \n                            className=\"new-conversation-btn\"\n                            onClick={startNewConversation}\n                            title=\"Nouvelle conversation\"\n                        >\n                            ✏️\n                        </button>\n                    </div>\n                    \n                    <div className=\"conversations-list\">\n                        {loading && conversations.length === 0 ? (\n                            <div className=\"loading\">Chargement...</div>\n                        ) : conversations.length === 0 ? (\n                            <div className=\"no-conversations\">\n                                <p>Aucune conversation</p>\n                                <button onClick={startNewConversation}>\n                                    Démarrer une conversation\n                                </button>\n                            </div>\n                        ) : (\n                            conversations.map(conversation => (\n                                <div\n                                    key={conversation.contact_id}\n                                    className={`conversation-item ${selectedConversation?.contact_id === conversation.contact_id ? 'active' : ''}`}\n                                    onClick={() => selectConversation(conversation)}\n                                >\n                                    <div className=\"conversation-avatar\">\n                                        {conversation.contact_nom.charAt(0).toUpperCase()}\n                                    </div>\n                                    <div className=\"conversation-info\">\n                                        <div className=\"conversation-name\">\n                                            {conversation.contact_nom}\n                                            <span className=\"conversation-role\">\n                                                {conversation.contact_role}\n                                            </span>\n                                        </div>\n                                        <div className=\"conversation-preview\">\n                                            {conversation.dernier_message || 'Aucun message'}\n                                        </div>\n                                        <div className=\"conversation-meta\">\n                                            <span className=\"conversation-time\">\n                                                {formatDate(conversation.derniere_activite)}\n                                            </span>\n                                            {conversation.messages_non_lus > 0 && (\n                                                <span className=\"unread-badge\">\n                                                    {conversation.messages_non_lus}\n                                                </span>\n                                            )}\n                                        </div>\n                                    </div>\n                                </div>\n                            ))\n                        )}\n                    </div>\n                </div>\n                \n                {/* Zone de chat */}\n                <div className=\"chat-panel\">\n                    {showNewConversation ? (\n                        <div className=\"new-conversation\">\n                            <div className=\"new-conversation-header\">\n                                <h3>Nouvelle conversation</h3>\n                                <button onClick={() => setShowNewConversation(false)}>✕</button>\n                            </div>\n                            <div className=\"new-conversation-content\">\n                                <select\n                                    value={selectedUser}\n                                    onChange={(e) => setSelectedUser(e.target.value)}\n                                    className=\"user-select\"\n                                >\n                                    <option value=\"\">Sélectionner un utilisateur...</option>\n                                    {authorizedUsers.map(user => (\n                                        <option key={user.id} value={user.id}>\n                                            {user.nom} ({user.role})\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n                    ) : selectedConversation ? (\n                        <div className=\"chat-header\">\n                            <div className=\"chat-contact-info\">\n                                <div className=\"chat-avatar\">\n                                    {selectedConversation.contact_nom.charAt(0).toUpperCase()}\n                                </div>\n                                <div>\n                                    <div className=\"chat-contact-name\">\n                                        {selectedConversation.contact_nom}\n                                    </div>\n                                    <div className=\"chat-contact-role\">\n                                        {selectedConversation.contact_role}\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    ) : (\n                        <div className=\"no-chat-selected\">\n                            <div className=\"no-chat-content\">\n                                <h3>💬 Messagerie</h3>\n                                <p>Sélectionnez une conversation ou démarrez-en une nouvelle</p>\n                                <button onClick={startNewConversation}>\n                                    Nouvelle conversation\n                                </button>\n                            </div>\n                        </div>\n                    )}\n                    \n                    {/* Messages */}\n                    {(selectedConversation || showNewConversation) && (\n                        <>\n                            <div className=\"messages-container\">\n                                {messages.map(message => {\n                                    const currentUserId = getCurrentUserId();\n                                    const isOwnMessage = parseInt(message.expediteur_id) === currentUserId;\n                                    const messageType = isOwnMessage ? 'sent' : 'received';\n\n                                    return (\n                                        <div\n                                            key={message.id}\n                                            className={`message ${messageType} ${isOwnMessage ? 'own-message' : 'other-message'}`}\n                                            onContextMenu={(e) => handleContextMenu(e, message)}\n                                            data-sender-id={message.expediteur_id}\n                                            data-receiver-id={message.destinataire_id}\n                                        >\n                                            {/* 👤 Affichage du nom de l'expéditeur pour les messages reçus */}\n                                            {!isOwnMessage && (\n                                                <div className=\"message-sender\">\n                                                    {message.expediteur_nom || 'Utilisateur'}\n                                                </div>\n                                            )}\n\n                                            <div className={`message-content ${messageType}-content`}>\n                                                {editingMessage === message.id ? (\n                                                    <div className=\"message-edit\">\n                                                        <textarea\n                                                            value={editContent}\n                                                            onChange={(e) => setEditContent(e.target.value)}\n                                                            className=\"edit-textarea\"\n                                                            autoFocus\n                                                        />\n                                                        <div className=\"edit-actions\">\n                                                            <button onClick={confirmEdit} className=\"confirm-edit\">\n                                                                ✓\n                                                            </button>\n                                                            <button onClick={cancelEditing} className=\"cancel-edit\">\n                                                                ✕\n                                                            </button>\n                                                        </div>\n                                                    </div>\n                                                ) : (\n                                                    <>\n                                                        <div className=\"message-text\">\n                                                            {message.message}\n                                                            {message.modifie === '1' && (\n                                                                <span className=\"message-edited\" title={`Modifié le ${formatDate(message.date_modification)}`}>\n                                                                    (modifié)\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                        <div className={`message-time ${messageType}-time`}>\n                                                            {formatDate(message.date_envoi)}\n                                                            {isOwnMessage && (\n                                                                <span className=\"message-status\">\n                                                                    {message.lu === '1' ? '✓✓' : '✓'}\n                                                                </span>\n                                                            )}\n                                                        </div>\n                                                    </>\n                                                )}\n                                            </div>\n\n                                            {/* 🔒 Indicateur de confidentialité (debug) */}\n                                            {process.env.NODE_ENV === 'development' && (\n                                                <div className=\"message-debug\" title={`Expéditeur: ${message.expediteur_id}, Destinataire: ${message.destinataire_id}`}>\n                                                    🔒\n                                                </div>\n                                            )}\n                                        </div>\n                                    );\n                                })}\n                                <div ref={messagesEndRef} />\n                            </div>\n                            \n                            {/* Zone de saisie */}\n                            <div className=\"message-input-container\">\n                                <div className=\"message-input-wrapper\">\n                                    <textarea\n                                        value={newMessage}\n                                        onChange={(e) => setNewMessage(e.target.value)}\n                                        placeholder=\"Tapez votre message...\"\n                                        className=\"message-input\"\n                                        rows=\"1\"\n                                        onKeyDown={(e) => {\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                sendMessage();\n                                            }\n                                        }}\n                                    />\n                                    <button\n                                        onClick={sendMessage}\n                                        className=\"send-button\"\n                                        disabled={!newMessage.trim() || loading}\n                                    >\n                                        📤\n                                    </button>\n                                </div>\n                            </div>\n                        </>\n                    )}\n                </div>\n            </div>\n            \n            {/* Menu contextuel */}\n            {showContextMenu && (\n                <div\n                    ref={contextMenuRef}\n                    className=\"context-menu\"\n                    style={{\n                        left: showContextMenu.x,\n                        top: showContextMenu.y\n                    }}\n                >\n                    {showContextMenu.message.can_modify === 1 && (\n                        <button onClick={() => startEditing(showContextMenu.message)}>\n                            ✏️ Modifier\n                        </button>\n                    )}\n                    <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_me')}>\n                        🗑️ Supprimer pour moi\n                    </button>\n                    {showContextMenu.message.can_delete_for_all === 1 && (\n                        <button onClick={() => deleteMessage(showContextMenu.message.id, 'for_everyone')}>\n                            🗑️ Supprimer pour tous\n                        </button>\n                    )}\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default MessagingSystem;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,WAAW,QAAQ,wBAAwB;AACpD,OAAO,+BAA+B;AAEtC,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,MAAM;IAAEC,IAAI;IAAEC,SAAS,EAAEC,WAAW;IAAEC;EAAgB,CAAC,GAAGN,UAAU,CAACC,WAAW,CAAC;EACjF,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACY,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoB,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEtC,MAAMoC,cAAc,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMmC,cAAc,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMoC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACnED,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAElC,IAAI,CAAC;;IAEtC;IACA,IAAIA,IAAI,EAAE;MACN;MACA,MAAMmC,WAAW,GAAG,CAACnC,IAAI,CAACoC,EAAE,EAAEpC,IAAI,CAACqC,OAAO,EAAErC,IAAI,CAACsC,cAAc,EAAEtC,IAAI,CAACuC,EAAE,EAAEvC,IAAI,CAACwC,OAAO,CAAC;MACvF,KAAK,MAAMJ,EAAE,IAAID,WAAW,EAAE;QAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;UAClBH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEE,EAAE,CAAC;UAChD,OAAOM,QAAQ,CAACN,EAAE,CAAC;QACvB;MACJ;IACJ;;IAEA;IACA,IAAI;MACA,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/CZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,UAAU,CAAC;MAExD,IAAIA,UAAU,EAAE;QACZ,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACvCV,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEY,QAAQ,CAAC;QAExD,IAAIA,QAAQ,EAAE;UACV;UACA,MAAMX,WAAW,GAAG,CAACW,QAAQ,CAACV,EAAE,EAAEU,QAAQ,CAACT,OAAO,EAAES,QAAQ,CAACR,cAAc,EAAEQ,QAAQ,CAACP,EAAE,EAAEO,QAAQ,CAACN,OAAO,CAAC;UAC3G,KAAK,MAAMJ,EAAE,IAAID,WAAW,EAAE;YAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;cAClBH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEE,EAAE,CAAC;cACjD,OAAOM,QAAQ,CAACN,EAAE,CAAC;YACvB;UACJ;QACJ;MACJ;IACJ,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACZa,OAAO,CAACgB,IAAI,CAAC,yEAAyE,EAAE7B,KAAK,CAAC;IAClG;;IAEA;IACA,MAAM8B,KAAK,GAAGN,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgB,KAAK,CAAC;IAE5C,IAAIA,KAAK,EAAE;MACP;MACA,IAAIA,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACrB,MAAMC,MAAM,GAAGF,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;QACrC,IAAIF,MAAM,IAAI,CAACX,KAAK,CAACW,MAAM,CAAC,EAAE;UAC1BnB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,MAAM,CAAC;UACjD,OAAOV,QAAQ,CAACU,MAAM,CAAC;QAC3B;MACJ;;MAEA;MACA,IAAI;QACA,IAAIF,KAAK,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;UACrB;UACA,MAAMI,OAAO,GAAGR,IAAI,CAACC,KAAK,CAACQ,IAAI,CAACN,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrD,MAAMlB,WAAW,GAAG,CAACoB,OAAO,CAACnB,EAAE,EAAEmB,OAAO,CAAClB,OAAO,EAAEkB,OAAO,CAACjB,cAAc,EAAEiB,OAAO,CAACE,GAAG,CAAC;UACtF,KAAK,MAAMrB,EAAE,IAAID,WAAW,EAAE;YAC1B,IAAIC,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,EAAE;cAClBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEE,EAAE,CAAC;cACxC,OAAOM,QAAQ,CAACN,EAAE,CAAC;YACvB;UACJ;QACJ;MACJ,CAAC,CAAC,OAAOhB,KAAK,EAAE;QACZa,OAAO,CAACgB,IAAI,CAAC,wCAAwC,EAAE7B,KAAK,CAAC;MACjE;IACJ;IAEAa,OAAO,CAACgB,IAAI,CAAC,+BAA+B,CAAC;IAC7C,OAAO,IAAI;EACf,CAAC;;EAED;EACA,MAAMS,WAAW,GAAGA,CAAA,KAAM;IACtB,MAAMN,MAAM,GAAGpB,gBAAgB,CAAC,CAAC;IACjC,OAAOoB,MAAM,IAAIA,MAAM,GAAG,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMO,gBAAgB,GAAGA,CAAA,KAAM;IAC3B1B,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;IAEpE;IACAD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAElC,IAAI,CAAC;IACtCiC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAElC,IAAI,GAAG4D,MAAM,CAACC,IAAI,CAAC7D,IAAI,CAAC,GAAG,KAAK,CAAC;IAChEiC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAElC,IAAI,GAAG4D,MAAM,CAACE,MAAM,CAAC9D,IAAI,CAAC,GAAG,KAAK,CAAC;;IAEpE;IACAiC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAChC,MAAMS,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAC/C,MAAMkB,WAAW,GAAGnB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACjDZ,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAES,UAAU,CAAC;IAC9CV,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6B,WAAW,CAAC;IAE3C,IAAIpB,UAAU,EAAE;MACZ,IAAI;QACA,MAAMqB,UAAU,GAAGjB,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACzCV,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE8B,UAAU,CAAC;QAC5C/B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,MAAM,CAACC,IAAI,CAACG,UAAU,CAAC,CAAC;QAC9D/B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0B,MAAM,CAACE,MAAM,CAACE,UAAU,CAAC,CAAC;MACtE,CAAC,CAAC,OAAOC,CAAC,EAAE;QACRhC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+B,CAAC,CAACC,OAAO,CAAC;MACjD;IACJ;;IAEA;IACAjC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;IAClC,MAAMiC,WAAW,GAAGC,cAAc,CAACvB,OAAO,CAAC,MAAM,CAAC;IAClD,MAAMwB,YAAY,GAAGD,cAAc,CAACvB,OAAO,CAAC,OAAO,CAAC;IACpDZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiC,WAAW,CAAC;IAC9ClC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmC,YAAY,CAAC;;IAEhD;IACApC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,YAAY,CAAC2B,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C,MAAME,GAAG,GAAG5B,YAAY,CAAC4B,GAAG,CAACF,CAAC,CAAC;MAC/BrC,OAAO,CAACC,GAAG,CAAC,QAAQsC,GAAG,KAAK5B,YAAY,CAACC,OAAO,CAAC2B,GAAG,CAAC,EAAE,CAAC;IAC5D;IAEAvC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;EAC/C,CAAC;EAED,MAAMuC,YAAY,GAAG,qDAAqD;;EAE1E;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI5C,cAAc,CAAC6C,OAAO,EAAE;MACxB7C,cAAc,CAAC6C,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IACjE;EACJ,CAAC;EAEDlF,SAAS,CAAC,MAAM;IACZ+E,cAAc,CAAC,CAAC;EACpB,CAAC,EAAE,CAAClE,QAAQ,CAAC,CAAC;;EAEd;EACAb,SAAS,CAAC,MAAM;IACZ,MAAMmF,kBAAkB,GAAIC,KAAK,IAAK;MAClC,IAAIhD,cAAc,CAAC4C,OAAO,IAAI,CAAC5C,cAAc,CAAC4C,OAAO,CAACK,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;QAC1EtD,kBAAkB,CAAC,IAAI,CAAC;MAC5B;IACJ,CAAC;IAEDuD,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEL,kBAAkB,CAAC;IAC1D,OAAO,MAAMI,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;EAC9E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,cAAc,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,KAAK,EAAEC,IAAI,GAAG,IAAI,KAAK;IACpE,IAAI;MACA,MAAMtC,KAAK,GAAGN,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,aAAa;MAE5D,MAAM4C,MAAM,GAAG;QACXF,MAAM;QACNG,OAAO,EAAE;UACL,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUxC,KAAK;QACpC;MACJ,CAAC;MAED,IAAIsC,IAAI,KAAKD,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,QAAQ,CAAC,EAAE;QACxEE,MAAM,CAACE,IAAI,GAAG5C,IAAI,CAAC6C,SAAS,CAACJ,IAAI,CAAC;MACtC;MAEA,MAAMK,GAAG,GAAG,GAAGpB,YAAY,WAAWa,QAAQ,EAAE;MAChDrD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;QAAE2D,GAAG;QAAEN,MAAM;QAAED,QAAQ;QAAEpC;MAAM,CAAC,CAAC;MAE7D,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAACF,GAAG,EAAEJ,MAAM,CAAC;MACzCxD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4D,QAAQ,CAACE,MAAM,EAAEF,QAAQ,CAACG,UAAU,CAAC;MAEzE,MAAMC,MAAM,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MACpClE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgE,MAAM,CAAC;MAEzC,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAC9E,KAAK,IAAI,YAAY,CAAC;MACjD;MAEA,OAAO8E,MAAM;IACjB,CAAC,CAAC,OAAO9E,KAAK,EAAE;MACZa,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAE;QAClCkE,QAAQ;QACRC,MAAM;QACNnE,KAAK,EAAEA,KAAK,CAAC8C,OAAO;QACpBoC,KAAK,EAAElF,KAAK,CAACkF;MACjB,CAAC,CAAC;MACF,MAAMlF,KAAK;IACf;EACJ,CAAC;;EAED;EACA,MAAMmF,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAC,YAAA;MACArF,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd;MACA,MAAMoF,aAAa,GAAGzE,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACyE,aAAa,EAAE;QAChBxE,OAAO,CAACgB,IAAI,CAAC,2DAA2D,CAAC;;QAEzE;QACA,MAAM,IAAIyD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;QAEvD,MAAME,WAAW,GAAG7E,gBAAgB,CAAC,CAAC;QACtC,IAAI,CAAC6E,WAAW,EAAE;UACd,MAAM,IAAIR,KAAK,CAAC,uDAAuD,CAAC;QAC5E;MACJ;MAEA,MAAMH,MAAM,GAAG,MAAMb,cAAc,CAAC,eAAe,CAAC;MAEpD,IAAI,CAACa,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAC9E,KAAK,IAAI,6CAA6C,CAAC;MAClF;;MAEA;MACA,MAAM0F,WAAW,GAAG9E,gBAAgB,CAAC,CAAC;;MAEtC;MACA,MAAM+E,mBAAmB,GAAG,CAACb,MAAM,CAACV,IAAI,IAAI,EAAE,EAAEwB,MAAM,CAACC,YAAY,IAAI;QACnE;QACA,MAAMC,SAAS,GAAGxE,QAAQ,CAACuE,YAAY,CAACE,UAAU,CAAC;QACnD,OAAOD,SAAS,IAAIA,SAAS,KAAKJ,WAAW,IAAII,SAAS,GAAG,CAAC;MAClE,CAAC,CAAC;MAEFjF,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QACjDkF,cAAc,EAAE,EAAAZ,YAAA,GAAAN,MAAM,CAACV,IAAI,cAAAgB,YAAA,uBAAXA,YAAA,CAAajC,MAAM,KAAI,CAAC;QACxC8C,eAAe,EAAEN,mBAAmB,CAACxC,MAAM;QAC3ClC,OAAO,EAAEyE,WAAW;QACpBQ,YAAY,EAAEtH,IAAI,GAAG,YAAY,GAAG;MACxC,CAAC,CAAC;MAEFK,gBAAgB,CAAC0G,mBAAmB,CAAC;IACzC,CAAC,CAAC,OAAO3F,KAAK,EAAE;MACZ,MAAMmG,YAAY,GAAGnG,KAAK,CAAC8C,OAAO,IAAI,iBAAiB;MACvD7C,QAAQ,CAAC,2CAA2C,GAAGkG,YAAY,CAAC;MACpEtF,OAAO,CAACb,KAAK,CAAC,mCAAmC,EAAE;QAC/CA,KAAK,EAAEmG,YAAY;QACnBlF,OAAO,EAAEL,gBAAgB,CAAC,CAAC;QAC3BsF,YAAY,EAAEtH,IAAI;QAClBwH,iBAAiB,EAAE5E,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C4E,kBAAkB,EAAE7E,YAAY,CAACC,OAAO,CAAC,OAAO;MACpD,CAAC,CAAC;IACN,CAAC,SAAS;MACN1B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMuG,YAAY,GAAG,MAAOR,SAAS,IAAK;IACtC,IAAI;MAAA,IAAAS,aAAA;MACAxG,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;;MAEd;MACA,MAAMoF,aAAa,GAAGzE,gBAAgB,CAAC,CAAC;MACxC,IAAI,CAACyE,aAAa,EAAE;QAChBxE,OAAO,CAACgB,IAAI,CAAC,8DAA8D,CAAC;QAC5E,MAAM,IAAIoD,KAAK,CAAC,uDAAuD,CAAC;MAC5E;MAEA,MAAMH,MAAM,GAAG,MAAMb,cAAc,CAAC,uBAAuB6B,SAAS,EAAE,CAAC;MAEvE,IAAI,CAAChB,MAAM,CAACE,OAAO,EAAE;QACjB,MAAM,IAAIC,KAAK,CAACH,MAAM,CAAC9E,KAAK,IAAI,wCAAwC,CAAC;MAC7E;;MAEA;MACA,MAAM0F,WAAW,GAAG9E,gBAAgB,CAAC,CAAC;;MAEtC;MACA,MAAM4F,cAAc,GAAG,CAAC1B,MAAM,CAACV,IAAI,IAAI,EAAE,EAAEwB,MAAM,CAAC9C,OAAO,IAAI;QACzD,MAAM2D,YAAY,GAAGnF,QAAQ,CAACwB,OAAO,CAAC4D,aAAa,CAAC;QACpD,MAAMC,cAAc,GAAGrF,QAAQ,CAACwB,OAAO,CAAC8D,eAAe,CAAC;;QAExD;QACA,OAAQH,YAAY,KAAKf,WAAW,IAAIiB,cAAc,KAAKjB,WAAW;MAC1E,CAAC,CAAC,CAACmB,GAAG,CAAC/D,OAAO,IAAI;QACd;QACA,MAAM2D,YAAY,GAAGnF,QAAQ,CAACwB,OAAO,CAAC4D,aAAa,CAAC;QAEpD,OAAO;UACH,GAAG5D,OAAO;UACVgE,YAAY,EAAEL,YAAY,KAAKf,WAAW,GAAG,MAAM,GAAG,UAAU;UAChEqB,cAAc,EAAEN,YAAY,KAAKf;QACrC,CAAC;MACL,CAAC,CAAC;MAEF7E,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;QAC1CkF,cAAc,EAAE,EAAAO,aAAA,GAAAzB,MAAM,CAACV,IAAI,cAAAmC,aAAA,uBAAXA,aAAA,CAAapD,MAAM,KAAI,CAAC;QACxC8C,eAAe,EAAEO,cAAc,CAACrD,MAAM;QACtClC,OAAO,EAAEyE,WAAW;QACpBK,UAAU,EAAED,SAAS;QACrBI,YAAY,EAAEtH,IAAI,GAAG,YAAY,GAAG;MACxC,CAAC,CAAC;MAEFS,WAAW,CAACmH,cAAc,CAAC;IAC/B,CAAC,CAAC,OAAOxG,KAAK,EAAE;MACZ,MAAMmG,YAAY,GAAGnG,KAAK,CAAC8C,OAAO,IAAI,iBAAiB;MACvD7C,QAAQ,CAAC,sCAAsC,GAAGkG,YAAY,CAAC;MAC/DtF,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAE;QAC1CA,KAAK,EAAEmG,YAAY;QACnBlF,OAAO,EAAEL,gBAAgB,CAAC,CAAC;QAC3BmF,UAAU,EAAED,SAAS;QACrBI,YAAY,EAAEtH,IAAI;QAClBwH,iBAAiB,EAAE5E,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C4E,kBAAkB,EAAE7E,YAAY,CAACC,OAAO,CAAC,OAAO;MACpD,CAAC,CAAC;IACN,CAAC,SAAS;MACN1B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMiH,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA,MAAMlC,MAAM,GAAG,MAAMb,cAAc,CAAC,OAAO,CAAC;MAC5CxE,kBAAkB,CAACqF,MAAM,CAACV,IAAI,IAAI,EAAE,CAAC;IACzC,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACZC,QAAQ,CAAC,0CAA0C,GAAGD,KAAK,CAAC8C,OAAO,CAAC;IACxE;EACJ,CAAC;;EAED;EACA,MAAMmE,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACA,MAAMnC,MAAM,GAAG,MAAMb,cAAc,CAAC,OAAO,CAAC;MAC5CxD,QAAQ,CAACqE,MAAM,CAACV,IAAI,IAAI,CAAC,CAAC,CAAC;IAC/B,CAAC,CAAC,OAAOpE,KAAK,EAAE;MACZa,OAAO,CAACb,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;EACJ,CAAC;;EAED;EACA,MAAMkH,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI,CAAC5H,UAAU,CAAC6H,IAAI,CAAC,CAAC,EAAE;IAExB,IAAI;MACA,MAAMR,cAAc,GAAGzH,oBAAoB,GAAGA,oBAAoB,CAAC6G,UAAU,GAAGnG,YAAY;MAE5F,IAAI,CAAC+G,cAAc,EAAE;QACjB1G,QAAQ,CAAC,uCAAuC,CAAC;QACjD;MACJ;MAEA,MAAMgE,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE;QACjC2C,eAAe,EAAED,cAAc;QAC/B7D,OAAO,EAAExD,UAAU,CAAC6H,IAAI,CAAC;MAC7B,CAAC,CAAC;MAEF5H,aAAa,CAAC,EAAE,CAAC;MACjBI,sBAAsB,CAAC,KAAK,CAAC;;MAE7B;MACA,MAAMwF,iBAAiB,CAAC,CAAC;MACzB,IAAIjG,oBAAoB,EAAE;QACtB,MAAMoH,YAAY,CAACpH,oBAAoB,CAAC6G,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACZC,QAAQ,CAAC,oCAAoC,GAAGD,KAAK,CAAC8C,OAAO,CAAC;IAClE;EACJ,CAAC;;EAED;EACA,MAAMsE,WAAW,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,KAAK;IACjD,IAAI;MACA,MAAMrD,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE;QAChCsD,UAAU,EAAEF,SAAS;QACrBvE,OAAO,EAAEwE;MACb,CAAC,CAAC;MAEFnH,iBAAiB,CAAC,IAAI,CAAC;MACvBE,cAAc,CAAC,EAAE,CAAC;;MAElB;MACA,IAAInB,oBAAoB,EAAE;QACtB,MAAMoH,YAAY,CAACpH,oBAAoB,CAAC6G,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACZC,QAAQ,CAAC,qCAAqC,GAAGD,KAAK,CAAC8C,OAAO,CAAC;IACnE;EACJ,CAAC;;EAED;EACA,MAAM0E,aAAa,GAAG,MAAAA,CAAOH,SAAS,EAAEI,UAAU,GAAG,QAAQ,KAAK;IAC9D,IAAI;MACA,MAAMxD,cAAc,CAAC,QAAQ,EAAE,QAAQ,EAAE;QACrCsD,UAAU,EAAEF,SAAS;QACrBK,WAAW,EAAED;MACjB,CAAC,CAAC;MAEFlH,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACA,IAAIrB,oBAAoB,EAAE;QACtB,MAAMoH,YAAY,CAACpH,oBAAoB,CAAC6G,UAAU,CAAC;MACvD;IAEJ,CAAC,CAAC,OAAO/F,KAAK,EAAE;MACZC,QAAQ,CAAC,sCAAsC,GAAGD,KAAK,CAAC8C,OAAO,CAAC;IACpE;EACJ,CAAC;;EAED;EACA,MAAM6E,kBAAkB,GAAG,MAAO9B,YAAY,IAAK;IAC/C1G,uBAAuB,CAAC0G,YAAY,CAAC;IACrClG,sBAAsB,CAAC,KAAK,CAAC;IAC7B,MAAM2G,YAAY,CAACT,YAAY,CAACE,UAAU,CAAC;EAC/C,CAAC;;EAED;EACA,MAAM6B,oBAAoB,GAAGA,CAAA,KAAM;IAC/BzI,uBAAuB,CAAC,IAAI,CAAC;IAC7BE,WAAW,CAAC,EAAE,CAAC;IACfM,sBAAsB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAED;EACA,MAAMkI,iBAAiB,GAAGA,CAAChF,CAAC,EAAEC,OAAO,KAAK;IACtCD,CAAC,CAACiF,cAAc,CAAC,CAAC;IAClBvH,kBAAkB,CAAC;MACfwH,CAAC,EAAElF,CAAC,CAACmF,OAAO;MACZC,CAAC,EAAEpF,CAAC,CAACqF,OAAO;MACZpF,OAAO,EAAEA;IACb,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMqF,YAAY,GAAIrF,OAAO,IAAK;IAC9B3C,iBAAiB,CAAC2C,OAAO,CAAC9B,EAAE,CAAC;IAC7BX,cAAc,CAACyC,OAAO,CAACA,OAAO,CAAC;IAC/BvC,kBAAkB,CAAC,IAAI,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM6H,aAAa,GAAGA,CAAA,KAAM;IACxBjI,iBAAiB,CAAC,IAAI,CAAC;IACvBE,cAAc,CAAC,EAAE,CAAC;EACtB,CAAC;;EAED;EACA,MAAMgI,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAIjI,WAAW,CAAC+G,IAAI,CAAC,CAAC,IAAIjH,cAAc,EAAE;MACtC,MAAMkH,WAAW,CAAClH,cAAc,EAAEE,WAAW,CAAC+G,IAAI,CAAC,CAAC,CAAC;IACzD;EACJ,CAAC;;EAED;EACA,MAAMmB,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAON,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QAAEC,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACnF,CAAC,MAAM,IAAIJ,QAAQ,IAAI,CAAC,EAAE;MACtB,OAAON,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEH,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrG,CAAC,MAAM;MACH,OAAOV,IAAI,CAACW,kBAAkB,CAAC,OAAO,EAAE;QAAEE,GAAG,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEL,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IACrH;EACJ,CAAC;;EAED;EACA3K,SAAS,CAAC,MAAM;IACZ,MAAMgL,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACpC1I,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;;MAE5D;MACA,IAAI,CAAClC,IAAI,EAAE;QACPiC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,MAAM,IAAIwE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAC1D;MAEA,MAAMvD,MAAM,GAAGpB,gBAAgB,CAAC,CAAC;MACjCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEkB,MAAM,CAAC;MAEjD,IAAIA,MAAM,EAAE;QACRnB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9DqE,iBAAiB,CAAC,CAAC;QACnB6B,mBAAmB,CAAC,CAAC;QACrBC,SAAS,CAAC,CAAC;MACf,CAAC,MAAM;QACHpG,OAAO,CAACgB,IAAI,CAAC,oCAAoC,CAAC;QAClD5B,QAAQ,CAAC,uDAAuD,CAAC;;QAEjE;QACAuF,UAAU,CAAC,MAAM;UACb,MAAMC,WAAW,GAAG7E,gBAAgB,CAAC,CAAC;UACtC,IAAI6E,WAAW,EAAE;YACb5E,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;YACzDb,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;YACdkF,iBAAiB,CAAC,CAAC;YACnB6B,mBAAmB,CAAC,CAAC;YACrBC,SAAS,CAAC,CAAC;UACf;QACJ,CAAC,EAAE,IAAI,CAAC;MACZ;IACJ,CAAC;IAEDsC,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAE,CAAC3K,IAAI,CAAC,CAAC;;EAEV;EACAL,SAAS,CAAC,MAAM;IACZ,MAAMiL,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/BtE,iBAAiB,CAAC,CAAC;MACnB,IAAIjG,oBAAoB,EAAE;QACtBoH,YAAY,CAACpH,oBAAoB,CAAC6G,UAAU,CAAC;MACjD;IACJ,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAM2D,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,CAACtK,oBAAoB,CAAC,CAAC;;EAE1B;EACA,MAAMmG,aAAa,GAAGzE,gBAAgB,CAAC,CAAC;;EAExC;EACA,IAAI,CAACyE,aAAa,IAAI,CAACvF,OAAO,EAAE;IAC5B;IACAzB,KAAK,CAACE,SAAS,CAAC,MAAM;MAClBgE,gBAAgB,CAAC,CAAC;IACtB,CAAC,EAAE,EAAE,CAAC;IAEN,oBACIlE,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,kBAAkB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC7B7L,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,sBAAsB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACjC7L,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,oBAAoB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBAC/B7L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,2DAA0C,CAAC,eAC/C7L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAG,iEAA+D,CAAC,eAEnE7L,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,YAAY;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACvB7L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,sCAA8B,CAAC,eACnC7L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACI7L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,wBAAsB,EAACtL,IAAI,GAAG,UAAU,GAAG,cAAmB,CAAC,eACnEP,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,mBAAiB,EAAC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuL,GAAG,MAAIvL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwL,IAAI,KAAI,YAAiB,CAAC,eACnE/L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,qBAAmB,EAAC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyL,KAAK,KAAI,YAAiB,CAAC,eACzDhM,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,4BAA0B,EAAC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,EAAE,KAAI,YAAiB,CAAC,eAC7D3C,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,iCAA+B,EAAC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,OAAO,KAAI,YAAiB,CAAC,eACvE5C,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,wCAAsC,EAAC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,cAAc,KAAI,YAAiB,CAAC,eACrF7C,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,sBAAoB,EAAC1I,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,WAAW,GAAG,UAAe,CAAC,eACvFpD,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,0BAAqB,EAAC1I,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,GAAG,aAAa,GAAG,YAAiB,CAC1F,CAAC,EAEJD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,iBACzBpD,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,UAAU;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACrB7L,KAAA,CAAAsL,aAAA;MAAAE,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAI,+CAAoC,CAAC,eACzC7L,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,YAAY;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACtB1I,YAAY,CAACC,OAAO,CAAC,MAAM,CAC3B,CACJ,CAER,CAAC,eAENpD,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzB7L,KAAA,CAAAsL,aAAA;MACIW,OAAO,EAAE/H,gBAAiB;MAC1BqH,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9B,iCAEO,CAAC,eACT7L,KAAA,CAAAsL,aAAA;MACIW,OAAO,EAAEA,CAAA,KAAM;QACX;QACA,MAAM/I,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAIF,UAAU,EAAE;UACZ,IAAI;YACA,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;YACvCV,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEY,QAAQ,CAAC;;YAEhE;YACA,MAAMX,WAAW,GAAG,CAACW,QAAQ,CAACV,EAAE,EAAEU,QAAQ,CAACT,OAAO,EAAES,QAAQ,CAACR,cAAc,EAAEQ,QAAQ,CAACP,EAAE,CAAC;YACzF,MAAMoJ,OAAO,GAAGxJ,WAAW,CAACyJ,IAAI,CAACxJ,EAAE,IAAIA,EAAE,IAAI,CAACK,KAAK,CAACL,EAAE,CAAC,CAAC;YAExD,IAAIuJ,OAAO,EAAE;cACT;cACA7I,QAAQ,CAACV,EAAE,GAAGM,QAAQ,CAACiJ,OAAO,CAAC;cAC/B/I,YAAY,CAACiJ,OAAO,CAAC,MAAM,EAAE9I,IAAI,CAAC6C,SAAS,CAAC9C,QAAQ,CAAC,CAAC;cACtDb,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;cACtD4J,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;YAC5B,CAAC,MAAM;cACHC,KAAK,CAAC,uDAAuD,CAAC;YAClE;UACJ,CAAC,CAAC,OAAOhI,CAAC,EAAE;YACRgI,KAAK,CAAC,kCAAkC,GAAGhI,CAAC,CAACC,OAAO,CAAC;UACzD;QACJ;MACJ,CAAE;MACF8G,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9B,8BAEO,CAAC,eACT7L,KAAA,CAAAsL,aAAA;MACIW,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,QAAQ,CAACG,IAAI,GAAG,QAAS;MAC/ClB,SAAS,EAAC,iBAAiB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9B,6BAEO,CAAC,eACT7L,KAAA,CAAAsL,aAAA;MACIW,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;MACxChB,SAAS,EAAC,mBAAmB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAChC,yBAEO,CACP,CACJ,CACJ,CACJ,CAAC;EAEd;EAEA,oBACI7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtB7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACtB7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAG,EAAC,CAAAtL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuL,GAAG,KAAI,aAAa,EAAC,QAAM,EAAC9E,aAAa,EAAC,GAAO,CACzF,CAAC,eACNhH,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE1J,KAAK,CAACuK,cAAc,IAAI,CAAQ,CAAC,eAChE1M,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,UAAc,CACzC,CAAC,eACP7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAE1J,KAAK,CAACwK,gBAAgB,IAAI,CAAQ,CAAC,eAClE3M,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,SAAa,CACxC,CAAC,eACP7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,WAAW;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACvB7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAElL,aAAa,CAACmE,MAAa,CAAC,eAC3D9E,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAmB,CAC9C,CACL,CACJ,CAAC,EAELlK,KAAK,iBACF3B,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,eAAe;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC1B7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAM,SAAE,EAAClK,KAAY,CAAC,eACtB3B,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAMrK,QAAQ,CAAC,EAAE,CAAE;IAAA4J,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAAC,eAC/C7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAO,mBAAiB,EAACtJ,gBAAgB,CAAC,CAAC,EAAC,cAAY,EAAChC,IAAI,GAAG,IAAI,GAAG,IAAY,CAClF,CACJ,CACR,eAEDP,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhC7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAChC7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACjC7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,eAAiB,CAAC,eACtB7L,KAAA,CAAAsL,aAAA;IACIC,SAAS,EAAC,sBAAsB;IAChCU,OAAO,EAAE1C,oBAAqB;IAC9BqD,KAAK,EAAC,uBAAuB;IAAApB,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChC,cAEO,CACP,CAAC,eAEN7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BpK,OAAO,IAAId,aAAa,CAACmE,MAAM,KAAK,CAAC,gBAClC9E,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,SAAS;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,eAAkB,CAAC,GAC5ClL,aAAa,CAACmE,MAAM,KAAK,CAAC,gBAC1B9E,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,qBAAsB,CAAC,eAC1B7L,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAE1C,oBAAqB;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,8BAE/B,CACP,CAAC,GAENlL,aAAa,CAAC6H,GAAG,CAAChB,YAAY,iBAC1BxH,KAAA,CAAAsL,aAAA;IACIvG,GAAG,EAAEyC,YAAY,CAACE,UAAW;IAC7B6D,SAAS,EAAE,qBAAqB,CAAA1K,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE6G,UAAU,MAAKF,YAAY,CAACE,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;IAC/GuE,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC9B,YAAY,CAAE;IAAAgE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEhD7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,qBAAqB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC/BrE,YAAY,CAACqF,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAC/C,CAAC,eACN/M,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BrE,YAAY,CAACqF,WAAW,eACzB7M,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9BrE,YAAY,CAACwF,YACZ,CACL,CAAC,eACNhN,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,sBAAsB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCrE,YAAY,CAACyF,eAAe,IAAI,eAChC,CAAC,eACNjN,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B7L,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B5B,UAAU,CAACzC,YAAY,CAAC0F,iBAAiB,CACxC,CAAC,EACN1F,YAAY,CAACmF,gBAAgB,GAAG,CAAC,iBAC9B3M,KAAA,CAAAsL,aAAA;IAAMC,SAAS,EAAC,cAAc;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACzBrE,YAAY,CAACmF,gBACZ,CAET,CACJ,CACJ,CACR,CAEJ,CACJ,CAAC,eAGN3M,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,YAAY;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACtBxK,mBAAmB,gBAChBrB,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,uBAAyB,CAAC,eAC9B7L,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAM3K,sBAAsB,CAAC,KAAK,CAAE;IAAAkK,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,QAAS,CAC9D,CAAC,eACN7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,0BAA0B;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACrC7L,KAAA,CAAAsL,aAAA;IACI6B,KAAK,EAAE5L,YAAa;IACpB6L,QAAQ,EAAG5I,CAAC,IAAKhD,eAAe,CAACgD,CAAC,CAACgB,MAAM,CAAC2H,KAAK,CAAE;IACjD5B,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAEvB7L,KAAA,CAAAsL,aAAA;IAAQ6B,KAAK,EAAC,EAAE;IAAA3B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,mCAAsC,CAAC,EACvD1K,eAAe,CAACqH,GAAG,CAACjI,IAAI,iBACrBP,KAAA,CAAAsL,aAAA;IAAQvG,GAAG,EAAExE,IAAI,CAACoC,EAAG;IAACwK,KAAK,EAAE5M,IAAI,CAACoC,EAAG;IAAA6I,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAChCtL,IAAI,CAACuL,GAAG,EAAC,IAAE,EAACvL,IAAI,CAAC8M,IAAI,EAAC,GACnB,CACX,CACG,CACP,CACJ,CAAC,GACNxM,oBAAoB,gBACpBb,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACxB7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC9B7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,aAAa;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GACvBhL,oBAAoB,CAACgM,WAAW,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CACvD,CAAC,eACN/M,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACI7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BhL,oBAAoB,CAACgM,WACrB,CAAC,eACN7M,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,mBAAmB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC7BhL,oBAAoB,CAACmM,YACrB,CACJ,CACJ,CACJ,CAAC,gBAENhN,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,kBAAkB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC7B7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,iBAAiB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAC5B7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAI,yBAAiB,CAAC,eACtB7L,KAAA,CAAAsL,aAAA;IAAAE,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAG,iEAA4D,CAAC,eAChE7L,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAE1C,oBAAqB;IAAAiC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAE/B,CACP,CACJ,CACR,EAGA,CAAChL,oBAAoB,IAAIQ,mBAAmB,kBACzCrB,KAAA,CAAAsL,aAAA,CAAAtL,KAAA,CAAAsN,QAAA,qBACItN,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,oBAAoB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC9B9K,QAAQ,CAACyH,GAAG,CAAC/D,OAAO,IAAI;IACrB,MAAMuC,aAAa,GAAGzE,gBAAgB,CAAC,CAAC;IACxC,MAAMgL,YAAY,GAAGtK,QAAQ,CAACwB,OAAO,CAAC4D,aAAa,CAAC,KAAKrB,aAAa;IACtE,MAAMwG,WAAW,GAAGD,YAAY,GAAG,MAAM,GAAG,UAAU;IAEtD,oBACIvN,KAAA,CAAAsL,aAAA;MACIvG,GAAG,EAAEN,OAAO,CAAC9B,EAAG;MAChB4I,SAAS,EAAE,WAAWiC,WAAW,IAAID,YAAY,GAAG,aAAa,GAAG,eAAe,EAAG;MACtFE,aAAa,EAAGjJ,CAAC,IAAKgF,iBAAiB,CAAChF,CAAC,EAAEC,OAAO,CAAE;MACpD,kBAAgBA,OAAO,CAAC4D,aAAc;MACtC,oBAAkB5D,OAAO,CAAC8D,eAAgB;MAAAiD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAGzC,CAAC0B,YAAY,iBACVvN,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC1BpH,OAAO,CAACiJ,cAAc,IAAI,aAC1B,CACR,eAED1N,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAE,mBAAmBiC,WAAW,UAAW;MAAAhC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACpDhK,cAAc,KAAK4C,OAAO,CAAC9B,EAAE,gBAC1B3C,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzB7L,KAAA,CAAAsL,aAAA;MACI6B,KAAK,EAAEpL,WAAY;MACnBqL,QAAQ,EAAG5I,CAAC,IAAKxC,cAAc,CAACwC,CAAC,CAACgB,MAAM,CAAC2H,KAAK,CAAE;MAChD5B,SAAS,EAAC,eAAe;MACzBoC,SAAS;MAAAnC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,CACZ,CAAC,eACF7L,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,gBACzB7L,KAAA,CAAAsL,aAAA;MAAQW,OAAO,EAAEjC,WAAY;MAACuB,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAE/C,CAAC,eACT7L,KAAA,CAAAsL,aAAA;MAAQW,OAAO,EAAElC,aAAc;MAACwB,SAAS,EAAC,aAAa;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,QAEhD,CACP,CACJ,CAAC,gBAEN7L,KAAA,CAAAsL,aAAA,CAAAtL,KAAA,CAAAsN,QAAA,qBACItN,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,cAAc;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GACxBpH,OAAO,CAACA,OAAO,EACfA,OAAO,CAACmJ,OAAO,KAAK,GAAG,iBACpB5N,KAAA,CAAAsL,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAACqB,KAAK,EAAE,cAAc3C,UAAU,CAACxF,OAAO,CAACoJ,iBAAiB,CAAC,EAAG;MAAArC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEzF,CAET,CAAC,eACN7L,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAE,gBAAgBiC,WAAW,OAAQ;MAAAhC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC9C5B,UAAU,CAACxF,OAAO,CAACqJ,UAAU,CAAC,EAC9BP,YAAY,iBACTvN,KAAA,CAAAsL,aAAA;MAAMC,SAAS,EAAC,gBAAgB;MAAAC,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAC3BpH,OAAO,CAACsJ,EAAE,KAAK,GAAG,GAAG,IAAI,GAAG,GAC3B,CAET,CACP,CAEL,CAAC,EAGLC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACnClO,KAAA,CAAAsL,aAAA;MAAKC,SAAS,EAAC,eAAe;MAACqB,KAAK,EAAE,eAAenI,OAAO,CAAC4D,aAAa,mBAAmB5D,OAAO,CAAC8D,eAAe,EAAG;MAAAiD,MAAA;MAAAC,QAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAA,GAAC,cAEnH,CAER,CAAC;EAEd,CAAC,CAAC,eACF7L,KAAA,CAAAsL,aAAA;IAAK6C,GAAG,EAAE9L,cAAe;IAAAmJ,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CAAE,CAC1B,CAAC,eAGN7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,yBAAyB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBACpC7L,KAAA,CAAAsL,aAAA;IAAKC,SAAS,EAAC,uBAAuB;IAAAC,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,gBAClC7L,KAAA,CAAAsL,aAAA;IACI6B,KAAK,EAAElM,UAAW;IAClBmM,QAAQ,EAAG5I,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACgB,MAAM,CAAC2H,KAAK,CAAE;IAC/CiB,WAAW,EAAC,wBAAwB;IACpC7C,SAAS,EAAC,eAAe;IACzB8C,IAAI,EAAC,GAAG;IACRC,SAAS,EAAG9J,CAAC,IAAK;MACd,IAAIA,CAAC,CAACO,GAAG,KAAK,OAAO,IAAI,CAACP,CAAC,CAAC+J,QAAQ,EAAE;QAClC/J,CAAC,CAACiF,cAAc,CAAC,CAAC;QAClBZ,WAAW,CAAC,CAAC;MACjB;IACJ,CAAE;IAAA2C,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,CACL,CAAC,eACF7L,KAAA,CAAAsL,aAAA;IACIW,OAAO,EAAEpD,WAAY;IACrB0C,SAAS,EAAC,aAAa;IACvBiD,QAAQ,EAAE,CAACvN,UAAU,CAAC6H,IAAI,CAAC,CAAC,IAAIrH,OAAQ;IAAA+J,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAC3C,cAEO,CACP,CACJ,CACP,CAEL,CACJ,CAAC,EAGL5J,eAAe,iBACZjC,KAAA,CAAAsL,aAAA;IACI6C,GAAG,EAAE7L,cAAe;IACpBiJ,SAAS,EAAC,cAAc;IACxBkD,KAAK,EAAE;MACHC,IAAI,EAAEzM,eAAe,CAACyH,CAAC;MACvBiF,GAAG,EAAE1M,eAAe,CAAC2H;IACzB,CAAE;IAAA4B,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAED5J,eAAe,CAACwC,OAAO,CAACmK,UAAU,KAAK,CAAC,iBACrC5O,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAMnC,YAAY,CAAC7H,eAAe,CAACwC,OAAO,CAAE;IAAA+G,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uBAEtD,CACX,eACD7L,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAM9C,aAAa,CAAClH,eAAe,CAACwC,OAAO,CAAC9B,EAAE,EAAE,QAAQ,CAAE;IAAA6I,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,uCAEpE,CAAC,EACR5J,eAAe,CAACwC,OAAO,CAACoK,kBAAkB,KAAK,CAAC,iBAC7C7O,KAAA,CAAAsL,aAAA;IAAQW,OAAO,EAAEA,CAAA,KAAM9C,aAAa,CAAClH,eAAe,CAACwC,OAAO,CAAC9B,EAAE,EAAE,cAAc,CAAE;IAAA6I,MAAA;IAAAC,QAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA;EAAA,GAAC,wCAE1E,CAEX,CAER,CAAC;AAEd,CAAC;AAED,eAAevL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module"}