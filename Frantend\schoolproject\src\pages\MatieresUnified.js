import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Swal from 'sweetalert2';
import Pagination from '../components/Pagination';
import usePagination from '../hooks/usePagination';
import useSearch from '../hooks/useSearch';
import '../css/UnifiedPages.css';
import '../css/Pagination.css';

const MatieresUnified = () => {
    const [matieres, setMatieres] = useState([]);
    const [loading, setLoading] = useState(true);

    // Hooks personnalisés
    const { 
        searchTerm, 
        setSearchTerm, 
        filterValue, 
        setFilterValue, 
        filteredData, 
        getUniqueFilterValues, 
        clearFilters, 
        hasActiveFilters 
    } = useSearch(
        matieres,
        ['nom', 'code', 'description'], // Champs de recherche
        'type' // Champ de filtrage (si disponible)
    );

    const { 
        paginatedData, 
        currentPage, 
        totalPages, 
        goToPage, 
        resetPagination, 
        paginationInfo 
    } = usePagination(filteredData, 10);

    useEffect(() => {
        fetchMatieres();
    }, []);

    useEffect(() => {
        resetPagination();
    }, [filteredData, resetPagination]);

    const fetchMatieres = async () => {
        try {
            const response = await axios.get('http://localhost/Project_PFE/Backend/pages/matieres/getMatieres.php');
            setMatieres(response.data);
        } catch (error) {
            console.error('Erreur lors du chargement des matières:', error);
            Swal.fire('Erreur', 'Impossible de charger les matières', 'error');
        } finally {
            setLoading(false);
        }
    };

    const handleClearFilters = () => {
        clearFilters();
    };

    if (loading) {
        return (
            <div className="unified-container">
                <div className="unified-loading">
                    <div className="unified-spinner"></div>
                    <p>Chargement des matières...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="unified-container">
            {/* En-tête */}
            <div className="unified-header">
                <h1 className="unified-title">
                    <span className="unified-title-icon">📚</span>
                    Gestion des Matières
                </h1>
                <div className="unified-header-actions">
                    <span className="unified-count">
                        {filteredData.length} matière(s)
                    </span>
                    <button className="unified-btn unified-btn-primary">
                        ➕ Nouvelle Matière
                    </button>
                </div>
            </div>

            {/* Filtres */}
            <div className="unified-filters">
                <div className="unified-filters-grid">
                    <div className="unified-search-box">
                        <input
                            type="text"
                            className="unified-search-input"
                            placeholder="🔍 Rechercher par nom, code ou description..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <span className="unified-search-icon">🔍</span>
                    </div>
                    
                    <select
                        className="unified-filter-select"
                        value={filterValue}
                        onChange={(e) => setFilterValue(e.target.value)}
                    >
                        <option value="all">Tous les types</option>
                        {getUniqueFilterValues().map(value => (
                            <option key={value} value={value}>{value}</option>
                        ))}
                    </select>
                    
                    {hasActiveFilters && (
                        <button 
                            className="unified-clear-btn"
                            onClick={handleClearFilters}
                        >
                            ✖️ Effacer
                        </button>
                    )}
                </div>
            </div>

            {/* Contenu principal */}
            <div className="unified-content">
                {filteredData.length === 0 ? (
                    <div className="unified-empty">
                        <div className="unified-empty-icon">📚</div>
                        <h3 className="unified-empty-title">Aucune matière trouvée</h3>
                        <p className="unified-empty-text">
                            {hasActiveFilters 
                                ? 'Aucune matière ne correspond à vos critères de recherche.'
                                : 'Aucune matière n\'est disponible pour le moment.'
                            }
                        </p>
                        {hasActiveFilters && (
                            <button 
                                className="unified-btn unified-btn-primary"
                                onClick={handleClearFilters}
                            >
                                Effacer les filtres
                            </button>
                        )}
                    </div>
                ) : (
                    <>
                        <table className="unified-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Code</th>
                                    <th>Nom de la Matière</th>
                                    <th>Description</th>
                                    <th>Coefficient</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {paginatedData.map((matiere) => (
                                    <tr key={matiere.id}>
                                        <td>
                                            <span className="unified-badge unified-badge-primary">
                                                #{matiere.id}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-info">
                                                {matiere.code || 'N/A'}
                                            </span>
                                        </td>
                                        <td>
                                            <strong style={{ color: '#2c3e50' }}>
                                                {matiere.nom}
                                            </strong>
                                        </td>
                                        <td>
                                            <span style={{ color: '#6c757d' }}>
                                                {matiere.description || 'Aucune description'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-warning">
                                                {matiere.coefficient || '1'}
                                            </span>
                                        </td>
                                        <td>
                                            <span className="unified-badge unified-badge-success">
                                                Actif
                                            </span>
                                        </td>
                                        <td>
                                            <div className="unified-actions">
                                                <button 
                                                    className="unified-btn unified-btn-info"
                                                    title="Voir détails"
                                                >
                                                    👁️ Voir
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-warning"
                                                    title="Modifier"
                                                >
                                                    ✏️ Modifier
                                                </button>
                                                <button 
                                                    className="unified-btn unified-btn-danger"
                                                    title="Supprimer"
                                                >
                                                    🗑️ Supprimer
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>

                        {/* Pagination */}
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={goToPage}
                            itemsPerPage={10}
                            totalItems={paginationInfo.totalItems}
                        />
                    </>
                )}
            </div>
        </div>
    );
};

export default MatieresUnified;
