import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import Swal from 'sweetalert2';
import '../css/EmploisDuTemps.css';

const EmploisDuTemps = () => {
    const { user } = useContext(AuthContext);
    const [emplois, setEmplois] = useState([]);
    const [classes, setClasses] = useState([]);
    const [matieres, setMatieres] = useState([]);
    const [enseignants, setEnseignants] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showModal, setShowModal] = useState(false);
    const [editingEmploi, setEditingEmploi] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [dayFilter, setDayFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    const [formData, setFormData] = useState({
        classe_id: '',
        jour: '',
        heure_debut: '',
        heure_fin: '',
        matiere_id: '',
        enseignant_id: ''
    });

    // Jours de la semaine
    const jours = [
        'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'
    ];

    // Vérifier les permissions
    const canManageEmplois = user?.role === 'admin';

    useEffect(() => {
        fetchEmplois();
        if (canManageEmplois) {
            fetchClasses();
            fetchMatieres();
            fetchEnseignants();
        }
    }, []);

    const fetchEmplois = async () => {
        try {
            console.log('🔄 Chargement des emplois du temps...');

            // CORRECTION URGENTE : Essayer d'abord l'API normale, puis l'API simplifiée
            let response;
            let apiUsed = '';

            try {
                response = await fetch('http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_no_auth.php');
                apiUsed = 'API normale';
            } catch (error) {
                console.warn('⚠️ API normale échouée, essai API simplifiée...');
                response = await fetch('http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_simple.php');
                apiUsed = 'API simplifiée';
            }

            console.log('🔍 DEBUG EMPLOIS API Response status:', response.status, '(' + apiUsed + ')');

            if (response.ok) {
                const data = await response.json();
                console.log('🔍 DEBUG EMPLOIS API Data:', data);

                // Vérifier si c'est un tableau comme les absences
                if (Array.isArray(data)) {
                    setEmplois(data);
                    console.log('✅ Emplois du temps chargés:', data.length, '(' + apiUsed + ')');
                } else {
                    console.warn('⚠️ Format de réponse inattendu:', data);
                    setEmplois([]);
                }
            } else {
                console.error('❌ Erreur lors du chargement des emplois du temps, status:', response.status);

                // SOLUTION DE SECOURS : Utiliser l'API simplifiée
                try {
                    console.log('🆘 Tentative avec API simplifiée...');
                    const fallbackResponse = await fetch('http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_simple.php');
                    if (fallbackResponse.ok) {
                        const fallbackData = await fallbackResponse.json();
                        if (Array.isArray(fallbackData)) {
                            setEmplois(fallbackData);
                            console.log('✅ Emplois du temps chargés via API simplifiée:', fallbackData.length);
                        }
                    }
                } catch (fallbackError) {
                    console.error('❌ Même l\'API simplifiée a échoué:', fallbackError);
                    setEmplois([]);
                }
            }
        } catch (error) {
            console.error('❌ Erreur:', error);
            setEmplois([]);
        } finally {
            setLoading(false);
        }
    };

    const fetchClasses = async () => {
        try {
            console.log('🔄 Chargement des classes...');

            // CORRECTION : Utiliser l'API sans authentification
            const response = await fetch('http://localhost/Project_PFE/Backend/pages/classes/classe.php');

            if (response.ok) {
                const data = await response.json();
                console.log('🔍 DEBUG CLASSES API Data:', data);

                if (data && data.success) {
                    setClasses(data.classes);
                    console.log('✅ Classes chargées:', data.classes.length);
                } else if (Array.isArray(data)) {
                    setClasses(data);
                    console.log('✅ Classes chargées (tableau):', data.length);
                } else {
                    setClasses([]);
                }
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des classes:', error);
            setClasses([]);
        }
    };

    const fetchMatieres = async () => {
        try {
            console.log('🔄 Chargement des matières...');

            // CORRECTION : Utiliser l'API sans authentification
            const response = await fetch('http://localhost/Project_PFE/Backend/pages/matieres/matiere.php');

            if (response.ok) {
                const data = await response.json();
                console.log('🔍 DEBUG MATIERES API Data:', data);

                if (data && data.success) {
                    setMatieres(data.matieres);
                    console.log('✅ Matières chargées:', data.matieres.length);
                } else if (Array.isArray(data)) {
                    setMatieres(data);
                    console.log('✅ Matières chargées (tableau):', data.length);
                } else {
                    setMatieres([]);
                }
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des matières:', error);
            setMatieres([]);
        }
    };

    const fetchEnseignants = async () => {
        try {
            console.log('🔄 Chargement des enseignants...');

            // CORRECTION : Utiliser l'API sans authentification
            const response = await fetch('http://localhost/Project_PFE/Backend/pages/enseignants/enseignant.php');

            if (response.ok) {
                const data = await response.json();
                console.log('🔍 DEBUG ENSEIGNANTS API Data:', data);

                if (data && data.success) {
                    setEnseignants(data.enseignants);
                    console.log('✅ Enseignants chargés:', data.enseignants.length);
                } else if (Array.isArray(data)) {
                    setEnseignants(data);
                    console.log('✅ Enseignants chargés (tableau):', data.length);
                } else {
                    setEnseignants([]);
                }
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement des enseignants:', error);
            setEnseignants([]);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            console.log('🔍 DEBUG SUBMIT EMPLOI:', formData);

            // CORRECTION : Utiliser l'API sans authentification
            const url = 'http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_no_auth.php';
            const method = editingEmploi ? 'PUT' : 'POST';
            const data = editingEmploi
                ? { ...formData, id: editingEmploi.id }
                : formData;

            console.log('🔍 Données envoyées:', data);

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            console.log('🔍 Response status:', response.status);

            if (response.ok) {
                const result = await response.json();
                console.log('✅ Réponse serveur:', result);

                Swal.fire({
                    title: 'Succès!',
                    text: editingEmploi ? 'Emploi du temps modifié avec succès' : 'Emploi du temps ajouté avec succès',
                    icon: 'success',
                    confirmButtonText: 'OK'
                });

                setShowModal(false);
                setEditingEmploi(null);
                setFormData({
                    classe_id: '',
                    jour: '',
                    heure_debut: '',
                    heure_fin: '',
                    matiere_id: '',
                    enseignant_id: ''
                });
                fetchEmplois();
            } else {
                const error = await response.json();
                console.error('❌ Erreur serveur:', error);
                Swal.fire({
                    title: 'Erreur!',
                    text: error.error || 'Une erreur est survenue',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        } catch (error) {
            console.error('Erreur:', error);
            Swal.fire({
                title: 'Erreur!',
                text: 'Une erreur est survenue lors de la sauvegarde',
                icon: 'error',
                confirmButtonText: 'OK'
            });
        }
    };

    const handleEdit = (emploi) => {
        setEditingEmploi(emploi);
        setFormData({
            classe_id: emploi.classe_id || '',
            jour: emploi.jour,
            heure_debut: emploi.heure_debut,
            heure_fin: emploi.heure_fin,
            matiere_id: emploi.matiere_id|| '',
            enseignant_id: emploi.enseignant_id|| ''
        });
        setShowModal(true);
    };

    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Êtes-vous sûr?',
            text: 'Cette action supprimera définitivement cet emploi du temps',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Oui, supprimer',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            try {
                console.log('🗑️ Suppression emploi ID:', id);

                // CORRECTION : Utiliser l'API sans authentification
                const response = await fetch('http://localhost/Project_PFE/Backend/pages/emplois-du-temps/index_no_auth.php', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id })
                });

                console.log('🔍 Delete response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('✅ Suppression réussie:', result);

                    Swal.fire({
                        title: 'Supprimé!',
                        text: 'L\'emploi du temps a été supprimé avec succès',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    });
                    fetchEmplois();
                } else {
                    const error = await response.json();
                    console.error('❌ Erreur suppression:', error);
                    Swal.fire({
                        title: 'Erreur!',
                        text: error.error || 'Erreur lors de la suppression',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            } catch (error) {
                console.error('Erreur:', error);
                Swal.fire({
                    title: 'Erreur!',
                    text: 'Une erreur est survenue lors de la suppression',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
            }
        }
    };

    // Filtrage des emplois du temps
    const filteredEmplois = emplois.filter(emploi => {
        const matchesSearch = 
            emploi.classe_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emploi.matiere_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emploi.enseignant_nom?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            emploi.enseignant_prenom?.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesDay = dayFilter === 'all' || emploi.jour === dayFilter;

        return matchesSearch && matchesDay;
    });

    // Pagination
    const totalPages = Math.ceil(filteredEmplois.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentEmplois = filteredEmplois.slice(startIndex, endIndex);

    const formatTime = (timeString) => {
        return timeString ? timeString.substring(0, 5) : '';
    };

    const getDayColor = (jour) => {
        const colors = {
            'Lundi': '#007bff',
            'Mardi': '#28a745',
            'Mercredi': '#ffc107',
            'Jeudi': '#dc3545',
            'Vendredi': '#6f42c1',
            'Samedi': '#fd7e14'
        };
        return colors[jour] || '#6c757d';
    };

    if (loading) {
        return (
            <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>Chargement des emplois du temps...</p>
            </div>
        );
    }

    return (
        <div className="emplois-du-temps-container">
            <div className="page-header">
                <h1>📅 Gestion des Emplois du Temps</h1>
                <div className="header-info">
                    <span className="total-count">
                        {filteredEmplois.length} emploi(s) du temps trouvé(s)
                        {totalPages > 1 && ` • Page ${currentPage}/${totalPages}`}
                    </span>
                    {canManageEmplois && (
                        <button 
                            className="btn btn-primary add-button"
                            onClick={() => setShowModal(true)}
                            title="Ajouter un nouvel emploi du temps - Planifier un cours avec classe, matière et enseignant"
                        >
                            <img src="/plus.png" alt="Ajouter" /> 
                            <span>Nouvel Emploi du Temps</span>
                            <div className="button-info">
                                <small>🏫 Classe • 📅 Jour/Heure • 📚 Matière • 👨‍🏫 Enseignant</small>
                            </div>
                        </button>
                    )}
                </div>
            </div>

            {/* Filtres */}
            <div className="search-filters">
                <div className="search-box">
                    <img src="/search.png" alt="Rechercher" />
                    <input
                        type="text"
                        placeholder="Rechercher par classe, matière, enseignant..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <div className="filter-group">
                    <select 
                        value={dayFilter} 
                        onChange={(e) => setDayFilter(e.target.value)}
                        className="filter-select"
                    >
                        <option value="all">Tous les jours</option>
                        {jours.map(jour => (
                            <option key={jour} value={jour}>{jour}</option>
                        ))}
                    </select>
                </div>
            </div>

            {/* Tableau des emplois du temps */}
            <div className="table-container">
                <table className="table">
                    <thead>
                        <tr>
                            <th>🏫 Classe</th>
                            <th>📅 Jour</th>
                            <th>⏰ Horaires</th>
                            <th>📚 Matière</th>
                            <th>👨‍🏫 Enseignant</th>
                            {canManageEmplois && <th>⚙️ Actions</th>}
                        </tr>
                    </thead>
                    <tbody>
                        {currentEmplois.length === 0 ? (
                            <tr>
                                <td colSpan={canManageEmplois ? "6" : "5"} className="no-data">
                                    <img src="/calendar.png" alt="Aucun emploi du temps" />
                                    <p>Aucun emploi du temps trouvé</p>
                                </td>
                            </tr>
                        ) : (
                            currentEmplois.map((emploi) => (
                                <tr key={emploi.id}>
                                    <td>
                                        <div className="classe-info">
                                            <strong>{emploi.classe_nom}</strong>
                                            <small>{emploi.niveau}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span 
                                            className="badge badge-day" 
                                            style={{ backgroundColor: getDayColor(emploi.jour) }}
                                        >
                                            {emploi.jour}
                                        </span>
                                    </td>
                                    <td>
                                        <div className="horaires-info">
                                            <strong>{formatTime(emploi.heure_debut)} - {formatTime(emploi.heure_fin)}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <div className="matiere-info">
                                            <strong>{emploi.matiere_nom}</strong>
                                            {emploi.matiere_code && <small>({emploi.matiere_code})</small>}
                                        </div>
                                    </td>
                                    <td>
                                        <div className="enseignant-info">
                                            <strong>{emploi.enseignant_nom} {emploi.enseignant_prenom}</strong>
                                            <small>{emploi.enseignant_email}</small>
                                        </div>
                                    </td>
                                    {canManageEmplois && (
                                        <td>
                                            <div className="action-buttons">
                                                <button 
                                                    className="btn btn-sm btn-warning edit-button"
                                                    onClick={() => handleEdit(emploi)}
                                                    title={`Modifier l'emploi du temps de ${emploi.classe_nom} - ${emploi.jour} ${formatTime(emploi.heure_debut)}`}
                                                >
                                                    <img src="/edit.png" alt="Modifier" />
                                                    <span className="btn-text">Modifier</span>
                                                    <div className="btn-info">
                                                        <small>✏️ Éditer horaires/matière</small>
                                                    </div>
                                                </button>
                                                <button 
                                                    className="btn btn-sm btn-danger delete-button"
                                                    onClick={() => handleDelete(emploi.id)}
                                                    title={`Supprimer définitivement l'emploi du temps de ${emploi.classe_nom} - ${emploi.jour} ${formatTime(emploi.heure_debut)}`}
                                                >
                                                    <img src="/delete.png" alt="Supprimer" />
                                                    <span className="btn-text">Supprimer</span>
                                                    <div className="btn-info">
                                                        <small>🗑️ Suppression définitive</small>
                                                    </div>
                                                </button>
                                            </div>
                                        </td>
                                    )}
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="pagination-container">
                    <div className="pagination-info">
                        Affichage de {startIndex + 1} à {Math.min(endIndex, filteredEmplois.length)} sur {filteredEmplois.length} emplois du temps
                    </div>
                    <div className="pagination">
                        <button 
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                            className="pagination-btn"
                        >
                            Précédent
                        </button>
                        
                        {[...Array(totalPages)].map((_, index) => (
                            <button
                                key={index + 1}
                                onClick={() => setCurrentPage(index + 1)}
                                className={`pagination-btn ${currentPage === index + 1 ? 'active' : ''}`}
                            >
                                {index + 1}
                            </button>
                        ))}
                        
                        <button 
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                            className="pagination-btn"
                        >
                            Suivant
                        </button>
                    </div>
                </div>
            )}

            {/* Modal d'ajout/modification */}
            {showModal && (
                <div className="modal-overlay" onClick={() => setShowModal(false)}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <div className="modal-header">
                            <h2>{editingEmploi ? 'Modifier l\'emploi du temps' : 'Nouvel emploi du temps'}</h2>
                            <button className="close-btn" onClick={() => setShowModal(false)}>×</button>
                        </div>
                        
                        <form onSubmit={handleSubmit} className="modal-form">
                            <div className="form-group">
                                <label htmlFor="classe_id">Classe *</label>
                                <select
                                    id="classe_id"
                                    value={formData.classe_id}
                                    onChange={(e) => setFormData({...formData, classe_id: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner une classe</option>
                                    {classes.map((classe) => (
                                        <option key={classe.id} value={classe.id}>
                                            {classe.nom} {classe.niveau && `- ${classe.niveau}`}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="form-group">
                                <label htmlFor="jour">Jour *</label>
                                <select
                                    id="jour"
                                    value={formData.jour}
                                    onChange={(e) => setFormData({...formData, jour: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner un jour</option>
                                    {jours.map((jour) => (
                                        <option key={jour} value={jour}>{jour}</option>
                                    ))}
                                </select>
                            </div>

                            <div className="form-group">
                                <label htmlFor="heure_debut">Heure de début *</label>
                                <input
                                    type="time"
                                    id="heure_debut"
                                    value={formData.heure_debut}
                                    onChange={(e) => setFormData({...formData, heure_debut: e.target.value})}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="heure_fin">Heure de fin *</label>
                                <input
                                    type="time"
                                    id="heure_fin"
                                    value={formData.heure_fin}
                                    onChange={(e) => setFormData({...formData, heure_fin: e.target.value})}
                                    required
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="matiere_id">Matière *</label>
                                <select
                                    id="matiere_id"
                                    value={formData.matiere_id}
                                    onChange={(e) => setFormData({...formData, matiere_id: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner une matière</option>
                                    {matieres.map((matiere) => (
                                        <option key={matiere.id} value={matiere.id}>
                                            {matiere.nom} {matiere.code && `(${matiere.code})`}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="form-group">
                                <label htmlFor="enseignant_id">Enseignant *</label>
                                <select
                                    id="enseignant_id"
                                    value={formData.enseignant_id}
                                    onChange={(e) => setFormData({...formData, enseignant_id: e.target.value})}
                                    required
                                >
                                    <option value="">Sélectionner un enseignant</option>
                                    {enseignants.map((enseignant) => (
                                        <option key={enseignant.id} value={enseignant.id}>
                                            {enseignant.nom} {enseignant.prenom}
                                        </option>
                                    ))}
                                </select>
                            </div>

                            <div className="modal-actions">
                                <button type="button" className="btn btn-secondary" onClick={() => setShowModal(false)}>
                                    Annuler
                                </button>
                                <button type="submit" className="btn btn-primary">
                                    {editingEmploi ? 'Modifier' : 'Ajouter'}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}
        </div>
    );
};

export default EmploisDuTemps;
