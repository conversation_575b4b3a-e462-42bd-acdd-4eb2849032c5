import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../context/AuthContext';
import { Link } from 'react-router-dom';
import { 
  FaUserTie, FaUsers, FaChalkboardTeacher, FaGraduationCap, 
  FaUserFriends, FaChartBar, FaCog, FaCalendarAlt, FaExclamationTriangle 
} from 'react-icons/fa';

const ResponsableDashboard = () => {
  const { user } = useContext(AuthContext);
  const [stats, setStats] = useState({
    totalEtudiants: 0,
    totalEnseignants: 0,
    totalParents: 0,
    totalClasses: 0,
    absencesAujourdhui: 0,
    coursAujourdhui: 0
  });
  const [recentActivities, setRecentActivities] = useState([]);
  const [alertes, setAlertes] = useState([]);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simuler des données pour le moment
      setStats({
        totalEtudiants: 1250,
        totalEnseignants: 85,
        totalParents: 980,
        totalClasses: 45,
        absencesAujourdhui: 23,
        coursAujourdhui: 156
      });

      setRecentActivities([
        { id: 1, type: 'inscription', message: 'Nouvel étudiant inscrit - Jean Martin', time: '10:30' },
        { id: 2, type: 'note', message: 'Notes saisies pour la classe 3A - Mathématiques', time: '09:15' },
        { id: 3, type: 'absence', message: '5 absences signalées ce matin', time: '08:45' },
        { id: 4, type: 'enseignant', message: 'Nouveau professeur ajouté - Mme Dubois', time: '08:00' }
      ]);

      setAlertes([
        { id: 1, type: 'urgent', message: 'Taux d\'absence élevé en classe 2B', priority: 'high' },
        { id: 2, type: 'info', message: 'Réunion pédagogique prévue demain', priority: 'medium' },
        { id: 3, type: 'maintenance', message: 'Maintenance serveur programmée ce weekend', priority: 'low' }
      ]);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch(type) {
      case 'inscription': return '👤';
      case 'note': return '📝';
      case 'absence': return '⚠️';
      case 'enseignant': return '👨‍🏫';
      default: return '📢';
    }
  };

  const getPriorityColor = (priority) => {
    switch(priority) {
      case 'high': return '#dc3545';
      case 'medium': return '#ffc107';
      case 'low': return '#28a745';
      default: return '#6c757d';
    }
  };

  const styles = {
    container: {
      padding: '20px',
      backgroundColor: '#f8f9fa',
      minHeight: '100vh',
    },
    header: {
      marginBottom: '30px',
    },
    welcomeText: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    subtitle: {
      color: '#666',
      fontSize: '1.1rem',
    },
    statsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
      gap: '20px',
      marginBottom: '30px',
    },
    statCard: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      display: 'flex',
      alignItems: 'center',
      transition: 'transform 0.3s ease',
      cursor: 'pointer',
    },
    statIcon: {
      fontSize: '2.5rem',
      marginRight: '20px',
      padding: '15px',
      borderRadius: '50%',
      color: 'white',
    },
    statContent: {
      flex: 1,
    },
    statNumber: {
      fontSize: '2rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '5px',
    },
    statLabel: {
      color: '#666',
      fontSize: '0.9rem',
    },
    quickActionsGrid: {
      display: 'grid',
      gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
      gap: '20px',
      marginBottom: '30px',
    },
    actionCard: {
      backgroundColor: 'white',
      padding: '20px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      textAlign: 'center',
      transition: 'transform 0.3s ease',
      cursor: 'pointer',
      textDecoration: 'none',
      color: 'inherit',
    },
    actionIcon: {
      fontSize: '2rem',
      color: '#007bff',
      marginBottom: '15px',
    },
    actionTitle: {
      fontSize: '1.1rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '10px',
    },
    actionDescription: {
      color: '#666',
      fontSize: '0.9rem',
    },
    contentGrid: {
      display: 'grid',
      gridTemplateColumns: '2fr 1fr',
      gap: '30px',
    },
    card: {
      backgroundColor: 'white',
      padding: '25px',
      borderRadius: '10px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
    },
    cardTitle: {
      fontSize: '1.3rem',
      fontWeight: 'bold',
      color: '#333',
      marginBottom: '20px',
      display: 'flex',
      alignItems: 'center',
    },
    cardIcon: {
      marginRight: '10px',
      color: '#007bff',
    },
    activityItem: {
      padding: '12px',
      borderBottom: '1px solid #eee',
      display: 'flex',
      alignItems: 'center',
    },
    activityIcon: {
      marginRight: '15px',
      fontSize: '1.2rem',
    },
    activityContent: {
      flex: 1,
    },
    activityMessage: {
      color: '#333',
      fontSize: '0.9rem',
      marginBottom: '4px',
    },
    activityTime: {
      color: '#999',
      fontSize: '0.8rem',
    },
    alertItem: {
      padding: '15px',
      borderRadius: '8px',
      marginBottom: '15px',
      borderLeft: '4px solid',
    },
    alertMessage: {
      fontWeight: 'bold',
      color: '#333',
    },
  };

  const statCards = [
    { 
      icon: FaGraduationCap, 
      number: stats.totalEtudiants, 
      label: 'Total Étudiants', 
      color: '#007bff',
      link: '/etudiants'
    },
    { 
      icon: FaChalkboardTeacher, 
      number: stats.totalEnseignants, 
      label: 'Total Enseignants', 
      color: '#28a745',
      link: '/enseignants'
    },
    { 
      icon: FaUserFriends, 
      number: stats.totalParents, 
      label: 'Total Parents', 
      color: '#ffc107',
      link: '/parentes'
    },
    { 
      icon: FaUsers, 
      number: stats.totalClasses, 
      label: 'Total Classes', 
      color: '#17a2b8',
      link: '/classes'
    },
    { 
      icon: FaExclamationTriangle, 
      number: stats.absencesAujourdhui, 
      label: "Absences Aujourd'hui", 
      color: '#dc3545'
    },
    { 
      icon: FaCalendarAlt, 
      number: stats.coursAujourdhui, 
      label: "Cours Aujourd'hui", 
      color: '#6f42c1'
    },
  ];

  const quickActions = [
    { icon: FaUsers, title: 'Gérer les Utilisateurs', description: 'Ajouter, modifier ou supprimer des utilisateurs', link: '/registers' },
    { icon: FaChalkboardTeacher, title: 'Gérer les Cours', description: 'Planifier et organiser les cours', link: '/cours' },
    { icon: FaGraduationCap, title: 'Gérer les Classes', description: 'Créer et organiser les classes', link: '/classes' },
    { icon: FaCog, title: 'Configuration', description: 'Paramètres système et configuration', link: '/roles' },
  ];

  return (
    <div style={styles.container}>
      {/* En-tête */}
      <div style={styles.header}>
        <h1 style={styles.welcomeText}>
          <FaUserTie style={{ marginRight: '15px', color: '#007bff' }} />
          Bienvenue, {user?.email || 'Responsable'}
        </h1>
        <p style={styles.subtitle}>Tableau de bord administrateur - Vue d'ensemble de l'école</p>
      </div>

      {/* Statistiques */}
      <div style={styles.statsGrid}>
        {statCards.map((stat, index) => (
          <Link
            key={index}
            to={stat.link || '#'}
            style={{ textDecoration: 'none', color: 'inherit' }}
          >
            <div 
              style={styles.statCard}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              <div style={{...styles.statIcon, backgroundColor: stat.color}}>
                <stat.icon />
              </div>
              <div style={styles.statContent}>
                <div style={styles.statNumber}>{stat.number}</div>
                <div style={styles.statLabel}>{stat.label}</div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Actions rapides */}
      <div style={styles.quickActionsGrid}>
        {quickActions.map((action, index) => (
          <Link
            key={index}
            to={action.link}
            style={styles.actionCard}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-5px)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
          >
            <div style={styles.actionIcon}>
              <action.icon />
            </div>
            <div style={styles.actionTitle}>{action.title}</div>
            <div style={styles.actionDescription}>{action.description}</div>
          </Link>
        ))}
      </div>

      {/* Contenu principal */}
      <div style={styles.contentGrid}>
        {/* Activités récentes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaChartBar style={styles.cardIcon} />
            Activités Récentes
          </h2>
          {recentActivities.map(activity => (
            <div key={activity.id} style={styles.activityItem}>
              <span style={styles.activityIcon}>
                {getActivityIcon(activity.type)}
              </span>
              <div style={styles.activityContent}>
                <div style={styles.activityMessage}>{activity.message}</div>
                <div style={styles.activityTime}>{activity.time}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Alertes */}
        <div style={styles.card}>
          <h2 style={styles.cardTitle}>
            <FaExclamationTriangle style={styles.cardIcon} />
            Alertes & Notifications
          </h2>
          {alertes.map(alerte => (
            <div 
              key={alerte.id} 
              style={{
                ...styles.alertItem,
                borderLeftColor: getPriorityColor(alerte.priority),
                backgroundColor: `${getPriorityColor(alerte.priority)}10`
              }}
            >
              <div style={styles.alertMessage}>{alerte.message}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ResponsableDashboard;
