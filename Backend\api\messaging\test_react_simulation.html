<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulation React - Test API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border-left: 4px solid #28a745; color: #155724; }
        .error { background: #f8d7da; border-left: 4px solid #dc3545; color: #721c24; }
        .info { background: #d1ecf1; border-left: 4px solid #17a2b8; color: #0c5460; }
        .warning { background: #fff3cd; border-left: 4px solid #ffc107; color: #856404; }
        button { background: #007bff; color: white; border: none; padding: 12px 25px; border-radius: 5px; cursor: pointer; margin: 8px; font-weight: 600; }
        button:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; line-height: 1.4; }
        .code-block { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; overflow-x: auto; font-family: 'Courier New', monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Simulation Exacte du Code React</h1>
        <p>Ce test reproduit exactement le code de MessagingSystem.js pour identifier le problème</p>
        
        <div class="info">
            <h3>📋 Code React Simulé</h3>
            <div class="code-block">
const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';<br>
const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {<br>
&nbsp;&nbsp;const token = localStorage.getItem('token') || 'test_user_1';<br>
&nbsp;&nbsp;const config = { method, headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token}` } };<br>
&nbsp;&nbsp;if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) config.body = JSON.stringify(data);<br>
&nbsp;&nbsp;const response = await fetch(`${API_BASE_URL}?action=${endpoint}`, config);<br>
&nbsp;&nbsp;return await response.json();<br>
};
            </div>
        </div>
        
        <button onclick="testExactReactCode()">🚀 Test Code React Exact</button>
        <button onclick="testLoadConversations()">💬 Test loadConversations()</button>
        <button onclick="testAllEndpoints()">🔧 Test Tous les Endpoints</button>
        <button onclick="clearResults()">🗑️ Effacer</button>
        
        <div id="results"></div>
    </div>

    <script>
        // Code exact de React MessagingSystem.js
        const API_BASE_URL = 'http://localhost/Project_PFE/Backend/api/messaging/';
        
        const makeAPIRequest = async (endpoint, method = 'GET', data = null) => {
            try {
                const token = localStorage.getItem('token') || 'test_user_1';
                
                const config = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                };
                
                if (data && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
                    config.body = JSON.stringify(data);
                }
                
                const response = await fetch(`${API_BASE_URL}?action=${endpoint}`, config);
                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.error || 'Erreur API');
                }
                
                return result;
            } catch (error) {
                console.error('Erreur API:', error);
                throw error;
            }
        };
        
        // Fonction exacte de React
        const loadConversations = async () => {
            try {
                const result = await makeAPIRequest('conversations');
                return result.data || [];
            } catch (error) {
                throw new Error('Impossible de charger les conversations: ' + error.message);
            }
        };
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testExactReactCode() {
            addResult('🚀 Test Code React Exact', 'Démarrage du test avec le code exact de React...', 'info');
            
            try {
                // Test 1: makeAPIRequest direct
                addResult('📡 Test 1: makeAPIRequest("test")', 'Appel direct de makeAPIRequest...', 'info');
                const testResult = await makeAPIRequest('test');
                addResult('✅ Test 1 Réussi', JSON.stringify(testResult, null, 2), 'success');
                
                // Test 2: makeAPIRequest conversations
                addResult('📡 Test 2: makeAPIRequest("conversations")', 'Appel direct de makeAPIRequest...', 'info');
                const conversationsResult = await makeAPIRequest('conversations');
                addResult('✅ Test 2 Réussi', JSON.stringify(conversationsResult, null, 2), 'success');
                
            } catch (error) {
                addResult('❌ Erreur Test Code React', `Erreur: ${error.message}\nStack: ${error.stack}`, 'error');
            }
        }
        
        async function testLoadConversations() {
            addResult('💬 Test loadConversations()', 'Test de la fonction exacte de React...', 'info');
            
            try {
                const conversations = await loadConversations();
                addResult('✅ loadConversations() Réussi', `Conversations chargées: ${conversations.length}\nDonnées: ${JSON.stringify(conversations, null, 2)}`, 'success');
            } catch (error) {
                addResult('❌ loadConversations() Échoué', `Erreur: ${error.message}`, 'error');
            }
        }
        
        async function testAllEndpoints() {
            const endpoints = ['test', 'conversations', 'users', 'stats'];
            
            for (const endpoint of endpoints) {
                try {
                    addResult(`🔧 Test ${endpoint}`, `Appel de l'endpoint ${endpoint}...`, 'info');
                    const result = await makeAPIRequest(endpoint);
                    addResult(`✅ ${endpoint} OK`, JSON.stringify(result, null, 2), 'success');
                } catch (error) {
                    addResult(`❌ ${endpoint} Erreur`, `Erreur: ${error.message}`, 'error');
                }
            }
        }
        
        // Test automatique au chargement
        window.onload = function() {
            addResult('🌐 Page Chargée', 'Simulation du code React MessagingSystem.js', 'info');
            
            // Afficher les informations de debug
            addResult('🔍 Informations Debug', `
URL de base: ${API_BASE_URL}
Token: ${localStorage.getItem('token') || 'test_user_1'}
User Agent: ${navigator.userAgent}
Origine: ${window.location.origin}
            `, 'info');
        };
    </script>
</body>
</html>
